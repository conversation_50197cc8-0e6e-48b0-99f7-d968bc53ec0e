﻿do (window, angular) ->
    app = angular.module('app')

    app.controller 'WLRegisterAccountController', [
        '$scope', '$stateParams','lepApi2', '$state' , '$sessionStorage','$rootScope',
        ($scope,  $stateParams,   lepApi2,   $state,    $sessionStorage, $rootScope) ->
            $scope.vis = {}
            $scope.vm = {}
            lepApi2.get('Account/WL/blank').then (r) -> 
                $scope.vm = r

            $scope.save = () ->
                lepApi2.post('Account/WL/Create', $scope.vm)
                .then (r) ->
                    toastr.info( "Success")
                    xx  = { username: $scope.vm.Username, password: $scope.vm.Password, extra: $stateParams.customerId }
                    lepApi2.post('Account/Authenticate',xx)
                    .then (r) ->
                            t = r.token
                            id = r.userId
                            if t
                                $sessionStorage.lepToken	 = t
                                $sessionStorage.WLCustomerId = id
                                $rootScope.WLCustomerId		 = id
                                $state.go('whitelabel.orderslist',{ customerId:  $stateParams.customerId, wlCustomerId: id}, {reload:true})


            $scope.makePostalSameAsBilling = (b) ->
                if b
                    $scope.vm.PostalAddress = angular.copy( $scope.vm.BillingAddress )

            return
        ]


    app.controller 'WLEditAccountController', [
        '$scope', '$state', '$http', 'OrderService', '$location', '$timeout', 'Utils', 'lepApi2',
        ($scope,   $state,   $http,   OrderService,   $location, $timeout, Utils,  lepApi2 ) ->
            #console.debug('YourSettingsController 0')

            $scope.vm = {}
            url = '/api/cust/settings'
            vmr = null
            vmo = null

            $scope.sortableOptions =  {
                update: (e, ui)  ->
            }

            $scope.load = () ->
                $http.get(url).then (r) ->
                    vmr = r.data
                    vmo = angular.copy(vmr)
                    $scope.vm = angular.copy(vmo)

            $scope.makePostalSameAsBilling = (b) ->
                if b then $scope.vm.PostalAddress = angular.copy($scope.vm.BillingAddress)

            $scope.save = () ->
                $http.post(url, $scope.vm).then (r) ->
                    toastr.success "Your settings has been updated"
                    $state.reload()


            $scope.load()

            $scope.updatePassword = (newpassword) ->
                $http.post('/api/cust/password', JSON.stringify(newpassword)).then  (r) ->
                    toastr.info('password updated')


            $scope.$watch('vm.BillingAddress', (n,o) ->
                if !n then return
                if  $scope.vm.PostalIsBilling
                    $scope.vm.PostalAddress = angular.copy($scope.vm.BillingAddress)
            ,true)
    ]
