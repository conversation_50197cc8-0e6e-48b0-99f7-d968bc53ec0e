{"name": "asp.net", "version": "0.0.0", "private": true, "devDependencies": {"body-parser": "^1.5.2", "bower": "^1.3.1", "browser-sync": "^2.6.5", "child_process": "^1.0.2", "coffee-script": "~1.9", "coffeelint": "^2.1.0", "del": "^1.2.1", "event-stream": "^3.3.3", "express": "^4.7.2", "gulp": "3.9.1", "gulp-angular-filesort": "^1.0.4", "gulp-autoprefixer": "^3.1.0", "gulp-babel": "5.1.0", "gulp-bower": "^0.0.10", "gulp-bower-assets": "0.0.3", "gulp-cache": "0.2.8", "gulp-changed": "^2.0.0", "gulp-coffee": "~2.3", "gulp-concat": "^2.6.1", "gulp-cssmin": "0.1.7", "gulp-debug": "^3.0.0", "gulp-htmlhint": "^4.0.2", "gulp-htmlmin": "^1.0.0", "gulp-imagemin": "2.2.1", "gulp-inject": "^4.2.0", "gulp-jshint": "^1.9.2", "gulp-less": "^3.0.3", "gulp-livereload": "^3.7.0", "gulp-load-plugins": "^1.6.0", "gulp-main-bower-files": "^1.5.2", "gulp-minify-css": "1.0.0", "gulp-ng-constant": "1.1.0", "gulp-ng-html2js": "^0.1.8", "gulp-nodemon": "^1.0.5", "gulp-order": "^1.2.0", "gulp-path": "^3.0.3", "gulp-plugins": "0.0.18", "gulp-plumber": "1.2.1", "gulp-print": "^1.1.0", "gulp-rename": "^1.4.0", "gulp-rev": "7.1.2", "gulp-rimraf": "1.0.0", "gulp-sass": "^3.2.1", "gulp-sourcemaps": "^1.12.1", "gulp-stylus": "^2.5.0", "gulp-tslint": "^7.0.1", "gulp-typescript": "^3.1.4", "gulp-uglify": "^3.0.2", "gulp-util": "^3.0.6", "jshint-stylish": "^1.0.0", "main-bower-files": "2.13.1", "method-override": "^2.1.2", "q": "^1.1.2", "rebuild-node-sass": "^1.1.0", "rimraf": "2.2.8", "shelljs": "^0.2.6", "stream-series": "^0.1.1"}, "scripts": {"postinstall": "bower install", "prestart": "npm install", "start": "node server.js", "build": "gulp release"}, "dependencies": {"@aspnet/signalr": "^1.0.2", "ci": "^2.2.0", "latest-version": "^5.1.0"}}