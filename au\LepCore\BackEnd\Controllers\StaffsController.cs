using AutoMapper;
using lep.email;
using lep.security;
using lep.user;
using lep.user.impl;
using LepCore.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using NHibernate.Criterion;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LepCore.Controllers
{
	/// <summary>
	/// </summary>
	[Produces("application/json")]
	[Route("api/Staff/[controller]")]
	[Authorize(Roles = LepRoles.Staff)]
	[ApiExplorerSettings(IgnoreApi = true)]
	public class StaffsController : Controller
	{
		private readonly IHttpContextAccessor _contextAccessor;
		private readonly IMapper _mapper;
		private readonly ISecurityApplication _securityApplication;
		private readonly IUserApplication _userApplication;
		private readonly IEmailApplication _emailApplication;

		public StaffsController(IHttpContextAccessor contextAccessor, IUserApplication userApplication, ISecurityApplication securityApplication, IMapper mapper, IEmailApplication emailApplication)
		{
			_contextAccessor = contextAccessor;
			_securityApplication = securityApplication;
			_userApplication = userApplication;
			_mapper = mapper;
			_emailApplication = emailApplication;
		}

		// called from staff pages to get a list of orders
		[HttpPost("")]
		//[ValidateActionParameters]
		[Produces(typeof(PagedResult<StaffListDTO>))]
		public IActionResult GetAll([FromBody] [Required] StaffSearchCriteria sp) //int? Id = null
		{
			var username = "";
			if (!string.IsNullOrEmpty(sp.UserName))
			{
				username = sp.UserName.Trim();
			}

			var firstname = "";
			if (!string.IsNullOrEmpty(sp.FirstName))
			{
				firstname = sp.FirstName.Trim();
			}

			var lastname = "";
			if (!string.IsNullOrEmpty(sp.LastName))
			{
				lastname = sp.LastName.Trim();
			}

			var roles = new List<Role>();

			Order order = null;
			if (!string.IsNullOrEmpty(sp.SortField))
			{
				order = new Order(sp.SortField, sp.SortDir == "true");
			}
			else
			{
				order = new Order("Username", true);
			}

			var criteria = _userApplication.StaffCriteria(username, firstname, lastname, sp.Roles ?? roles);

			var sortOrder = new[] { order };

			var list = Utils.GetPagedResult(criteria, sp.Page, 20, x => new StaffListDTO(x), sortOrder);

			return new OkObjectResult(list);
		}

		[HttpGet("{id:int}")]
		[Produces(typeof(StaffDto))]
		public IActionResult Get(int id) //int? Id = null
		{
			IStaff staff = null;

			if (id == 0)
			{
				staff = _userApplication.NewStaff();
			}
			else
			{
				staff = (IStaff)_userApplication.GetUser(id);
			}

			if (staff == null) return NotFound();

			return new OkObjectResult(staff);
		}

		[HttpPut("{id:int}")]
		//[ValidateActionParameters]
		[ReturnBadRequestOnModelError]
		public IActionResult PutStaff([FromRoute] int id, [FromBody] [Required] StaffDto dto) //int? Id = null
		{
			var staff = (Staff)_userApplication.GetUser(dto.Id);
			if (staff == null)
			{
				staff = (Staff)_userApplication.NewStaff();
				var password = dto.Password ?? Utils.GeneratePassword(10);
				_userApplication.SetPassword(staff, password);
			}
			staff = _mapper.Map(dto, staff);
			_userApplication.Save(staff);

			Response.Headers.Add("Id", staff.Id.ToString());
			return new StatusCodeResult(id == 0 ? StatusCodes.Status201Created : StatusCodes.Status204NoContent);
		}

		[HttpPatch("{id:int}")]
		//[ValidateActionParameters]
		public IActionResult PatchStaff(int id, [FromBody] JsonPatchDocument patchDoc) //int? Id = null
		{
			if (patchDoc == null)
			{
				return BadRequest("Model is null");
			}
			var o = (IStaff)_userApplication.GetUser(id);
			patchDoc.ApplyTo(o);
			_userApplication.Save(o);
			return Ok();
		}

		[HttpDelete("{id:int}")]
		[Produces(typeof(StaffDto))]
		public IActionResult Delete(int id) //int? Id = null
		{
			IStaff staff = (IStaff)_userApplication.GetUser(id);
			if (staff == null) return NotFound();
			_userApplication.Delete(staff);
			return Ok();
		}
	}
}
