﻿using Microsoft.AspNetCore.Mvc.Formatters;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace LepCore
{
	public class PatchInputFormatter : IInputFormatter
	{
		public bool CanRead(InputFormatterContext context)
		{
			if (context == null)
				throw new ArgumentNullException(nameof(context));

			var contentType = context.HttpContext.Request.ContentType;
			if (contentType == null || contentType == "application/json-patch+json")
				return true;

			return false;
		}

		public Task<InputFormatterResult> ReadAsync(InputFormatterContext context)
		{
			if (context == null)
				throw new ArgumentNullException(nameof(context));
			var request = context.HttpContext.Request;


			if (request.ContentLength == 0)
			{
				if (context.ModelType.IsValueType)
					return InputFormatterResult.SuccessAsync(Activator.CreateInstance(context.ModelType));
				return InputFormatterResult.SuccessAsync(null);
			}

			var encoding = Encoding.UTF8;//do we need to get this from the request im not sure yet
			using (var reader = new StreamReader(context.HttpContext.Request.Body))
			{
				var json = reader.ReadToEnd();
				var model = JsonConvert.DeserializeObject(json, context.ModelType);

				return InputFormatterResult.SuccessAsync(model);
			}


		}
	}
}
