using lep.barcode;
using lep.security;
using lep.user;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace LepCore.Controllers
{
	public class ScanRequest
	{
		[Required]
		public string Scanner { get; set; }

		[Required]
		public string Barcode { get; set; }

		[Required]
		public DateTime Time { get; set; }
	}

	[Produces("application/json")]
	[Route("api/[controller]")]
	[Authorize]
	[ApiExplorerSettings(IgnoreApi = true)]
	public class BarcodeController : Controller
	{
		private readonly IBarcodeService _barcodeService;
		private readonly IHttpContextAccessor _contextAccessor;
		private readonly ISecurityApplication _securityApplication;

		public BarcodeController(ISecurityApplication securityApplication, IBarcodeService barcodeService,
			IHttpContextAccessor contextAccessor)
		{
			_contextAccessor = contextAccessor;
			_securityApplication = securityApplication;
			_barcodeService = barcodeService;
		}

		[HttpPost("scan")]
		public IActionResult ScanBarcode([FromBody] ScanRequest req, [FromServices]NHibernate.ISession session)
		{
			var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();

			if (userId == 0)
			{
				userId = 1;
			}
			var _currentUser = session.Get<IStaff>(userId);

			//if (!_securityApplication.HasSession() || !(_securityApplication.Identity is IStaff) || !ModelState.IsValid)
			//    return BadRequest();
			string message = "";
			try
			{
				_barcodeService.ScanBarcode(req.Scanner, req.Barcode, DateTime.Now, out message, _currentUser);
				return new OkObjectResult(new { data = message });
			}
			catch (Exception ex)
			{
				return new BadRequestObjectResult(new { data = ex.Message });
			}
		}
	}
}
