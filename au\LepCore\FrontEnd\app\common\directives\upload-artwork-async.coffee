do (window, angular)  ->
    app = angular.module('app')


    app.directive 'lepJobArtworkUploaderAsync', () ->
        restrict		: 'EA'
    #	scope			 : { job : '=', files : '='}
        templateUrl : 'common/directives/upload-artwork-async.html'
        controller	: 'JobArtworkUploaderAysncController'


    app.controller 'JobArtworkUploaderAysncController', [
        '$scope', '$rootScope', 'OrderService',  '$location', 'enums', 'Utils', '$log', '$http', '$state', 'cfpLoadingBar',
        ($scope , $rootScope,    OrderService,    $location,   enums,   utils,   $log,   $http,   $state, cfpLoadingBar) ->
            $scope.pos2label       = utils.pos2label

            $scope.ignoreErrorAndAttachAnyways = false
            if typeof(window.ignoreErrorAndAttachAnyways) is not undefined
                $scope.ignoreErrorAndAttachAnyways = window.ignoreErrorAndAttachAnyways
                if window.ignoreErrorAndAttachAnyways
                    $scope.artworkConfirmed = true

            if window.isWL is true
                $scope.isWL = true

            $scope.suppliedArtworks = _.filter($scope.job.Artworks, (a)-> a.Supplied && !a.Ready)

            $scope.init = () ->

            $scope.init()

            $scope.downloadArtwork = OrderService.downloadArtwork
            $scope.downloadReport  = (Id) -> lepApi2.download("/api/Document/report/#{Id}/download")

            return @
        ]

