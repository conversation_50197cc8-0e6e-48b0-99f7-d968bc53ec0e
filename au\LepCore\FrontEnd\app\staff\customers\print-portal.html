﻿<form name="form">

	<div leppane="Print Portal" visible="true">
        <div class="form-horizontal">

            <div class="form-group">
                <label class="col-xs-2 control-label" for="order-no">Print Portal Enabled? </label>
                <div class="col-xs-6">
                    <span class="radio-inline">
                        <label>
                            <input name="IsPrintPortalEnabled" type="radio" ng-model="vm.IsPrintPortalEnabled" ng-value="{{false}}" />
                            No
                        </label>
                    </span>
                    <span class="radio-inline">
                        <label>
                            <input name="IsPrintPortalEnabled" type="radio" ng-model="vm.IsPrintPortalEnabled" ng-value="{{true}}" />
                            Yes
                        </label>
                    </span>

                </div>
            </div>

            <div ng-if="vm.IsPrintPortalEnabled">
            <div class="form-group">
                <label class="control-label col-xs-2" for="CustomCssURL"> Print Portal version </label>
                <div class="col-xs-2">
                    <select class="form-control" ng-model="vm.PrintPortalSettings.Version">
                        <option ng-value="1*1">Version 1</option>
                        <option ng-value="2*1">Version 2</option>
                        <option ng-value="3*1">Version 3</option>
                    </select>

                    <span class="help-block">
                        
                    </span>
                </div>
            </div>

            <div lep-white-label-setup vm="vm" ></div>



            </div>
        </div>
	</div>

	<div class="row">
		<div class="col-sm-12">
			<div class="form-horizontal col-sm-12">
				<div class="row">
					<div class="form-actions pull-right">
						<a ui-sref="staff.customers-view({Id: vm.Id})" class="btn"> <i class="glyphicon glyphicon-chevron-left"></i> Back </a>
						<button type="submit" class="btn" ng-click="save()"> <i class="glyphicon glyphicon-floppy-save"></i> Save Settings</button>
					</div>
				</div>
			</div>
		</div>
	</div>

</form>
