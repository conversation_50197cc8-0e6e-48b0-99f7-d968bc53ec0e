appStaff = angular.module('app.staff')


appStaff.controller 'OrderFreightOtherFreightController', [
    '$scope', ($scope) ->
        $scope.xx = [
            'Errand Transport',
            'Farlow Transport',
            'Thunderbolt Couriers' ,
            'Type in'
            ]
        $scope.oc2 = null
        $scope.returnCustomCourier = () ->
            name  =  if $scope.oc == 'Type in' then $scope.oc2 else $scope.oc
            price = parseFloat($scope.price)
            val = {Name: 'Other ~ ' + name, Price: price}
            $scope.closeThisDialog(val)
    ]


appStaff.controller 'OrderFreightController', [
    '$scope','$state','OrderService','lepApi2','$stateParams','ngDialog',
    ($scope , $state , OrderService , lepApi2 , $stateParams , ngDialog ) ->
        orderId           = $stateParams.orderId || $scope.orderIdPopup
        url               = "Packages/order/#{orderId}/FreightPackDetails"
        vm                = {}
        vmo               = {}
        $scope.vm         = vm
        $scope.packages   = []


        # function to obtain orders freight and packing data
        $scope.getFreightPackDetails = () ->
            lepApi2.get(url).then (d) ->
                d.PackDetail.Price = Math.round(d.PackDetail.Price * 100) / 100
                $scope.vm  = angular.copy(d)
                vmo = angular.copy(d)

                # assign rates customer sees
                OrderService.getFreights2(orderId).then (f) ->
                    $scope.freights  =  f
                    # if there are FG packages get the FG rates
                    if $scope.vm.PackDetail.FGPackageCount > 0
                        OrderService.getFreightsFromFacility(orderId, "FG")
                        .then (f) ->
                            $scope.freightsFG  =  f
                    # if there are PM packages get the PM rates
                    if $scope.vm.PackDetail.PMPackageCount > 0
                        OrderService.getFreightsFromFacility(orderId, "PM")
                        .then (f) ->
                            $scope.freightsPM  =  f

        # load the data
        $scope.getFreightPackDetails()

        # when customer selected courier changes, update the packing details
        # price and is custom flag
        $scope.$watch 'vm.Courier', (f,o) ->
            if !f or !o  then return
            if f is o then return
            so = _.find($scope.freights, (x) -> x.Value == f)
            if !so  then return
            $scope.vm.Courier = so.Value
            $scope.vm.PackDetail.Price = so.CustomerCharge
            $scope.vm.PackDetail.IsCustom = false

        $scope.otherCourier = (f) ->
            $scope.vm.PackDetail.FGCourier = "test ~ test ~ 1"
            #price += 10


        # when Staff selected FG/PM courier changes, update the packing details
        # price and is custom flag
        $scope.$watchGroup [
            'vm.PackDetail.FGCourier','vm.PackDetail.PMCourier'
        ], (n,o) ->
            price = 0
            changed = false

            if (n[0] and o[0])
                if n[0].indexOf("Other ~ Other") == 0
                    dialog =  ngDialog.open
                        template: 'staff/orders/order-freightsOthersDlg.html'
                        controller: 'OrderFreightOtherFreightController'
                    dialog.closePromise.then (r) ->
                        rv = r.value
                        if rv
                            $scope.vm.PackDetail.FGCourier = rv.Name
                            price +=  rv.Price
                            changed = true
                            $scope.vm.PackDetail.Price2 = Math.round(price * 100) / 100
                            $scope.vm.PackDetail.IsCustom = true
                else
                    # if a FG courier is found accumulate the price
                    so = _.find($scope.freightsFG, (x) -> x.Value == n[0])
                    if so
                        price +=  so.CustomerCharge
                        changed = true

            if (n[1] and o[1])
                if n[1].indexOf("Other ~ Other") == 0
                    dialog =  ngDialog.open
                        template: 'staff/orders/order-freightsOthersDlg.html'
                        controller: 'OrderFreightOtherFreightController'
                    dialog.closePromise.then (r) ->
                        rv = r.value
                        if rv
                            $scope.vm.PackDetail.PMCourier = rv.Name
                            price +=  rv.Price
                            changed = true
                            $scope.vm.PackDetail.Price2 = Math.round(price * 100) / 100
                            $scope.vm.PackDetail.IsCustom = true
                else

                    # if a PM courier is found accumulate the price  as well
                    so = _.find($scope.freightsPM, (x) -> x.Value == n[1])
                    if so
                        price +=  so.CustomerCharge
                        changed = true

            if changed
                $scope.vm.PackDetail.Price2 = Math.round(price * 100) / 100
                $scope.vm.PackDetail.IsCustom = true
        , true


        # on Save button
        $scope.setFreightPackDetails = () ->
            update = angular.copy($scope.vm)

            # Check if FG or PM PackageJson has been modified, if so mark pack
            # details as custom
            if !angular.equals(vmo.PackDetail.FGPackageJson, update.PackDetail.FGPackageJson) or
                !angular.equals(vmo.PackDetail.PMPackageJson, update.PackDetail.PMPackageJson)
                    $scope.vm.PackDetail.IsCustomPackages = true
                    update.PackDetail.IsCustomPackages = true

            # if there is a price in override price box and that differs from PackDetails price
            overridePrice = parseFloat(update.PackDetail.Price2)
            if !isNaN(overridePrice)
                if update.PackDetail.Price != overridePrice
                    update.PackDetail.Price = overridePrice
                    update.PackDetail.IsCustom = true

            update.PackDetail.IsFGCourierCustom = update.PackDetail.FGCourier != "None ~ None ~ "
            update.PackDetail.IsPMCourierCustom = update.PackDetail.PMCourier != "None ~ None ~ "
            lepApi2.post(url, update).then (d) ->
                toastr.info("saved freight data")
                $state.reload()
                #$state.go('cust.order.view', {orderId: r.Id}, {reload:true})

        $scope.backToOrder = () ->
            $state.go('staff.order.view', {orderId: orderId}, {reload:true})


        # nested package in side package tree functions
        # deletes a package node
        $scope.deleteNode  = (p,i) ->
            #p.Items = p.Items ||[]
            if p.splice
                if p[i].Items and p[i].Items.length
                    si = angular.copy(p[i].Items)
                    p.splice(i,1)
                    for ii in si
                        p.push(ii)
                else
                    p.splice(i,1)
            else if p.Items.splice
                if p.Items[i]
                    si = angular.copy(p.Items[i].Items)
                    p.Items.splice(i,1)
                    if si then p.Items.push(ii) for ii in si
                        

        
        # delete package node along with children
        $scope.deleteNode2  = (p,i) ->
            if p.splice then p.splice(0,1)
            else if p.Items.splice then p.Items.splice(0,1)

        # add a child not on the given node
        $scope.addNodeUnder = (p,i) ->
            p.Items = p.Items ||[]
            newNode = {JobId: p.JobId}
            p.Items.push(newNode)

        # replicate same node multiple times under parent
        $scope.replicateNode = (s) ->
            pcakageToReplicate  = angular.copy(s.$parent.package)
            nodeP = s.$parent.parent
            n = parseInt(window.prompt('How many times do you want to replicate this package?', 1))
            if !isNaN(n)
                while n--
                    if nodeP.Items then nodeP.Items.push(pcakageToReplicate)
                    else nodeP.push(pcakageToReplicate)


        $scope.wrapIn = (arr) ->
            if arr.length < 2 then return
            temp = arr.slice()
            kgs = _.sumBy( temp, 'Weight')
            arr = []
            arr.push({Items: temp, Weight: kgs})
            arr

        $scope.logNodeAndParent = (p,i) ->

        $scope.syncConnotes = () ->
            url  = "orders/Order/#{orderId}/SyncConnotes"
            lepApi2.post(url, {}).then (r2) ->
                toastr.success("Connotes Synchronised")
                $state.reload()


        # get list of packages so we can show them in the drop down
        lepApi2.get('packages/all', null, true).then (r) ->
            $scope.packages = r

        $scope.closeWindow = () -> window.close()
]
