using AutoMapper;
using lep;
using lep.job;
using lep.pricing;
using lep.freight.impl;
using lep.user;
using LepCore.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using NHibernate;
using System.Linq;
using NHibernate.Transform;
using System.Security.Claims;
using static lep.job.JobTypeOptions;
using System.Collections.Generic;
using System;

namespace LepCore.Controllers
{
	[Produces("application/json")]
	[Route("api/[controller]")]
	[AllowAnonymous]
	[ResponseCache(Duration = 300, VaryByHeader = "VaryByQueryKeys", Location = ResponseCacheLocation.Client)]
	public class JobOptionsController : Controller
	{
		// private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
		private NHibernate.ISession _session { get; }

		private IMapper _mapper { get; }
		private readonly IJobApplication _jobApplication;
		private readonly IPricePointApplication _pricePointApplication;

		public JobOptionsController(IJobApplication jobApplication, IPricePointApplication pricePointApplication, IMapper mapper, NHibernate.ISession session)
		{
			_jobApplication = jobApplication;
			_pricePointApplication = pricePointApplication;
			_mapper = mapper;
			_session = session;
		}

		//private bool? _currentUserIsCust;

		[ApiExplorerSettings(IgnoreApi = true)]
		public override void OnActionExecuting(ActionExecutingContext context)
		{
			_isWLCustomer = User?.Claims.Where(c => c.Type == ClaimTypes.Role)
				.Select(c => c.Value).ToList()
				.Contains(LepRoles.AnonymousWLCustomer) ?? false;
			
		}

		[HttpGet("CompleteOptions")]
		public IActionResult GetCompleteOptions()
		{
			var templates = _jobApplication.ListAllTemplates();
			var templates1 = (from t in templates
							  select new
							  {
								  Id = t.Id,
								  Name = t.Name,
								  Sizes = from s in  _jobApplication.ListSizeOptions(t)
										  select new
										  {
											  Size = s.PaperSize.Name,
											  Stocks = (from st in s.StockOptions
														orderby st.Stock.Id
														select st).Select(x => _mapper.Map<SpecStockDto>(x)).ToList()
										  }
							  }).ToList();

			var stocks = (from s in _jobApplication.ListStock()
						  orderby s.Id
						  select new
						  {
							  Id = s.Id,
							  Name = s.Name,
						  }).ToList();

			dynamic r = new
			{
				Templates = templates1,
				Stocks = stocks
			};
			return new OkObjectResult(r);
		}

		[HttpGet("PackingBoxes")]
		public IActionResult GetBoxes([FromServices] CartonFinder cartonFinder)
		{
			return new OkObjectResult(cartonFinder.ListCarton(null));
		}

		[HttpGet("DCats/{custId:int}/WL")]
		[Produces(typeof(List<int>))]
		public IActionResult GetDeniedTemplatesForCustomersWhiteLabelSite(int custId)
		{
			var result = _session.Get<ICustomerUser>(custId)?.PrintPortalSettings.DeniedTemplates ?? new List<int>();
			return new OkObjectResult(result);
		}

		[HttpGet("DCats/{custId:int}")]
		[Produces(typeof(List<int>))]
		public IActionResult GetDeniedTemplatesForCustomer(int custId)
		{
			var result = _session.Get<ICustomerUser>(custId)?.DeniedTemplates ?? new List<int>();
			return new OkObjectResult(result);
		}

		[HttpGet("Templates")]
		[Produces(typeof(List<JobTemplateDto>))]
		public IActionResult GetTemplates()
		{
			var result = _jobApplication.ListAllTemplates().OrderBy(x => x.Name).Select(x => _mapper.Map<JobTemplateDto>(x)).ToList();
			return new OkObjectResult(result);
		}

		[HttpGet("Sizes")]
		[Produces(typeof(List<PaperSizeDto>))]
		public IActionResult GetSizes()
		{
			var result = _jobApplication.ListPaperSize().Select(x => _mapper.Map<PaperSizeDto>(x)).ToList();
			return new OkObjectResult(result);
		}

		private int CustomSortForStock(IStock _)
		{
			var multiplier = 1;
			if( _.Name.Contains("Speciality")) 
					multiplier = 100;

			return _.GSM * multiplier;
		}

		[HttpGet("Stocks")]
		[Produces(typeof(List<StockDto>))]
		public IActionResult GetStocks()
		{
			var result = _jobApplication.ListStock().OrderBy(CustomSortForStock).Select(x => _mapper.Map<StockDto>(x)).ToList();
			return new OkObjectResult(result);
		}

		[HttpGet("StocksForRuns")]
		[Produces(typeof(List<StockDto>))]
		public IActionResult GetStocksForRuns()
		{
			var result = _jobApplication.ListStockForRuns()
			.OrderBy( _ => _.GSM).Select(x => _mapper.Map<StockDto>(x)).ToList();
			return new OkObjectResult(result);
		}


		[HttpGet("Stocks2")]
		[Produces(typeof(List<StockDto>))]
		public IActionResult GetStocks2()
		{
			var result = _jobApplication.ListStock().OrderBy(CustomSortForStock).Select(x => _mapper.Map<StockDto>(x)).ToList();
			return new OkObjectResult(result);
		}

		[HttpGet("GetSizeOptions/Template/{templateId:int}")]
		[Produces(typeof(List<PaperSizeDto>))]
		public IActionResult GetSizeOptions(int templateId)
		{
			var template = _jobApplication.GetJobTemplate(templateId);
			var sizeOptions = _jobApplication.ListSizeOptions(template);
			var res = from s in sizeOptions select s.PaperSize;
			var result = res.Select(x => _mapper.Map<PaperSizeDto>(x))
				.OrderBy(_ => _.Name)
				.ThenBy(_ => _.Width * _.Height).ToList();

			if (_isWLCustomer)
			{
				result = result.Where(_ => _.Name != "Custom").ToList();
			}

			return new OkObjectResult(result);
		}

		[HttpGet("GetSizeOptions/Templates")]
		[Produces(typeof(List<PaperSizeDto>))]
		public IActionResult GetSizeOptions([FromQuery]int[] templateId)
		{
			List<PaperSizeDto> r = new List<PaperSizeDto>();

			foreach (var t in templateId)
			{
				var template = _jobApplication.GetJobTemplate(t);
				var sizeOptions = _jobApplication.ListSizeOptions(template);
				var res = from s in sizeOptions select s.PaperSize;
				var result = res.Select(x => _mapper.Map<PaperSizeDto>(x))
					.OrderBy(_ => _.Name)
					.ThenBy(_ => _.Width * _.Height)
					.ToList(); 
				r.AddRange(result);
			}

			r = r.DistinctBy(_ => _.Name).ToList();//.OrderBy(_ => _.Height * _.Width)

			r = r.Where(_ => _.Id != 12).ToList();
			if (_isWLCustomer)
			{
			}
			return new OkObjectResult(r);
		}

		/// <summary>
		/// Return the list of stocks for a given template and size
		/// </summary>
		/// <param name="templateId"></param>
		/// <param name="paperSizeId"></param>
		/// <returns></returns>
		[HttpGet("GetStockOptions/Template/{templateId:int}/SizeOptions/{paperSizeId:int}")]
		[Produces(typeof(List<StockDto>))]
		public IActionResult GetStockOptions(int templateId, int paperSizeId)
		{
			var template = _jobApplication.GetJobTemplate(templateId);
			var sizeOptions = _jobApplication.ListSizeOptions(template);
			var stocks = (from s in sizeOptions
						  where s.PaperSize.Id == paperSizeId
						  from so in s.StockOptions
						  select so.Stock).Distinct();
			var result = stocks.OrderBy(CustomSortForStock) .Select(x => _mapper.Map<StockDto>(x)).ToList();

			return new OkObjectResult(result);
		}

		/// <summary>
		/// Return the list of paper size for a given template
		/// </summary>
		/// <param name="templateId"></param>
		/// <returns></returns>
		[HttpGet("GetStockOptions/Template/{templateId:int}")]
		[Produces(typeof(List<StockDto>))]
		public IActionResult GetStockOptionsForTemplate(int templateId)
		{
			var template = _jobApplication.GetJobTemplate(templateId);
			var sizeOptions = _jobApplication.ListSizeOptions(template);
			var stocks = (from s in sizeOptions
						  from so in s.StockOptions
						  select so.Stock).Distinct();
			var result = stocks.OrderBy(_ => _.Name).ThenBy(_ => _.GSM).Select(x => _mapper.Map<StockDto>(x)).ToList();
			return new OkObjectResult(result);
		}

		/// <summary>
		/// return the list of possible stocks for cover given a text stock's id
		/// applies to magazine separate cover
		/// </summary>
		/// <param name="textStockId"></param>
		/// <returns></returns>
		[HttpGet("GetStocksForCover/TextStock/{textStockId:int}")]
		[Produces(typeof(List<StockDto>))]
		public IActionResult GetStocksForCover(int textStockId)
		{
			//var s1 = $@"select Id, Name, GSM
			//		from [Stock] where 	Cover='Y'
			//		and not GSM < (select GSM from[Stock] where id = {textStockId})
			//		order by GSM";

			var s2 = $@"declare @gsm int;
select @gsm = GSM from [Stock] where id = {textStockId};
select Id, Name, GSM from [Stock] where
Cover = 'Y' and GSM > @gsm
order by GSM";

			var result = _session
				.CreateSQLQuery(s2)
				.SetResultTransformer(Transformers.AliasToBean(typeof(StockDto)))
				.List();
			return new OkObjectResult(result);
		}


		/// <summary>
		/// return the list of possible stocks for cover given a text stock's id
		/// applies to things like Wiro magazine 
		/// </summary>
		/// <param name="textStockId"></param>
		/// <returns></returns>
		[HttpGet("GetStocksForCover/250")]
		[Produces(typeof(List<StockDto>))]
		public IActionResult GetStocksForCoverOver250(int textStockId)
		{
			var result = _session
				.QueryOver<IStock>()
				.Where(s => s.IsCover  && s.GSM >= 250)
				.OrderBy(s => s.SType).Asc
				.ThenBy(s => s.GSM).Asc
				.List()
				.Select(x => _mapper.Map<StockDto>(x))
				.ToList();
			return new OkObjectResult(result);
		}

		private List<KeyValuePair<string, string>> celloOptions2All = new List<KeyValuePair<string, string>> {
						new KeyValuePair<string, string>("00","None"),
						new KeyValuePair<string, string>("10","Gloss front only"),
						new KeyValuePair<string, string>("20","Matt front only"),
						new KeyValuePair<string, string>("11","Gloss front & back"),
						new KeyValuePair<string, string>("22","Matt front & back"),
						new KeyValuePair<string, string>("12","Gloss front, Matt back")
					};

		private bool _isWLCustomer;

		[HttpGet("GetStockSpec/Template/{templateId:int}/SizeOptions/{paperSizeId:int}/stock/{stockId:int}")]
		[Produces(typeof(List<SpecStockDto>))]
		public IActionResult GetStockSpec(int templateId, int paperSizeId, int stockId)
		{
			var template = _jobApplication.GetJobTemplate(templateId);
			List<SpecStockDto> result = null;

			if (templateId == (int)JobTypeOptions.Custom)
			{
				var folds = _jobApplication.ListPaperSize().Select(x => _mapper.Map<PaperSizeDto>(x)).ToList();

				result = new List<SpecStockDto>();
				result.Add(new SpecStockDto
				{
					FrontPrintOptions = Enum.GetValues(typeof(JobPrintOptions)).Cast<JobPrintOptions>().ToList(),
					BackPrintOptions = Enum.GetValues(typeof(JobPrintOptions)).Cast<JobPrintOptions>().ToList(),
					CelloOptions2 = celloOptions2All,
					FoldOptions = folds,
					PrintType = PrintType.O,
					Magnet = true
				});

				result.Add(new SpecStockDto
				{
					FrontPrintOptions = Enum.GetValues(typeof(JobPrintOptions)).Cast<JobPrintOptions>().ToList(),
					BackPrintOptions = Enum.GetValues(typeof(JobPrintOptions)).Cast<JobPrintOptions>().ToList(),
					CelloOptions2 = celloOptions2All,
					FoldOptions = folds,
					PrintType = PrintType.D,
					Magnet = true
				});
			}
			else
			{
				var sizeOptions = _jobApplication.ListSizeOptions(template);
				result = (from s in sizeOptions
						  where s.PaperSize.Id == paperSizeId
						  from st in s.StockOptions
						  where st.Stock.Id == stockId
						  select st).Select(x => _mapper.Map<SpecStockDto>(x)).ToList();

				result.ForEach(ss =>
				{
					bool isNormal = !(ss.JobOptionSpecSize.JobTemplate.Is(PresentationFolder,PresentationFolderNDD, Magazine, MagazineNDD, MagazineSeparate , A4CalendarSelfCover, A4CalendarSeparateCover));

					ss.CelloOptions2 = _jobApplication.GetCelloOptions(ss.Id, isNormal).ToList();

					if (_isWLCustomer)
					{
						ss.FoldOptions = ss.FoldOptions.Where(_ => _.Name != "Custom").ToList();
					}
				});
			}

			return new OkObjectResult(result);
		}

		[HttpGet("GetFoldOptions/SpecStock/{jobOptionSpecStockId:int}")]
		[Produces(typeof(List<IPaperSize>))]
		public IActionResult GetFoldOptions(int jobOptionSpecStockId)
		{
			var specStock = _jobApplication.GetSpecStock(jobOptionSpecStockId);
			var result = specStock.FoldOptions;

			if (_isWLCustomer)
			{
				result = result.Where(_ => _.Name != "Custom").ToList();
			}

			return new OkObjectResult(result);
		}

		[HttpGet("GetPrintTypes/Template/{templateId:int}/SizeOptions/{paperSizeId:int}/stock/{stockId:int}/quantity/{quantity:int}")]
		[Produces(typeof(PrintType?))]
		public IActionResult GetPrintTypes(int templateId, int paperSizeId, int stockId, int quantity)
		{
			PrintType? result;

			if (templateId == (int)JobTypeOptions.Custom)
			{
				result = PrintType.B;
			}
			else
			{
				if (quantity == 0)
					result = _pricePointApplication.GetPrintTypesAll(templateId, paperSizeId, stockId);
				else
					result = _pricePointApplication.GetPrintTypesAvailable(templateId, paperSizeId, stockId, quantity);
			}
			return new OkObjectResult(result);
		}

		[HttpGet("GetBindingOptions/Template/{templateId:int}/SizeOptions/{paperSizeId:int}/stock/{stockId:int}/PrintType/{printType}/pages/{pages:int}")]
		[Produces(typeof(IList<IdAndName<IBindingOption>>))]
		public IActionResult GetBindingOptions(int templateId, int paperSizeId, int stockId, PrintType printType, int pages)
		{
			var result = _jobApplication
				.FindBindingOptions(templateId, paperSizeId, stockId, printType, pages)
				.Select(x => new IdAndName<IBindingOption> { Id = x.BindingOptionId.Id, Name = x.BindingOptionId.Name })
				.DistinctBy(x => x.Id)
				.ToList();
			return new OkObjectResult(result);
		}

		// todo: the following 2 could potentially be a part of GetStockSpec
		// based on a spec stock get the allowed celloglaze options
		[HttpGet("GetCelloOptions/{stockSpecId:int}/{isNormal:bool}")]
		[Produces(typeof(IList<KeyValuePair<string, string>>))]
		public IActionResult GetCelloOptions(int stockSpecId, bool isNormal)
		{
			var result = _jobApplication.GetCelloOptions(stockSpecId, isNormal);
			return new OkObjectResult(result);
		}

		// based on a spec stock get the allowed number of coloured sides
		[HttpGet("GetColourOptions/{stockSpecId:int}")]
		[Produces(typeof(IList<KeyValuePair<string, string>>))]
		public IActionResult GetColourOptions(int stockSpecId)
		{
			var stockSpec = _jobApplication.GetSpecStock(stockSpecId);
			IList<int> result = _jobApplication.GetColourOptions(stockSpec);
			return new OkObjectResult(result);
		}

		//[HttpGet("GetCelloOptions/{stockSpecId:int}/{isNormal:bool}")]
		//[Produces(typeof(Dictionary<JobTypeOptions, int[]>))]
		//public IActionResult GetSetQualityValues ()
		//{
		//    var qty = new Dictionary<JobTypeOptions, int[]>();
		//    qty[JobTypeOptions.DL] = new[] { 1000, 2000, 5000, 10000 };
		//    qty[JobTypeOptions.Brochure] = new[] { 1000, 5000 };
		//    qty[JobTypeOptions.DLSpecial] = new[] { 2000 };
		//    qty[JobTypeOptions.Brochure] = new[] { 2000, 5000, 10000 };
		//    qty[JobTypeOptions.BrochureSpecial] = new[] { 1000 };
		//    return new OkObjectResult(qty);
		//}

		[HttpGet("GetBrohureMailHouses")]
		public IActionResult GetBrohureMailHouses()
		{
			var mailHouses = _jobApplication.GetBrohureMailHouses().ToList();
			return new OkObjectResult(mailHouses);
		}

		[HttpGet("enums")]
		[ApiExplorerSettings(IgnoreApi = true)]
		public dynamic GetEnumns()
		{
			return L.Main();
		}


		[HttpGet("GetAllCellosForOverride")]
		[ApiExplorerSettings(IgnoreApi = true)]
		public dynamic GetAllCellosForOverride()
		{
			return  CelloUtils.GetAllPossibleCelloOptions();
		}

		[HttpGet("portal/OptionsCombined")]
		public IActionResult PortalOptionsCombined([FromQuery] int templateId, [FromQuery] string paperSizeName, [FromQuery] string printType)
		{
			bool hasTrim = (paperSizeName != null);
			bool hasPrintType = (printType != null);

			// availableTrims *************************************************************************************************
			string s = $@"SELECT ps.Name
						FROM JobOptionSpecSize AS s INNER JOIN
						PaperSize AS ps ON s.PaperSizeId = ps.Id
						WHERE (s.JobOptionId = { templateId}) and ps.Name <> 'Custom'  ";
			var availableTrims = _session.CreateSQLQuery(s).List<string>();
			if (availableTrims.Any()) availableTrims.Insert(0, "Any");

			// availablePrintTypes *************************************************************************************************
			s = $@"SELECT DISTINCT st.PrintType
					FROM JobOptionSpecStock AS st INNER JOIN
					JobOptionSpecSize AS s ON st.JobOptionSpecSizeId = s.Id INNER JOIN
					PaperSize AS fps ON s.PaperSizeId = fps.Id
					WHERE s.JobOptionId = { templateId}";
			s += hasTrim ? $" AND  (fps.Name = '{paperSizeName}' )" : "";

			var availablePrintTypes = _session.CreateSQLQuery(s).List<string>();
			availablePrintTypes = availablePrintTypes.Select(_ => ((PrintType)Enum.Parse(typeof(PrintType), _)).ToDescription()).ToList();
			if (availablePrintTypes.Any()) availablePrintTypes.Insert(0, "Any");

			// availableFolds *************************************************************************************************
			s = $@"
				SELECT DISTINCT  PaperSize.Name
				FROM            JobOptionSpecFold AS f1 INNER JOIN
										 PaperSize ON f1.PaperSizeId = PaperSize.Id
				WHERE        (f1.JobOptionSpecStockId IN
									(SELECT        st.Id
									FROM            JobOptionSpecStock AS st INNER JOIN
																JobOptionSpecSize AS s ON st.JobOptionSpecSizeId = s.Id INNER JOIN
																PaperSize AS fps ON s.PaperSizeId = fps.Id
				where s.JobOptionId = {templateId} ";
			s += hasTrim ? $" AND  (fps.Name = '{paperSizeName}' )" : "";
			s += hasPrintType ? $"  AND  (st.PrintType = '{printType}' )" : "";

			s += ")) and PaperSize.Name <> 'Custom'";

			var availableFolds = _session.CreateSQLQuery(s).List<string>();
			if (availableFolds.Any()) availableFolds.Insert(0, "Any");

			// availableCellos *************************************************************************************************
			s = $@"Select  distinct    Concat( c1.CelloFront, '/' , c1.CelloBack)
					from  JobOptionSpecCello c1 where JobOptionSpecStockId in (
					SELECT       st.Id
					FROM            JobOptionSpecStock AS st INNER JOIN
									JobOptionSpecSize AS s ON st.JobOptionSpecSizeId = s.Id INNER JOIN
									 PaperSize AS fps ON s.PaperSizeId = fps.Id
					where s.JobOptionId = { templateId}";
			s += hasTrim ? $" AND  (fps.Name = '{paperSizeName}')" : "";
			s += hasPrintType ? $"  AND  (st.PrintType = '{printType}')" : "";

			s += ")";

			var availableCellos = _session.CreateSQLQuery(s).List<string>();
			if (availableCellos.Any()) availableCellos.Insert(0, "Any");

			return new OkObjectResult(new { availableTrims, availablePrintTypes, availableFolds, availableCellos });
		}
	}
}
