﻿<div ng-repeat="o in ordersList  " class="order-box">
	<div class="row order-row" ng-init="o.shown=open">

        <div class="col-xs-7  rpad0 form-control-static">
            <a class="bold" ng-click="openCustOrder(o.Id)">Order #{{::o.Id}}</a>

            <sup>
                <span ng-if="::o.IsWLOrder" class="bold">


                    <span ng-if="::o.IsWLOrderPaidFor" style="color:green" title="{{::o.WLOrderPaymentDetails}}"> Web </span>
                    <span ng-if="::!o.IsWLOrderPaidFor" style="color:red"> Web</span>




                </span>
            </sup>
            <!--&nbsp;&nbsp;  {{::o.StatusC}}-->
            &nbsp;&nbsp;  {{::o.WLCustomerName}}



        </div>
		<!--<div class="col-xs-3  form-control-static">

		</div>-->
		<div class="col-xs-4  form-control-static right">

			<span ng-if="::o.SubmissionDate!=null" title="Submission date">
				{{::o.SubmissionDate | datex:'dd-MMM-yyyy':'not submitted'}}
			</span>
		</div>
		<div class="col-xs-1 lpad0 pull-right" ng-click="o.shown = (!!!o.shown)">
			<a class="cbtn btn bold">
				<i class="qa glyphicon ng-class:{'glyphicon-chevron-down': !o.shown,'glyphicon-chevron-up': o.shown } "></i>
			</a>
		</div>
	</div>

	<div class="row jobs-row" ng-show="o.shown">
		<div class="col-xs-12 flash">
			<div class="line"></div>
			<div ng-repeat="j in ::o.Jobs" class="row job-box">
					
				<div class="col-xs-2 " style="text-align:center">
					<div ng-if="(j.Thumbs.length>0)">
						<span ng-repeat="t in j.Thumbs track by $index">
							<img loading="lazy" height="50" bn-lazy-src="/api/orders/Job/{{::j.Id}}/thumb/{{t}}"
							     class="template-img1 grow " />
						</span>
					</div>
					<img ng-if="(j.Thumbs.length==0)"
							 ng-src="{{imgLookup(j.TemplateId)}}"
							 class="template-img1 " />
				</div>
				<div class="col-xs-6">
					{{::j.Name}}  -  {{::j.Quantity}} {{::$root.enums.ValueDesc.JobTypeOptions[j.TemplateId]}}
					<div class="help-block"><span style="cursor:pointer" ng-click="openCustJob(o.Id,j.Id)" class="bold">job #{{::j.Id}}</span></div>
				</div>

				<div class="col-xs-3" ng-if="!pp">
					<br />

					<div class="progress progress-cust" >
						<div class="progress-bar bg-info   ng-class:{'progress-bar-success': j.P == 99}"
							 role="progressbar" aria-valuenow="{{::j.P}}" aria-valuemin="0" aria-valuemax="100"
							 ng-style="{width:(j.P+ '%')}">
						</div>
						<!--<div class="popOver" data-animation="false" data-toggle="tooltip"
							 data-placement="top" title="{{::$root.enums.ValueDesc.JobStatusOptions[j.Status]}}  {{j.StatusC}}"
							 style="width:1px; display: table;">
						</div>-->


					</div>
					<span ng-style="{left: ((j.P - 3)+ '%')}" style="top: -10px;"
						  data-balloon-visible data-balloon="{{::$root.enums.ValueDesc.JobStatusOptions[j.Status]}} {{j.StatusC}}" data-balloon-pos="up">
					</span>

					<div ng-click="openCustJob(o.Id,j.Id)" class="bold">
						<div ng-if="j.SupplyArtworkApproval == $root.enums.KeyVal.JobApprovalOptions.Rejected" style="color: red" role="alert">
							<i class="glyphicon glyphicon-warning-sign"></i> Supplied artwork rejected
						</div>
						<div ng-if="j.ReadyArtworkApproval == $root.enums.KeyVal.JobApprovalOptions.NeedsApproval" style="color: purple" role="alert">
							<i class="glyphicon glyphicon-warning-sign"></i> Ready artwork requires approval
						</div>

						<div ng-if="j.QuoteNeedApprove == true" ng-style="{color: j.StatusCss}" role="alert">
							<i class="glyphicon glyphicon-warning-sign"></i> Needs approval
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

</div>
