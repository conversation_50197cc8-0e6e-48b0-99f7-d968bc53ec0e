do (window, angular) ->
    app = angular.module('app')

    #
    b = '/images/ProductIcon2/'
    iCG = 'calendar.svg'
    iBC = 'Business-Cards.svg'
    iBr = 'brochure-folded.svg'
    iMag = 'Magazines-Booklets.svg'
    iStat = 'Stationery.svg'
    iBp = 'BannersPullUps.svg'
    iNcr = 'NCRBooks.svg'
    iAs = 'AdhesiveSignsStickers.svg'
    iPf = 'Presentation-Folders.svg'
    h = {
    26:iBC
    27:iBC
    1: iBC
    7:'calendar.svg'
    23: iBr
    18: iBr
    7:  iBr
    22: iMag
    8: iMag
    14: iMag
    24:iStat
    25:iStat
    5:iStat
    6:iStat
    21: iPf
    9: iPf
    10:'Posters.svg'
    11:'Postcards.svg'
    16:'Notepads-Deskpads.svg'
    15:'Golf-Score-Cards.svg'
    28:iStat
    29:iStat
    32:iBp
    33:iBp
    34:iBp
    35:iBp
    36:iBp
    37:iBp
    38:iBp
    41: iAs
    42: iAs
    43: iAs
    44: iAs
    51: iNcr
    52: iNcr
    53: iNcr
    71: iCG
    72: iCG
    73: iCG
    74: iCG
    75: iCG
    }
    tid2img = (tid) ->
        (b + h[tid]) || ''

    app.constant('imgLookup', tid2img);
    app.run(['$rootScope', ($rootScope)->
        $rootScope.imgLookup = tid2img
    ])
