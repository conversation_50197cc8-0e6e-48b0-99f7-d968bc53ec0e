{
    "IMP": {
        "HotfolderPath": "\\\\ppp01\\HotFolderRoot\\MyLEP"
    },
    "TestBox": true,
    "HolidayMode": false,
    "Logging": {
        "IncludeScopes": false,
        "LogLevel": {
            "Default": "Debug",
            "System": "Debug",
            "Microsoft": "Debug",
            "Microsoft.AspNetCore.Mvc": "Debug",
            "Microsoft.AspNetCore.Authentication": "Debug",
            "Microsoft.AspNetCore.Routing.RouteBase": "Debug",
            "Microsoft.AspNetCore.Server.Kestrel": "Debug",
            "NHibernate": "Debug",
            "NHibernate.SQL": "Debug",
            "Microsoft.AspNetCore.SignalR": "None",
            "Microsoft.AspNetCore.Hosting": "None",
            "Microsoft.AspNetCore.StaticFiles": "None"
        }
    },
    "jobboard": {
        "messagequeue": ".\\Private$\\leponline.jobboardqueue"
    },

    "CustomerLogoDirectory": "C:\\lepdata\\logos",
    "DataDirectory": "C:\\lepdata",
    "StaticAssets": "C:\\LepSF\\au\\StaticAssets",
    "OldDataDirectory": "c:\\lepdata\\oldorders",
    "DataDirectoryPC": "C:\\lepdata",
    "DataDirectoryMac": "C:\\lepdata",
    "InvoicerPDFFolder": "c:\\LEPDATA\\INVOICER",

    //"AbsolutePathURL": "http://lepold.localtest.me",

    "lepcrm.webservice.url": "http://winston.internal.lepcolourprinters.com.au:1000/MSCRMService.asmx",
    "compdata.webservice.url": "http://harry:8089/iFreightChargeEnquiryService.svc",
    "AbsolutePathURL": "http://localhost:5000",

    "reports": {
        "LepQuote": "C:\\LepData\\labels2\\t1.frx"
    },

    "email": {
        "sendMail": false,
        "server": "smtp.sendgrid.net",
        "port": "587",
        "username": "<EMAIL>",
        "password": "*********************************************************************"
    },
    "print.messagequeue": ".\\Private$\\leponline.messageprintqueue",
    "jobboard.messagequeue": ".\\Private$\\leponline.jobboardqueue",

    "Nhibernate": {
        "Con": "Data Source=.; user id=sa; password=********$%MpHU9;Initial Catalog=PRD_AU;MultipleActiveResultSets=true;Max Pool Size=10;App=LepCore",
        "Con2": "Data Source=.; user id=sa; password=********$%MpHU9;Initial Catalog=PRD_AU_CustNote;MultipleActiveResultSets=true;Max Pool Size=10;App=LepCore"
    },

    "SupplyMaster": {
        "Con": "Data Source=*************; user id=sa; password=*************; Initial Catalog=SupplyMaster;MultipleActiveResultSets=true"
    },

    "Seq": {
        "ServerUrl": "http://localhost:5341",
        "MinimumLevel": "Trace",
        "LevelOverride": {
            "Microsoft": "Warning"
        }
    },

    "FreightProvider": "SmartFreight", // or  CompData "current"

    "Labels": {
        "LogoLabel": "c:\\LepData\\Labels2\\LogoLabel_102x73mm.frx",
        "PayMeLabel": "c:\\LepData\\Labels2\\PayMeLabel.frx",
        "FillingLabel": "c:\\LepData\\Labels2\\FillingLabel.frx",
        "SampleLabel": "c:\\LepData\\Labels2\\SampleLabel.frx",
        "AddressA4Label": "c:\\LepData\\Labels2\\AddressA4Label.frx",
        "PickupLabel": "c:\\LepData\\Labels2\\PickupLabel.frx",
        "OneDeliveryOnly": "c:\\LepData\\Labels2\\OneDeliveryOnly.frx",
        "AddressLabel": "c:\\LepData\\Labels2\\SampleLabel.frx",
        "AddressLabelOther": "c:\\LepData\\Labels2\\SampleLabel.frx",
        "CartonLabel": "c:\\LepData\\labels2\\CartonLabel.frx",
        "CartonLabelMH": "c:\\LepData\\labels2\\CartonLabelMH.frx"
    },

    "GhostScript": "C:\\Program Files\\gs\\gs10.00.0\\bin\\gswin64c.exe",
    "PdfTk": "C:\\LepData\\\\pdftk.exe",
    "LibTiff": "C:\\LepData\\LibTiff\\tiff2pdf.exe",
    "PdfToPrinter": "c:\\LepData\\PdfToPrinter.exe",

    "AutomatedArtworkCheck": {
        "Enabled": true,
        "Timeout": "00:00:40",

        "Method": "DoPitStopViaCommandLine",
        "profiles": {
            "default": "C:\\LEPDATA\\aac\\default.ppp",
            "spotcolor": "C:\\LEPDATA\\aac\\Spotcolour.ppp",
            "wideformat": "C:\\LEPDATA\\aac\\Wideformat.ppp"
        },

        "DoPitStopViaCommandLine": {
            "path": "C:\\Program Files\\Enfocus\\Enfocus PitStop Server 24\\PitStopServerCLI.exe",
            "mutator": "C:\\LEPDATA\\LEP Check 2013-1.ppp"
        },

        "DoPitStopViaHotFolder": {
            "input": "\\\\Henry\\hotfolder\\Input Folder",
            "output": "\\\\Henry\\hotfolder\\Output Folder"
        }
    },

    "Dispatchers": [
        {
            "Name": "FG-DISTRIB-02",
            "DispatchFacility": "FG"
        },
        {
            "Name": "FG-DISTRIB-03",
            "DispatchFacility": "FG"
        },
        {
            "Name": "PM-DISTRIB-01",
            "DispatchFacility": "PM"
        },
        {
            "Name": "PM-BINDERY-05",
            "DispatchFacility": "PM"
        }
    ],

    "SmartFreight": {
        "Url": {
            "SFOv1": "http://api-r1.smartfreight.com/api/soap/classic",
            "DeliveryOptions": "http://api-r1.smartfreight.com/api/soap/deliveryoptions"
        },

        "Senders": {
            "LEPQLD": {
                "Id": "NWM",
                "Key": "RqB0fJOapwP79AVDdzF5Pgg7fESwF4GxHQ"
            },
            "TESTQLD": {
                "Id": "RqB0fJOapwP79AVDdzF5Pgg7fESwF4GxHQ",
                "Key": ""
            },

            "LEPVIC": {
                "Id": "NGU",
                "Key": "JoIJ2bXndxCk0V3ZfkM6VxKuzv1qgEAiA0"
            },

            "TESTVIC": {
                "Id": "NGU",
                "Key": "JoIJ2bXndxCk0V3ZfkM6VxKuzv1qgEAiA0"
            }
        }
    },


    "Serilog": {
        "AllowedHosts": "*",
        "Using": [
            //"Serilog.Sinks.Console",
            //"Serilog.Sinks.File",
            "Serilog.Sinks.Seq"

        ],
        "MinimumLevel": {
            "Default": "Debug",
            "Override": {
                "Microsoft": "Warning",
                "System": "Warning"
            }
        },

        "WriteTo": [
            //{ "Name": "Console" },
            //{
            //    "Name": "File",
            //    "Args": { "path": "Serilog\\logs.txt" }
            //},
            {
                "Name": "Seq",
                "Args": { "serverUrl": "http://localhost:5341" }
            }
        ],

        "Enrich": [ "FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId" ]
        //"Destructure": [
        //    {
        //        "Name": "With",
        //        "Args": { "policy": "Sample.CustomPolicy, Sample" }
        //    },
        //    {
        //        "Name": "ToMaximumDepth",
        //        "Args": { "maximumDestructuringDepth": 4 }
        //    },
        //    {
        //        "Name": "ToMaximumStringLength",
        //        "Args": { "maximumStringLength": 100 }
        //    },
        //    {
        //        "Name": "ToMaximumCollectionCount",
        //        "Args": { "maximumCollectionCount": 10 }
        //    }
        //],
        //"Properties": {
        //    "Application": "Sample"
        //}
    }
}
