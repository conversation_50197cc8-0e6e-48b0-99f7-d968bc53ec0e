using AutoMapper;
using lep.configuration;
using lep.courier;
using lep.despatch.impl;
using lep.email;
using lep.freight;
using lep.job;
using lep.jobmonitor.impl;
using lep.order;
using lep.pricing;
using lep.promotion;
using lep.run;
using lep.user;
using LepCore.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Security.Claims;
using lep.freight.impl;

namespace LepCore.Controllers
{


	[ApiExplorerSettings(IgnoreApi = true)]
	[Produces("application/json")]
	[Route("api/[controller]")]
	[Authorize]
	[ResponseCache(Duration = 0, VaryByHeader = "*", Location = ResponseCacheLocation.None, NoStore = true)]
	public partial class JobSplitDeliveryController : Controller
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		private readonly IConfigurationApplication _configApp;

		private readonly ICourierApplication _courierApp;
		private readonly IEmailApplication _emailApp;
		private readonly IFreightApplication _freightApplication;
		private readonly IJobApplication _jobApp;
		private readonly JobBoardDTOHelper _jobBoardDTOHelper;
		private readonly IOrderApplication _orderApp;
		private readonly IPricingEngine _pricingEngine;
		private readonly IPromotionApplication _promotionApplication;
		private readonly IUserApplication _userApplication;
		private readonly LabelPrinterApplication _labelPrintApp;
		private readonly IRunApplication _runApplication;
		private readonly CartonFinder _cartonFinder;
		private readonly IPackageApplication _packApp;
		private bool _currentUserIsCust;
		private bool _currentUserIsStaff;
		private bool _currentUserIsAnonymousWLCustomer;
		private bool _currentUserIsLoggedInWLCustomer;

		private IUser _currentUser;

		private IMapper _mapper;
		private IMemoryCache _cache;
		private IConfigurationRoot _config;
		private CachedAccessTo _cachedAccessTo;

		private IHttpContextAccessor _httpContextAccessor;

		public JobSplitDeliveryController(
			IUserApplication ua,
			IOrderApplication oa,
			IJobApplication ja,
			IEmailApplication ea,
			IPricingEngine pe,
			IFreightApplication fa,
			ICourierApplication ca,
			IPromotionApplication pa,
			IRunApplication ra,
			IConfigurationApplication cona,
			CartonFinder cartonFinder,
			JobBoardDTOHelper jbh,
			LabelPrinterApplication labelPrintApp,
			IMapper mapper,
			IConfigurationRoot config,
			IHttpContextAccessor httpContextAccessor,
			IMemoryCache cache,
			IPackageApplication packApp,
			CachedAccessTo cachedAccessTo
		)
		{
			_orderApp = oa;
			_jobApp = ja;
			_emailApp = ea;
			_userApplication = ua;
			_pricingEngine = pe;
			_freightApplication = fa;
			_courierApp = ca;
			_promotionApplication = pa;
			_configApp = cona;
			_jobBoardDTOHelper = jbh;
			_runApplication = ra;
			_labelPrintApp = labelPrintApp;
			_packApp = packApp;
			_mapper = mapper;
			_config = config;
			_httpContextAccessor = httpContextAccessor;
			_cache = cache;
			_cachedAccessTo = cachedAccessTo;
			_cartonFinder = cartonFinder;
		}


		public override void OnActionExecuting(ActionExecutingContext context)
		{
			if (context.HttpContext.Request.Path.ToString().Contains("WestPackPayment"))
				return;

			var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
			_currentUser = _userApplication.GetUser(userId);
			//_currentUserIsCust = !_currentUser?.IsStaff ?? false;
			//	_currentUserIsStaff = _currentUser?.IsStaff ?? false;
			var rl = User.Claims.ToList();

			var roles = User.Claims.Where(c => c.Type == ClaimTypes.Role).Select(c => c.Value).ToList();

			_currentUserIsStaff = roles.Contains(LepRoles.SuperAdministrator) || roles.Contains(LepRoles.Administrator) || roles.Contains(LepRoles.Staff); // TODO

			_currentUserIsCust = roles.Contains(LepRoles.Customer);

			_currentUserIsAnonymousWLCustomer = roles.Contains(LepRoles.AnonymousWLCustomer);
			_currentUserIsLoggedInWLCustomer = roles.Contains(LepRoles.LoggedInWLCustomer);
		}



 
		private static string toJson(OrderSearchCriteriaDto dto)
		{
			return JsonConvert.SerializeObject(dto,
									 new JsonSerializerSettings
									 {
										 NullValueHandling = NullValueHandling.Ignore,
										 DefaultValueHandling = DefaultValueHandling.Ignore
									 });
		}
 
		[ApiExplorerSettings(IgnoreApi = false)]
		[HttpGet("Order/{orderId:int}/GetAvailableRates")]
		public IActionResult GetAvailableRates([FromRoute] int orderId)
		{
			var order = _orderApp.GetOrder(orderId);
			var facilityFromPostCode = _orderApp.GetProductionFacilityByPostCode(order.DeliveryAddress.Postcode)
									   ?? Facility.FG;
			var rates = _courierApp.GetAvailableRates(order, facilityFromPostCode, true, false);
			return new OkObjectResult(rates);
		}

		[ApiExplorerSettings(IgnoreApi = false)]
		[HttpGet("/GetProductionFacilityByPostCode")]
		public IActionResult GetProductionFacilityByPostCode([FromRoute] string postcode)
		{
			 return  new OkObjectResult(_orderApp.GetProductionFacilityByPostCode(postcode));
		}


		[HttpGet("Order/{orderId:int}/GetAvailableRatesFromFacility/{facility}")]
		public IActionResult GetAvailableRatesFromFacility([FromRoute] int orderId, [FromRoute] Facility facility)
		{
			var order = _orderApp.GetOrder(orderId);
			var rates = _courierApp.GetAvailableRates(order, facility, true, _currentUserIsStaff);
			return new OkObjectResult(rates);
		}


		[HttpPost("CancelSplit/{jobId:int}")]
		public IActionResult CancelSplit([FromRoute] int jobId)
		{
			var job = _jobApp.GetJob(jobId);
			if (job == null)
				return NoContent();
			
			job.Splits = null;
			var order = job.Order;


			order.PackDetail.Price = 0;
			order.PackDetail.IsCustom = false;
			order.Courier = CourierType.None;
			_orderApp.BaseSave(order);
			return Ok();
		}


		[HttpPost("GetSuggestions/{jobId:int}")]
		public IActionResult GetSuggestions([FromBody] JobSplits splits, [FromRoute] int jobId)
		{
			var job = _jobApp.GetJob(jobId);
			if (job == null)
				return NoContent();

			var fCode = job.Order.Customer.FreightPriceCode;
			var margin = (double)_courierApp.GetCustomerFreightMarginFromCode(fCode);

			var allowSkidDelivery = true; // todo figure out how to get this from the job/order

			var total = splits.Sum(s => s.Quantity);
			if(total != job.Quantity)
				return BadRequest("Total quantity must be equal to job quantity");

			// see if all splits have post code
			var allHavePostCode = splits.All(s => !string.IsNullOrEmpty(s.Address.Postcode));
			if (!allHavePostCode)
				return BadRequest("All splits must have a post code");

			// find the split with max quantity and then use its postcode to determine job facility
			var maxSplit = splits.OrderByDescending(s => s.Quantity).First();
			var facility = _orderApp.GetProductionFacilityByPostCode(maxSplit.Address.Postcode) ?? Facility.FG;


			var result = new JobSplitSuggestions();
			result.Facility = facility;


			 
			var singleJobsDetails = _packApp.GetSingleJobThicknessWeight(job);




			for (int i = 0; i < splits.Count; i++)
			{
				JobSplit split = splits[i];
				var distPackInfo = split.BrochureDistPackInfo;

				ListOfPackages packages = split.Packages ?? null;

				FixPackagesDimentions(packages);

				if (!split.IsCustom)
					packages = new ListOfPackages(_packApp.GetPackagesConsolidated(job, split.Quantity, allowSkidDelivery, distPackInfo).ToList());

				var rates = _courierApp.GetAvailableRatesBase(facility, split.Address, packages, false).ToList();

				if(i == 0)
				{
					_courierApp.MakeLeastPricedRateZeroBasedOnMargin(margin, rates);
				}
				

				// brochure banding
				if (job.IsBrochure() && (distPackInfo?.MailHouse?.Id > 0
							|| job.BrochureDistPackInfo?.PackingInstruction == PackingInstruction.Banding
							|| job.BrochureDistPackInfo?.PackingInstruction == PackingInstruction.ShrinkWrapping
					))
				{
					var bundlesOf = _packApp.GetInBundlesOf(job, singleJobsDetails, distPackInfo);
					if (bundlesOf > 0)
					{
						// bundleCount  from split.Quantity and bundlesOf
						var bundleCount = (split.Quantity / bundlesOf) + (split.Quantity % bundlesOf);

						if (rates.Any() && bundleCount != 0)
						{
							rates.ForEach(r => r.CustomerCharge += bundleCount);
						}
					}
				}



				var splitEx = new JobSplitEx1(split)
				{
					Rates = rates,
					PackagesStr = packages.ToString2(),
					Packages = packages
				};

				result.Splits.Add(splitEx);
			}


			return new OkObjectResult(result);
		}

		private  void FixPackagesDimentions(ListOfPackages packages)
		{
			if (packages == null || !packages.Any())
			{
				return;
			}
			foreach (var p in packages)
			{
				if ((p.Height == 0 || p.Width == 0 || p.Depth == 0 || p.Weight == 0) && p.CartonCode != "")
				{
					var x = _cartonFinder.GetCarton(p.CartonCode);
					p.Height = x.Height;
					p.Width = x.Width;
					p.Depth = x.Depth;
				}
			}
		}

		[HttpPost("Save/{jobId:int}")]
		public IActionResult Save([FromBody] JobSplits splits, [FromRoute] int jobId)
		{
			var job = _jobApp.GetJob(jobId);
			if (job == null)
				return NoContent();

			var fCode = job.Order.Customer.FreightPriceCode;
			var margin = (double)_courierApp.GetCustomerFreightMarginFromCode(fCode);

			//var allowSkidDelivery = true; // todo figure out how to get this from the job/order

			var total = splits.Sum(s => s.Quantity);
			if (total != job.Quantity)
				return BadRequest("Total quantity must be equal to job quantity");

			// see if all splits have post code
			var allHavePostCode = splits.All(s => !string.IsNullOrEmpty(s.Address.Postcode));
			if (!allHavePostCode)
				return BadRequest("All splits must have a post code");

			var allHaveCourier = splits.All(s => !string.IsNullOrEmpty(s.Courier));
			if (!allHaveCourier)
				return BadRequest("All splits must have a Courier");

			// find the split with max quantity and then use its postcode to determine job facility
			var maxSplit = splits.OrderByDescending(s => s.Quantity).First();
			var facility = _orderApp.GetProductionFacilityByPostCode(maxSplit.Address.Postcode) ?? Facility.FG;


			var result = new JobSplitSuggestions();
			result.Facility = facility;

			splits.ForEach(_ => { 
				FixPackagesDimentions(_.Packages);
				_.PackagesStr = _.Packages.ToString2();
			});
			
			job.Splits = splits;
			var order = job.Order;
			
			var freightTotal =  splits.Sum(s => s.CustomerCharge);
			if (freightTotal == 0)
				return BadRequest("Some Splits should have a Courier Charge");
			
			var handlingCharge = splits.Count * 25;
			order.PackDetail.Price = freightTotal + handlingCharge;
			order.PackDetail.IsCustom = true;
			order.Courier = "Other ~ Other";
			_orderApp.BaseSave(order);
			return new OkObjectResult(result);
		}





	}
}
