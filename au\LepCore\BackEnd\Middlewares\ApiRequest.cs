using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

using Serilog;
using System.Security.Claims;
using Serilog.Context;

namespace LepCore
{
	public class ApiRequest
	{
		private readonly RequestDelegate _next;

		//private static readonly log4net.ILog log =
		//	log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

		public ApiRequest(RequestDelegate next)
		{
			_next = next;
		}

		public async Task Invoke(HttpContext context)
		{
			var username = context.User.FindFirst(ClaimTypes.Name)?.Value ?? "Annonymous";
			LogContext.PushProperty("User", username);

			var p = context.Request.Path.ToString();
			if (!p.Contains("JobOptions") && !p.Contains("jobboard"))
			{
				Log.Information(@"{User} {method} {path} {IP}",
					   username,
					   context.Request.Method,
					   context.Request.Path,
					   context.Connection.RemoteIpAddress.ToString()
					   );
			}

			//var clients = context.RequestServices.GetService<Microsoft.AspNetCore.SignalR.Infrastructure.IConnectionManager>()
			//	.GetHubContext<StaffHub>().Clients;
			//clients.All.echo(logTemplate);
			await _next.Invoke(context);
		}
	}
}
