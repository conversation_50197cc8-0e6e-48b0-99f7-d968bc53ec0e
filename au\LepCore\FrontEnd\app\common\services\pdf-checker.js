angular.module("app")
.service("PdfChecker", ['$q', function ($q) {
	return {
		check: check
	};

	function check(f /* file */) {
		var d = $q.defer();
		
		if (!f.name.toLowerCase().endsWith('.pdf')) {
			d.reject(false);
		}
		var fr;
		try {
			fr = new FileReader();
			fr.addEventListener("loadend", function (e) {
				var target = e.target || e.srcElement;
				var header = target.result;

				if (header.indexOf('%PDF-') === 0) {
					d.resolve(true);
				} else {
					d.reject(false);
				}
			});

			if (f) {
				var slice = f.slice(0, 8);
				fr.readAsText(slice);
			}
		} catch (e) {
			d.resolve(true);
		}



		return d.promise;
	}


}]);
