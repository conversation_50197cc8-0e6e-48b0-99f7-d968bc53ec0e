﻿<div class="row">
    <!-- Left Column -->
    <div class="col-sm-2 hidden-xs">
        <div class="affix0">

            <div class="blue-box" ui-sref-active="active dark-blue-bck">
                <div class="row">
                    <div class="col-sm-12 box-title" ng-if="order.Id && order.Id != 0">
                        <a ui-sref="cust.order.view({orderId: order.Id})" ui-sref-opts="{reload:true}">Order #{{order.Id}}</a>
                    </div>
                </div>
            </div>

            <!--box 1-->
            <div class="blue-box ">

                <div ng-repeat="j in order.Jobs" id="order-jobs">
                    <a ui-sref=".job({orderId: {{order.Id}}, jobId: {{j.Id}}  })"  ui-sref-active="active dark-blue-bck">
                        Job #{{::j.Id}}
                    </a>
                </div>


                <!-- <a class="new-job" ui-sref="cust.order.addnewjob" ng-show="order.Visibility.addButton">
                    <i class="glyphicon glyphicon-plus"></i> Add new job
                </a> -->

            </div>


            <!--box 2-->
            <!--<div class="blue-box">
                <div class="row">
                    <div class="col-md-2 box-num">
                        2
                    </div>
                    <div class="col-md-10 box-title">
                        Prepare a draft
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-offset-2 col-md-10">
                        <ul>
                            <li><span>Current cost:  {{order.Price| currency}}</span> </li>
                            <li><span>Current retail cost: {{order.PriceOfOnlyJobs | currency }} </span> </li>
                             <li><span>Your mark up:</span> </li>
                        </ul>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">

                    </div>

                </div>
            </div>-->



            <!--box 2-->
            <div class="blue-box">
            </div>
        </div>
    </div>



    <!-- Right Column -->
    <div class="col-sm-10 view" id="rightCol" ui-view="">

    </div>



</div>
