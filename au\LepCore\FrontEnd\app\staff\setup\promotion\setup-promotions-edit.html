﻿<div id="regularContent" class="entry ng-scope">

    <fieldset class="">
        <div class="form-section-expand">
            <h3>Promotion</h3>
        </div>
        <div class="row">
            <div class="col-sm-6 form-horizontal">

                <div class="form-group">
                    <label class="control-label col-sm-4" for="textPromotion">
                        Promotion Code
                    </label>
                    <div class="col-sm-8">
                        <input class="form-control" type="text" ng-model="vm.PromotionCode" maxlength="20" size="20" id="textPromotion" title="Unique identifier of promotions." style="">
                    </div>
                </div>


                <div class="form-group">
                    <label class="control-label col-sm-4" for="cbActive">Active?</label>
                    <div class="col-sm-8">
                        <input class="checkbox " id="cbActive" type="checkbox" ng-model="vm.Active" style="">
                    </div>
                </div>


                <div class="form-group">
                    <label class="control-label col-sm-4" for="chkListSalesCat" style="padding-left: 0 !important;">
                        Sales Categories
                    </label>
                    <div class="col-sm-8 checkbox">
                        <label class="checkbox"><input class="checkbox" type="checkbox" ng-model="vm.SalesCategoryLead">Lead</label>
                        <label class="checkbox"><input class="checkbox" type="checkbox" ng-model="vm.SalesCategoryProspect">Prospect</label>
                        <label class="checkbox"><input class="checkbox" type="checkbox" ng-model="vm.SalesCategoryLapsed">Lapsed</label>
                        <label class="checkbox"><input class="checkbox" type="checkbox" ng-model="vm.SalesCategoryCustomer">Customer</label>
                    </div>
                </div>


                <div class="checkbox">
                    <label class="control-label col-sm-4" for="promoValid" >Life span</label>
                    <div class="col-sm-8 ">
                        <label class="radio"><input class="radio-inline" ng-model="vm.LifeSpan" type="radio" ng-value="0"> Date Range</label>
                        <label class="radio"><input class="radio-inline" ng-model="vm.LifeSpan" type="radio" ng-value="1"> Windowed</label>
                    </div>
                </div>

                <div ng-show="vm.LifeSpan==0">
                    <div class="form-group dateAbsolute">
                        <label class="control-label col-sm-4" for="textDateValidFrom">Starts from</label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <input class="form-control datetimepicker" type="text" id="textDateValidFrom" ng-model="vm.DateValidStart" datetime-picker date-format="dd-MMM-yyyy" date-only >
                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>

                    </div>


                    <div class="form-group dateAbsolute">
                        <label class="control-label col-sm-4" for="textDateValidTo">
                            Finishes on
                        </label>
                        <div class="col-sm-8">

                            <div class="input-group">
                                <input class="form-control datetimepicker" type="text" id="textDateValidTo" ng-model="vm.DateValidEnd" datetime-picker date-format="dd-MMM-yyyy"  date-only>
                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div ng-show="vm.LifeSpan==1">
                    <div class="form-group dateWindow">
                        <label class="control-label col-sm-4" for="textWindow">
                            Within days
                        </label>
                        <div class="col-sm-8">
                            <input class="form-control" type="text" ng-model="vm.Window"
                                   maxlength="3" size="3" id="textWindow"
                                   title="Promotion is only available within this much day of a customer being assigned to a campaign in CRM">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label col-sm-4" for="textDeduction">Max Deduction (Exclude freight) $</label>
                    <div class="col-sm-8">
                        <input class="form-control" type="text"
                               ng-model="vm.MaxDeduction" maxlength="10" size="10" id="textDeduction">
                    </div>
                    <span id="ctl00" style="visibility: hidden;">Required</span>
                    <span id="ctl01" style="display: none;">Invalid currency</span>
                </div>


                <div class="form-group">
                    <label class="control-label col-sm-4" for="textDiscount">Discount %</label>
                    <div class="col-sm-8">
                        <input class="form-control" type="text" ng-model="vm.Discount" maxlength="3" size="3" id="textDiscount">
                    </div>
                </div>


                <div class="form-group">
                    <label class="control-label col-sm-4" for="textDiscount">Max Discount</label>
                    <div class="col-sm-8">
                        <input class="col-md- 8 form-control" type="text" ng-model="vm.MaxDiscount" maxlength="10" size="10" id="textMaxDiscount">
                    </div>


                </div>
                <div class="form-group">
                    <label class="control-label col-sm-4" for="textMinJobPrice">Job price $</label>
                    <div class="col-sm-8">
                        <div class="input-group">
                            <span class="input-group-addon">min</span><input class="form-control" type="text" ng-model="vm.MinJobPrice" maxlength="10" size="10" id="textMinJobPrice" title="Promotion is only available for jobs of more than this value">
                            <span class="input-group-addon">max</span><input class="form-control" type="text" ng-model="vm.MaxJobPrice" maxlength="10" size="10" id="textMaxJobPrice" title="Promotion is only available for jobs of no more than this value.">
                        </div>
                    </div>

                </div>

                <div class="form-group">
                    <label class="control-label col-sm-4" for="textMinOrderPrice">Order price $</label>

                    <div class="col-sm-8">
                        <div class="input-group">
                            <span class="input-group-addon">min</span><input class="form-control col-sm-3" type="text" ng-model="vm.MinOrderPrice" maxlength="10" size="10" id="textMinOrderPrice" title="Promotion is only available for orders of more than this value.">
                            <span class="input-group-addon">max</span><input class="form-control  col-sm-3" type="text" ng-model="vm.MaxOrderPrice" maxlength="10" size="10" id="textMaxOrderPrice" title="Promotion is only available for orders of no more than this value.">
                        </div>
                    </div>
                </div>

            </div>


            <div class="col-sm-6 form-horizontal">

                <div class="form-group">
                    <label class="control-label col-sm-4" for="textMinJobQty">Min Job Quantity</label>
                    <div class="col-sm-8">
                        <input class="form-control" type="text" ng-model="vm.MinJobQuantity" maxlength="10" size="10" id="textMinJobQty" title="Promotion is only available for jobs that have at least this quantity." autocomplete="off">
                    </div>
                </div>


                <div class="form-group">
                    <label class="control-label col-sm-4" for="chkOnlyFirstOrder">Only First Order?</label>
                    <div class="col-sm-8" title="Promotion will only deliver a benefit on the very first order submitted to LEP by some customer.">
                        <input class="checkbox" id="chkOnlyFirstOrder" type="checkbox"
                               ng-model="vm.OnlyFirstOrder" ng-value="true">
                    </div>
                </div>


                <div class="form-group">
                    <label class="control-label col-sm-4" for="chkCanUseOnce">Can be used only once?</label>
                    <div class="col-sm-8" title="If this is ticked this promotion can only be used once, i.e. after first application the promo becomes unusable">
                        <input class="checkbox" id="chkCanUseOnce" type="checkbox" ng-model="vm.CanUseOnce" ng-value="true">
                    </div>
                </div>


                <div class="form-group">
                    <label class="control-label col-sm-4" for="chkFreeDelivery">Free Delivery?</label>
                    <div class="col-sm-8" title="The promotion will create a benefit exactly equal to -1 x Freight Cost.">
                        <input class="checkbox" id="chkFreeDelivery" type="checkbox" ng-model="vm.FreeDelivery" ng-value="true">
                    </div>
                </div>


                <div class="form-group">
                    <label class="control-label col-sm-4" for="chkFreeBusinessCard">Free Business Card?</label>
                    <div class="col-sm-8" title="">
                        <input class="checkbox" id="chkFreeBusinessCard" type="checkbox" ng-model="vm.FreeBusinessCard" ng-value="true">
                    </div>
                </div>


                <div class="form-group onlyForWindowedPromotion">
                    <label class="control-label col-sm-4" for="chkCheckCustAgaintCRM">Check Customer Against CRM?</label>
                    <div class="col-sm-8" title="Are customers checked againt CRM?">
                        <input class="checkbox" id="chkCheckCustAgaintCRM" type="checkbox" ng-model="vm.CheckCustomerAgainstCampaign" ng-value="true">
                    </div>
                </div>


                <div class="form-group">
                    <label class="control-label col-sm-4" for="textShortDesc">Short Description</label>
                    <div class="col-sm-8">
                        <input class="form-control" type="text" ng-model="vm.ShortDescription" maxlength="255" size="40" id="textShortDesc" title="Used to identify the promotion." style="">
                    </div>
                </div>


                <div class="form-group">
                    <label class="control-label col-sm-4" for="textSalesDesc">Sales Description</label>
                    <div class="col-sm-8">
                        <input class="form-control" type="text" ng-model="vm.SalesDescription" maxlength="255" size="40" id="textSalesDesc" title="Used to explain when the promotion should be assigned to people. Used by sales staff to understand how to sell." style="">
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label col-sm-4">Long Description</label>
                    <div class="col-sm-8">
                        <textarea rows="8" cols="20" id="textLongDesc" class="form-control" ng-model="vm.LongDescription" style=""></textarea>
                    </div>
                </div>

            </div>
        </div>

        <label class="checkbox"><input type="checkbox" ng-click="toggleAllTemplates($event)" /> All templates</label>

        <div class="row template-matrix-row" ng-repeat="t in d.templatesL">
            <div class="col-sm-12">
                <label>
                    <input type="checkbox" ng-click="toggleAllUnderTemplate($event,t.Id)" /> <h4 style="display: inline">{{::t.Name}}</h4>
                </label>
            </div>
            <div class="col-md-1 ">



            </div>
            <div class="col-sm-3">
                <label class="checkbox alltoggle"><input type="checkbox" ng-click="toggleSizesUnderTemplate($event,t.Id)" /> All sizes</label>
                <div ng-repeat="k in d.matrix[t.Id].Sizes">
                    <label class="checkbox"><input type="checkbox" checkbox-model="d.matrix2[t.Id].Sizes" checkbox-value="k*1" multiple>{{:: d.sizesD[k]}}</label>
                </div>
            </div>

            <div class="col-sm-8">
                <div class="row ">
                    <div class="col-sm-12">
                        <label class="checkbox alltoggle"><input type="checkbox" ng-click="toggleStocksUnderTemplate($event,t.Id)" /> All stocks</label>
                    </div>
                </div>

                <div class="row ">
                    <div ng-repeat="k in d.matrix[t.Id].Stocks" class="col-md-5">
                        <label class="checkbox"><input type="checkbox" checkbox-model="d.matrix2[t.Id].Stocks" checkbox-value="k*1" multiple>{{:: d.stocksD[k]}}</label>
                    </div>
                </div>
            </div>

        </div>
    </fieldset>

    <div class="row">
        <div class="col-sm-12">
            <div class="buttons-large pull-right border-end">
                <a ui-sref="staff.setup-promotions"> <i class="glyphicon glyphicon-chevron-left"></i>   Back to promotions list</a>
                <button class="btn btn-default" type="submit" value="Save" ng-click="save()">
                    <i class="glyphicon glyphicon-floppy-save"></i>    Save
                </button>

            </div>
        </div>
    </div>


</div>
