<!-- Artwork not required, show list of artworks -->
    <div ng-form="artworkForm" ng-init="upload_artwork=false" class="form-horizontal">

        <div ng-show="job.Visibility.previewButton">{{::job.Visibility.previewButtonText}}</div>
        <!--<div id="removeArtButton" >remove this artwork?</div>
    -->
        <div ng-show="job.Visibility.artworkRequiredMsg" class="alert alert-warning">Artwork required</div>
        <div ng-show="job.Visibility.awaitingArtwork" class="alert alert-warning">Awaiting artwork</div>

        <!--<div id="artworkMultiPage"  ></div>-->
        <!--<div id="artworkWarning"></div>-->




        <div ng-repeat="a in job.Artworks" ng-class="{'errorrow': job.Visibility.hasReject}">
            <div class="form-group">
                <div class="col-xs-3 control-label">
                    {{a.Supplied? "Supplied " : ""}}
                    {{a.Ready? "Ready " : ""}}
                    {{pos2label(a.Position)}}
                </div>
                <div class="col-xs-9 form-control-static">
                    <span class="breakword">  {{a.Supplied || a.Ready }} </span>

                    <span class="pull-right">
                        &nbsp;&nbsp;&nbsp;
                        <a ng-click="downloadArtwork(job.Id, a.Id, a.Supplied != '')" title="Download artwork" class=""
                           style="font-size:14pt;">
                            <i class="glyphicon glyphicon-cloud-download"></i>
                        </a>
                        &nbsp;&nbsp;&nbsp;
                        <a ng-click="jobCommand('RemoveArtworks',false, a.Position)" title="Remove artwork" ng-show="(job.Visibility.removeArtButtonVisible)" class=""
                           style="font-size:14pt;">
                            <i class="glyphicon glyphicon-remove-sign"></i>
                        </a>
                    </span>


                </div>
            </div>
        </div>




        <div ng-hide="!job.Visibility.IsArtworkEditable" class="form-group
          ng-class:{'has-error': ((job.UploadType=='singleart' ||  job.UploadType=='multiart') && artworkConfirmed != true)}">
            <label class="col-xs-3 control-label">
                Add artwork file(s)
            </label>
            <div class="col-xs-7">
                <label class="radio-inline">
                    <input type="radio" name="artworktype" ng-model="job.UploadType" value="singleart">Single File
                </label>
                <label class="radio-inline">
                    <input type="radio" name="artworktype" ng-model="job.UploadType" value="multiart">Multiple File
                </label>
                <label class="radio-inline" ng-hide="isWL">
                    <input type="radio" name="artworktype" ng-model="job.UploadType" value="later">Later
                </label>
            </div>
        </div>

        <!--
        <pre>
            artworkConfirmed = {{artworkConfirmed}}<br />
            requiredPositions.length = {{requiredPositions.length}} <br />
            job.AttachmentsToProcess.length = {{job.AttachmentsToProcess.length}} <br />
            ignoreErrorAndAttachAnyways = {{ignoreErrorAndAttachAnyways}}
    
        </pre>	
		-->

    <div ng-show="job.UploadType!='later'">
        <div class="form-group" ng-repeat="p in requiredPositions">


            <label class="col-xs-3 control-label">{{pos2label(p)}}</label>
            <div class="col-xs-6 form-control-static">
                <a ng-click="btn_remove(file)" title="Remove from list to be uploaded">
                    <i class="icon-remove"></i>
                </a>
                <input type="file" class="inputfile"
                       ng-file-model-pdf-aac attach-files-to="job.AttachmentsToProcess" job="job" name="{{p}}" accept=".pdf" id="{{p}}" aac="aac" />
                <!-- attach-artwork="job" attach-artwork-enabled="job.Visibility.IsArtworkEditable" -->
                <label for="{{p}}">
                    <span><i class="glyphicon   glyphicon-paperclip"></i> Browse for file...   </span>
                </label>

                <!--<progress style="width: 400px;" value="{{files[$index].loaded}}" max="{{files[$index].size}}"></progress> -->


            </div>


        </div>
 
        <!--
                <div class="form-group" ng-hide="!job.Visibility.IsArtworkEditable"
                     ng-if="!job.Visibility.saveButton && job.Visibility.IsArtworkEditable">

                    <label class="col-xs-3 control-label"> </label>

                    <div class="col-xs-7 ">
                        <button ng-click="btn_upload()"> upload </button>

                    </div>
                </div> -->
    </div>


        <!--<div>
            <div style="float: left;">
                <div class="btn btn-sm" ng-click="btn_upload()">Save</div>

            </div>
        </div>    <pre>
    {{job.Artworks | json}}
        </pre>
    -->






    </div>
