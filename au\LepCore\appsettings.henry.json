{
    "TestBox": false,
    "HolidayMode": false,
    "ApplicationInsights": {
        "InstrumentationKey": ""
    },
    "Logging": {
        "IncludeScopes": false,
        "LogLevel": {
            "Default": "Debug",
            "System": "Error",
            "Microsoft": "Error",
            "Microsoft.AspNetCore.Mvc": "Error",
            "Microsoft.AspNetCore.Authentication": "Error",
            "Microsoft.AspNetCore.Routing.RouteBase": "Error",
            "Microsoft.AspNetCore.Server.Kestrel": "Error",
            "NHibernate": "Warn",
            "NHibernate.SQL": "DEBUG",
            "Microsoft.AspNetCore.SignalR": "None",
            "Microsoft.AspNetCore.Hosting": "None",
            "Microsoft.AspNetCore.StaticFiles": "None"
        }
    },
    "CustomerLogoDirectory": "\\\\dfs01\\customerlogos",
    "DataDirectory": "\\\\dfs01\\resource",
    "StaticAssets": "C:\\LepData\\StaticAssets",
    "OldDataDirectory": "\\\\dfs01\\resource",
    "DataDirectoryPC": "\\\\dfs01\\resource",
    "DataDirectoryMac": "/Volumes/resource",
    "InvoicerPDFFolder": "\\\\dfs01\\resource\\invoices",
    "job.option.csv.folder": "C:\\Lepdata\\StaticAssets\\jobOptionCSVs",
    "lepcrm.webservice.url": "http://winston.internal.lepcolourprinters.com.au:1000/MSCRMService.asmx",
    "compdata.webservice.url": "http://harry:8089/iFreightChargeEnquiryService.svc",
    "AbsolutePathURL": "https://my.lepcolourprinters.com.au",
    "reports": {
        "LepQuote": "C:\\LEPDATA\\t1.frx"
    },
    "email": {
        "sendMail": true,
        "server": "smtp.sendgrid.net",
        "port": "587",
        "username": "<EMAIL>",
        "password": "*********************************************************************"
    },

    "jobboard": {
        "messagequeue": ".\\Private$\\lordjobboardqueue"
    },
    "Nhibernate": {
        "Con": "Data Source=*************; user id=sa; password=*************; Initial Catalog=PRD_AU;MultipleActiveResultSets=true;Max Pool Size=200;App=LepCore"
    },
    "SupplyMaster": {
        "Con": "Data Source=*************; user id=sa; password=*************; Initial Catalog=SupplyMaster;MultipleActiveResultSets=true"
    },
    "GhostScript": "C:\\gs10\\bin\\gswin64c.exe",
    "PdfTk": "C:\\gs952\\bin\\pdftk.exe",
    "LibTiff": "C:\\LepData\\LibTiff\\tiff2pdf.exe",
    "PdfToPrinter" : "c:\\LepData\\PdfToPrinter.exe",
    
    "Labels": {
        "LogoLabel": "c:\\LEPDATA\\Labels2\\LogoLabel_102x73mm.frx",
        "PayMeLabel": "c:\\LEPDATA\\Labels2\\PayMeLabel.frx",
        "FillingLabel": "c:\\LEPDATA\\Labels2\\FillingLabel.frx",
        "SampleLabel": "c:\\LEPDATA\\Labels2\\SampleLabel.frx",
        "AddressA4Label": "c:\\LEPDATA\\Labels2\\AddressA4Label.frx",
        "PickupLabel": "c:\\LEPDATA\\Labels2\\PickupLabel.frx",
        "OneDeliveryOnly": "c:\\LEPDATA\\Labels2\\OneDeliveryOnly.frx",
        "AddressLabel": "c:\\LEPDATA\\Labels2\\SampleLabel.frx",
        "AddressLabelOther": "c:\\LEPDATA\\Labels2\\SampleLabel.frx",
        "CartonLabel": "c:\\LEPDATA\\labels2\\CartonLabel.frx",
        "CartonLabelMH": "c:\\LepData\\labels2\\CartonLabelMH.frx"
    },
    "Seq": {
        "ServerUrl": "http://localhost:5341",
        "MinimumLevel": "Trace",
        "LevelOverride": {
            "Microsoft": "Warning"
        }
    },
    "FreightProvider": "SmartFreight", // or  CompData "current"
    "AutomatedArtworkCheck": {
        "Enabled": true,
        "Timeout": "00:05:59",
        "Method": "DoPitStopViaCommandLine",
        "profiles": {
            "default": "C:\\LEPDATA\\default.ppp",
            "spotcolor": "C:\\LEPDATA\\Spotcolour.ppp",
            "wideformat": "C:\\LEPDATA\\Wideformat.ppp",
        },
        "DoPitStopViaCommandLine": {
            "path": "C:\\Program Files\\Enfocus\\Enfocus PitStop Server 24\\PitStopServerCLI.exe",
            "mutator": "C:\\LEPDATA\\LEP Check 2013-1.ppp"
        },
        "DoPitStopViaHotFolder": {
            "input": "c:\\hotfolder\\Input Folder",
            "output": "c:\\hotfolder\\Output Folder"
        }
    },
    "Dispatchers": [
        {
            "Name": "FG-DISTRIB-02",
            "DispatchFacility": "FG"
        },
        {
            "Name": "FG-DISTRIB-03",
            "DispatchFacility": "FG"
        },
        {
            "Name": "PM-DISTRIB-01",
            "DispatchFacility": "PM"
        },
        {
            "Name": "PM-BINDERY-05",
            "DispatchFacility": "PM"
        }
    ],
    "SmartFreight": {
        "Url": {
            "SFOv1": "https://api-au-1.smartfreight.com/api/soap/classic",
            "DeliveryOptions": "https://api-au-1.smartfreight.com/api/soap/deliveryoptions"
        },
        "Senders": {
            "LEPQLD": {
                "Id": "NWM",
                "Key": "RqB0fJOapwP79AVDdzF5Pgg7fESwF4GxHQ"
            },
            "TESTQLD": {
                "Id": "RqB0fJOapwP79AVDdzF5Pgg7fESwF4GxHQ",
                "Key": ""
            },
            "LEPVIC": {
                "Id": "NGU",
                "Key": "JoIJ2bXndxCk0V3ZfkM6VxKuzv1qgEAiA0"
            },
            "TESTVIC": {
                "Id": "NGU",
                "Key": "JoIJ2bXndxCk0V3ZfkM6VxKuzv1qgEAiA0"
            }
        },
    },
}