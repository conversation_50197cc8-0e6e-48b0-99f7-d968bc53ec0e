using Serilog;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace LepCore
{
	public class ErrorHandlingMiddleware
	{
		private readonly RequestDelegate next;

		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		private static readonly string[] separator1 = { "--- End of stack trace from previous location where exception was thrown ---" };

		public ErrorHandlingMiddleware(RequestDelegate next)
		{
			this.next = next;
		}

		public async Task Invoke(HttpContext context)
		{
			try
			{
				await next(context);
			}
			catch (Exception ex)
			{
				await HandleExceptionAsync(context, ex);
			}
		}

		private async Task HandleExceptionAsync(HttpContext context, Exception exception)
		{
			if (exception == null) return;

			var code = HttpStatusCode.InternalServerError;

			//if (exception is MyNotFoundException) code = HttpStatusCode.NotFound;
			//else if (exception is MyUnauthorizedException) code = HttpStatusCode.Unauthorized;
			//else if (exception is MyException) code = HttpStatusCode.BadRequest;

			await WriteExceptionAsync(context, exception, code).ConfigureAwait(false);
		}

		private async Task WriteExceptionAsync(HttpContext context, Exception exception, HttpStatusCode code)
		{
			var name = context.User?.Identity?.Name ?? " ?? ";
			var browser = context.Request.Headers["User-Agent"].ToString() ?? "";

			var logTemplate = string.Format(@"{0}	{1}	{2}", context.Request.Method, context.Request.Path, context.Connection.RemoteIpAddress.ToString());

			Log.Error(logTemplate);
			Log.Error($"User: {name} | Browser: {browser}");

			var response = context.Response;
			//response.ContentType = "application/json";
			//response.StatusCode = (int)code;

			var sb = new StringBuilder();

			if (exception.InnerException != null)
			{
				sb.AppendLine(exception.InnerException.StackTrace);
			}

			sb.AppendLine(exception.StackTrace);

			var lines = sb.ToString()//.Replace("lep.","").Replace("LepCore.","")
				.Split(new[] { Environment.NewLine }, StringSplitOptions.None)
				.Where(l => Regex.IsMatch(l, @"([^\)]*\)) in (.*):line (\d)*$"))
				.Where(l => !l.Contains("Middleware"))
				.ToArray();

			var exceptionStackTrace = string.Join(Environment.NewLine, lines);

			var errorJson = JsonConvert.SerializeObject(new
			{
				error = new
				{
					message = exception.Message,
					exception = exception.GetType().Name,
					stackTrace = exceptionStackTrace
				}
			});
			Log.Error(exception.Message);
			Log.Error(exception.StackTrace);
			await response.WriteAsync(errorJson).ConfigureAwait(false);
		}
	}
}
