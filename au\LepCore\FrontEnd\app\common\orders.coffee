do (window, angular, toastr, bootbox) ->
    app = angular.module('app')

    submitMsg = "Your order has been submitted to LEP and will be reviewed as soon as possible.
                If there is a problem with your order, LEP will notify you of amendments to be made before proceeding with the order."
    sure = "Are you sure you want to "


    jobCommandPrompts = {
        "Copy"                 : "create a copy of this job into a new order?"
        "Withdraw"             : "withdraw this job?"
        "Reactivate"           : "reactivate this job?"
        "Reorder"              : "re-order this job?"
        "Reject"               : "reject this job?"
        "Delete"               : "delete this job?"
        "Restart"              : "restart this job?"
        "ApproveQuote"         : "approve the price and save this job?"
        "ApproveQuoteAndSubmit": "approve the price and submit the order?"
        "RemoveArtworks"       : "remove this artwork?"
        "moveArtwork"          : "move Supplied artwork to Ready artwork?"
        "AcceptSupplyArtwork"  : "proceed with Supplied artwork?"
        "AcceptSupplyArtworkWithSubmit" : "proceed with Supplied artwork and submit the order?"
        "UnableToMeetPrice": "Reject this job as 'Unable To Meet Price' and send Email to Customer?"
        "RejectionOfVariationRequest" : "Reject this job as 'Rejection Of Variation Request' and send Email to Customer?"
        }


    app.controller 'OrderController', [
        '$scope', 'order'
        ($scope , order) ->
            $scope.order = order
    ]

    estimators =   ['Management', 'Callum Bromfield',  'Lauren McMillan']
    myobAccounts = [{Id: '4-1004', Name: 'Business Cards'}
                    {Id: '4-1005', Name: 'Brochures'}
                    {Id: '4-1006', Name: 'Letterheads/Compliment Slips'}
                    {Id: '4-1007', Name: 'Magazines & Booklets'}
                    {Id: '4-1008', Name: 'Presentation Folders'}
                    {Id: '4-1009', Name: 'Other'}
                    {Id: '4-2004', Name: 'DPC Sales - Business Card'}
                    {Id: '4-2005', Name: 'DPC Sales - Brochures'}
                    {Id: '4-2006', Name: 'DPC Sales - Stationery'}
                    {Id: '4-2007', Name: 'DPC Sales - Magazines'}
                    {Id: '4-2008', Name: 'DPC Sales - Pres Folder'}
                    {Id: '4-2009', Name: 'DPC Sales - Other'}
                    {Id: '4-3020', Name: 'Wide Format - Adhesives'}
                    {Id: '4-3021', Name: 'Wide Format - Banners/Pullups'}
                    {Id: '8-1050', Name: 'Freight recovered'}
                    {Id: '4-1010', Name: 'Discounts allowed'}
                    {Id: '8-1100', Name: 'Other Income'}
                    ]

    app.controller 'OrderViewController', [
        '$scope', '$state', 'OrderService', '$location', '$http', '$stateParams','order', '$rootScope',  'ngDialog', '$log', 'lepApi2','$q','imgLookup', 'Utils','$window',
        ($scope ,  $state,   OrderService,   $location,   $http, $stateParams,  order, $rootScope  ,   ngDialog,   $log,   lepApi2,  $q, imgLookup, utils, $window) ->

            $scope._ = "OrderViewController"
            $scope.imgLookup = imgLookup
            $scope.pos2label = utils.pos2label
            $scope.updatingCourier = false

            $scope.selectedJobs = []

            for j in order.Jobs
                if 'front' in j.RequiredPositions and 'back' in j.RequiredPositions
                    j.RequiredPositions.push('multiart')
                if 'Cover' in j.RequiredPositions and 'Text' in j.RequiredPositions
                    j.RequiredPositions.push('multiart')
                if j.RequiredPositions.length == 0
                    j.RequiredPositions.push('multiart')

            orderVmo = angular.copy(order)
            $scope.order = order
            $rootScope.title = "O#{order.Id}"

            $scope.addressx = {}
            $scope.fileAttachmentPending = false
            IsCustomer = $scope.$root.globals.IsCustomer
            orderState = if IsCustomer then  "cust.order.view" else "staff.order.view"
            jobState   = if IsCustomer then "cust.order.job"   else "staff.order.job"

            orderbase = "/api/Orders/order/#{$scope.order.Id}"

            $scope.qoText = 'order'

            $scope.$watch 'order.PriceOfJobs', (p,p0) ->
                if order.Status == 0
                    if p is null
                        $scope.qoText = 'draft'
                    else if p is 0
                        $scope.qoText = 'order'


            # if the courier changes,update order.PackDetail.Price and save order
            $scope.$watch 'order.Courier', (f,o) ->
                if f is o then return
                window.toastr.remove()
                so = _.find($scope.freights, (x) -> x.Value == f)
                $scope.order.PackDetail.Price =  so?.CustomerCharge || 0
                $scope.saveOrder()
            , true

            # if the courier changes,update order.PackDetail.Price and save order
            $scope.$watch 'order.PackWithoutPallets', (f,o) ->
                if f is o then return
                $scope.saveOrder()
            , true



            # get list of freights for this order on page load
            getFreights = (id) ->
                if order.HasSplitDelivery then return
                postcode = JSON.stringify($scope.order.DeliveryAddress) + "-" + $scope.order.PackWithoutPallets
                freightFields = ""
                freightFields += order.Jobs.length
                for j in order.Jobs
                    freightFields += j.DateModified
                $scope.updatingCourier = true
                cd("getFreights", postcode )
                OrderService.getFreights(id, postcode, freightFields).then (f) ->

                    # isPM = _.every($scope.order.Jobs, (j) => j.Facility == 1)
                    # if isPM
                    #     for c in f
                    #         if c.name1 is "FASTWAY"
                    #             c.disabled = true

                    # get rid of fastways
                    f = _.filter(f, (x) -> x.name1 != "FASTWAY")

                    f = _.reject(f, (x) -> x.name1 == "TOLL NQX")

                    $scope.freights  =  f
                    $scope.updatingCourier = false
                    # after getting freights see if the selected customer charge matches packing details price
                    # if not matches that means post code was updated and we need to update it

                    so = _.find($scope.freights, (x) -> x.Value == order.Courier)
                    if  so and  so.CustomerCharge.toFixed(2) != order.PackDetail.Price.toFixed(2) and so.Value != "None ~ None"
                        order.PackDetail.Price = so.CustomerCharge.toFixed(2)
                        window.toastr.remove()
                        $scope.order.PackDetail.Price =  so.CustomerCharge.toFixed(2)
                        $scope.saveOrder()
                    if !so
                        order.Courier =   ''
                        order.PackDetail.Price = 0
                        if order.PriceOfJobs
                            window.toastr.warning('Please select a courier')


            if $state.current.name == 'cust.order.view' && order.Visibility.addressPnl	&& !order.PackDetail.IsCustom
                getFreights(order.Id)

            # update the list of freights for this order when the delivery address changes
            # but dont do it on every keystroke, debounce 2 seconds
            getFreightsThrottled = _.debounce(getFreights, 3000, {trailing:true})

            $scope.deliveryDetailsChanged = false
            $scope.addressDefault = angular.equals($scope.order.DeliveryAddress, $scope.order.Visibility.DefaultAddress)
            w1 = $scope.$watch('order.DeliveryAddress',(n,o) ->
                if angular.equals(n,o)  then return
                $scope.deliveryDetailsChanged = true
                $scope.addressDefault = angular.equals($scope.order.DeliveryAddress, $scope.order.Visibility.DefaultAddress)
            , true)

            w2 = $scope.$watchGroup ['order.RecipientName', 'order.RecipientPhone'], (n,o) ->
                if angular.equals(n,o)  then return
                $scope.deliveryDetailsChanged = true

            ###
            w3 = $scope.$watch 'order.DeliveryAddress.Postcode',(n,o) ->
                if n == o then return
                dto = angular.copy(order)
                delete dto.Visibility
                $http.post(orderbase,  dto).then (r) ->
                    getFreights(order.Id)
            ###



            success1 = (d) ->
                if d.data
                    window.toastr.success(d.data)
                orderId = parseInt(d.headers('OrderId'), 10)
                if orderId
                    $state.go( orderState ,{orderId: orderId}, {reload:true})
                else
                    $state.reload()
                return

            fail1 = (d) ->
                window.toastr.warning(d.data)
                return

            # lord-1189 when tab is closed send bacon to release the order in use by current user
            # ps Visibility change wont work as the user could be switchin to another app to preflight file
            window.onbeforeunload = (e) ->
                if window.navigator and window.navigator.sendBeacon
                    name = $scope.globals.User.FirstName
                    url = "#{orderbase}/o/c/#{name}"
                    window.navigator.sendBeacon url, null
                return

            $scope.backToOrders = (justSubmitted) ->
                p = {}
                if justSubmitted then p =  {justSubmitted:true}

                if $scope.$root.globals.IsCustomer
                    $state.go('cust.orders', p)
                else
                    name = $scope.globals.User.FirstName
                    url = "#{orderbase}/o/c/#{name}"
                    $http.post(url, null).then () ->
                        $state.go('staff.orders')

            $scope.hasArtworkPosition = (p) ->
                if !(p) or p is 'files[]' then return false
                return true

            gatherJobNameAndAttachmentChanges = () ->
                AttachmentsToProcess = {}                       # see which jobs has a Files array and gather them
                for j in order.Jobs
                    if j.Files and j.Files.length
                        jFiles = []
                        for xa in j.Files
                            jFiles.push({Id: xa.Id, Name: xa.Name, Position: xa.Position, AACPerformed: xa.AACPerformed})
                        AttachmentsToProcess[j.Id]  = jFiles

                changedNames = {}                        # gather changes in names
                for j,i in order.Jobs
                    oldName = orderVmo.Jobs[i].Name
                    if j.Name != oldName
                        changedNames[j.Id] = j.Name

                commentsToAdd = {}
                for j,i in order.Jobs
                    commentsToAdd[j.Id] = j.CommentsToAdd || []

                order.AttachmentsToProcess = AttachmentsToProcess
                order.ChangedNames = changedNames
                order.CommentsToAdd = commentsToAdd


                l = Object.keys(AttachmentsToProcess).length
                if l > 0
                    $scope.fileAttachmentPending = true
                else
                    $scope.fileAttachmentPending = false

            $scope.$on 'fileAttached', gatherJobNameAndAttachmentChanges()

            $scope.saveOrder = () ->
                gatherJobNameAndAttachmentChanges()
                dto = angular.copy(order) # first we copy the order to a temp
                delete dto.Visibility	 # no need to post this
                delete dto.Jobs			 # no need to post this
                dx = $q.defer()

                contFn = () ->
                    $http.post(orderbase,  dto).then(success1, fail1)
                    dx.resolve(true)
                    return

                artworkConfirm.callback = (agreed)->
                        if not agreed
                            dx.resolve(true)
                            return
                        contFn()

                if Object.keys(dto.AttachmentsToProcess||{}).length> 0
                    bootbox.confirm artworkConfirm
                else
                    contFn()
                dx.promise


            $scope.freightChange = () ->

            $scope.submitOrder = () ->
                dto = angular.copy(order)
                delete dto.Visibility
                $http.post(orderbase,  dto).then(->
                    $http.post(orderbase + "/submit", {}).then(->
                                window.toastr.remove()
                                window.toastr.success(submitMsg)
                                $scope.backToOrders(true)
                    )
                )

            $scope.approveQuoteAndSubmitOrder = () ->
                dto = angular.copy(order)
                delete dto.Visibility
                $http.post(orderbase,  dto).then(->
                    $http.post(orderbase + "/ApproveQuoteAndSubmit", {}).then(->
                                window.toastr.remove()
                                window.toastr.success(submitMsg)
                                $scope.backToOrders(true)
                    )
                )

            $scope.submitReadyOrder = () ->
                $http.post("#{orderbase}/submitReady", {}).then(success1, fail1)

            $scope.deleteOrder = () ->
                $http.post("#{orderbase}/delete", {}).then((r) ->
                        $scope.backToOrders()
                    , fail1)

            $scope.orderCommand = (command) ->
                $http.post("#{orderbase}/#{command}", {}).then(success1, fail1)

            $scope.orderWithdraw = (command) ->
                prompt = "Are you sure you want to Withdraw the order?"
                bootbox.confirm prompt, (r) ->
                    if not r then return
                    $scope.orderCommand(command)

            $scope.applyPromotion = (code) ->
                $http.post("#{orderbase}/applyPromotion", JSON.stringify(code)).then(success1, fail1)

            $scope.jobCommand = (command, jobId) ->
                url = "#{orderbase}/job/#{jobId}/#{command}"
                body = {}
                prompt =  jobCommandPrompts[command]
                if prompt
                    prompt = sure + prompt
                    bootbox.confirm prompt, (r) ->
                        if not r then return
                        $http.post(url, body).then(success1, fail1)
                else
                    $http.post(url, body).then(success1, fail1)

            $scope.jobsCommand = (command, jobIds) ->
                cd arguments
                url = "#{orderbase}/jobs/#{command}" + "?jobIds=" + jobIds.join(',')
                cd url
                body = {}
                prompt =  jobCommandPrompts[command]
                if prompt
                    prompt = sure + prompt
                    bootbox.confirm prompt, (r) ->
                        if not r then return
                        $http.post(url, body).then(success1, fail1)
                else
                    $http.post(url, body).then(success1, fail1)




            $scope.reorderJob = (jobId) ->
                command = "Reorder"
                url = "#{orderbase}/job/#{jobId}/#{command}"
                prompt =  jobCommandPrompts[command]
                if prompt then prompt = sure + prompt
                bootbox.confirm prompt, (rr) ->
                    if !rr then return
                    $http.post(url, {}).then((r) ->
                            orderId = parseInt(r.headers('OrderId'), 10)
                            jobId = parseInt(r.headers('jobId'), 10)
                            window.toastr.remove()
                            window.toastr.info(r.data)
                            $state.go('cust.order.job', {orderId: orderId, jobId: jobId}, {reload:true})
                            return
                        fail1
                    )
                return

            $scope.copyJobToNewOrder = (jobId) ->
                command = "Copy"
                url = "#{orderbase}/job/#{jobId}/Copy"
                prompt =  jobCommandPrompts[command]
                if prompt then prompt = sure + prompt
                bootbox.confirm prompt, (rr) ->
                    if !rr then return
                    $http.post(url, {}).then((r) ->
                            orderId = parseInt(r.headers('OrderId'), 10)
                            jobId = parseInt(r.headers('jobId'), 10)
                            window.toastr.remove()
                            $state.go('cust.order.job', {orderId: orderId, jobId: jobId}, {reload:true})
                            window.toastr.info(r.data)
                            return
                        fail1
                    )
                return

            $scope.copyJobNTimes = (jobId) ->
                dialog =  ngDialog.open({template: 'common/jobcopydialog.html'})
                dialog.closePromise.then((r) ->
                    n = r.value
                    if not n then return
                    if isNaN(n) then return
                    n = parseInt(n,10)
                    if n >= 100 then n = 99
                    url = "#{orderbase}/job/#{jobId}/ncopy/#{n}"
                    $http.post(url, {}).then(success1, fail1)
                )
                return

            $scope.reorderOrder = () ->
                $http.post("#{orderbase}/reorder", {}).then((r) ->
                        orderId = parseInt(r.headers('OrderId'), 10)
                        window.toastr.info(r.data)
                        $state.go( orderState ,{orderId: orderId}, {reload:true})
                        return
                    fail1
                )
                return



            $scope.copyOrder = () ->
                $http.post("#{orderbase}/copy", {}).then((r) ->
                        orderId = parseInt(r.headers('OrderId'), 10)
                        window.toastr.info(r.data)
                        $state.go( orderState ,{orderId: orderId}, {reload:true})
                        return
                    fail1
                )
                return

            $scope.getPdf =  (x) ->
                reqTxt = if x then "?htmlFormat=true" else ""
                lepApi2.download("/api/orders/order/#{order.Id}/quotePdf#{reqTxt}")
                return

            $scope.orderRefund = () ->
                 dialog =  ngDialog.open {
                    template   : 'staff/orders/order-credit-dlg.html'
                    controller : [ '$scope', ($scope) ->
                            $scope.cn = {
                                DateCreated: new Date() # Set default date to today
                            }
                            $scope.myobAccounts = myobAccounts
                            $scope.returnOrderCredit = () ->
                                price = parseFloat($scope.cn.Amount)
                                $scope.cn.Amount = price
                                $scope.cn.GST = price * 0.10
                                $scope.cn.Total = price + price * 0.10
                                # Ensure DateCreated is properly formatted
                                if $scope.cn.DateCreated
                                    $scope.cn.DateCreated = new Date($scope.cn.DateCreated)
                                $scope.closeThisDialog($scope.cn)
                        ]

                    }
                 dialog.closePromise.then (r) ->
                    rv = r.value
                    if !rv then return
                    $http.post("#{orderbase}/credit", rv).then (r) ->
                            toastr.info "Refund created"
                 return

            $scope.downloadOrderExtraFile = lepApi2.downloadOrderExtraFile

            $scope.emailExtraFile = (orderId, fileName ) ->
                x = "document/order/#{orderId}/extrafiles/Email/#{fileName}"
                lepApi2.post(x).then (r) ->


            #if order.Visibility.submitButton and    then toastr.success "Order ready to submit!"
            if order.Visibility.RdyArtworkInst  then toastr.warning "Upload ready artwork."
            if order.Visibility.ApprovalInst    then toastr.warning "Send to client for approval/review."
            if order.Visibility.ReadyInst       then toastr.warning "Send ready order for layout."
            if order.Visibility.ExpensiveInst   then toastr.warning "Ask a manager to approve this expensive order."
            if order.Visibility.NoActionInst    then toastr.success "No action required."
            #if order.Visibility.estPnl         then toastr.success order.Visibility.minDayText


            custFavouriteDeliveryDetails = "cust/FavouriteDeliveryDetails"
            $scope.deleteFavouriteDeliveryDetails = (dto) ->
                lepApi2.post(custFavouriteDeliveryDetails + "/Delete", dto).then (r) ->
                    $scope.favAddresses = r

            $scope.addFavouriteDeliveryDetails = () ->
                dto = {}
                dto.RecipientName = order.RecipientName
                dto.RecipientPhone = order.RecipientPhone
                dto.Address = order.DeliveryAddress
                lepApi2.post(custFavouriteDeliveryDetails + "/Add", dto).then  (r) ->
                    order.Visibility.currentDeliveryDetailsIsInFavourite = true


            $scope.openFavouriteDeliveryDetails = () ->
                lepApi2.get(custFavouriteDeliveryDetails + "/List").then (r) ->
                    $scope.favAddresses = r
                    dialog = ngDialog.open(
                        template:   'cust/SelectAddress.html',
                        className:  'ngdialog-theme-default custom-width-900',
                        width: '90%'
                        height: '90%'
                        trapFocus: true
                        scope: $scope
                    )
                    dialog.closePromise.then( (r) ->
                        v = r.value
                        if v is '$escape' or v is '$document' or v is '$closeButton' or !v then return
                        $scope.order.DeliveryAddress =  v.Address
                        $scope.order.RecipientName   =  v.RecipientName
                        $scope.order.RecipientPhone  =  v.RecipientPhone
                        $scope.order.DeliveryInstruction  =  v.DeliveryInstruction
                        #$scope.deliveryDetailsChanged = true
                        $scope.saveOrder()
                    )
                    return

            $scope.setOrderReipientDetails = (rd) ->
                    $scope.order.Contact = rd
                    $scope.saveOrder()


            $scope.getCustomersRecipients = () ->
                lepApi2.get(custContacts + "/List").then (r) ->
                    $scope.ContactsList = r


            custContacts = "cust/Contacts"
            $scope.openContacts = () ->
                lepApi2.get(custContacts + "/List").then (r) ->
                    $scope.ContactsList = r
                    dialog = ngDialog.open(
                        template:   'cust/SelectContact.html',
                        className:  'ngdialog-theme-default custom-width-900',
                        scope: $scope
                    )
                    dialog.closePromise.then( (r) ->
                        v = r.value
                        if v is '$escape' or v is '$document' or v is '$closeButton' or !v then return
                        $scope.order.Contact = v
                        $scope.saveOrder()
                    )
                    return
            $scope.saveSignature = (sig) ->
                sig = sig.substr(22)
                url = "document/order/#{order.Id}/SavePOD"
                lepApi2.post(url, JSON.stringify(sig)).then (r) ->
                    $state.reload()

            $scope.$watch('order.Visibility.artworkFolderNavigateUrl', (newVal, oldVal) ->
                unless newVal then return
                setTimeout(() ->
                    # Construct the URL when the artworkFolderNavigateUrl changes
                    platform = navigator.platform.toUpperCase()
                    isMac = platform.indexOf('MAC') >= 0
                    isWindows = platform.indexOf('WIN') >= 0

                    if isWindows
                        artworkUrl =  'localexplorer:' +  newVal.replace("\\\\dfs01\\resource\\", "P:\\").replace(/\//g, "\\")
                    else if isMac
                        artworkUrl =  'file:' +  newVal.replace("\\\\dfs01\\resource\\", "/resource/").replace(/\\/g, "\/")

                    # Use jQuery to update the href dynamically
                    $('#artwork-link').attr('href', artworkUrl)
                , 500)
            )



    ]

    app.controller 'OrderJobController1', [
        '$scope', '$rootScope', 'OrderService','$location','$stateParams','job','$state', '$timeout', 'Utils',   '$http', 'ngDialog', 'templateTree','$log', '$q',	'cfpLoadingBar', 'enums','lepApi2','dcatsCust', 'JobService',
        ($scope,  $rootScope, OrderService,  $location,  $stateParams,  job,  $state,   $timeout, utils,   $http, ngDialog, templateTree, $log, $q, cfpLoadingBar, enums, lepApi2, dcatsCust, JobService) ->
            $scope._ = "OrderJobController1"
            $scope.job = job
    ]


    #OrderJobController
    app.controller 'OrderJobController', [
        '$scope', '$rootScope', 'OrderService','$location','$stateParams','job','$state', '$timeout', 'Utils',   '$http', 'ngDialog', 'templateTree','$log', '$q',	'cfpLoadingBar', 'enums','lepApi2','dcatsCust', 'JobService', '$sce',
        ($scope,  $rootScope, OrderService,  $location,  $stateParams,  job,  $state,   $timeout, utils,   $http, ngDialog, templateTree, $log, $q, cfpLoadingBar, enums, lepApi2, dcatsCust, JobService, $sce ) ->
            $scope._ = "OrderJobController"
            IsCustomer = $scope.$root.globals.IsCustomer
            orderState = if IsCustomer then "cust.order.view" else "staff.order.view"
            jobState   = if IsCustomer then "cust.order.job"  else "staff.order.job"

            $scope.estimators = estimators
            $scope.dcats = dcatsCust
            lastJobId = 0
            lastOrderId = 0
            $scope.files = []
            $scope.requiredPositions = []
            $scope.artwork = {}

            $scope.jobVmo = angular.copy(job)

            $rootScope.title = "O#{$stateParams.orderId} > J#{$stateParams.jobId}"

            orderbase = "/api/Orders/order/#{$stateParams.orderId}"
            jobbase   = "/api/Orders/order/#{$stateParams.orderId}/job/#{$stateParams.jobId}"

            $scope.goJob = (jid) ->
                JobService.getJob(jid).then (j) ->
                    $state.go('staff.order.job', {orderId: j.OrderId, jobId: jid })

            $scope.$watch('job.Visibility.artworkFolderNavigateUrl', (newVal, oldVal) ->
                    unless newVal then return
                    setTimeout(() ->
                        # Construct the URL when the artworkFolderNavigateUrl changes
                        platform = navigator.platform.toUpperCase()
                        isMac = platform.indexOf('MAC') >= 0
                        isWindows = platform.indexOf('WIN') >= 0

                        if isWindows
                            artworkUrl =  'localexplorer:' +  newVal.replace("\\\\dfs01\\resource\\", "P:\\").replace(/\//g, "\\")
                        else if isMac
                            artworkUrl =  'file:' +  newVal.replace("\\\\dfs01\\resource\\", "/resource/").replace(/\\/g, "\/")

                        # Use jQuery to update the href dynamically
                        $('#artwork-link').attr('href', artworkUrl)
                    , 500)
            )

            $scope.$watchGroup ['job.UploadType', 'job.Template.Id', 'job.FrontCelloglaze'], (nv,ov) ->
                n = job.UploadType
                if !n? then return

                # if Upload type changed, then empty the AttachmentsToProcess
                if nv[0] != ov[0] then job.AttachmentsToProcess = []
                rps = utils.getRequiredPositions($scope.job)
                if n is 'multiart'
                    $scope.requiredPositions = rps
                else if n is 'singleart'
                    if rps.length is 1
                        $scope.requiredPositions = rps
                    else
                        $scope.requiredPositions = ['multiart']
                else if n is 'later'
                    $scope.files = []
                    $scope.requiredPositions = []

            #if $stateParams.templateId
            #   job.Template =  _.find(window.custTemplates, {Id:  parseInt($stateParams.templateId) })

            if $stateParams.category
                $timeout(() ->
                    $rootScope.$broadcast('set-category-force', $stateParams.category)
                ,100)
                #job.Template =  _.find(window.custTemplates, {Id:  parseInt($stateParams.templateId) })

            $scope.setCategory = (c) ->
                $scope.$broadcast('set-category', c)

            if !job.IsCustomFacility
                job.Facility = null

            $scope.jobVmo = angular.copy(job)
            $scope.job = job


            $scope.$watch "job.Copies", (n,o) ->
                if n && parseInt(n,10) > 1
                    job.UploadType = "later"

            $scope.openOrder = () ->
                $state.go(orderState, {orderId: $stateParams.orderId}, {reload:true})

            $scope.reload = () ->
                $state.reload()

            $scope.nextJob =  ->
                jobs = $scope?.$parent?.order?.Jobs
                if !jobs then return
                ids = _.map(jobs, "Id").sort()
                nextId = _.dropWhile(ids, (x) -> x <=$scope.job.Id)[0]
                if(!nextId) then  nextId = ids[0]
                $state.go(jobState, {orderId: $stateParams.orderId, jobId: nextId}, {reload:true})



            $scope.saveJobBasic = (cmd) ->
                d = $q.defer()
                isNewJobBeingSaved = $stateParams.jobId == 0
                $scope.job.NumberOfMagnets = $scope.job.NumberOfMagnets || 0
                job = angular.copy($scope.job)
                url = jobbase
                if cmd is 'copy'
                    job.Id = 0
                    url   = "/api/Orders/order/#{$stateParams.orderId}/job/0"

                job.Copies = parseInt(job.Copies, 10) || 1
                delete job.Visibility
                delete job.Comments

                formData = new FormData
                formData.append 'request',  angular.toJson(job, true)

                #now add all of the assigned files
                i = 0
                while i < $scope.files.length
                    #add each file to the form data and iteratively name them
                    formData.append $scope.requiredPositions[i], $scope.files[i].file
                    i++

                jobId = $scope.job.Id
                $rootScope.$broadcast("percentage", 1)
                $http(
                    method          : 'POST'
                    url             : url
                    headers         : {'Content-Type': undefined}
                    transformRequest: angular.identity
                    data: formData
                    #ignoreLoadingBar: true
                    uploadEventHandlers:
                        progress: (e) ->
                            if (e.lengthComputable)
                                px = (e.loaded / e.total)
                                cfpLoadingBar.set(px)
                                $rootScope.$broadcast("percentage",  Math.floor(px * 100))
                                #if e.loaded == e.total and $scope.files.length > 0
                                #	window.toastr.info("File upload finished! Server will perform extra work now...", "please wait...", {timeout:10000})

                ).then((result, status, headers, config) ->
                    r = result.data
                    lastJobId = parseInt(result.headers('JobId'), 10)
                    lastOrderId = parseInt(result.headers('OrderId'), 10)
                    d.resolve(r)
                    #$state.go(orderState, {orderId: r.Id}, {reload:true})
                    return
                ,(data, status, headers, config) ->
                    d.reject(data, status, headers, config)
                    $rootScope.$broadcast("percentage",  -1)
                    return
                )
                d.promise

            $scope.saveJob2 = (cmd) ->
                isNewJobBeingSaved = $stateParams.jobId == 0

                dx = $q.defer()
                contFn = () ->
                    $scope.saveJobBasic(cmd).then (r)->
                        if cmd is 'copy'
                            $state.reload()
                            toastr.info "A copy of this job has been created under this order"
                            dx.resolve(true)
                            return

                        # if isNewJobBeingSaved and lastJobId
                        #     $state.go(jobState, {orderId: r.Id, jobId : lastJobId}, {reload: true, inherit: false, notify: true})
                        #     #dx.resolve(true)
                        #     return

                        $state.go(orderState, {orderId: r.Id}, {reload:true})
                        dx.resolve(true)
                        return

                artworkConfirm.callback = (agreed)->
                        if not agreed
                            dx.resolve(true)
                            return
                        contFn()

                if $scope.job.AttachmentsToProcess.length
                    bootbox.confirm artworkConfirm
                else
                    contFn()
                dx.promise

            $scope.$on 'savejob-refresh', () ->
                $scope.saveJobBasic().then((r) ->
                    $state.go(jobState, {orderId: lastOrderId, jobId : lastJobId}, {reload: true, inherit: false, notify: true})
                )

            $scope.$on 'savejob', () ->
                $scope.saveJobBasic().then((r) ->
                    m = {command: 'savejobresult', data: r}
                    window.parent?.postMessage(m, '*')
                )
            $scope.job.newComment = {LepOnly:true}
            $scope.addComment = () ->
                url = "/api/Orders/Job/#{$stateParams.jobId}/comments"
                $http.post(url, $scope.job.newComment).then ()->
                    $scope.job.newComment =  {LepOnly:true}
                    $http.get(url).then (r) ->
                        $scope.job.Comments = r.data


            $scope.jobCommand = (command, needsSaveFirst, extra) ->
                body = JSON.stringify(extra || '')
                if $scope.job.newComment.CommentText
                    body = JSON.stringify($scope.job.newComment.CommentText)

                JSON.stringify($scope.job.newComment.CommentText)

                commandFn = () ->
                    url = "#{jobbase}/#{command}"
                    prompt = jobCommandPrompts[command]
                    if prompt then prompt = sure + prompt
                    bootbox.confirm prompt, (rr)->
                        if !rr then return
                        $http.post url, body
                        .then (d) ->
                                    window.toastr.success(d.data)
                                    if command == 'Delete'
                                        $state.go($state.$current.parent.name + '.view', {}, {reload:true})
                                    else
                                        $state.reload()
                                ,(d) ->
                                    window.toastr.error(d.data)

                if needsSaveFirst
                    $scope.saveJobBasic().then(()-> commandFn())
                else
                    commandFn()

            $scope.addSpecialInstructions = () ->
                dialog = ngDialog.open
                    template: 'common/special-instructions-popup.html',
                    scope: $scope
                dialog.closePromise.then (r) ->
                    if r.value and r.value[0] is '$' then return
                    job.SpecialInstructions = r.value
                    if job.Id > 0
                        $scope.saveJobBasic().then( ()-> $state.reload())
                return


            #toastr.remove()
            if job.Visibility?.QuoteInst        then toastr.warning "Supply quote."
            if job.Visibility?.RdyArtworkInst   then toastr.warning "Upload ready artwork."
            if job.Visibility?.inPrintInst      then toastr.warning "Job sheet has already been printed."
            if job.Visibility?.priceExpireInst  then toastr.error   "Our prices are valid for up to 30 days. We have updated your job price to reflect our current pricing."
            if job.Visibility?.quoteExpireInst  then toastr.error   "Our quotes are valid for up to 60 days. Please contact us to confirm if this quote is still valid."
            #if job.Visibility.NoActionInst then toastr.success " No action required."

            $scope.rrLabel = $stateParams.ncrCmd
            $scope.reprintReqVm = { "Id": job.Id,  "ReprintCost": job.Price,  "InvoicePrice": "0",  "NcrNo": "",  "CopyPreflight": true,  "HasDispatch": true,  "PredefinedReason": "", "Reason": "", "Result": "", "Quantity":job.Quantity}

            $scope.$watchGroup ["reprintReqVm.PredefinedReason","reprintReqVm.Quantity"] , (n,o) ->
                if !n[0] then return
                l = $scope.rrLabel
                lu = l.toUpperCase()
                x = """NCR #{lu}
                #{n[0]}
                #{l} x #{n[1]}
                """

                $scope.reprintReqVm.Reason =  x
                $scope.reprintReqVm.Result =  x

            $scope.reprintRestartJob = (command) ->
                url = "/api/Orders/Order/#{$stateParams.orderId}/job/#{$stateParams.jobId}/ReprintRestart"
                $scope.reprintReqVm.Command = command
                $http.post(url, $scope.reprintReqVm).then((r) ->
                        orderId = parseInt(r.headers('OrderId'), 10)
                        toastr.info(r.data)
                        $state.go( orderState ,{orderId: orderId}, {reload:true})
                        return)

            # $scope.reprintOrder = () ->
            #     $scope.reprintReqVm.ReprintCost = 0
            #     $http.post("#{orderbase}/ReprintRestart", $scope.reprintReqVm).then((r) ->
            #             orderId = parseInt(r.headers('OrderId'), 10)
            #             window.toastr.info(r.data)
            #             $state.go( orderState ,{orderId: orderId}, {reload:true})
            #             return
            #     )
            #     return

            $scope.showSlotEntryInput = false

            jto   = enums.KeyVal.JobTypeOptions
            jco   = enums.KeyVal.JobCelloglazeOptions

            $scope.$watch "job", (n,o) ->
                isPrePress      = $rootScope.globals.IsPrepress
                isOffsetPrint   = job.PrintType == 'O'
                isBusienssCardy = [jto.BusinessCard,jto.DoubleBusinessCard,jto.BusinessCardNdd,jto.BusinessCardSdd,jto.Postcard].indexOf(job.Template?.Id) > -1
                isCustomSized   = job.FinishedSize?.PaperSize?.Id == 12
                isFoilOrEmbossingInvolved = [jco.Foil,jco.EmbossFoil,jco.EmbossedGlossFront,jco.EmbossedMattFront].indexOf(job.FrontCelloglaze) > -1
                isCustomSlotVisible =
                    (isPrePress and  isOffsetPrint and isBusienssCardy and
                    (
                        isCustomSized or
                        isFoilOrEmbossingInvolved or
                        job.SuggestedSlot > 1)
                    )
                $scope.showSlotEntryInput = isCustomSlotVisible

            , true

            # extraFiles
            $scope.extraFiles =  []
            if job.Id > 0
                lepApi2.get("document/job/#{job.Id}/extrafiles").then (r) ->
                    $scope.extraFiles = r



            $scope.downloadExtraFile = lepApi2.downloadExtraFile
            $scope.deleteExtraFile = (jobId, fileName ) ->
                $http.post('/api/Document/job/' + jobId + '/extrafiles/delete/' + fileName).then (r) ->
                    lepApi2.get("document/job/#{jobId}/extrafiles").then (r) ->
                        $scope.extraFiles = r

            $scope.uploadExtraFile = (jobId) ->
                ngDialog.openConfirm({template: 'common/filebrowser.html', closeByDocument: false, closeByEscape: false,  backdrop: 'static', disableAnimation :true}).then (file) ->
                    if !file then return

                    formData = new FormData
                    formData.append 'file', file.file
                    $http(
                        method          : 'POST'
                        url             : "api/Document/job/#{jobId}/ExtraFiles"
                        headers         : {'Content-Type': undefined}
                        transformRequest: angular.identity
                        data            : formData
                    ).then (r) ->
                        $scope.extraFiles = r.data

            $scope.jobToDPC = (jobId) ->
                $http.post('/api/orders/JobToDpc/' + jobId).then () ->
                    window.toastr.info("Job " + jobId + " moved to DPC Pre Production")
                    setTimeout(() ->
                        $state.reload(true)
                    , 10)


            # crp means customer requested price
            # crp is visible when the job is a quote
            # crp is required only when the job price is 0
            crp = {visible: false, required : false, valid: false}
            if !job.Id
                crp.btnIcon = 'glyphicon-plus'
                crp.btnText = 'Continue'
            else
                crp.btnIcon = 'glyphicon-floppy-save'
                crp.btnText = 'Continue'

            $scope.crp = crp
            $scope.$watchGroup ['job.crpDate','job.CustomerRequestedPrice'], (n,o) ->
                $scope.updateCrp()

            $scope.$on 'priceResult', (x,y) ->
                $scope.updateCrp()

            $scope.updateCrp = () ->
                #cd +new Date
                f = $scope.jobDetailsForm
                if !f then return
                e = f.$error

                if !($scope.$root.globals.IsCustomer)
                    crp.visible = false
                    return

                # # if job is while label crp not visible
                if job.IsWhiteLabel
                    crp.visible = false
                    return
                if job.Price > 0
                    crp.visible = false
                    return
                # crp is visible when the job is a quote
                crp.visible = (f.$valid && !job.Price)







    ]


    app.controller 'StaffOrdersListController', [
        '$scope', '$location', 'enums', 'JobService', '$sessionStorage', '$state', 'OrderService',  'templateTree', '$q' , '$localStorage', '$timeout',
        ($scope, $location, enums, JobService, $sessionStorage, $state, OrderService, templateTree, $q, $localStorage, $timeout) ->
            toastr.remove()
            $sessionStorage.searchresults = $sessionStorage.searchresults || {}
            $sessionStorage.search  = $sessionStorage.search || {staff:{}}
            cs = "staffOrders"

            JobService.getTemplates().then (d) -> $scope.templates = d
            JobService.getAllSizes() .then (d) -> $scope.allSizes  = d
            JobService.getAllStocks().then (d) -> $scope.allStocks = d

            $scope.templateTree = templateTree
            $scope.filters = $sessionStorage.search[cs] || {Page: 1}
            $scope.r       = $sessionStorage.searchresults[cs] || []

            c = undefined

            $scope.clear = () ->
                $scope.filters           = {Page: 1}
                $sessionStorage.search[cs] = {Page: 1}
                $sessionStorage.searchresults[cs] =  []

            $scope.search = () ->
                $sessionStorage.search[cs]  = $scope.filters
                c?.resolve()
                c = $q.defer()
                OrderService.getOrdersStaff($scope.filters, c).then (r) ->
                    $scope.r = r
                    $sessionStorage.searchresults[cs] = r
                return

            $scope.goPage = (p) ->
                $scope.filters.Page = p

            $scope.toggleSort = (sort) ->
                $scope.filters.SortDir =  sort == $scope.filters.SortField and !$scope.filters.SortDir
                $scope.filters.SortField = sort

            #$scope.search()

            $scope.$watch('filters', _.debounce( (n,o) ->
                                            $scope.search()
                                        , 500)
            , true)


            $scope.$on 'visibilitychange', (v, e) ->
                if e is 'visible' then $scope.search()


            # LORD-1181: fav searches
            favSearches   = $localStorage.favSearches || []
            $scope.favSearches = favSearches

            $scope.setAsCurrentSearch = (fs) ->
                $scope.filters = angular.copy(fs.filters)

            $scope.saveAsFavSearch = () ->
                bootbox.prompt
                    title: "Enter a name for this search!"
                    centerVertical: true
                    value:  ""
                    closeButton: false
                    callback: (name) ->
                        if !name then return
                        $timeout ()->
                            newSearch = {Name: name, filters : angular.copy($scope.filters)}
                            newSearch.page = 1
                            favSearches.push(newSearch)
                            # Make a deep copy to ensure it's properly saved to localStorage
                            $localStorage.favSearches = angular.copy(favSearches)
                            # Force localStorage to persist the data immediately
                            try
                              localStorage.setItem('ngStorage-favSearches', JSON.stringify($localStorage.favSearches))
                            catch e
                              console.error('Error saving to localStorage:', e)
                        , 0



            $scope.removeFavSearch = (fs) ->
                _.remove(favSearches, {Name: fs.Name})
                $localStorage.favSearches = angular.copy(favSearches)
                $scope.favSearches = angular.copy(favSearches)
                # Force localStorage to persist the data immediately
                try
                  localStorage.setItem('ngStorage-favSearches', JSON.stringify($localStorage.favSearches))
                catch e
                  console.error('Error saving to localStorage:', e)

            return @
        ]








    app.controller 'OrderPrintController', [
        '$scope', '$state', 'OrderService', '$location', '$http', '$stateParams','order', '$rootScope',  'ngDialog', '$log', 'lepApi2','$q',
        ($scope ,  $state,   OrderService,   $location,   $http, $stateParams,  orderVmo, $rootScope  ,   ngDialog,   $log,   lepApi2,  $q) ->

            $scope._ = "OrderViewController"

            $scope.updatingCourier = false

            order = angular.copy(orderVmo)
            $scope.orderVmo =orderVmo;
            $scope.order = order;
            $rootScope.title = "O#{order.Id}"

            $scope.addressx = {}
            $scope.fileAttachmentPending = false
            IsCustomer = $scope.$root.globals.IsCustomer
            orderState = if IsCustomer then  "cust.order.view" else "staff.order.view"
            jobState   = if IsCustomer then "cust.order.job"   else "staff.order.job"

            orderbase = "/api/Orders/order/#{$scope.order.Id}"

            $scope.qoText = 'order'

            $scope.$watch 'order.PriceOfJobs', (p,p0) ->
                if order.Status == 0
                    if p is null
                        $scope.qoText = 'draft'
                    else if p is 0
                        $scope.qoText = 'order'


            # if the courier changes,update order.PackDetail.Price and save order
            $scope.$watch 'order.Courier', (f,o) ->
                if f is o then return
                #if !f   then return
                so = _.find($scope.freights, (x) -> x.Value == f)
                if so
                    window.toastr.remove()
                    $scope.order.Courier = so.Value
                    $scope.order.PackDetail.Price =  so.CustomerCharge
                    $scope.saveOrder()
                if !so
                    $scope.order.Courier = ''
                    $scope.order.PackDetail.Price =  0
            , true


            # get list of freights for this order on page load
            getFreights = (id) ->

                postcode = $scope.order.DeliveryAddress.Postcode + "-" +  $scope.order.DeliveryAddress.City
                freightFields = ""
                freightFields += order.Jobs.length
                for j in order.Jobs
                    freightFields += j.DateModified
                $scope.updatingCourier = true
                OrderService.getFreights(id, postcode, freightFields).then (f) ->
                    $scope.freights  =  f
                    $scope.updatingCourier = false
                    # after getting freights see if the selected customer charge matches packing details price
                    # if not matches that means post code was updated and we need to update it

                    so = _.find($scope.freights, (x) -> x.Value == order.Courier)
                    if  so and  so.CustomerCharge != order.PackDetail.Price and so.Value != "None ~ None"
                        order.PackDetail.Price = so.CustomerCharge
                        window.toastr.remove()
                        $scope.order.PackDetail.Price =  so.CustomerCharge
                        #$scope.saveOrder()
                    if !so
                        order.Courier =   ''
                        order.PackDetail.Price = 0
                        if order.PriceOfJobs
                            window.toastr.warning('Please select a courier')



            #getFreights(order.Id)

            $scope.backToOrders = (justSubmitted) ->
                p = {}
                if justSubmitted then p =  {justSubmitted:true}

                if $scope.$root.globals.IsCustomer
                    $state.go('cust.orders', p)
                else
                    $state.go('staff.orders')


    ]




    artworkConfirmmessage =
            """
            <ol><b style="color: red; font-weight: 800;"> I have </b>
            <li>Checked my Artwork files to make sure they are
              <a target="blank" href="https://www.lepcolourprinters.com.au/support-centre/artwork-templates/">  <i class="glyphicon glyphicon-new-window"></i> Press Ready</a>
             </li>
            <li>Understood any Artwork warnings and fixes and accept responsibility for any fixes made or not made</li>
            <li>Read, understood and accept LEP's
                <a href="/images/pdfs/LEP_Terms_Conditions.pdf"
                target="_blank">Terms & Conditions</a> of supply</li>
            </ol>
            """
    artworkConfirm =
        centerVertical: true
        closeButton: false
        message:   artworkConfirmmessage
        buttons:
            confirm:
                label: 'I confirm the above'
                className: 'btn-success pull-right'
            cancel:
                label: 'Cancel'
                className: 'btn-warning pull-left'





    app.controller 'OrderReprintController', [
        '$scope', '$state', 'OrderService', '$location', '$http', '$stateParams','order', '$rootScope',  'ngDialog', '$log', 'lepApi2','$q','imgLookup', 'Utils',
        ($scope ,  $state,   OrderService,   $location,   $http, $stateParams,  order, $rootScope  ,   ngDialog,   $log,   lepApi2,  $q, imgLookup, utils) ->

            $scope._ = "OrderViewController"
            $scope.imgLookup = imgLookup
            $scope.pos2label = utils.pos2label
            $scope.updatingCourier = false

            orderVmo = angular.copy(order)
            $scope.order = order;
            $scope.rrLabel = 'Reprint'
            reprintReqVm = {Jobs:[]}
            wasDispatched = order.Status >= 4
            for j in order.Jobs
                x =
                    Id : j.Id
                    Command : 'Reprint'
                    Price: j.Price
                    InvoicePrice: 0
                    ReprintCost: j.Price
                    CopyPreflight: true
                    HasDispatch: wasDispatched
                    Reason: ''
                    Result: ''
                    NcrNo: ''
                    PredefinedReason: ''
                    Quantity: j.Quantity
                reprintReqVm.Jobs.push(x)
            $scope.reprintReqVm = reprintReqVm

            $rootScope.title = "O#{order.Id}"

            IsCustomer = $scope.$root.globals.IsCustomer
            orderState = if IsCustomer then  "cust.order.view" else "staff.order.view"
            jobState   = if IsCustomer then "cust.order.job"   else "staff.order.job"

            orderbase = "/api/Orders/order/#{$scope.order.Id}"

            $scope.qoText = 'order'

            success1 = (d) ->
                if d.data
                    window.toastr.success(d.data)
                orderId = parseInt(d.headers('OrderId'), 10)
                if orderId
                    $state.go( orderState ,{orderId: orderId}, {reload:true})
                else
                    $state.reload()
                return

            fail1 = (d) ->
                window.toastr.warning(d.data)
                return


            $scope.reasonOrQuantityChanged = (job) ->
                if !job.PredefinedReason then return
                l = $scope.rrLabel
                lu = l.toUpperCase()
                x = """NCR #{lu}
                #{job.PredefinedReason}
                #{l} x #{job.Quantity}
                """
                job.Reason =  x
                job.Result =  x

            $scope.updateFromCommonNCRNo  = (NcrNo) ->
                for j in reprintReqVm.Jobs
                    j.NcrNo = NcrNo

            $scope.backToOrders = (justSubmitted) ->
                p = {}
                if justSubmitted then p =  {justSubmitted:true}

                if $scope.$root.globals.IsCustomer
                    $state.go('cust.orders', p)
                else
                    $state.go('staff.orders')


            $scope.reprintOrder = () ->
                url = "#{orderbase}/ReprintRestart"
                $http.post(url, reprintReqVm).then((r) ->
                            orderId = parseInt(r.headers('OrderId'), 10)
                            jobId = parseInt(r.headers('jobId'), 10)
                            window.toastr.remove()
                            window.toastr.info(r.data)
                            $state.go('staff.order.view', {orderId: orderId}, {reload:true})
                            return
                        fail1
                    )
                return

            $scope.removeJob = (i) ->
                reprintReqVm.Jobs.splice(i,1)


    ]
