﻿<div ng-form="orderForm">

    <div leppane="Order details" visible="true" class="form-horizontal">

        <!-- common order details -->
        <div class="row">
            <div class="col-md-6 col-xs-12">
                <div class="form-group">
                    <label class="col-xs-4 control-label" for="person">Order # </label>
                    <div class="col-xs-8   form-control-static">{{order.Id}}</div>
                </div>
                <div class="form-group">
                    <label class="col-xs-4 control-label ">Status</label>
                    <div class="col-xs-8   form-control-static">{{::order.StatusC}}</div>
                </div>
                <div class="form-group">
                    <label class="control-label col-xs-4 nowrap" for="person">Purchase Order#</label>
                    <div class="col-xs-8">
                        <input class="form-control" type="text" ng-model="order.PurchaseOrder" maxlength="40" />
                    </div>
                </div>
                <div class="form-group" ng-show="order.SubmissionDate">
                    <label class="col-xs-4 control-label ">Submitted</label>
                    <div class="col-xs-8   form-control-static">
                        {{::order.SubmissionDate | datex:'dd-MMM-yy HH:mm'}}, <span am-time-ago="::order.SubmissionDate" class="text-muted"></span>
                    </div>
                </div>
                <div class="form-group" ng-show="order.DispatchEst && !order.RevisedDispatchDate">
                    <label class="col-xs-4 control-label ">Est. dispatch </label>
                    <div class="col-xs-8   form-control-static">
                        {{::order.DispatchEst | datex:'dd-MMM-yy  HH:mm'}}, <span am-time-ago="::order.DispatchEst" class="text-muted"></span>
                    </div>
                </div>

                <div class="form-group" ng-show="order.RevisedDispatchDate">
                    <label class="col-xs-4 control-label ">Revised dispatch </label>
                    <div class="col-xs-8   form-control-static">
                        {{::order.RevisedDispatchDate | datex:'dd-MMM-yy  HH:mm'}}, <span am-time-ago="::order.RevisedDispatchDate" class="text-muted"></span>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-xs-12">

                <div class="form-group" ng-show="order.Visibility.quotePnlVisible">
                    <label class="control-label col-sm-4"></label>
                    <div class="col-sm-8" style="color:red">
                        Special Price. No further discount allowed
                    </div>
                </div>

                <div class="form-group" ng-show="order.Visibility.promotionPnlVisible">
                    <label class="control-label col-xs-4">Promo Code</label>
                    <div class="col-xs-8">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="" ng-model="order.PromotionPromotionCode">
                            <span class="input-group-btn">
                                <button id="applyPromo" class="btn  " ng-click="applyPromotion(order.PromotionPromotionCode)">Apply</button>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="form-group" ng-show="order.Payments.length">

                    <label class="control-label  col-sm-4">Payments made</label>
                    <div class="col-sm-8 form-control-static">

                        <div ng-repeat="p in ::order.Payments">{{p.PaidAmount | currency}} on {{p.At | date:'dd-MMM-yyyy hh:mm' }} </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div leppane="Ordered by" visible="true" class="form-horizontal">

        <!--<p class="form-text text-muted">
            To change the contact for this order - override the usual contact person and number
        </p>-->
        <!--<a class="btn  btn-sm " trigger="hover" style="position: relative; display: inline"
               placement="top"
               title="Select from your contacts"
               bs-tooltip ng-click="openContacts( )">
            <i class="glyphicon glyphicon-search"></i>
        </a>

        <a>
            <i class="glyphicon glyphicon-chevron-down"></i>
        </a>
        -->

        <div disabled="!order.Visibility.contactPnl">

            <div class="form-horizontal" all-input-disabled="{{!order.Visibility.contactPnl}}">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group form-group-sm">
                            <label class="control-label  col-xs-3" for="person">Person</label>
                            <div class="col-xs-7">
                                <div class="input-group">
                                    <div class="input-group-addon">
                                        <i class="glyphicon glyphicon-user"></i>
                                    </div>
                                    <input class="form-control" type="text" ng-model="order.Contact.Name" ng-maxlength="50" size="50" />

                                    <div class="input-group-btn" ng-click="getCustomersRecipients()" ng-if="order.Visibility.contactPnl">
                                        <a class="btn btn-sm " data-toggle="dropdown" href="#"><b class="glyphicon glyphicon-menu-down"></b></a>
                                        <ul class="dropdown-menu" role="menu">
                                            <li ng-repeat="fa in ContactsList | orderBy:'Name'">
                                                <a href="" ng-click="setOrderReipientDetails(fa)">{{fa.Name}}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-6">
                        <div class="form-group form-group-sm">
                            <label class="control-label  col-xs-3" for="email">Email</label>
                            <div class="col-xs-7">
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <i class="glyphicon   glyphicon-envelope"></i>
                                    </span>
                                    <input class="form-control" type="text" ng-model=" order.Contact.Email" name="email" maxlength="200" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-6">
                        <div class="form-group form-group-sm">
                            <label class="control-label col-xs-3" for="phone">Phone</label>
                            <div class="col-xs-7">
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <i class="glyphicon glyphicon-phone-alt"></i>
                                    </span>

                                    <div class="col-sm-3 col-xs-2 lpad0 rpad0">
                                        <input class="form-control" type="text" ng-model=" order.Contact.AreaCode" maxlength="6" />
                                    </div>
                                    <div class="col-sm-9 col-xs-10 lpad0 rpad0">
                                        <input class="form-control" type="text" ng-model=" order.Contact.Phone" maxlength="12" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-6">
                        <div class="form-group form-group-sm">
                            <label class="control-label  col-xs-3" for="tel">Mobile</label>
                            <div class="col-xs-7">
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <i class="glyphicon   glyphicon-phone"></i>
                                    </span>
                                    <input class="form-control" type="text" ng-model=" order.Contact.Mobile" maxlength="12" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div ng-if="order.IsWLOrder">

        <div leppane="{{::(order.WLCustomerId? 'Registered Customer': 'Guest User')}}" visible="true" class="form-horizontal">
            <div lep-contact-details contact="order.WLContact" disabled="!order.Visibility.saveButton" email-required="true"></div>
        </div>
    </div>





    <div ng-if="!order.HasSplitDelivery">
        <div leppane="Delivery details" visible="!(order.HasSplitDelivery)" class="form-horizontal">
            <!--<p class="form-text text-muted">
                You can change your default delivery address for this order below.
                Please note that all jobs in this order will be delivered to this address. Jobs to a different delivery address must be placed in a separate order
            </p>-->
            <!--Extra fields to enter Recepient and contact number if for customer's customer-->
            <div class="row form-horizontal" ng-form="deliveryDetailsForm">

                <div class="col-md-6 col-xs-12">

                    <fieldset ng-disabled="!order.Visibility.saveButton">
                        <div class="form-group form-group-sm  ng-class:{'has-error': deliveryDetailsForm.RecipientName.$invalid} ">
                            <label class="control-label  col-xs-4" for="person">Recipient / Business Name</label>
                            <div class="col-xs-8">
                                <input class="form-control" type="text" ng-model="order.RecipientName"
                                    name="RecipientName" ng-required="!addressDefault"
                                    ng-maxlength="50" size="50" mvs />
                            </div>
                        </div>

                        <div class="form-group form-group-sm">
                            <label class="control-label  col-xs-4" for="person">Recipient Phone</label>
                            <div class="col-xs-8">
                                <input class="form-control" type="text" ng-model="order.RecipientPhone" name="RecipientPhone"
                                    ng-maxlength="12" size="12" mvs />
                            </div>
                        </div>

                        <div class="form-group form-group-sm" ng-show="::order.Visibility.custAttachLogoChkEnabled">
                            <label class="control-label checkbox col-xs-4" for="CustomerLogoRequired">Attach logo label</label>
                            <div class="col-xs-8"><input class="checkbox" type="checkbox" id="CustomerLogoRequired" ng-model="order.CustomerLogoRequired" /></div>
                        </div>

                        <div class="form-group form-group-sm">
                            <label class="control-label  col-xs-4" for="person">Logo Reference </label>
                            <div class="col-xs-8">
                                <input class="form-control" type="text" ng-model="order.CustomerLogoYourRef"
                                    ng-disabled="!order.CustomerLogoRequired" maxlength="20" size="20" mvs />
                            </div>
                        </div>


                        <div class="form-group form-group-sm">
                            <label class="control-label  col-xs-4" for="person">Delivery Instructions</label>
                            <div class="col-xs-8">
                                <input name="DeliveryInstructions" class="form-control" type="text" ng-model="order.DeliveryInstructions"
                                    ng-maxlength="28" size="28" mvs />
                                <div class="redAlert bold" ng-if="deliveryDetailsForm.DeliveryInstructions.$error.maxlength">* max 28 charecters</div>
                            </div>
                        </div>
                        <div class="form-group form-group-sm" ng-show="order.Visibility.PackWithoutPallets">
                            <label class="col-xs-4 control-label" for="customer">Pack </label>
                            <div class="col-xs-8">
                                <label class="control-label"><input type="radio" ng-model="order.PackWithoutPallets" data-ng-value="false" /> Palletized </label>&nbsp;&nbsp;
                                <label class="control-label" ng-hide="order.Visibility.NoLooseCarton"><input type="radio" ng-model="order.PackWithoutPallets" data-ng-value="true" ng-disbled="true" /> Loose Cartons</label>&nbsp;&nbsp;
                            </div>
                        </div>
                        <!-- {{order.Visibility.PackWithoutPallets | json}} -->


                    </fieldset>
                </div>

                <div class="col-md-6 col-xs-12">
                    <div address-details address="order.DeliveryAddress" noteditable="!order.Visibility.addressPnl"></div>
                </div>
            </div>

            <div class="row form-horizontal">
                <div class="col-md-12 col-xs-12">
                    <div class="pull-right">

                        <a ng-show="order.Visibility.addressPnl"
                        class="btn  btn-sm " trigger="hover"
                        placement="top"
                        title="Search from previous addresses"
                        bs-tooltip ng-click="openFavouriteDeliveryDetails()">
                            <i class="glyphicon glyphicon-search"></i>
                        </a>

                        <a ng-hide="order.Visibility.currentDeliveryDetailsIsInFavourite || deliveryDetailsForm.$invalid" class="btn  btn-sm " trigger="hover"
                        placement="top"
                        title="Make current address a favourite"
                        bs-tooltip ng-click="addFavouriteDeliveryDetails(order.DeliveryAddress)">
                            <i class="glyphicon glyphicon-star"></i>
                        </a>

                        <a class="btn btn-sm " ng-show="order.Visibility.resetAddress" ng-click="orderCommand('resetAddress')"
                        trigger="hover"
                        placement="top"
                        title="Reset address to your default delivery address"
                        bs-tooltip>
                            Reset
                        </a>

                        <a class="btn btn-sm  bold"
                        ng-show="deliveryDetailsChanged && order.Visibility.resetAddress"
                        ng-click="saveOrder()" style="display:inline-block;border:4px solid red">
                            Save address and update freight options
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!--{{order.PackDetail.IsCustom}}-->
        <div
            
            id="sds"
            style="    color: black;
            position: relative;
            width: 230px;
            left: -249px;
            padding: 4px;
            height: 0px;
            /* top: 96px; */
            display: table-caption;"
            ng-if="order.Visibility.splitDeliveryPossible == true && order.Visibility.splitCount == 0"
            ng-init="j0Id =order.Jobs[0].Id ">
             
             <h5>Exciting news! Split Deliveries!!!</h5>
             <img src="images/split.png" style=" height: 76px; float:left">
           
            <div style="text-align: justify;">
                On single job orders like this, we are allowing you to split the delivery to multiple addresses.
                This is useful if you have a large job that needs to be delivered to multiple locations.
                if you want to split the delivery, please click
                
                <a class="btn btn-info btn-sm bg-orange" style="margin: 5px 10px;"
                       ui-sref="^.jobsplit({orderId: order.Id, jobId: j0Id})"> Split delivery for job {{j0Id}}</a>
            </div>
        
        </div>

        <div leppane="Freight" visible="true" class="form-horizontal  ">

            <div class="form-group  ng-class:{'has-error': order.Visibility.addressPnl   && (order.Courier == 'None ~ None' || !order.Courier ) }">

                <div class="col-xs-12">
                    <!--&& !order.PackDetail.IsCustom-->
                    <span ng-if="order.Visibility.addressPnl" ng-hide="updatingCourier">
                        <select id="freights" name="freights" class="form-control input monospace" ng-disabled="!order.Visibility.addressPnl"
                                ng-model="order.Courier"
                                ng-options="f.Value as f.Name disable when f.disabled for f in freights "

                                >

                            <option value="">Select a courier and service</option>
                        </select>
                    </span>
                    <span ng-show="updatingCourier" style="font-weight:bold; color: orangered">
                        Finding couriers and freight charges, please wait...
                    </span> <!--|| order.PackDetail.IsCustom-->
                    <span ng-if="!order.Visibility.addressPnl">{{::order.Courier}}</span>
                </div>
            </div>
            <div style="font-family: monospace !important;white-space: pre !important; font-weight: bold;">

                {{order.PackDetail.CustomerSummary}}
            </div>


        </div>
    </div>



    




      <!--{{order.DateModified}} == {{order.Courier | json}} ==-->
    <div class="col-md-12 ng-scope help-block" ng-if="order.Status < 1" style="text-align: justify;color: rgba(0, 0, 0, 0.68);">
        <h5> Upload your artwork </h5>
        <img src="/images/dragdrop.png" style="top: -15px;float: left;position: relative;">
        <b>Drag and drop</b> your PDF artwork over job lines on the below.  You can drop single or multiple files.  New art work will replace the old ones.
        If you drop multiple files then you will need to denote position of each files.
    </div>

    <div leppane="Jobs" visible="true">
        <table class="table table-striped" ng-form="artworkForm">
            <thead>
                <tr class="jobrow">
                    <th>Status</th>
                    <th>Job#</th>
                    <th></th>
                    <th>Job Name</th>
                    <th class="rlabel">Quantity</th>
                    <th>Type</th>
                    <th class="rlabel">Price</th>
                    <th class="actions">Action</th>
                </tr>
            </thead>
            <tbody>

                <tr></tr>
            </tbody>
            <tbody ng-repeat="j in order.Jobs">
                <tr class="jobrow"
                    attach-artwork="j" order="order" attach-artwork-enabled="j.Visibility.IsArtworkEditable" style="margin: 2px;">

                    <td class="" style="padding: 2px; vertical-align: middle; text-align: center; width: 140px;">
                        <span ng-show="::(j.Visibility.Thumbs.length>0)">
                            <img ng-repeat="t in j.Visibility.Thumbs track by $index"
                                 ng-src="/api/orders/Job/{{::j.Id}}/thumb/{{t}}"
                                 class="template-img1 grow axxx" />
                        </span>

                        <img ng-if="::(j.Visibility.Thumbs.length==0)"
                             ng-src="{{imgLookup(j.TemplateId)}}"
                             class="template-img1 " />

                        <i ng-style="{color: j.StatusCss}" ng-show="j.Visibility.artworkRequiredMsg && ( j.Files == undefined || (j.Files && j.Files.length==0))"
                           style="font-size: 30px;"
                           bs-tooltip placement="left" trigger="hover" toggle="tooltip" html="true"
                           data-title="This job needs artwork! <br />Drag and drop your PDF artwork into this job">

                            <i class="glyphicon glyphicon-cloud-upload " ></i>
                        </i>
                    </td>

                    <td class="actions">
                        <a class="bold" style="font-size: larger" ui-sref="^.job({jobId: j.Id})">{{::j.Id}}</a>
                        <div style="white-space: nowrap">
                            <span ng-style="{color: j.StatusCss}">{{::j.StatusC}}</span>
                        </div>

                        <!--<pre>{{j.Files | json}}</pre>-->
                        <div ng-if="j.Files.length" style="font-size: medium">
                            <table >
                                <tr ng-repeat="f in j.Files">
                                    <td style="padding: 2px;width:200px;" class="breakword">{{f.Name}}</td>
                                    <td class="form-group form-group-sm  ng-class:{'has-error': !(f.Position) || f.Position === 'files[]'  } ">
                                        <select class="filepos"  ng-model="f.Position"
                                        ng-options="rp as pos2label(rp) for rp in j.RequiredPositions"
                                        value-needs-to-pass="hasArtworkPosition" name="artPos"
                                        >
                                        <option></option>
                                        </select>

                                    </td>
                                    <td style="vertical-align: middle"> <i class="glyphicon glyphicon-remove-circle" ng-click="j.Files.splice($index,1)"></i></td>
                                </tr>
                            </table>
                        </div>

                          <!--	{{j.RequiredPositions}} {{ j.Files | json }}	-->
                    </td>
                    <td class="nowrap">

                        <!--{{::j.PrintType | printtype}} - {{ enums.ValueKey.Facility[j.Facility] }}-->
                        <!---->
                    </td>
                    <td>
                        <!--<div gg-editable-text="j.Name" gg-select-all gg-keep-focus ggIsEditing="j.isEditingName">
                        </div>-->

                        <input type="text" ng-model="j.Name" class="form-control" maxlength="80" ng-show="order.Visibility.saveButton"
                               ng-readonly="j.Name.indexOf('Reorder of') >-1" />
                        <span ng-show="!order.Visibility.saveButton">{{::j.Name}}</span>
                    </td>
                    <td class="rlabel">{{::j.Quantity | number : 0}}</td>
                    <td class="">{{::j.TemplateName}}</td>
                    <td class="rlabel">
                        <span ng-if="j.Price == '' && j.Status != 32">Price suggested</span>
                        <span ng-if="j.Price != ''">{{::j.Price | currency}}</span>
                    </td>
                    <td class="actions">
                        <!-- these need to be checked for permissions -->
                        <a class="" ng-show="::(j.Visibility.copy && j.Status == 0)" ng-click="copyJobNTimes(j.Id)">Copy</a>
                        <a class="" ng-show="::(j.Visibility.copy && j.Status > 0)" ng-click="copyJobToNewOrder(j.Id)">Copy</a>
                        <!--<a class="" ng-show="::j.Visibility.withdraw" ng-click="jobCommand('Withdraw',j.Id)">Withdraw</a>-->
                        <!--<a class="" ng-show="::j.Visibility.reactivate" ng-click="jobCommand('Reactivate',j.Id)">Reactivate</a>-->
                        <a class="" ng-show="::j.Visibility.reorder" ng-click="reorderJob(j.Id)">Reorder</a>
                        <a class="" ng-show="::j.Visibility.deleteButton" ng-click="jobCommand('Delete',j.Id)">Delete</a>
                        <a class="" ng-show="::j.Visibility.restart" ng-click="jobCommand('Restart',j.Id)">Restart</a>
                        <a class="" ng-show="::j.Visibility.approvalButton" ng-click="jobCommand('ApproveQuote',j.Id)">Approve</a>
                    </td>
                </tr>
                <tr ng-show="j.Files.length>0">
                    <td colspan="8">
                        <b style="color: red;">
                            {{j.Files.length}} file will be attached to this job. <span ng-if="j.Files.length > 0">
                                Your file will be attached to this job once you make a selection from the dropdown beside your file name</span>
                                Press 'Save Order' button to save changes. To change these files drag and drop  new file(s).
                        </b>
                    </td>
                </tr>
            </tbody>

            <tbody>
                <tr ng-if="(order.PriceOfJobs === '' || order.PriceOfJobs === null)  ">
                    <td colspan="6" class="rlabel">
                        System unable to price, please submit suggested price for approval.
                    </td>
                    <td class="total rlabel">
                        TBD
                    </td>
                    <td></td>
                </tr>

                <tr ng-if="order.Visibility.promoBenifit">
                    <td colspan="6" class="rlabel">
                        - {{order.Visibility.promoBenifit}}
                    </td>
                    <td class="total rlabel">
                        - {{order.Visibility.promoBenifitAmount | currency}}
                    </td>
                    <td></td>
                </tr>

                <tr id="freightTr">
                    <td colspan="6" class="rlabel">
                        <a  class="bg-orange" style="margin-right: 15px;    padding: 7px 15px;
                        border-radius: 2px;"  ng-if="order.HasSplitDelivery"
                        ui-sref="^.jobsplit({orderId: order.Id, jobId:  order.Jobs[0].Id})" > Split delivery details for Job {{order.Jobs[0].Id}} </a>
   
                        
                        Freight (Inc. handling)
                    </td>
                    <td class="rlabel">
                        {{order.PackDetail.Price | currency}}
                    </td>
                    <td></td>
                </tr>

                <tr ng-if="!(order.PriceOfJobs === '' || order.PriceOfJobs === null)">
                    <td colspan="6" class="rlabel"></td>
                    <td class="total rlabel">
                        {{order.PriceOfJobs | currency}}
                    </td>
                    <td></td>
                </tr>

                <tr id="">
                    <td colspan="6" class="rlabel">
                        GST( {{::order.GST}} %)
                    </td>
                    <td class="total rlabel">
                        {{::order.PriceOfGST | currency}}
                    </td>
                    <td></td>
                </tr>

                <tr>
                    <td colspan="6" class="price rlabel">Total (Inc. GST)</td>
                    <td colspan="1" class="total  rlabel price">{{::order.Price | currency}}</td>
                    <td></td>
                </tr>
            </tbody>
        </table>

        <!--<a ng-show="order.Visibility.addButton" class="btn btn-sm pull-right"
           ui-sref="cust.order.addnewjob({orderId:order.Id})">
            Add  new job
        </a>-->
    </div>

    <div class="col-md-12 rpad0" ng-show="needsConcent">
        <div class="well well-sm notice" style="font-size: smaller; padding: 20px;">
            <b style="color: red; font-weight: 800;"> I have </b>
            <ul>
                <li>checked these files to make sure they are PRESS READY, that means:</li>
                <ul>
                    <li>There are no low resolution images, RGB or Pantone colours.</li>
                    <li>All artwork is correctly sized with the required amounts of bleed.</li>
                    <li>All fonts have been converted to curves.</li>
                    <li>All transparency effects have been flattened.</li>
                </ul>
                <li>
                    Read, understood and accept LEP's
                    <a href="/images/pdfs/LEP_Terms_Conditions.pdf" target="_blank">
                        Terms &amp; Conditions
                    </a> of supply
                </li>
            </ul>
            <div class="" style="height: 30px; padding-left: 13px; color:red; font-weight:700; ">
                <label class="checkbox">
                    <input type="checkbox" ng-required="needsConcent" ng-model="artworkConfirmed" name="artworkConfirmed" />
                    I confirm the above
                </label>
            </div>
        </div>
    </div>

    <div class="row" ng-hide="pending > 0">
        <div class="col-md-12">

            <div class="form-actions pull-right">
                <a class="btn" ng-click="backToOrders()"><i class="glyphicon  glyphicon-chevron-left"></i> Back to orders</a>
                <!--<a ui-sref="cust.order.print" ui-sref-active="active-nav">Print</a> &nbsp;&nbsp;&nbsp;-->
                <!--<div style="text-align:right">
                </div>-->

                <div class="btn-group">
                    <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown"
                     aria-haspopup="true" aria-expanded="false">
                     <i class="glyphicon glyphicon-print"></i> Print Order
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" ng-click="getPdf()">Pdf format</a></li>
                        <li><a class="dropdown-item" ng-click="getPdf(1)">Html format</a></li>
                    </ul>
                </div>


                <a class="btn " ng-show="order.Visibility.addButton" ui-sref="cust.order.addnewjob({orderId:order.Id})">
                    <i class="glyphicon glyphicon-plus"></i>Add new job
                </a>
                <a class="btn " ng-show="order.Visibility.deleteButton" ng-click="deleteOrder()"><i class="glyphicon glyphicon-trash"></i> Delete</a>

                <a class="btn " ng-show="order.Visibility.reactivateButton" ng-click="orderCommand('Reactivate')">Reactivate</a>
                <a class="btn " ng-show="order.Visibility.withdrawButton" ng-click="orderWithdraw('Withdraw')">Withdraw</a>

                <!--<button class="btn " ng-click="getPdf()">
                    <span class="glyphicon glyphicon-print"></span> Print Order
                </button>-->

                <a class="btn " ng-show="order.Visibility.copyButton  " ng-click="copyOrder()">
                    Copy order
                </a>

                <button type="button" class="btn btn-default" ng-show="order.Visibility.saveButton && orderForm.artworkForm.$valid"
                        click-and-wait="saveOrder()">
                    <i class="glyphicon glyphicon-floppy-save"></i>Save
                </button>



                <button type="button" class="btn btn-success bold"
                        ng-show="order.Visibility.submitButton && orderForm.$valid && !deliveryDetailsChanged && !updatingCourier && !fileAttachmentPending"
                        ng-click="submitOrder()">
                    <i class="glyphicon glyphicon-send"></i>
                    Submit
                </button>

                <button class="btn btn-success bold" ng-show="order.Visibility.approveQuoteAndSubmitButton"
                        ng-click="approveQuoteAndSubmitOrder()">
                    <i class="glyphicon glyphicon-send"></i>
                    Approve and Submit Order
                </button>

                <button class="btn  " ng-show="order.Visibility.reorder" ng-click="reorderOrder()">
                    Reorder order
                </button>
            </div>
        </div>
    </div>

    <div class="alert alert-warning" ng-show="order.Visibility.projectedEstimatedDispatchDate">
        <img src="/images/truck.png" class="template-img1 axxx">   Estimated Dispatch Date: {{::order.Visibility.projectedEstimatedDispatchDate}} (if this was submitted now)
    </div>


    <div connotes order="order">
    </div>

    <br><br><br>

    <div ng-if="order.Visibility.ExtraFiles.length">
        <h4>Invoices &amp; Order related files</h4>
        <!--
        <button class="btn btn-sm " ng-click="uploadExtraFile(job.Id)">Upload extra files <i class="glyphicon glyphicon-upload"></i></button>
        -->
        <div class="row">
            <div
            ng-repeat="t in order.Visibility.ExtraFiles track by $index"
            class="col-md-4" >

                <div ng-init="srcx=('/api/Document/order/'+ order.Id +'/extrafiles/downloadT/' + t+'#toolbar=0' )" >
                        <a ng-click="downloadOrderExtraFile(order.Id, t)">

                        <i class="glyphicon glyphicon-cloud-download"></i>
                        {{t}} </a>
                    <embed src="{{ srcx | trustThisUrl }}" type="application/pdf"   class="res"
                    style="width: 332px;display: block;height: 444px;"
                    />
                </div>

            </div>

        </div>

    </div>


    <br><br><br>

    <div style="width: 5px; height: 5px; background: gray; border-radius: 5px;"
         title="
{{order.PackDetail.FGPackageStr}}

{{order.PackDetail.PMPackageStr}}
"></div>
    <!---->
</div>
