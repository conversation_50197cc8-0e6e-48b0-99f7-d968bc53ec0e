{
    "IMP": {
        "HotfolderPath": "\\\\ppp01\\HotFolderRoot\\MyLEP"
    },
    "TestBox": true,

    "ApplicationInsights": {
        "InstrumentationKey": ""
    },

    "Logging": {
        "IncludeScopes": false,
        "LogLevel": {
            "Default": "Warning",
            "LepCore": "Information",
            "LepCore.RequestLoggingMiddleware": "None",
            "Microsoft.AspNetCore.Mvc.Internal": "None",
            "Microsoft.AspNetCore.SignalR.Transports": "Debug",
            "Microsoft.AspNetCore.SignalR": "Debug",
            "Microsoft.AspNetCore.Http.Connections": "Debug"
        }
    },
    "jobboard": {
        "mes**gequeue": ".\\Private$\\leponline.jobboardqueue"
    },

    "HolidayMode": false,

    "CustomerLogoDirectory": "d:\\lepdata\\logo",
    "DataDirectory": "d:\\lepdata",
    "OldDataDirectory": "d:\\lepdata\\oldorders",
    "DataDirectoryPC": "d:\\lepdata",
    "DataDirectoryMac": "d:\\lepdata",

    "job.option.csv.folder": "C:\\LepSF\\au\\StaticAssets\\~\\app_data\\job",

    //"AbsolutePathURL": "http://lepold.localtest.me",

    "lepcrm.webservice.url": "http://crm.lep.icemedia.com.au/LEPDummyCRMService.asmx",
    "compdata.webservice.url": "http://lepfreight.localtest.me/iFreightChargeEnquiryService.svc",

    "AbsolutePathURL": "http://localhost:5000",
    "reports": {
        "LepQuote": "D:\\LEPDATA\\labels2\\t1.frx"
    },
    "email": {
        "sendMail": false,
        "server": "smtp.office365.com",
        "port": "587",
        "username": "<EMAIL>",
        "password": "11_Sandalwood"
    },
    "print.mes**gequeue": ".\\Private$\\leponline.mes**geprintqueue",
    "jobboard.mes**gequeue": ".\\Private$\\leponline.jobboardqueue",

    "Nhibernate": {
        // "Con": "Data Source=PC11532; user id=lepcore; password=********$%MpHU9; Initial Catalog=LEP_20190806;MultipleActiveResultSets=true;Max Pool Size=200;App=LepCore",
        // "Con": "Data Source=PC11532; user id=lepcore; password=********$%MpHU91; Initial Catalog=PRD_AU;MultipleActiveResultSets=true;Max Pool Size=200;App=LepCore"
        "Con": "Data Source=.;user id=**;password=**;Initial Catalog=PRD_AU;MultipleActiveResultSets=true;Max Pool Size=200;App=LepCore"
        //"Con": "Data Source=PC11532; user id=lepcore; password=********$%MpHU9; Initial Catalog=LEP_20190317;MultipleActiveResultSets=true;Max Pool Size=200;App=LepCore",
    },

    "SupplyMaster": {
        "Con": "Data Source=.;Initial Catalog=SupplyMaster;Integrated Security=True;MultipleActiveResultSets=true"
    },

    "FreightProvider": "SmartFreight", // or  CompData "current"

    "Labels": {
        "LogoLabel": "d:\\LEPDATA\\Labels2\\LogoLabel_102x73mm.frx",
        "PayMeLabel": "d:\\LEPDATA\\Labels2\\PayMeLabel.frx",
        "FillingLabel": "d:\\LEPDATA\\Labels2\\FillingLabel.frx",
        "SampleLabel": "d:\\LEPDATA\\Labels2\\SampleLabel.frx",
        "AddressA4Label": "d:\\LEPDATA\\Labels2\\AddressA4Label.frx",
        "PickupLabel": "d:\\LEPDATA\\Labels2\\PickupLabel.frx",
        "OneDeliveryOnly": "d:\\LEPDATA\\Labels2\\OneDeliveryOnly.frx",
        "AddressLabel": "d:\\LEPDATA\\Labels2\\SampleLabel.frx",
        "AddressLabelOther": "d:\\LEPDATA\\Labels2\\SampleLabel.frx",
        "CartonLabel": "D:\\lepdata\\labels2\\CartonLabel.frx"
    },


    "GhostScript": "C:\\GS\\gs952\\bin\\gswin64c.exe",
    "PdfTk": "C:\\GS\\gs952\\bin\\pdftk.exe",

    "AutomatedArtworkCheck": {
        "Enabled": false,
        "Timeout": "00:00:30",
        "Method": "DoPitStopViaCommandLine",
        //"Method":   "DoPitStopViaHotFolder0",

        "profiles": {
            "default": "D:\\LEPDATA\\CMYK_NEW LEP PROFILE.ppp",
            "default": "D:\\LEPDATA\\LEP FIX_2018_V2.ppp",
            "spotcolor": "D:\\LEPDATA\\LEP FIX_2018_V2 SPOT.ppp",
            "wideformat": "D:\\LEPDATA\\LEP Wide Format_V1.ppp"
        },

        "DoPitStopViaCommandLine": {
            "path": "D:\\Enfocus\\Enfocus PitStop Server 18\\PitStopServerCLI.exe",
            "mutator": "D:\\LEPDATA\\CMYK_NEW LEP PROFILE.ppp"
        },

        "DoPitStopViaHotFolder": {
            "input": "D:\\0000\\Input Folder",
            "output": "D:\\0000\\Output Folder"
        }
    },

    "Dispatchers": [
        {
            "Name": "FG-DISTRIB-02",
            "DispatchFacility": "FG"
        },
        {
            "Name": "FG-DISTRIB-03",
            "DispatchFacility": "FG"
        },
        {
            "Name": "PM-DISTRIB-01",
            "DispatchFacility": "PM"
        },
        {
            "Name": "PM-BINDERY-05",
            "DispatchFacility": "PM"
        }
    ],

    "SmartFreight": {
        "Url": {
            "SFOv1": "http://api-r1.smartfreight.com/api/soap/classic",
            "DeliveryOptions": "http://api-r1.smartfreight.com/api/soap/deliveryoptions"
        },

        "Senders": {
            "LEPQLD": {
                "Id": "NWM",
                "Key": "RqB0fJOapwP79AVDdzF5Pgg7fESwF4GxHQ"
            },
            "TESTQLD": {
                "Id": "RqB0fJOapwP79AVDdzF5Pgg7fESwF4GxHQ",
                "Key": ""
            },

            "LEPVIC": {
                "Id": "NGU",
                "Key": "JoIJ2bXndxCk0V3ZfkM6VxKuzv1qgEAiA0"
            },

            "TESTVIC": {
                "Id": "NGU",
                "Key": "JoIJ2bXndxCk0V3ZfkM6VxKuzv1qgEAiA0"
            }
        }
    }
}
