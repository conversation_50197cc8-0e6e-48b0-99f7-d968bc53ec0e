using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.IO;
using System.Net.Http;
using System.Security.Cryptography.X509Certificates;

namespace LepCore
{
	public class Program
	{
		public static HttpClient ClientWestPack { get; } = new HttpClient();
		public static string secretKey = @"SecretKeySecretKeySecretKeySecretKeySecretKeySecretKeySecretKeyS";
		public static X509Certificate2 cert;

		public static void Main()
		{
			Log.Logger = new LoggerConfiguration()
			   .Enrich.FromLogContext()
			   .Enrich.WithCorrelationId()
			   .WriteTo.Console()
			   .CreateLogger();


			try
			{
				//https://blogs.perficient.com/2016/04/28/tsl-1-2-and-net-support/
				//System.Net.ServicePointManager.SecurityProtocol |= SecurityProtocolType.Tls |  SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;

				var basePath = Directory.GetCurrentDirectory();
				var certPath = basePath + "\\lepcore.pfx";
				//cert = new X509Certificate2(certPath, "lepcore");

				var config = new ConfigurationBuilder()
								.AddJsonFile("appsettings.json")
								//.AddCommandLine(args)
								.Build();


				Log.Logger = new LoggerConfiguration()
					.ReadFrom.Configuration(config)
					.CreateLogger();

				var host = new WebHostBuilder()

								.UseDefaultServiceProvider((context, options) =>
								{
									options.ValidateScopes = true; 
									// https://docs.microsoft.com/en-us/aspnet/core/fundamentals/host/web-host?view=aspnetcore-2.1#scope-validation
								})
								.UseConfiguration(config)
								.UseKestrel()
								.UseUrls("http://*:5000;https://*:5001")
								.ConfigureKestrel((context, options) =>
								{
									options.Limits.MaxRequestBodySize = null;
								})

								.UseContentRoot(basePath)
								.UseIIS()
								.UseStartup<Startup>()
								.UseSerilog()
								.Build();
				host.Run();
			}
			catch (Exception ex)
			{
				Log.Fatal(ex, "Application start-up failed");
				Console.WriteLine(ex.Message);
			}
			finally
			{
				Log.CloseAndFlush();
			}
		}
	}
}
