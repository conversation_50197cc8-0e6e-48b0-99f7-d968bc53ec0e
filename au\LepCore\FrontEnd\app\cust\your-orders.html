﻿<div class="row">
	<!---->
	<div class="col-sm-12 col-md-3">
		<div class="row">
			<div class="col-sm-12">
				<h3 style="margin-top:0px;">Your orders</h3>
			</div>
		</div>
		<form ng-submit="search()">
            <div class="blue-box">
                <h5><b>Search</b></h5>
                <div class="input-group  form-group-sm">
                    <label class="control-label" for="size">by order #,&nbsp;&nbsp;job # or name</label>
                    <input type="text" class="form-control  ng-class:{ 'highlight-search': vm.OrderNr}" ng-model="vm.OrderNr"
                           placeholder=""
                           />
                </div>
                <!--<div class="form-group"><label class="control-label"><input name="checkbox" type="checkbox" /> Submitted Orders</label></div>
    <div class="form-group"><label class="control-label"><input name="checkbox" type="checkbox" /> Completed Orders</label></div>
    <div class="form-group"><label class="control-label"><input name="checkbox" type="checkbox" /> Incomplete Orders</label></div>
    <div class="form-group"><label class="control-label"><input name="checkbox" type="checkbox" /> Orders requiring attention</label></div>
    <div class="form-group"><label class="control-label"><input name="checkbox" type="checkbox" /> Archived Orders</label></div>-->

                <div style="height:10px"></div>

                <div class="form-group-sm ">
                    <label class="control-label" for="size">by product category</label>
                    <select ng-model="vm.JobTypes" id="job-types" class="form-control input ng-class:{ 'highlight-search': vm.JobTypes != null}"
                            ng-options="templateTree.Ids(tv) as t for (t, tv)  in templateTree.topdown.Category" ng-change="search()">
                        <option value="">Any</option>
                    </select>
                </div>

                <div style="height:10px"></div>

                <div ng-show="tabs.current=='You\'ve Started'">
                    <div class="form-group-sm ">
                        <label class="control-label">
                            <input type="checkbox" ng-model="vm.IsRejected" />
                            Artwork Rejected
                        </label>
                    </div>

                    <div class="form-group-sm ">
                        <label class="control-label">
                            <input type="checkbox" ng-model="vm.IsWaitingApproval" />
                             Needs approval
                        </label>
                    </div>
                </div>

                <div class="form-group-sm " ng-if="::globals.IsPrintPortalEnabled">
                    <label class="control-label">
                        <input type="checkbox" ng-model="vm.IsWhiteLabel" />
                        Only Web Orders
                    </label>

                    <label class="control-label">
                        <input type="checkbox" ng-model="vm.IsWLOrderPaidFor" />
                        Web Orders Placed
                    </label>
                </div>

                <div class="input-group  form-group-sm" ng-if="::globals.IsPrintPortalEnabled">
                    <label class="control-label" for="size">by customer</label>
                    <input type="text" class="form-control  ng-class:{ 'highlight-search': vm.OrderNr}" ng-model="vm.Customer"
                           placeholder="e.g. name, contact, email, phone..."
                            />




                </div>

                <!--<div class="form-group-sm ">
        <label class="control-label">
            <input type="checkbox" ng-model="vm.IsQuoteRequired" />
            Quoting...
        </label>
    </div>-->


                <div style="height:20px"></div>

                <div class="right">
                    <button type="reset" class="btn btn-default" ng-click="clear()"> <i class="glyphicon glyphicon-erase"></i> Clear</button>
                    <button type="submit" class="btn btn-default" ng-click="search()"><i class="glyphicon glyphicon-search"></i>  Search</button>
                </div>
                <div style="height:20px"></div>

            </div>
		</form>
	</div>
	<style>
		.tab-content {
			height: 0px;
		}
	</style>
	<div class="col-sm-12 col-md-9">

		<div class="row">
			<div ng-show="bOrderStatusUsed">
				<div class="col-md-12" ng-model="tabs.current" bs-tabs style="margin-left: -1px;">
					<div ng-repeat="tab in ::tabs.list" data-title="{{ ::tab.title }}" name="{{ ::tab.title }}" bs-pane>
					</div>
				</div>
			</div>

			<div class="col-md-12" ng-if="list.List.length">
				<div lep-cust-orders-list orders-list="list.List" open="true"></div>
			</div>

			<div class="col-md-12" ng-if="list.List.length == 0">
				{{msg}}
			</div>

			<div class="col-md-12" id="ff">
				{{list.Summary}}
				<div paging
					 page-count="3"
					 page="list.Page"
					 page-size="list.PageLength"
					 total="list.Total"
					 paging-action="goPage(page)"
					 scroll-top="false"
					 hide-if-empty="true"
					 show-prev-next="true"
					 show-first-last="true"
					 text-next-class="glyphicon glyphicon-chevron-right"
					 text-prev-class="glyphicon glyphicon-chevron-left"
					 text-first-class="glyphicon glyphicon-backward"
					 text-last-class="glyphicon glyphicon-forward">
				</div>
			</div>



		</div>


	</div>

</div>
