<link rel="stylesheet" href="http://cdnjs.cloudflare.com/ajax/libs/select2/3.4.5/select2.css">
<form>
	<div leppane="Offers search">

		<div class="row form-horizontal">
			<div class="col-md-6 ">
				<div class="row form-group form-group-sm"  >
					<label class="col-xs-2 control-label" for="customer">Customer</label>
					<div class="col-xs-10" >
						<ui-select  theme="bootstrap" 
						multiple sortable="true" close-on-select="false" 
						ng-model="filters.Customers" 
								   style="min-width: 300px; width: 100%; height: auto!important;" title="Choose a customer"
								   append-to-body="true">
							<ui-select-match placeholder="Select a customer..." allow-clear="true">
								{{$item.Name}}
							</ui-select-match>
							
							<ui-select-choices refresh="searchCust($select.search)" refresh-delay="2000"
											   repeat="p in customers | propsFilter: {Name: $select.search}">

								<div>
									<div>{{p.Name}}</div>
								</div>
							</ui-select-choices>
						</ui-select><br />
					</div>
				</div>

                <div class="row form-group form-group-sm">
                    <label class="col-xs-2 control-label" for="order-no">Promotion </label>
                    <div class="col-xs-10">

                        <ui-select ng-model="filters.Promotion" theme="bootstrap"
                                   style="min-width: 300px;" title="Choose a promo"
                                   append-to-body="true">
                            <ui-select-match placeholder="Select a promo..." allow-clear="true">
                                {{$select.selected.PromotionCode}} - {{$select.selected.Name}}
                            </ui-select-match>
                            <ui-select-choices repeat="p in promos | propsFilter: {Name: $select.search, PromotionCode: $select.search}">

                                <div>
                                    <div>{{p.PromotionCode}}</div>
                                    <div>{{p.Name}}</div>
                                </div>

                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>

					<div class="row form-group form-group-sm">
					<label class="col-xs-2 control-label"></label>
					<div class="col-xs-10">
						<button  ng-click="clear()">Clear</button>
						<button  ng-click="search()">Search</button>
					</div>
				</div>		
			</div>

			<div class="col-md-6 " ng-show="filters.Promotion.Id && filters.Customers.length">
				<!--
				<ol>
					<li>To work with a promotion, start typing out its code and then selecting it from the suggestions </li>

					<li ng-show="filters.Promotion.Id && offers.List.length>0">The offers list shows all the Offers from this promotion {{filters.Promotion.PromotionCode}}.</li>

					<li ng-show="filters.Promotion.Id && offers.List.length==0">The promotion {{filters.Promotion.PromotionCode}} has not been offered to any customer yet.</li>

					<li ng-show="filters.Promotion.Id">
						To Offer {{filters.Promotion.PromotionCode}} to a Customer, select a customer by typing out the Customer's name
					</li>
					<li ng-show="filters.Customers">and then click the  <b>Add Offer</b> button</li>
				</ol>
				-->

				<div class="form-group dateAbsolute">
                        <label class="control-label col-xs-4" for="textDateValidFrom">Starts from</label>
                        <div class="col-xs-4">
                            <div class="input-group">
                                <input class="form-control datetimepicker" type="text" id="textDateValidFrom"
                                 ng-model="vm.DateOffered" datetime-picker date-format="dd-MMM-yyyy" date-only >
                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>

                    </div>


                    <div class="form-group dateAbsolute">
                        <label class="control-label col-xs-4" for="textDateValidTo">
                            Finishes on
                        </label>
                        <div class="col-xs-4">

                            <div class="input-group">
                                <input class="form-control datetimepicker" type="text" id="textDateValidTo" ng-model="vm.DateOfferEnds" datetime-picker date-format="dd-MMM-yyyy"  date-only>
                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                    </div>

			  	<div class="form-group">
                    <label class="control-label col-xs-4" for="chkCanUseOnce">Allow reuse?</label>
                    <div class="col-xs-4" title="If this is ticked this promotion can only be used once, i.e. after first application the promo becomes unusable">
                        <input class="checkbox" id="chkCanUseOnce" type="checkbox" ng-model="vm.AllowReuse" ng-value="true">
                    </div>
                </div>

				<div class="row form-group form-group-sm">
					<label class="col-xs-4 control-label"></label>
					<div class="col-xs-4">
						<button type="submit" class="btn" ng-click="addOffer()"
								ng-show="filters.Promotion.Id && filters.Customers.length">
							<i class="glyphicon glyphicon-plus"></i> Add Offer
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</form>



<div leppane="Offers  ">
	<div class="row">
		<div class="col-xs-12">
			<table class="table table-striped table-hover" style="font-size: smaller">
				<thead>
					<tr>
						<th></th>
						<th><a ng-click="toggleSort('Id')">Promo</a></th>
						<th><a ng-click="toggleSort('Status')">Customer</a></th>

						<th class="nowrap"><a ng-click="toggleSort('SubmissionDate')">Offered Date</a></th>
						<th class="nowrap"><a ng-click="toggleSort('DispatchDate')">Offer ends Est</a></th>
						<th><a ng-click="toggleSort('c.Name')">Date Taken up</a></th>
						<th class="nowrap">Allow reuse</th>
						<th class="nowrap"> </th>
						<th> </th>

					</tr>
				</thead>
				<tbody>

					<tr ng-repeat="o in  offers.List" class="{{::o.Css}}">
						<td></td>
						<td class="nowrap">{{::o.Promotion.PromotionCode}} </td>
						<td class="nowrap">{{::o.Customer.Name}} </td>

						<td class="nowrap">
							<input type="date" ng-model="o.DateOffered" />
						</td>
						<td class="nowrap">
							<input type="date" ng-model="o.DateOfferEnds" />
						</td>
						<td class="nowrap">
							{{::o.DateTakenUp  | date:'dd/MMM/yyyy HH:mm'}}
							<!--
							<div ng-repeat="u in ::o.OrderNumberUsedOn">
								{{::u.On  | date:'dd/MMM/yyyy HH:mm'}} - O# {{::u.OrderId}}
							</div>
							-->
						</td>

						<td class="nowrap"> 
							<input class="checkbox" type="checkbox" ng-model="o.AllowReuse" >

						</td>
						
						<td class="nowrap">
						 	<a ng-click="updateOffer(o)">Save</a>
						</td>
						<td class="nowrap" ng-hide="o.DateTakenUp != null">
							<a ng-click="deleteOffer(o.Id)">Delete</a>
						</td>

						<td class="nowrap" title="created:  {{::o.DateCreated | date:'dd-MMM-yy HH:mm'}}  {{::o.CreatedBy}}
modified: {{::o.DateModified | date:'dd-MMM-yy HH:mm'}} {{::o.ModifiedBy}}
						">
							info
					
						</td>
					</tr>

				</tbody>
			</table>

			{{r.Summary}}
			<div paging
				 page="r.Page"
				 page-size="r.PageLength"
				 total="r.Total"
				 paging-action="goPage(page)"
				 scroll-top="false"
				 hide-if-empty="true"
				 show-prev-next="true"
				 show-first-last="true"
				 text-next-class="glyphicon glyphicon-chevron-right"
				 text-prev-class="glyphicon glyphicon-chevron-left"
				 text-first-class="glyphicon glyphicon-backward"
				 text-last-class="glyphicon glyphicon-forward">
			</div>


		</div>
	</div>
</div>
		<!--

<pre>{{filters | json }}</pre>-->
