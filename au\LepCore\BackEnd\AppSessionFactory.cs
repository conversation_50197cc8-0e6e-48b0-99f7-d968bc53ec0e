﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace LepCore
{
	/*
	public class AppSessionFactory
	{
		private readonly IServiceScopeFactory _serviceScopeFactory;
		private readonly IServiceProvider _serviceProvider;
		private readonly IConfigurationRoot _configurationRoot;
		public Configuration Configuration { get; }
		public ISessionFactory SessionFactory { get; }

		//Microsoft.Extensions.Logging.ILoggerFactory loggerFactory,, IHostingEnvironment _env
		public AppSessionFactory(IConfigurationRoot configurationRoot, IServiceProvider serviceProvider, IServiceScopeFactory serviceScopeFactory)
		{
			_serviceScopeFactory = serviceScopeFactory;
			_configurationRoot = configurationRoot;
			_serviceProvider = serviceProvider;
			//config.Configure();
			var config = ReadConfigFromCacheFileOrBuildIt();
			SessionFactory = config.BuildSessionFactory();

		}

		public ISession OpenSession()
		{
			//return SessionFactory.OpenSession(new DependencyInjectionInterceptor(_serviceProvider));

			//return SessionFactory.WithOptions().Interceptor(new DependencyInjectionInterceptor(_serviceProvider)).OpenSession();
			//return SessionFactory.OpenSession();
			//return SessionFactory.WithOptions().Interceptor().OpenSession();

			return SessionFactory.WithOptions().Interceptor(
				new DependencyInjectionInterceptor(_serviceScopeFactory.CreateScope().ServiceProvider)
				)
				.OpenSession();
			//return SessionFactory.OpenSession();
		}

		public ISession OpenAuditedSession()
		{
			return null;
			//return SessionFactory.WithOptions().Interceptor(new DependencyInjectionInterceptor(_serviceProvider)).OpenSession();
		}


		private Configuration ReadConfigFromCacheFileOrBuildIt()
		{

			if (LepGlobal.Instance.TestBox)
			{
			}
			NHibernateProfiler.Initialize();
			//NHibernateProfiler.Initialize();
			//NHibernate.NHibernateLogger.SetLoggersFactory(new NHibernateToMicrosoftLoggerFactory(loggerFactory));
			//NHibernateLogger.SetLoggersFactory(new NHibernate.Logging.Serilog.SerilogLoggerFactory());
			Configuration config;
			var nhCfgCache = new NHConfigurationFileCache(typeof(ICustomerUser).Assembly);
			var cachedCfg = nhCfgCache.LoadConfigurationFromFile();
			if (cachedCfg == null)
			{
				config = new Configuration();

				config.SetProperty(Dialect, typeof(NHibernate.Dialect.MsSql2005Dialect).AssemblyQualifiedName);
				config.SetProperty(ConnectionDriver, typeof(NHibernate.Driver.SqlClientDriver).AssemblyQualifiedName);
				config.SetProperty(Isolation, "ReadCommitted");
				config.SetProperty(PrepareSql, "true");
				config.SetProperty(CacheProvider, typeof(NHibernate.Caches.SysCache2.SysCacheProvider).AssemblyQualifiedName);
				config.SetProperty(UseSecondLevelCache, "true");
				config.SetProperty(UseQueryCache, "true");

				config.SetProperty(Hbm2ddlAuto, "none");
				config.SetProperty(Hbm2ddlKeyWords, "none");

				var connstr = _configurationRoot["Nhibernate:Con"];
				config.SetProperty(ConnectionString, connstr);
				config.SetProperty(CommandTimeout, "600");

				// call session context works for both HangFire and Web
				config.SetProperty(CurrentSessionContextClass, "call");

				config.SetProperty(DefaultBatchFetchSize, "128");
				config.SetProperty(BatchSize, "50");

				config.SetProperty(NHibernate.Cfg.Environment.ShowSql, "false");
				config.SetProperty(NHibernate.Cfg.Environment.FormatSql, "false");
				//nhConfigurationCache.SetProperty(Environment.CurrentSessionContextClass, typeof(UnitTestSessionContext).AssemblyQualifiedName);

				config.AddAssembly(typeof(ICustomerUser).Assembly);

				//config.AppendListeners(NHibernate.Event.ListenerType.PostUpdate, new[] { new AuditEventListener() });
				config.AppendListeners(NHibernate.Event.ListenerType.PostUpdate, new[] { new NhAuditEventListener() });
				//config.AppendListeners(NHibernate.Event.ListenerType.PostInsert, new[] { new AuditEventListener() });
				//nhconfig.AppendListeners(NHibernate.Event.ListenerType.Flush, new[] { new AuditEventListener() });

				////config.SetInterceptor(new NoUpdateInterceptor());
				foreach (NHibernate.Mapping.PersistentClass persistentClass in config.ClassMappings)
				{
					persistentClass.DynamicUpdate = true;
				}

				//config.Configure();
				nhCfgCache.SaveConfigurationToFile(config);
			}
			else
			{
				config = cachedCfg;
			}
			return config;
		}
	}
	*/
}
