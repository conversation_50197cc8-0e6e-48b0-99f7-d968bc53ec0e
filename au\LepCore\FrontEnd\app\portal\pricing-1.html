﻿<div>
    <div class="form-group" style="text-align: right">
        <div class="col-xs-12">
            <a class="btn btn-sm"
               ng-click="useFromTemplate()"> <i class="glyphicon glyphicon-import"></i> Use an existing template </a>
            <a class="btn btn-sm"
               ng-show="vm.PrintPortalSettings.CategoryMarkups.length > 0"
               ng-click="saveAsTemplate('CategoryRange', vm.PrintPortalSettings.CategoryMarkups)">Save these as a template  <i class="glyphicon glyphicon-export"></i> </a>
        </div>
    </div>

    <div class="form-group">
        <div class="col-xs-12">
            <label class="control-label col-xs-12" style="text-align:left">Category Markup (%)</label>
        </div>

        <div class="col-xs-12">
            <table class="table table-condensed stripped-cols">
                <tr>
                    <th></th>
                    <th> Template </th>
                    <th> Trim </th>
                    <!--<th> PrintType </th>-->

                    <th> Fold </th>
                    <th> Cello </th>
                    <th class="right"> Quantity from</th>
                    <th class="right"> Quantity upto</th>
                    <th class="right"> Mark up (%)</th>
                    <th></th>
                </tr>
                <tbody ui-sortable="sortableOptions" ng-model="vm.PrintPortalSettings.CategoryMarkups">
                    <tr ng-repeat="e in vm.PrintPortalSettings.CategoryMarkups track by $index">
                        <td></td>
                        <td>
                            {{e.Template.Name}}
                        </td>
                        <td>{{e.FinishedSize}}</td>
                        <!--<td>{{e.PrintType}}</td>-->
                        <td>{{e.Fold}} </td>
                        <td>{{e.Cello}} </td>
                        <td class="right">{{e.QtyMin}}</td>
                        <td class="right">{{e.QtyMax}}</td>
                        <td class="right">{{e.Markup}}</td>
                        <td class="pre">
                            <a ng-click="vm.PrintPortalSettings.CategoryMarkups.splice($index, 1)">
                                <i class=" glyphicon glyphicon-trash" style="font-size: 12pt"></i>
                            </a>
                            <i class="glyphicon glyphicon-resize-vertical" style="font-size: 12pt"
                               title="drag drop to reorder this list"></i>
                        </td>
                    </tr>
                </tbody>
                <tbody ng-form="newMarkupForm">
                    <tr>
                        <td>*</td>
                        <td>
                            <select style="max-width: 400px;" name="nTemplate" class="form-control" ng-required="true"
                                    ng-model="n.Template"
                                    ng-options="c as c.Name for c in availableTemplates"></select>
                        </td>
                        <td>
                            <select style="max-width: 350px;" ng-model="n.FinishedSize" name="nFinishedSize" class="form-control"
                                    ng-options="cc  as cc for cc in availableTrims"></select>
                        </td>
                        <!--<td>
                            <select style="max-width: 250px;" ng-model="n.PrintType" name="nPrintType" class="form-control"
                                    ng-options="pt as pt for pt in availablePrintTypes"></select>
                        </td>-->

                        <td>
                            <select ng-show="availableFolds.length" style="max-width: 250px;" class="form-control" ng-model="n.Fold"
                                    ng-options="cc  as cc  for cc in availableFolds"></select>
                        </td>
                        <td>
                            <select ng-show="availableCellos.length" style="max-width: 250px;" class="form-control" ng-model="n.Cello"
                                    ng-options="cc as cc for cc in availableCellos"></select>
                        </td>
                        <td><input size="5" type="number" ng-model="n.QtyMin" class="form-control pull-right" ng-required="true" style="max-width: 80px;  text-align:right; -webkit-appearance: none; "/></td>
                        <td><input size="5" type="number" ng-model="n.QtyMax" class="form-control pull-right" ng-required="true" style="max-width: 80px; text-align:right; -webkit-appearance: none; "/></td>
                        <td><input size="5" type="number" ng-model="n.Markup" class="form-control pull-right" ng-required="true" style="max-width: 80px; text-align:right; -webkit-appearance: none; "/></td>
                        <td colspan="1">
                            <button ng-click="addMarkup()" ng-disabled="newMarkupForm.$invalid">
                                <i class="glyphicon glyphicon-plus"></i>
                            </button>




                        </td>
                    </tr>

                </tbody>

            </table>
        </div>
    </div>




</div>
