do (window, angular, toastr)->
    app = angular.module('app')

    # used in both customer and staff print portal settings page
    app.directive 'lepWhiteLabelSetup', () ->
        restrict    : 'EA'
        scope       : {vm : '='}
        templateUrl : 'portal/setup.html'
        controller  : 'WhiteLabelSetupController'

    app.controller 'WhiteLabelSetupController', [
        '$scope', 'JobService', 'lepApi2', '$interval',
        ($scope, JobService, lepApi2, $interval) ->
            vm = $scope.vm # the customer object for which we are setting up 	WhiteLabel parameters

            $scope.$watch "vm.PrintPortalSettings.Version", (n,o) ->
                $scope.ppv = n

            # shows up in the DeniedTemplates box so user can select things he does not want
            $scope.allTemplatesName = []

            getTemplatesToShow = () ->
                JobService.getTemplates()
                .then (d) ->
                    lepApi2.get("joboptions/dcats/#{vm.Id}")
                    .then (omit) ->
                          d2 = _.reject d, (x) ->
                              omit.indexOf(x.Id) > -1
                          $scope.allTemplatesName = d2
            getTemplatesToShow()

            # construct the iframe include code so UI can show them
            baseSite = window.document.location.origin
            baseSite = 'https://printportal.cloud'
            
            $scope.baseSite2 = "#{baseSite}/wl/#{vm.Id}"
            #cd $scope.baseSite2
            wlInclude = ""
            if vm.IsPrintPortalEnabled
                wlInclude = """
                <iframe id="wl" src="#{baseSite}/wl/#{vm.Id}"
                    width="560" height="600" style="border:none;"></iframe>
                <script src="#{baseSite}/images/resize.txt"
                    type="text/javascript"></script>
                """

                lepApi2.get('Cust/Settings/ppc/matrix3').then (d) ->
                    $scope.d = d
                    $scope.d.matrix2 = {}
                    pps = $scope.vm.PrintPortalSettings.WhiteLabelAllowedProducts
                    r = {}
                    for t in d.templatesL
                        if !(r[t.Id]) then  r[t.Id] = { Sizes: [], Stocks: [] }

                    _.each pps, (x) ->
                        if r[x.T].Sizes.indexOf(x.P) == -1 then r[x.T].Sizes.push(x.P)
                        if r[x.T].Stocks.indexOf(x.S) == -1 then r[x.T].Stocks.push(x.S)
                    $scope.d.matrix2 = r


            $scope.updateVm = () ->
                a = []
                for t in  $scope.d.templatesL
                    x = $scope.d.matrix2[t.Id]
                    if !x then continue
                    for stk in x.Stocks
                        for ps in x.Sizes
                            a.push({T:t.Id, S:stk, P: ps})
                $scope.vm.PrintPortalSettings.WhiteLabelAllowedProducts = a
            
            $scope.wlInclude = wlInclude
            $scope.baseSite = baseSite


            $scope.toggleSizesUnderTemplate = (e,templateId) ->
                checked = e.target.checked
                if !$scope.d.matrix2[templateId] then  $scope.d.matrix2[templateId] = { Sizes: [], Stocks: [] }
                if checked
                    $scope.d.matrix2[templateId].Sizes = angular.copy($scope.d.matrix[templateId].Sizes)
                else
                    $scope.d.matrix2[templateId].Sizes = []

            $scope.toggleStocksUnderTemplate =  (e,templateId) ->
                checked = e.target.checked
                if !$scope.d.matrix2[templateId] then  $scope.d.matrix2[templateId] = { Sizes: [], Stocks: [] }
                if checked
                    $scope.d.matrix2[templateId].Stocks = angular.copy($scope.d.matrix[templateId].Stocks)
                else
                    $scope.d.matrix2[templateId].Stocks = []

            $scope.toggleAllUnderTemplate = (e,templateId) ->
                $scope.toggleSizesUnderTemplate(e,templateId)
                $scope.toggleStocksUnderTemplate(e,templateId)

            $scope.toggleAllTemplates = (e) ->
                checked = e.target.checked
                if checked
                    $scope.d.matrix2 = angular.copy($scope.d.matrix)
                else
                    $scope.d.matrix2 = {}

            $scope.filterOutDeniedTemplates = (t) -> 
                r = !(t.Id in $scope.vm.PrintPortalSettings.DeniedTemplates) and !(t.Id in $scope.$root.globals.DeniedTemplates)
                r

            return @
    ]

    # used in both customer and staff print portal settings page
    app.directive 'lepWhiteLabelSetupPricing1', () ->
        restrict    : 'EA'
        scope       : { vm: '=' }
        templateUrl : 'portal/pricing-1.html'
        controller  : [
            '$scope', 'JobService', 'lepApi2', 'ngDialog',
            ($scope,   JobService,   lepApi2,   ngDialog) ->
                vm = $scope.vm # the customer object for which we are setting up 	WhiteLabel parameters
                vm.PrintPortalSettings.DeniedTemplates = vm.PrintPortalSettings.DeniedTemplates || []
                vm.PrintPortalSettings.CategoryMarkups = vm.PrintPortalSettings.CategoryMarkups || []
                dt = vm.PrintPortalSettings.DeniedTemplates

                #lepApi2.downloadJson(vm.PrintPortalSettings.CategoryMarkups, "CategoryMarkups-#{vm.Id}");
                $scope.n = { Template: { "Id": null}, FinishedSize: null, PrintType: null}  # blank markup row

                $scope.availableTrims = [] # gets filled in after category is selected
                $scope.availableTemplates = angular.copy($scope.ct)

                getTemplatesToShow1 = () ->
                    JobService.getTemplates()
                    .then (d) ->
                        lepApi2.get("joboptions/dcats/#{vm.Id}")
                        .then (omitFC) ->
                            d2 = _.reject d, (x) ->
                                     omitFC.indexOf(x.Id) > -1

                            d3 = _.reject d2, (x) ->
                                     dt.indexOf(x.Id) > -1
                            $scope.availableTemplates = d3
                getTemplatesToShow1()

                $scope.$watch "vm.PrintPortalSettings.DeniedTemplates", (dtx) ->
                    getTemplatesToShow1()
                ,true

                # get folds and other
                $scope.$watchGroup ['n.Template.Id', 'n.FinishedSize', 'n.PrintType'], (v)->
                    if !v[0] then return
                    url = "joboptions/portal/OptionsCombined?templateId=#{v[0]}"
                    url += "&paperSizeName=#{v[1]}"  if v[1] and v[1] != 'Any'
                    url += "&printType=#{v[2][0]}"   if v[2] and v[2] != 'Any'
                    lepApi2.get(url, null, true).then (r) ->
                        angular.extend($scope, r)
                    return

                # add a new markup to this customers CategoryMarkups array
                $scope.addMarkup = () ->
                    xx = angular.copy($scope.n)
                    vm.PrintPortalSettings.CategoryMarkups = vm.PrintPortalSettings.CategoryMarkups || []
                    vm.PrintPortalSettings.CategoryMarkups.push(xx)
                    yy = _.orderBy(vm.PrintPortalSettings.CategoryMarkups, ['Template.Name','FinishedSize','PrintType','Fold','Cello','QtyMin'])
                    vm.PrintPortalSettings.CategoryMarkups = yy
                    $scope.n = {}
                    $scope.newMarkupForm.$setPristine()

                $scope.deleteFav = (n)->
                    delete $scope.favs[n]
                
                favsUrl =  "cust/settings/pp/favT/Cm"

                # obtain favs and open a dialog with list of favs to display
                $scope.useFromTemplate = () ->
                    lepApi2.get favsUrl
                    .then (favs)->
                        l1 = Object.keys(favs).length
                        $scope.favs  = favs
                        favs2 = angular.copy(favs)
                        dialog =  ngDialog.open  { template: 'portal/choose-favourite.html', scope: $scope}
                        dialog.closePromise.then (r) ->
                            # if favs length differ then update the favs
                            l2 = Object.keys(favs).length
                            if l1 != l2 then lepApi2.post favsUrl, favs

                            name = r.value
                            if !name or name[0] == '$' then return

                            # copy from the favs
                            list = angular.copy(favs[name])
                            $scope.vm.PrintPortalSettings.CategoryMarkups = list
                        return
                    return

                $scope.saveAsTemplate = (field, object) ->
                    bootbox.prompt
                        title: "Enter a name for these category range markups!"
                        centerVertical: true
                        value:  ""
                        closeButton: false
                        callback: (name) ->
                            if !name then return
                            lepApi2.get favsUrl 
                            .then (favs)->
                                favs[name] = angular.copy(vm.PrintPortalSettings.CategoryMarkups)
                                lepApi2.post favsUrl, favs
                            return
                return @
        ]


    # used in Customers settings in both customer and staff settings page
    app.directive 'lepWhiteLabelSetupPricing2', () ->
        restrict   : 'EA'
        scope      : {vm: '=' }
        templateUrl: 'portal/pricing-2.html'
        controller : [
            '$scope', 'lepApi2','ngDialog',
            ($scope,   lepApi2,  ngDialog ) ->
                vm = $scope.vm
                vm.PrintPortalSettings.PriceRangeMarkups = vm.PrintPortalSettings.PriceRangeMarkups || []
                $scope.n = {} # blank markup row

                # add a new markup to this customers PriceRangeMarkups array
                $scope.addMarkup = () ->
                    xx = angular.copy($scope.n)
                    vm.PrintPortalSettings.PriceRangeMarkups.push(xx)
                    vm.PrintPortalSettings.PriceRangeMarkups = _.orderBy(vm.PrintPortalSettings.PriceRangeMarkups, ['PriceFrom'])
                    $scope.n = {}
                    $scope.newMarkupForm.$setPristine()

                $scope.deleteFav = (n)->
                    delete $scope.favs[n]

                favsUrl =  "cust/settings/pp/favT/Pr"

                # obtain favs and open a dialog with list of favs to display
                $scope.useFromTemplate = () ->
                    lepApi2.get favsUrl
                    .then (favs)->
                        l1 = Object.keys(favs).length
                        $scope.favs  = favs
                        dialog =  ngDialog.open { template: 'portal/choose-favourite.html', scope: $scope}    
                        dialog.closePromise.then (r) ->
                            # if favs length differ then update the favs
                            l2 = Object.keys(favs).length
                            if l1 != l2 then lepApi2.post favsUrl, favs

                            name = r.value
                            if !name or name[0] == '$' then return

                            # copy from the favs
                            list = angular.copy(favs[name])
                            $scope.vm.PrintPortalSettings.PriceRangeMarkups = list


                $scope.saveAsTemplate = (field, object) ->
                    bootbox.prompt
                        title: "Enter a name for these price range markups!"
                        centerVertical: true
                        value:  ""
                        closeButton: false
                        callback: (name) ->
                            if !name then return
                            lepApi2.get favsUrl
                            .then (favs)->
                                favs[name] = angular.copy(vm.PrintPortalSettings.PriceRangeMarkups)
                                lepApi2.post favsUrl, favs
                            return
                return @
        ]


