﻿<div ng-form="totalJobForm" id="pricecalc">
    <div leppane="Online Pricer">
        <div class="row">
            <div class="col-md-6">
                <form name="jobDetailsForm">
                    <div lep-job-details job="job" class="row" price-calc="true" dcats="dcats"></div>
                </form>













                <div ng-repeat="(k,v) in EDRs" class="form-control-static success col-xs-offset-3 number">
                    EDR discount of {{v | currency}} will reduce price to
                    {{( p - v ) | currency}}
                </div>
            </div>

            <div class="col-md-6">
                <div class="row form-horizontal">

                    <div class="form-group ">
                        <label class="col-xs-4 control-label" for="BackColour">Country</label>
                        <div class="col-xs-8">
                            <select name="BackColour" class="form-control input" ng-model="job.DeliveryAddress.Country"
                                ng-required>

                                <option value="">-- Select --</option>
                                <option value="AU" selected="selected">Australia</option>
                                <option value="NZ">New Zealand</option>

                            </select>
                        </div>
                    </div>

                    <div class="form-group ">
                        <label class="col-xs-4 control-label" for="BackColour">Postcode</label>
                        <div class="col-xs-8">
                            <angucomplete-alt type="text" id="postcode" input-class="form-control input"
                                placeholder="Search postcode" pause="400" selected-object="address.Postcode"
                                remote-url="/api/postcode/getsuburbs?postcode=" remote-url-data-field="Data"
                                title-field="PostCode" description-field="Suburb" ng-required />
                        </div>
                    </div>

                    <div class="form-group " ng-show="job.DeliveryAddress.City">
                        <label class="col-xs-4 control-label" for="BackColour">Suburb, State </label>
                        <div class="col-xs-8 form-control-static">
                            {{job.DeliveryAddress.City}}, {{job.DeliveryAddress.State}}
                        </div>
                    </div>
                </div>


                <div class="form-group " ng-show="courierRates.length > 0">
                    <table class="table">
                        <tr>
                            <td colspan="3">Freight price</td>
                        </tr>

                        <tr ng-repeat="r in courierRates">
                            <td>
                                {{r.CarrierName}}
                            </td>

                            <td>
                                {{r.ServiceName}}
                            </td>

                            <td>{{r.CustomerCharge | currency}}</td>
                        </tr>


                    </table>
                </div>

            </div>


        </div>
    </div>

    <div class="col-md-6" ng-if="globals.IsStaff">
        <pre style="background-color: inherit; border: none; font-size: smaller">
{{FGPackageStr}}
{{PMPackageStr}}
</pre>
    </div>

    <div id="crpdiv" ng-if="crp.visible" class="ng-class:{'red': !job.CustomerRequestedPrice}">
        <div class="talk-bubble tri-right btm-right-in">
            <div class="info " style="margin:5px">
                Don’t know the price you wish to pay? put $1 and we will Quote.
            </div>

            <div class="">
                <span class="control-label pulse" style="margin:5px; display: inline-block;">Price I want to pay</span>
                <input id="crp" name="crp" type="number" ng-model="job.CustomerRequestedPrice" class="form-control form-control-sm" style="width: 40%; display: inline-block; border-radius: 4px;" length="6" inputmode="numeric" ng-required="{{true}}" ng-min="1" />                *
            </div>

            <div style="text-align: right;margin-top: 10px;">
                Click <span style="padding: 5px;margin: 2px 4px;">
                    <i class="glyphicon {{crp.btnIcon}}"></i> {{crp.btnText}}</span>

            </div>
        </div>

    </div>



    <div class="row">
        <div class="col-xs-12 right">
            <div ng-show="job && job.Price > 0  && $root.globals.IsCustomer == true">
                <button class="btn btn-success bold " ng-click="convertToJob()"> Okay I want this! Create an
                    order</button>
            </div>
            <div ng-show="job && job.Price == 0  &&  job.QuoteNeedApprove && $root.globals.IsCustomer == true">
                <button class="btn btn-success bold " ng-click="convertToQuote()" ng-disabled="totalJobForm.$invalid">
                    Continue</button>
            </div>
        </div>
    </div>

</div>