﻿<div leppane="Search">
    <form>
        <div class="row">
            <div class="col-sm-6 form-horizontal">
                <div class="form-group form-group-sm">
                    <label class="col-sm-3 control-label" for="Staff">Username</label>
                    <div class="col-sm-6">
                        <input id="Staff" type="text" class="form-control input" ng-model="vm.Username"
                               placeholder="" />
                    </div>
                </div>

                <div class="form-group form-group-sm">
                    <label class="col-sm-3 control-label" for="order-no">FirstName</label>
                    <div class="col-sm-6">
                        <input id="order-no" type="text" class="form-control input" ng-model="vm.FirstName" />
                    </div>
                </div>

                <div class="form-group form-group-sm">
                    <label class="col-sm-3 control-label" for="Job-num">LastName</label>
                    <div class="col-sm-6">
                        <input id="Job-num" type="text" class="form-control input" ng-model="vm.LastName" />
                    </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group form-group-sm">
                    <span class="help-block">Only show Staffs that are</span>
                    <div class="row">
                        <div ng-repeat="(k,v) in Role1" >
                             
                                <label class="col-sm-6 control-label"><input type="checkbox" checkbox-model="vm.Roles" checkbox-value="k*1" multiple> {{v}}</label>
                          
                        </div>
                    </div>
                </div>


            </div>
        </div>

        <div class="row">
            <div class="col-sm-12 ">

            <div class="pull-left"><a class="btn btn-default" ui-sref="staff.setup.staff-view({id: 0})">Add new Staff</a></div>

            <div class="pull-right">
                <button type="reset" class="btn btn-default" ng-click="clear()"> <i class="glyphicon glyphicon-erase"></i>  Clear</button>
                <button type="submit" class="btn btn-default" ng-click="search()"><i class="glyphicon glyphicon-search" ></i> Search</button></div>
            </div>
        </div>
    </form>
</div>




<div class="row">
    <div class="col-sm-12">

        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th><a ng-click="toggleSort('Username')">Username</a></th>
                    <th><a ng-click="toggleSort('LastName')">Name</a></th>
                    <th><a ng-click="toggleSort('Email')">Email</a></th>
                    <th><a ng-click="toggleSort('Mobile')">Contact</a></th>
                    <th><a ng-click="toggleSort('Role')">Role</a></th>

                    <th>Action</th>
                </tr>
            </thead>
            <tbody>

                <tr ng-animate="'animate'" ng-repeat="o in r.List | filter:vm.Staff" class="animate">
                    <td>{{::o.Username}} </td>
                    <td>{{::o.Name }}</td>
                    <td>{{::o.Email}}</td>
                    <td>{{::o.Contact}}</td>
                    <td>{{::enums.ValueDesc.Role[o.Role]}}</td>
                    <td>
                        <a ui-sref="staff.setup.staff-view({id: o.Id})">View Staff member</a>
                    </td>
                </tr>
            </tbody>
        </table>
		{{r.Summary}}
		<div paging
			 page="r.Page"
			 page-size="r.PageLength"
			 total="r.Total"
			 paging-action="goPage(page)"
			 scroll-top="false"
			 hide-if-empty="true"
			 show-prev-next="true"
			 show-first-last="true"
			 text-next-class="glyphicon glyphicon-chevron-right"
			 text-prev-class="glyphicon glyphicon-chevron-left"
			 text-first-class="glyphicon glyphicon-backward"
			 text-last-class="glyphicon glyphicon-forward">
		</div>
    </div>
</div>


