﻿<form name="form">
	<div leppane="Contact details">
		<div class="row form-horizontal">

			<div class="col-sm-6">
				<div class="form-group ">
					<label class="col-sm-3 control-label" for="FirstName">First name</label>
					<div class="col-sm-6">
						<input name="FirstName" type="text" ng-model="vm.FirstName" class="form-control input" ng-required="true">
						<div ng-if="form.FirstName.$dirty">
							<div ng-show="form.FirstName.$error.required">This is required</div>
						</div>
					</div>
				</div>
				<div class="form-group ">
					<label class="col-sm-3 control-label" for="LastName">Last name</label>
					<div class="col-sm-6">
						<input name="LastName" type="text" ng-model="vm.LastName" class="form-control input" ng-required="true">
						<div ng-if="form.LastName.$dirty">
							<div ng-show="form.LastName.$error.required">This is required</div>
						</div>
					</div>
				</div>
			</div>

			<div class="col-sm-6">
				<div class="form-group ">
					<label class="control-label col-sm-3" for="phone">Phone</label>
					<div class="col-sm-6">
						<div class="input-group">
							<span class="input-group-addon"><i class="glyphicon glyphicon-phone-alt"></i></span>

							<div class="col-sm-3 lpad0 rpad0">
								<input class="form-control" type="text" name="PhoneAreaCode" ng-model="vm.AreaCode" size="2" ng-pattern="/^[0-9]{2}$/" />
							</div>
							<div class="col-sm-9 lpad0 rpad0">
								<input class="form-control" type="text" name="PhoneNumber" ng-model="vm.Phone" size="10" ng-pattern="/^[0-9]{8}$/" />
							</div>
						</div>
						<div>
							<div ng-show="form.PhoneAreaCode.$error.required">area code is required</div>
							<div ng-show="form.PhoneAreaCode.$error.pattern">area code can only be 2 digits </div>
						</div>

						<div>
							<div ng-show="form.PhoneAreaCode.$error.required">Phone number is required</div>
							<div ng-show="form.PhoneAreaCode.$error.pattern">Phone number  can only be 8 digits </div>
						</div>
					</div>
				</div>


				<div class="form-group">
					<label class="control-label  col-sm-3" for="Mobile">Mobile</label>
					<div class="col-sm-6">
						<div class="input-group">
							<span class="input-group-addon"><i class="glyphicon   glyphicon-phone"></i></span>
							<input class="form-control" type="text" ng-model="vm.Mobile" name="Mobile" />
						</div>

					</div>
				</div>

				<div class="form-group ">
					<label class="col-sm-3 control-label" for="Email">Email</label>
					<div class="col-sm-6">
						<div class="input-group">
							<span class="input-group-addon"><i class="glyphicon   glyphicon-envelope"></i></span>
							<input type="text" ng-model="vm.Email" class="form-control input" name="Email" ng-required="true">
						</div>
						<div ng-shows="form.Email.$error">
							<div ng-show="form.Email.$error.required">Email is required</div>
							<div ng-show="form.Email.$error.pattern">Not a valid email </div>
						</div>

					</div>
				</div>

			</div>
		</div>
	</div>

	<div ng-if="globals.IsA || vm.Id ==  globals.User.Id">
		<div leppane="Login">

			<div class="row form-horizontal">
				<div class="col-sm-6">
					<div class="form-group ">
						<label class="col-sm-3 control-label" for="Username">User name</label>

						<div class="col-sm-6">
							<input name="Username" type="text" ng-model="vm.Username" ng-disabled="!vm || vm.Id != 0"
								   class="form-control input" ng-required="vm.Id == 0" />
						</div>
						<div ng-if="form.Username.$dirty">
							<div ng-show="form.Username.$error.required">This is required</div>
						</div>
					</div>

					<div class="form-group form-group-sm">
						<label class="control-label col-sm-3">Password</label>
						<div class="col-sm-6">
							<input class="form-control" type="text" ng-model="vm.Password" />
						</div>
					</div>
					<div class="form-group form-group-sm">
						<label class="control-label col-sm-3">Confirm Password</label>
						<div class="col-sm-6">
							<input class="form-control" type="text" ng-model="vm.Password1" />
						</div>
					</div>


				</div>

				<div class="col-sm-6" ng-if="globals.IsA">
					<div class="form-group ">
						<label class="col-sm-3 control-label">Role</label>
						<div class="col-sm-6">
							<select name="roleDropDown" id="roleDropDown" tabindex="10" class="form-control input"
									ng-model="vm.Role" ng-integer
									ng-options="k*1 as v for (k,v) in Role1">
								<option value="">-- Select --</option>
							</select>
						</div>
					</div>
					<div class="form-group ">
						<label class="col-sm-3 control-label" for="">Enabled</label>
						<div class="col-sm-6 ">
							<input type="checkbox" class="checkbox" ng-model="vm.IsEnabled">
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>



	<div class="row">
		<div class="col-sm-12">
			<form class="form-horizontal col-sm-12">
				<div class="row">
					<div class="form-actions pull-right">
						<a class="btn" ui-sref="staff.setup.staff-list"><i class="glyphicon glyphicon-chevron-left"></i> Back</a>

						<button class="btn" ng-show="globals.IsA || vm.Id ==  globals.User.Id" ng-click="resetPassword()">
							<span ng-if="vm.Password  && vm.Password == vm.Password1">Set password </span>
							<span ng-if="!vm.Password || vm.Password != vm.Password1">Reset password</span>
						</button>


						<button type="submit" class="btn" ng-click="delete()" ng-show="globals.IsA"><i class="glyphicon glyphicon-trash"></i>  Delete</button>
						<button type="submit" class="btn" ng-click="save()" ng-show="globals.IsA || vm.Id ==  globals.User.Id">  <i class="glyphicon glyphicon-floppy-save"></i> Save </button>
						<!--<button type="submit" class="btn" ng-click="patch()">Patch Staff</button>-->
					</div>
				</div>
			</form>
		</div>
	</div>

</form>
