﻿appStaff = angular.module('app.staff')

appStaff.controller 'LogoLabelPrinter1Controller', [
    '$scope','lepApi2', '$location','enums', '$stateParams','Utils', '$http', '$state', 'JobService', 'OrderService', '$localStorage', 'ngDialog', '$q', '$window','dispatchers'
    ($scope,   lepApi2,   $location,  enums,  $stateParams,  Utils,   $http,   $state,   JobService,   OrderService, $localStorage, ngDialog, $q, $window, dispatchers ) ->

        $scope.edtSearch = ''

        jobId = 0
        jobName = ""
        $scope.jobId = jobId
        $scope.jobName = jobName
        $scope.jobSendSample = false

        $scope.qtyPerCarton = 0


        order = {}
        $scope.order     = order

        job = null
        $scope.job = job

        $scope.scan = (jobId) ->
            lepApi2.get("Orders/job/#{jobId}/logolabel").then (j)->
                customerId = j.CustomerId
                $scope.job = j
                $scope.job.Copies = j.CountOfPackages || 1
                $scope.job.Logo = "/api/staff/customers/#{customerId}/logo?r=" + (+new Date())

        if $stateParams.jobId
            $scope.edtSearch = $stateParams.jobId
            $scope.scan($stateParams.jobId)
            e = angular.element('#qtyPerCarton')
            e.focus()
            e.select()

        focusJobScanInput = () ->
          e = angular.element('#edtSearch')
          e.focus()
          e.select()

        $scope.$watch 'edtSearch', (n,o) ->
            if !n then return
            if n.length < 6 then return
            if n[0] is 'J' or n[0] is 'j' then n = n.substring(1)
            jobId = n
            $scope.scan(n)

        $scope.print = () ->
            currDisp = _.findKey(dispatchers, (x)-> x.Facility == $scope.job.Facility).toString()
            printerName = dispatchers[currDisp].Printers.LogoLabel
            url =  "orders/Order/DispatcherPrint"
            printReq =
                label       : 'LogoLabel'
                printerName : printerName
                orderId     : $scope.job.OrderId
                jobId       : $scope.job.JobId
                copies      : $scope.job.Copies
                courier     : ''

            lepApi2.post(url, printReq).then (o) ->
                toastr.info  "LogoLabel printed on #{printerName}"
        return
    ]

