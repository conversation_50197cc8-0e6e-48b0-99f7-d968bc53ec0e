do (window, angular, toastr)->
    appCore = angular.module('app.core')

    appCore.directive 'lepContactListDetails', () ->
        restrict    : 'EA'
        scope       : { vm : '=contacts', disabled: '=?disabled', receiveMarketingEmails: '='}
        templateUrl : 'common/directives/contact-list.component.html'
        controller  : 'ContactDetailsDirectiveController'


    appCore.controller 'ContactDetailsDirectiveController', [
        '$scope', ($scope) ->
            $scope.disabled  = $scope.disabled || false

    ]


    appCore.directive 'lepContactListDetailsSm', () ->
        restrict    : 'EA'
        scope       : { vm : '=contacts', disabled: '=?disabled', receiveMarketingEmails: '='}
        templateUrl : 'common/directives/contact-list.component-sm.html'
        controller  : 'ContactDetailsDirectiveController'

