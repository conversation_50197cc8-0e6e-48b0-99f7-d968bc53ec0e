<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="SCOPE_TYPE" value="3" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c903bbe5-ff42-4c2e-8fe6-395b5fd4e72d" name="Default" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CreatePatchCommitExecutor">
    <option name="PATCH_PATH" value="" />
  </component>
  <component name="FavoritesManager">
    <favorites_list name="app" />
  </component>
  <component name="ProjectId" id="1PwQbtdHjjmYLKk3SZjzy6CiCHf" />
  <component name="PropertiesComponent">
    <property name="HbShouldOpenHtmlAsHb" value="" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="editor.config.accepted" value="true" />
    <property name="js-jscs-nodeInterpreter" value="C:\Program Files\nodejs\node.exe" />
    <property name="js.eslint.eslintPackage" value="" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/portal" />
    <property name="nodejs_interpreter_path" value="C:/Program Files/nodejs/node" />
    <property name="settings.editor.selected.configurable" value="preferences.lookFeel" />
  </component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Lep\au\LepCore\app\portal" />
      <recent name="C:\LepCoreFreight\au\LepCore\app\staff\orders" />
      <recent name="C:\LepCoreFreight\au\LepCore\app\common\directives" />
      <recent name="C:\LepCoreFreight\au\LepCore\app\staff\jobboards" />
      <recent name="C:\LepCoreFreight\au\LepCore\app\staff\Runs" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager">
    <configuration default="true" type="js.build_tools.gulp">
      <node-interpreter>project</node-interpreter>
      <node-options />
      <gulpfile />
      <tasks />
      <arguments />
      <envs />
      <method v="2" />
    </configuration>
  </component>
  <component name="StructuralSearchPlugin">
    <replaceConfiguration name="user defined" created="1484617656672" text="'" recursive="false" caseInsensitive="true" type="XML" reformatAccordingToStyle="true" shortenFQN="true" useStaticImport="true" replacement="&quot;" history="1" />
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c903bbe5-ff42-4c2e-8fe6-395b5fd4e72d" name="Default" comment="" />
      <created>1471912961441</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1471912961441</updated>
      <workItem from="1471912963055" duration="1824000" />
      <workItem from="1476278344418" duration="759000" />
      <workItem from="1483665967443" duration="1963000" />
      <workItem from="1483668030710" duration="1899000" />
      <workItem from="1483682326223" duration="872000" />
      <workItem from="1484525101138" duration="1198000" />
      <workItem from="1484616437472" duration="847000" />
      <workItem from="1484617566539" duration="397000" />
      <workItem from="1494224208882" duration="3000" />
      <workItem from="1566782757834" duration="1185000" />
      <workItem from="1568337236647" duration="7664000" />
      <workItem from="1568608158145" duration="1447000" />
      <workItem from="1568777499801" duration="3035000" />
      <workItem from="1568844032601" duration="2878000" />
      <workItem from="1568858490673" duration="5072000" />
      <workItem from="1570660921282" duration="717000" />
      <workItem from="1574730231515" duration="780000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="Vcs.Log.UiProperties">
    <option name="RECENTLY_FILTERED_USER_GROUPS">
      <collection />
    </option>
    <option name="RECENTLY_FILTERED_BRANCH_GROUPS">
      <collection />
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <ignored-roots>
      <path value="$PROJECT_DIR$/../../.." />
    </ignored-roots>
  </component>
</project>