﻿<div class="row">
	<div class="col-md-2">
		<div lep-rewards cid="$root.globals.User.Id" vertical="true"></div>
	</div>

	<div class="col-md-7 ">
		<h3>Select a product</h3>
		<div>
			<div class="row" ng-init="flipped={}">
				<div ng-repeat="(t, tv) in templateTree.topdown.Category" class="col-md-3 col-sm-4 col-xs-6 col-lg-2"
					ng-init="flipped[t]  = false">

					<div class="template-card" ng-mouseenter="flipped[t] = true" ng-mouseleave="flipped[t]  = false"
						ng-click="chooseTemplate2(t)">
						<div class="flipper" ng-class="{'flipped': flipped[t] }">
							<div class="front" ng-click="chooseTemplate2(t)">
								<!--<span class="tick glyphicon glyphicon-star-empty"></span>-->
								<img ng-src="{{ templateTree.image()(t)}}" alt="" />
							</div>



							<div class="bottom-txt">
								<a class="name" ng-click="chooseTemplate2(t)">
									{{::t}}
								</a><button class="btn btn-default" ng-click="chooseTemplate2(t)">Start Order</button>
								<ul ng-init="list = templateTree.links()(t)">
									<li ng-repeat="l in list">
										<a target="_blank" href="{{::l.href}}">{{::l.link}}</a>
									</li>
								</ul>

							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="col-md-3">
		<div ng-include="'/images/EDMs/latest/latestEDM.html'">
		</div>
	</div>
</div>