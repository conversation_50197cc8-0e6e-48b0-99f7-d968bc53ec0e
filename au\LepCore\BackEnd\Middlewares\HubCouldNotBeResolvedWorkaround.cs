namespace LepCore
{
	//public class HubCouldNotBeResolvedWorkaround : IAssemblyLocator
	//{
	//	private static readonly string AssemblyRoot = typeof(Hub).GetTypeInfo().Assembly.GetName().Name;
	//	private readonly Assembly _entryAssembly;
	//	private readonly DependencyContext _dependencyContext;

	//	public HubCouldNotBeResolvedWorkaround (IHostingEnvironment environment)
	//	{
	//		_entryAssembly = Assembly.Load(new AssemblyName(environment.ApplicationName));
	//		_dependencyContext = DependencyContext.Load(_entryAssembly);
	//	}


	//	private bool IsCandidateLibrary (RuntimeLibrary library)
	//	{
	//		return library.Dependencies.Any(dependency => string.Equals(AssemblyRoot, dependency.Name, StringComparison.Ordinal));
	//	}

	//	IList<Assembly> IAssemblyLocator.GetAssemblies ()
	//	{
	//		if (_dependencyContext == null) {
	//			return new[] { _entryAssembly };// Use the entry assembly as the sole candidate.
	//		}

	//		return Enumerable.SelectMany<RuntimeLibrary, AssemblyName>(_dependencyContext
	//				.RuntimeLibraries
	//				.Where(IsCandidateLibrary), l => DependencyContextExtensions.GetDefaultAssemblyNames(l, _dependencyContext))
	//			.Select(assembly => Assembly.Load(new AssemblyName(assembly.Name)))
	//			.ToArray();
	//	}
	//}
}
