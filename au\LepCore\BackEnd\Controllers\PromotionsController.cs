﻿using AutoMapper;
using lep.configuration;
using lep.courier;
using lep.freight;
using lep.job;
using lep.order;
using lep.pricing;
using lep.promotion;
using lep.promotion.impl;
using lep.security;
using lep.user;
using LepCore.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NHibernate.Criterion;
using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace LepCore.Controllers
{
	/// <summary>
	/// </summary>
	[Serializable]
	public class PromotionSearchCriteria
	{
		public int Id { get; set; }
		public bool IsSearchPanelOpen { get; set; }
		public string FreeText { get; set; }

		[Required]
		public int Page { get; set; }

		public string SortField { get; set; }
		public string SortDir { get; set; }
	}

	[Produces("application/json")]
	[Route("api/Staff/[controller]")]
	[Authorize(Roles = LepRoles.Staff)]
	[ApiExplorerSettings(IgnoreApi = true)]
	public class PromotionsController : Controller
	{
		private readonly IConfigurationApplication _configApp;
		private readonly IHttpContextAccessor _contextAccessor;
		private readonly ICourierApplication _courierApplication;
		private readonly IFreightApplication _freightApplication;
		private readonly IJobApplication _jobApp;
		private readonly IOrderApplication _orderApplication;
		private readonly IPricePointApplication _pricePointApplication;
		private readonly IPricingEngine _pricingEngine;
		private readonly ISecurityApplication _securityApplication;
		private readonly IPromotionApplication _promoApp;
		private readonly IUserApplication _userApplication;

		private ILogger<JobOptionsController> _logger;

		private readonly IMapper _mapper;

		public PromotionsController(
			ISecurityApplication securityApplication,
			IUserApplication userApplication,
			IOrderApplication orderApplication,
			IJobApplication jobApp,
			IHttpContextAccessor contextAccessor,
			IPricingEngine pricingEngine,
			IPricePointApplication pricePointApplication,
			IFreightApplication freightApplication,
			ICourierApplication courierApplication,
			IConfigurationApplication configApp,
			IPromotionApplication promotionApplication,
			IMapper mapper,
			ILogger<JobOptionsController> logger
		)
		{
			_contextAccessor = contextAccessor;
			_orderApplication = orderApplication;
			_jobApp = jobApp;
			_securityApplication = securityApplication;
			_userApplication = userApplication;
			_pricePointApplication = pricePointApplication;
			_pricingEngine = pricingEngine;
			_freightApplication = freightApplication;
			_courierApplication = courierApplication;
			_configApp = configApp;
			_promoApp = promotionApplication;
			_logger = logger;
			_mapper = mapper;
		}

		[HttpPost("")]
		// [ValidateActionParameters]
		[Produces(typeof(PagedResult<PromotionListDto>))]
		public IActionResult GetAll([FromBody] [Required] PromotionSearchCriteria sp) //int? Id = null
		{
			Order order = null;
			if (!string.IsNullOrEmpty(sp.SortField))
			{
				order = new Order(sp.SortField, sp.SortDir == "true");
			}
			else
			{
				order = new Order("Id", true);
			}

			var criteria = _promoApp.GetPromotionsCriteria(sp.FreeText);
			var sortOrder = new[] { order };
			var list = Utils.GetPagedResult(criteria, sp.Page, 20, x => _mapper.Map<PromotionListDto>(x), sortOrder);
			return new OkObjectResult(list);
		}

		[HttpGet("{id:int}")]
		[Produces(typeof(IPromotion))]
		public IActionResult Get(int id) //int? Id = null
		{
			IPromotion promo = null;
			if (id == 0)
			{
				promo = _promoApp.NewPromotion();
			}
			else
			{
				promo = _promoApp.GetPromotion(id);
			}

			if (promo == null) return NotFound();
			var result = _mapper.Map<PromotionDto>(promo);
			return new OkObjectResult(result);
		}

		[HttpPut("{id:int}")]
		//[ValidateActionParameters]
		[ReturnBadRequestOnModelError]
		public IActionResult PutPromotion(int id, [FromBody] [Required] PromotionDto dto) //int? Id = null
		{
			Promotion promo = null;

			if (id == 0)
			{
				promo = (Promotion)_promoApp.NewPromotion();
			}
			else
			{
				promo = (Promotion)_promoApp.GetPromotion(dto.Id);
			}

			// we dont send / receive the pomotionId  in json to minimise payload size
			// so we assign it here
			foreach (var pp in dto.PromotedProducts)
			{
				pp.PromotionId = dto.Id;
			}

			promo = _mapper.Map(dto, promo);
			_promoApp.Save(promo);

			Response.Headers.Add("Id", promo.Id.ToString());
			return new StatusCodeResult(id == 0 ? StatusCodes.Status201Created : StatusCodes.Status204NoContent);
		}

		//[HttpGet("matrix")]
		//public IActionResult GetMatrix () //int? Id = null
		//{
		//    var templates = jobApp.ListAllTemplates();
		//    var templates1 = (from t in templates
		//                      where t.Id != 2 || t.Id != 12
		//                      select new {
		//                          Id = t.Id,
		//                          Name = t.Name,
		//                          Sizes = from s in t.SizeOptions
		//                                  where !s.PaperSize.Name.Contains("Custom")
		//                                  select new {
		//                                      Id   = s.PaperSize.Id,
		//                                      Size = s.PaperSize.Name,
		//                                      Stocks = (from st in s.StockOptions
		//                                                orderby st.Stock.Id
		//                                                select new
		//                                                {
		//                                                    Id = st.Stock.Id,
		//                                                    Name = st.Stock.Name
		//                                                }).ToList()
		//                                  }
		//                      }).ToList();

		//    return new OkObjectResult(templates1);
		//}

		//[HttpGet("matrix2")]
		//public IActionResult GetMatrix2 () //int? Id = null
		//{
		//    var templates = jobApp.ListAllTemplates();
		//    var templates1 = (from t in templates
		//                      where t.Id != 2 || t.Id != 12
		//                      select new {
		//                          Id = t.Id,
		//                          Name = t.Name,
		//                          Sizes = (from s in t.SizeOptions
		//                                  where !s.PaperSize.Name.Contains("Custom")
		//                                  select new IdAndName<IPaperSize>() {
		//                                      Id = s.PaperSize.Id,
		//                                      Name = s.PaperSize.Name,
		//                                      }).ToList(),

		//                         Stocks = (from s in t.SizeOptions
		//                                   from st in s.StockOptions
		//                                   orderby st.Stock.GSM
		//                                   select new IdAndName<IStock>() {
		//                                                    Id = st.Stock.Id,
		//                                                    Name = st.Stock.Name
		//                                   }).DistinctBy(x=> x.Id).ToList()
		//                        }).ToList();

		//    return new OkObjectResult(templates1);
		//}

		[HttpGet("matrix3")]
		public IActionResult GetMatrix3() //int? Id = null
		{
			var sizesD = _jobApp.ListPaperSize().ToDictionary(x => x.Id, x => x.Name);
			var stocksD = _jobApp.ListStock().ToDictionary(x => x.Id, x => x.Name);

			var templateList = _jobApp.ListAllTemplates();
			var templatesD = templateList.ToDictionary(x => x.Id, x => x.Name);
			var templatesL = templateList.Select(x => new { Id = x.Id, Name = x.Name }).ToList();
			var matrix = (from t in templateList
						  where t.Id != 2 || t.Id != 12
						  //|| t.Id != 14 || t.Id != 7 || t.Id !=8 || t.Id !=30
						  let sizeOptions = _jobApp.ListSizeOptions(t)
						  select new
						  {
							  Id = t.Id,
							  // Name = t.Name,
							  Sizes = (from s in sizeOptions
										   //where !s.PaperSize.Name.Contains("Custom")
									   select s.PaperSize.Id).ToList(),

							  Stocks = (from s in sizeOptions
										from st in s.StockOptions
										orderby st.Stock.GSM
										select st.Stock.Id).Distinct().ToList(),
							  //select new { Id = st.Stock.Id }).Distinct().ToList()
						  }).ToDictionary(x => x.Id, x => new { Sizes = x.Sizes, Stocks = x.Stocks });

			var result = new
			{
				sizesD,
				stocksD,
				templatesD,
				templatesL,
				matrix
			};
			return new OkObjectResult(result);
		}
	}
}
