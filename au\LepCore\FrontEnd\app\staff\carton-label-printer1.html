<div>
    <div class="modal-header">
        <h3 class="modal-title">Carton Label Printer</h3>
    </div>
    <div class="modal-body">
        <div class="form-horizontal">
            <div  class="form-group ng-class:{'has-error' : (!job)}">
                <label class="col-xs-4 control-label" for="edtSearch">Job #</label>
                <div class="col-xs-5">

                    <input name="edtSearch" type="text" tabindex="0"
                           ng-model="edtSearch" class="form-control input"
                           placeholder="Scan or type a job number" ng-minlength="7"/>
                </div>
            </div>



        </div>


        <div class="form-horizontal" ng-show="job">
            <div  class="form-group">
                <label class="col-xs-4 control-label" for="jobName">Job Name</label>
                <div class="col-xs-5 form-control-static">

                    <input type="text"  name="jobName"
                    class="form-control input" ng-model="job.JobName"   required>
                </div>
            </div>
            <!--
            <div class="form-group">
                <label class="col-xs-4 control-label">Order Id</label>
                <div class="col-xs-5 form-control-static">
                    {{job.OrderId}}
                </div>
            </div>
          -->

            <div class="form-group">
                <label class="col-xs-4 control-label" for="jobQuantity">Quantity</label>
                <div class="col-xs-5 form-control-static">

                    <input type="text"  name="jobQuantity"
                    class="form-control input" ng-model="job.JobQuantity" required>
                </div>

            </div>

            <div class="form-group">
                <label class="col-xs-4 control-label" for="qtyPerCarton">Quantity per Carton</label>
                <div class="col-xs-5">
                    <input type="number" step="1" id="qtyPerCarton" name="qtyPerCarton" tabindex="1"
                           class="form-control input" ng-model="qtyPerCarton" min="1" max="{{job.JobQuantity}}"  ng-pattern="integerval" required>
                </div>
            </div>


            <div ng-show="qtyPerCarton>0">
              <div class="form-group">
                  <label class="col-xs-4 control-label">Total Carton</label>
                  <div class="col-xs-5 form-control-static">
                      {{job.TotalCarton}}
                  </div>

              </div>
              <div class="form-group">
                  <label class="col-xs-4 control-label">Remainder</label>
                  <div class="col-xs-5 form-control-static">
                      {{job.Remainder}}
                  </div>
            </div>
            </div>

        </div>


    </div>

    <div class="modal-footer">
        <div>
            <button class="btn btn-default" ng-click="print()" ng-show="qtyPerCarton>0">
                <i class="glyphicon glyphicon-print"></i>
                Print Carton Label
            </button>

            <button class="btn btn-default" ng-click="closeThisDialog()">

                Cancel
            </button>
        </div>
    </div>
</div>
