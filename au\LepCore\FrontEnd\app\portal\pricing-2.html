﻿<div class="">
   
    <div class="form-group" style="text-align: right">
        <div class="col-xs-12">
            <a class="btn btn-sm"
               ng-click="useFromTemplate()"> <i class="glyphicon glyphicon-import"></i> Use an existing template </a>
            <a class="btn btn-sm"
               ng-show="vm.PrintPortalSettings.PriceRangeMarkups.length > 0"
               ng-click="saveAsTemplate('PriceRange', vm.PrintPortalSettings.PriceRangeMarkups)">Save these as a template <i class="glyphicon glyphicon-export"></i> </a>
        </div>
    </div>


    <div class="form-group">
        <label class="control-label col-xs-2">Price Range Markup (%)</label>
        <div class="col-xs-10">
            <table class="table table-condensed stripped-cols">
                <tr>
                    <th></th>
                    <th class="right"> Price from</th>
                    <th class="right"> Price upto</th>
                    <th class="right"> Mark up (%)</th>
                    <th></th>
                </tr>
                <tbody ui-sortable="sortableOptions" ng-model="vm.PriceRangeMarkups">

                    <tr ng-repeat="e in vm.PrintPortalSettings.PriceRangeMarkups track by $index">
                        <td></td>
                        <td class="right">{{e.PriceFrom}}</td>
                        <td class="right">{{e.PriceTo}}</td>
                        <td class="right">{{e.Markup}}</td>

                        <td class="pre">
                            <a ng-click="vm.PrintPortalSettings.PriceRangeMarkups.splice($index, 1)">
                                <i class=" glyphicon glyphicon-trash" style="font-size: 16pt"></i>
                            </a>
                            <i class="glyphicon glyphicon-resize-vertical" style="font-size: 16pt"
                               title="drag drop to reorder this list"></i>
                        </td>
                    </tr>
                </tbody>
                <tbody ng-form="newMarkupForm">
                    <tr>
                        <td>*</td>

                        <td class="right">
                            <input size="5" type="number" class="form-control" name="from"
                                   ng-model="n.PriceFrom" ng-required="true" />
                        </td>
                        <td class="right">
                            <input size="5" type="number" class="form-control"
                                   ng-model="n.PriceTo" ng-required="true"
                                   ng-min="n.PriceFrom" />
                        </td>
                        <td class="right ">
                            <input size="5" type="number" class="form-control"
                                   ng-model="n.Markup" ng-required="true" />
                        </td>
                        <td colspan="1">
                            <button ng-click="addMarkup()" ng-disabled="newMarkupForm.$invalid">
                                <i class="glyphicon glyphicon-plus"></i>
                            </button>

                        </td>
                    </tr>

                </tbody>

            </table>
        </div>
    </div>

 
</div>
