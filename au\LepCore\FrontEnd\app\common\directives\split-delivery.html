<div ng-form="splitsForm">
    <h5>Split delivery details for Job {{job.Id}}</h5>
    <style>
        #splitDeliveryTable td {
            padding: 5px;
            vertical-align: top;
        }
    </style>
    <div  all-input-disabled="{{!job.Visibility.saveButton}}" >

        <!-- instuctions box customer  -->
        <div class="" style="color:black"  ng-if="!($root.globals.IsStaff)">

            <img src="images/split.png" style="float: left;height: 76px; margin-right: 20px;">
            <ol style="font-size: smaller;">
                <li>Press the <b>'Add address' </b>button to add as many delivery addresses as you need.</li>
                <li ng-class="{'bold redAlert': splitsForm.$invalid }">Fill in the Quantity, Address and Recipient details making sure they all add up to your total job
                    quantity.
                </li>
                <li>press the <b>'Suggest Courier'</b> button to get a list of couriers that can deliver to that
                    address.</li>
                <li>Once you are content with your selections press the <b>'Save'</b> button to save the split delivery
                    details.
                </li>
            </ol>
        </div>

        <!-- instuctions box staff  -->
        <div class="" style="color:black"  ng-if="($root.globals.IsStaff)">

            <img src="images/split.png" style="float: left;height: 76px; margin-right: 20px;">
            <ol style="font-size: smaller;">
                <li>Staff members can edit  Edit in the Quantity, Address and Recipient details.
                     This will be reflected in the customers side.
                     Meaning there is only one details thats shared between staff and customer.
                </li>
                <li>press the <b>'Suggest Courier'</b> button to get a list of couriers that can deliver to that
                    address.  Your modifications will be reflected in the customers side. (This is unlike order freight, where the customer dont see staff overriden courier and cost.)
                </li>
                <li>Staff would also be able to modify the carton/box packing details. TODO: This is not yet implemented.
                </li>
                <li>This same details will be shown on the Dispatcher screen. If Dispatch needs to edit any details, they can do so here in this page.
                    as they do for orders. The View Freight button on Dispatcher screen will bring them here.
                </li>
                <li>Todo:  Dispatcher, create multiple consignments, import multiple consignments and other things yet to be discovered.</li>
            </ol>
        </div>


        <!-- split entry table -->
        <table id="splitDeliveryTable" class="table table-sm table-stripped" >
            <tr>
                <td style="width:120px"></td>
                <td style="width:700px"></td>
                <td></td>
                <td ng-if="$root.globals.IsStaff"></td>
            </tr>
            <tbody ng-repeat="a in job.Splits" >

           
            <tr>
                <td>
                    <!-- Split quantity -->
                    <label class="control-label" for="person">Part {{$index+1}}. quantity</label>
                    <input type="number" ng-model="a.Quantity" ng-model-options="{ updateOn: 'blur' }" 
                     ng-required="true" placeholder="enter quantity" class="form-control" style="border: 2px solid; border-radius: 3px;" />

                    <!-- {{xf[$index] | json0}} -->
                </td>
                <td >

                    <div class="row form-horizontal" ng-hide="totalsDontMatch" style="padding-top: 40px;">
                        <!-- Recipient details -->
                        <div class="col-md-6 col-xs-12">
                            <fieldset >
                                <div
                                    class="form-group form-group-sm  ng-class:{'has-error': deliveryDetailsForm.RecipientName.$invalid} ">
                                    <label class="control-label  col-xs-4" for="person">To Recipient / Business Name</label>
                                    <div class="col-xs-8">
                                        <input class="form-control" type="text" ng-model="a.RecipientName"
                                            name="RecipientName{{$index}}" ng-maxlength="50" size="50" mvs />
                                    </div>
                                </div>

                                <div class="form-group form-group-sm">
                                    <label class="control-label  col-xs-4" for="person">Recipient Phone</label>
                                    <div class="col-xs-8">
                                        <input class="form-control" type="text" ng-model="a.RecipientPhone"
                                            name="RecipientPhone{{$index}}" ng-maxlength="12" size="12" mvs />
                                    </div>
                                </div>

                                <!-- ng-show="order.Visibility.custAttachLogoChkEnabled" -->
                                <div class="form-group form-group-sm">
                                    <label class="control-label checkbox col-xs-4" for="CustomerLogoRequired{{$index}}">Attach
                                        logo label</label>
                                    <div class="col-xs-8"><input class="checkbox" type="checkbox" id="CustomerLogoRequired{{$index}}"
                                            ng-model="a.CustomerLogoRequired"
                                            ng-change="!a.CustomerLogoRequired && (a.CustomerLogoYourRef=null)" 
                                            
                                            /></div>
                                </div>
                        
                                <div class="form-group form-group-sm">
                                    <label class="control-label  col-xs-4" for="person">Logo Reference </label>
                                    <div class="col-xs-8">
                                        <input class="form-control" type="text" ng-model="a.CustomerLogoYourRef"
                                            ng-disabled="!a.CustomerLogoRequired" maxlength="20" size="20" mvs />
                                    </div>
                                </div>


                                <div class="form-group form-group-sm">
                                    <label class="control-label  col-xs-4" for="person">Delivery Instructions</label>
                                    <div class="col-xs-8">
                                        <input name="DeliveryInstructions{{$index}}" class="form-control" type="text"
                                            ng-model="a.DeliveryInstructions" ng-maxlength="28" size="28" mvs />
                                        <div class="redAlert bold"
                                            ng-if="deliveryDetailsForm.DeliveryInstructions.$error.maxlength">* max 28
                                            charecters</div>
                                    </div>
                                </div>
                                <!-- <div class="form-group form-group-sm" ng-show="order.Visibility.PackWithoutPallets">
                                    <label class="col-xs-4 control-label" for="customer">Pack </label>
                                    <div class="col-xs-8">
                                        <label class="control-label"><input type="radio" ng-model="order.PackWithoutPallets" data-ng-value="false" /> Palletized </label>&nbsp;&nbsp;
                                        <label class="control-label" ng-hide="order.Visibility.NoLooseCarton"><input type="radio" ng-model="order.PackWithoutPallets" data-ng-value="true" ng-disbled="true" /> Loose Cartons</label>&nbsp;&nbsp;
                                    </div>
                                </div> -->
                                <!-- {{order.Visibility.PackWithoutPallets | json}} -->


                            </fieldset>
                        </div>

                        <!-- Address details -->
                                          
                        <div class="col-md-6 col-xs-12"> 
                            
           
                            <!-- {{a.Address | json}} -->
                            <div address-details address="a.Address" noteditable="false"></div>
                           
                            <!--     
                            <a class="btn  btn-sm " trigger="hover"
                            placement="top"
                            title="Make current address a favourite"
                            bs-tooltip ng-click="addFavouriteDeliveryDetails(order.DeliveryAddress)">
                                <i class="glyphicon glyphicon-star"></i>
                            </a> -->
                            <a class="pull-right"  trigger="hover" placement="top" title="Search from previous addresses" bs-tooltip
                            ng-click="openFavouriteDeliveryDetails($index)">
                            <i class="glyphicon glyphicon-search"></i> 
                        </a>
                        </div>


                        
                        <div class="col-md-6 col-xs-12" >
                            <!-- <div class="form-group form-group-sm" ng-hide="a.BrochureDistPackInfo.MailHouse.Id">
                                <label class="control-label checkbox col-xs-4" for="CustomerLogoRequired">Mailhouse?</label>
                                <div class="col-xs-8"><input class="checkbox" type="checkbox" 
                                        ng-model="a.showMailhouse" /></div>
                            </div> -->
                        </div>

                        <!-- <pre>{{a.BrochureDistPackInfo | json}}</pre> -->


                        <div class="col-md-6" ng-i0f="a.showMailhouse || a.BrochureDistPackInfo.MailHouse.Id">
                            <div data-title="{{a.BrochureDistPackInfo.MailHouse.Instructions}}">
                                <!-- MailhouseInstructions -->
                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="mailhouseInstructions">Mailhouse</label>
                                    <div class="col-xs-7">
                                        <select id="mailhouseInstructions" name="mailhouseInstructions" class="form-control input"
                                                ng-options="s as s.Name for s in brohureMailHouses track by s.Id" 
                                                ng-model="a.BrochureDistPackInfo.MailHouse"
                                                ng-change="changeMailHouse($index, a.BrochureDistPackInfo.MailHouse)">
                                                
                                            <option value="">N/A</option>
                                        </select>
                                    </div>
                                </div>
                                <div ng-hide="a.BrochureDistPackInfo.MailHouse != null  && a.BrochureDistPackInfo.MailHouse.Id > 0">
                                    <!-- If a Customer has chosen Mailhouse Distribution Option, then Packing Instructions Option should be disabled -->
                                    <!-- Packing Instructions -->
                                    <div class="form-group">
                                        <label class="col-xs-3 control-label" for="packingInstructions">Packing Instructions</label>
                                        <div class="col-xs-7">
                                            <select id="packingInstructions" name="packingInstructions" class="form-control input"
                                                    ng-options="k*1 as v for (k,v) in $root.enums.ValueDesc.PackingInstruction"
                                                    ng-model="a.BrochureDistPackInfo.PackingInstruction"></select>
                                        </div>
                                    </div>
                                    <!--Bundles-->
                
                                    <div class="form-group  ng-class:{'has-error' : (!a.BrochureDistPackInfo.BundlesOf)} " ng-show="currentBundlesOf != null">
                                        <label class="col-xs-3 control-label" for="bundles">Bundles of approx</label>
                                        <div class="col-xs-7">
                
                                            <input name="BundlesOf" type="number"
                                                    ng-model="a.BrochureDistPackInfo.BundlesOf" ng-max="{{((80 - (80 % job.ThicknessOfSingleJob)) / job.ThicknessOfSingleJob)}}"
                                                    ng-min="{{(a.BrochureDistPackInfo.PackingInstruction == 2? 100: 50)}}"  />
                                            <div class="error-redish bold" ng-show="JobDetailsForm.BundlesOf.$error.max">reduce bundle size</div>
                                            <div class="error-redish bold" ng-show="JobDetailsForm.BundlesOf.$error.min">min bundle size is {{(a.BrochureDistPackInfo.PackingInstruction == 2? 100: 50)}}</div>
                                        </div>
                                    </div>
                                </div>
                
                                <!--Extra fields for  Salmat/Local Delivery Network-->
                                <div ng-show="a.BrochureDistPackInfo.MailHouse != null  && (a.BrochureDistPackInfo.MailHouse.Id == 1 ||a.BrochureDistPackInfo.MailHouse.Id == 2 ||a.BrochureDistPackInfo.MailHouse.Id == 3 ||a.BrochureDistPackInfo.MailHouse.Id == 5)">
                                    <div class="form-group">
                                        <label class="col-xs-3 control-label" for="BookingIdNumber">Booking ID Number</label>
                                        <div class="col-xs-7">
                                            <input id="BookingIdNumber" name="BookingIdNumber" class="form-control input"
                                                   ng-model="a.BrochureDistPackInfo.BookingIdNumber" />
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-xs-3 control-label" for="CampaignName">Campaign Name</label>
                                        <div class="col-xs-7">
                                            <input id="CampaignName" name="CampaignName" class="form-control input"
                                                   ng-model="a.BrochureDistPackInfo.CampaignName" />
                                        </div>
                                    </div>
                                </div>
                                <!--Extra fields for Fuji Xerox-->
                                <div ng-show="a.BrochureDistPackInfo.MailHouse != null  && a.BrochureDistPackInfo.MailHouse.Id ==5">
                                    <div class="form-group">
                                        <label class="col-xs-3 control-label" for="QtyPerBox">Qty per box</label>
                                        <div class="col-xs-7">
                                            <input id="QtyPerBox" name="QtyPerBox" class="form-control input" type="text"
                                                   ng-model="a.BrochureDistPackInfo.QtyPerBox" />
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-xs-3 control-label" for="MHDatePacked">Date packed</label>
                                        <div class="col-xs-7">
                                            <input id="MHDatePacked" name="MHDatePacked" class="form-control input"
                                                   ng-model="a.BrochureDistPackInfo.DatePacked" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    
                    <!-- freight details for row -->
                    <div class="row" style="padding: 15px;" ng-if="(a.Rates2.length || a.Courier ) && !totalsDontMatch">
                        <div class="col-xs-2">
                            <div class="control-label">
                                Freight: 
                            </div>
                        </div>
                        <div class="col-xs-10">
                            <div class="col-xs-8 form-control-static bold"> {{a.Courier}} </div>

                            <div class="col-xs-4 form-control-static bold"> {{a.CustomerCharge | currency}}</div>

                            <span ng-show="updatingCourier" style="font-weight:bold; color: orangered">
                                Finding couriers and freight charges, please wait...
                            </span>

                            <select class="form-control input monospace" ng-model="a.$Courier" ng-show="a.Rates2"
                                ng-options="f as f.Name for f in a.Rates2" ng-change="changeRate(a)">
                                <option value="">Select a courier and service</option>
                            </select>
                            <!-- {{a.$Courier | json}}
                            <br/>
                            {{a.Courier | json}}  ----- {{a.CustomerCharge | currency}} -->
                        </div>
                    </div>

                    <!-- <pre>{{a.PackagesStr }}</pre> -->
                    <!-- <hr style="border-top: 4px solid #ccc" /> -->
                </td>
                <td  style="padding-top: 40px;">
                    <a ng-click="removeAddress($index)" title="Delete address">
                        <span class="glyphicon glyphicon-remove"></span>
                    </a>
                </td>

                <!-- for staff only column -->
                <td ng-if="$root.globals.IsStaff && !totalsDontMatch && splitsForm.$valid" style="padding-top: 40px;">
                    <div>
                        <label>Packaging Override <input type="checkbox" ng-model="a.IsCustom"/> </label>
                    </div>
                    <br />


                    <!--      
                    <pre>{{a.PackagesStr }}</pre>    -->
                 

                    <!--  <span class="help-block"> Todo: Provide a way to ammend the packages for this split here, Im thinking of a popup as some packing/boxing details will get very lengthy</span>
                    -->
                    <ol style="padding-inline-start: 0px;">
                        <li ng-repeat="package in a.Packages  track by $index" ng-include="'packageTree'" ng-init="parent = a.Packages">
                        </li>
                        <span ng-click="a.Packages.push({JobId: job.Id })"><i class="glyphicon glyphicon-plus"></i> Add a package</span>
                        <span ng-click="a.Packages = wrapIn(a.Packages)" ng-show="a.Packages.length>1"><i
                                class="glyphicon glyphicon-plus"></i>Wrap in a package</span>
                    </ol>
                    
                </td>
            </tr>
            </tbody>
            <!-- total row -->

            <tbody style="background: cornsilk;font-size: large;">

            
            <tr ng-if="job.Splits.length>1" >
                <td>
                    <label ng-model="total" value-needs-to-pass="totalsMatch" class="form-control-static number">{{total}}</label>
                </td>
                <td>

                    <div class="col-xs-2">

                    </div>
                    <div class="col-xs-10">
                        <div class="control-label" ng-if="sumOfFreight > 0">
                            Total Cost of freight {{sumOfFreight | currency}} + 
                            handling charge $25 x {{job.Splits.length}} = {{sumOfFreight + (job.Splits.length * 25) | currency}}
    
                        </div>
                    </div>

                    

                </td>
                <td>

                </td>
                <!-- for staff only column -->
                <td ng-if="$root.globals.IsStaff">

                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <span ng-if="job.Splits.length>1 && totalsDontMatch" class="redAlert bold">Quantities dont add up to {{job.Quantity}}</span>
    <div class="row" ng-hide="pending > 0">
        <div class="col-md-12">

            <div class="form-actions pull-right">

                
                <a class="btn" 
                    ui-sref="^.view({orderId:job.OrderId})" ui-sref-opts="{reload:true}" >
                    <i class="glyphicon glyphicon-chevron-left"></i> back to order
                </a>
                <span ng-if="job.Visibility.saveButton">
                    <a class="btn btn-warn" ng-click="cancelSplit()" title="Cancel Split Delivery">
                        <i class="glyphicon glyphicon-trash"></i> Cancel
                    </a>

                    <button type="button" class="btn btn-info" ng-click="createAddress(j)"><span
                            class="glyphicon glyphicon-plus"></span> Add address</button>

                    <button type="button" class="btn btn-info" ng-click="getSuggestions()" ng-disabled="splitsForm.$invalid">Suggest
                        Couriers</button>

                    <button type="button" class="btn   btn-success" ng-click="save()">

                        <i class="glyphicon glyphicon-floppy-save"></i>
                        Save</button>
                </span>
            </div>

        </div>
    </div>
    <pre>
    <!-- {{splitsForm | json}} -->
</pre>
    <!-- 
            <pre>{{job.Splits | json}}</pre>

            <pre>
        1. Find the split with max Quantity
        2. Find the delivery post code from 1    
        3. Find Job.Facility from that post code
        4. Find courier suggesstion from 2, 3
            </pre>
        -->
        <script type="text/ng-template" id="packageTree">
            <div>
                <!-- <span ng-hide="(package.JobId==0 || package.JobId=='0') ">
                    <input style="width:80px; text-align: right; padding:2px; height:24px;  border: 1px solid lightgray;" ng-model="package.JobId" title="Job Id">
                </span> -->
        
                <select style=" padding:2px; height:24px;  border: 1px solid lightgray;" title="Carton"
                ng-options="c.Code as c.Code for c in packages" ng-model="package.CartonCode" ng-change="packageChanged(package, $index)"> </select>
        
                <input style="width:60px; text-align: right; padding:2px; height:24px;  border: 1px solid lightgray;" ng-model="package.Weight" title="Conent + package Weight in Kg"> Kg
        
                <span ng-hide="package.JobQty && package.JobQty==0">
                    @<input style="width:80px; text-align: right; padding:2px; height:24px;  border: 1px solid lightgray;" ng-model="package.JobQty" placeholder="Qty" title="Quantity in each box">
                </span>
        
                <!--<span style="font-size:x-small" ng-if="package.CartonLevel"> (Level: {{ package.CartonLevel}}) </span>-->
        
                <span class="glyphicon glyphicon-plus" style="color:lightgray; cursor: pointer; " ng-click="addNodeUnder(package)" title="Add Box Inside"></span>
                <span class="glyphicon glyphicon-sort-by-attributes" style="color:lightgray; cursor: pointer; " ng-click="replicateNode(this)" title="Replicate package n times"></span>
                <span class="glyphicon glyphicon-remove" style="color:lightgray; cursor: pointer;" ng-click="deleteNode(parent,$index)" title="Remove this box preserving Boxes inside"></span>
                <span class="glyphicon glyphicon-remove-sign" style="color:lightgray; cursor: pointer;" ng-click="deleteNode2(parent,$index)" title="Remove this box including Boxes inside"></span>
        
                <!--parent.splice($index,1)-->
            </div>
        
            <ol ng-if="package.Items">
                <li ng-repeat="package in package.Items track by $index" ng-include="'packageTree'" ng-init="parent = $parent.$parent.package"></li>
            </ol>
        </script>
</div>