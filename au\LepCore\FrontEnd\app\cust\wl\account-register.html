﻿<div ng-form="form" style="margin-top:30px;">


	<h4>Account details</h4>
	<div class="form-horizontal">
		<div class="row">
			<div class="col-xs-8 col-md-8">

				<div class="form-group">
					<label class="col-sm-4 control-label" for="bus-name">Business Name</label>
					<div class="col-sm-8">
						<input name="Name" type="text"
							   class="form-control input"
							   ng-maxlength="200"
							   ng-model="vm.Name"
							   ng-required="true"
							   lep-unique-businessname
							   user-id="vm.Id" mvs />

					</div>
				</div>

				<div class="form-group">
					<label class="col-sm-4 control-label">Email</label>
					<div class="col-sm-8">
						<input name="Email" type="email" class="form-control input" ng-model="vm.Email" ng-required="true" mvs />
					</div>
				</div>

				<div class="form-group">

					<label class="col-sm-4 control-label" for="username">Username</label>
					<div class="col-sm-8">
						<input name="Username" type="text" ng-model="vm.Username"
							   ng-model-options="{allowInvalid:true}" class="form-control input"
							   ng-required="true" ng-minlength="5" ng-maxlength="40" lep-unique-username user-id="vm.Id" mvs>

					</div>
				</div>

				<div class="form-horizontal">
					<div class="form-group">
						<label class="control-label col-sm-4">Password </label>
						<div class="col-sm-8">
							<input class="form-control" type="password" ng-model="vm.Password" ng-required="true" name="Password" mvs />
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-sm-4">Confirm Password</label>
						<div class="col-sm-8">
							<input class="form-control" type="password" ng-model="vm.Password1" ng-required="true" name="Password1" mvs />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="form-horizontal">
		<h4>Primary Contact</h4>
		<div lep-contact-details contact="vm.Contacts[0]" email-required="true" mobile-required="true"></div>
	</div>


	<div leppane="Address details">
		<div class="form-horizontal">
			<div class="row">

				<div class="col-sm-6">
					<div class="form-group">
						<div class="col-sm-9">
							<h6>Billing address</h6>
						</div>
					</div>
					<div class="form-group">

						<div class="col-sm-10 col-sm-offset-2">
							<label class="checkbox">

							</label>
						</div>
					</div>

					<div address-details address="vm.BillingAddress"></div>

				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<div class="col-sm-9">
							<h6>Delivery address</h6>
						</div>
					</div>
					<div class="form-group">

						<div class="col-sm-10 col-sm-offset-3">
							<label class="checkbox">
								<input name="checkbox" type="checkbox" ng-model="vm.PostalIsBilling" ng-change="makePostalSameAsBilling(vm.PostalIsBilling)" />
								Same as billing address
							</label>
						</div>
					</div>


					<div address-details address="vm.PostalAddress" noteditable="vm.PostalIsBilling"></div>


				</div>
			</div>
		</div>

	</div>






	<div class="row">
		<div class="col-xs-8">
			<div class="form-horizontal col-xs-8">
				<div class="row">
					<div class="form-actions pull-right">
						<button type="submit" class="btn-sm" ng-click="save()" ng-disabled="form.$invalid">
							<i class="glyphicon glyphicon-floppy-save"></i>
							Create Account
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
