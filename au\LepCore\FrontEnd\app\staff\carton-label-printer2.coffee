﻿appStaff = angular.module('app.staff')

appStaff.controller 'CartonLabelMailHousePrinter1Controller', [
    '$scope','lepApi2', '$location','enums', '$stateParams','Utils', '$http', '$state', 'JobService', 'OrderService', '$localStorage', 'ngDialog', '$q', '$window', 'dispatchers'
    ($scope,   lepApi2,   $location,  enums,  $stateParams,  Utils,   $http,   $state,   JobService,   OrderService, $localStorage, ngDialog, $q, $window, dispatchers ) ->
        $scope.dispatchers = dispatchers
        currDisp  = {}
        $scope.currDisp  = currDisp
        $scope.currDispName = $localStorage.currDispName || null

        $scope.edtSearch = ''
        if $stateParams.jobId
            $scope.edtSearch = $stateParams.jobId
            e = angular.element('#qtyPerCarton')
            e.focus()
            e.select()

        jobId = 0
        jobName = ""
        $scope.jobId = jobId
        $scope.jobName = jobName
        $scope.jobSendSample = false

        $scope.qtyPerCarton = 0


        order = {}
        $scope.order     = order

        job = null
        $scope.job = job


        focusJobScanInput = () ->
          e = angular.element('#edtSearch')
          e.focus()
          e.select()

        $scope.scan = (jobId) ->
            lepApi2.get("Orders/job/#{jobId}/cartonlabel").then (j)->
                $scope.job = j

        $scope.$watch 'edtSearch', (n,o) ->
            if !n then return
            if n.length < 6 then return
            if n[0] is 'J' or n[0] is 'j' then n = n.substring(1)
            jobId = n
            $scope.scan(n)

        $scope.$watch 'qtyPerCarton', (n,o) ->
            if !n then return
            $scope.job.Remainder = $scope.job.JobQuantity % n;
            $scope.job.TotalCarton = Math.ceil($scope.job.JobQuantity /  n)




        $scope.print = () ->
            printerName =  dispatchers[$scope.currDispName].Printers.AustraliaPost
            q = $scope.qtyPerCarton
            if !q then return
            url =  "orders/Job/cartonlabel2"
            req =
                jobId : $scope.job.JobId
                jobName: $scope.job.JobName
                jobQty: parseInt($scope.job.JobQuantity)
                qtyPerCarton: $scope.qtyPerCarton
                printerName: printerName
            lepApi2.post(url, req).then (o) ->
            focusJobScanInput()
            $scope.closeThisDialog()



        return
    ]

