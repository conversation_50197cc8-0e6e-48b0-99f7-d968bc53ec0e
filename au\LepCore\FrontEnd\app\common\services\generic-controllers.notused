﻿(function (window, angular, undefined) {
    'use strict';


    var app = angular.module('app');
    // basic wrapper for backend BaseController<T, M> where M is IPagedManager<T>
    app.factory('adminServiceCrudMixin', ['lepApi', 'DateService',
        function (lepApi, DateService) {
            return function adminServiceCrudMixin(entity, searchPagedPath, getPath) {

                var isGuid = function (str) {
                    if (str.length != 36) {
                        return false;
                    }

                    if (str.charAt(8) == '-' && str.charAt(13) == '-' && str.charAt(18) == '-' && str.charAt(23)) {
                        return true;
                    }

                    return false;
                };

                // by default the search path is this

                searchPagedPath = searchPagedPath || '/SearchPaged';
                getPath = getPath || '/Get';

                return {
                    searchPaged: function (pageIndex, pageSize, filters, sort) {
                        var p = {
                            'pageIndex': pageIndex,
                            'pageSize': pageSize,
                            'filters': filters,
                            'sort': sort
                        };
                        return lepApi.post('/' + entity + searchPagedPath, JSON.stringify(p, DateService.serialiser));
                    },

                    search: function () {
                        return lepApi.post('/' + entity + '/Search');
                    },

                    save: function (data) {
                        return lepApi.post('/' + entity + '/Save', JSON.stringify(data, DateService.serialiser));
                    },

                    get: function (id) {
                        if (isGuid(id)) {
                            return lepApi.post('/' + entity + getPath, '"' + id + '"');
                        } else {
                            return lepApi.post('/' + entity + getPath, JSON.stringify(id));
                        }
                    },

                    remove: function (id) {
                        if (isGuid(id)) {
                            return lepApi.post('/' + entity + '/Delete', '"' + id + '"');
                        } else {
                            return lepApi.post('/' + entity + '/Delete', JSON.stringify(id));
                        }
                    },

                    csvExport: function () {
                        return '/v5/api/' + entity + '/CSVExport';
                    },

                    // more stuff that are common...
                    listEnable: function (ids) {
                        return lepApi.post('/' + entity + '/ListEnable', ids);

                    },

                    listDisable: function (ids) {
                        return lepApi.post('/' + entity + '/ListDisable', ids);

                    },

                    listDelete: function (ids) {
                        return lepApi.post('/' + entity + '/ListDelete', ids);

                    }
                };

            };
        }
    ]);

    app.factory('adminGenericListController', //
        ['lepApi', 'modalDialog', 'toastr', '_', '$log',
            function (lepApi, modalDialog, toastr, _, $log) {

                return function adminGenericListController(entity, entityState, $scope, $state, DS) {

                    $scope.items = $scope.items || [];

                    $scope.create = function () {
                        $state.transitionTo(entityState + 'Edit', {
                            id: 0
                        });
                    };

                    $scope.dlg = function () {
                        modalDialog.confirmUnsavedChanges()
                            .then(function (usersAnswer) { });
                    };

                    $scope.edit = function (id) {
                        // $Log.Debug('adminGenericListController : $scope.edit () : ' + id);
                        $state.transitionTo(entityState + 'Edit', {
                            id: id
                        });
                    };

                    $scope.loadData = function () {
                        DS.search()
                            .then(function (data) {
                                $scope.items = data.DataList;
                                $scope.allCheckBox = false;
                                $scope.canAddItem = data.CanAddItem;
                                $scope.canDeleteItem = data.CanDeleteItem;
                                $scope.canUpdateItem = data.CanUpdateItem;
                            });
                    };

                    // $scope.loadData();

                    $scope.remove = function (id) {

                        modalDialog.confirmDeleteYesNo()
                            .then(function (usersAnswer) {
                                if (usersAnswer === true) {
                                    DS.remove(id)
                                        .then(function (result) {
                                            if (result.isSuccess) {
                                                toastr.success($scope.entityTypeTitle + ' deleted');
                                                $scope.loadData();
                                            } else {
                                                $scope.SummaryError = result.SummaryError;
                                                $scope.renderElementErrors(result.Errors);
                                            }
                                        });
                                }
                            });
                    };

                    $scope.cancel = function () {
                        $state.transitionTo(entityState);
                    };

                    $scope.makeDefault = function (id) {
                        DS.setDefault(id)
                            .then(function (result) {
                                if (result.isSuccess) {
                                    $scope.loadData();
                                } else {
                                    $scope.SummaryError = result.SummaryError;
                                    $scope.renderElementErrors(result.Errors);
                                    toastr.success($scope.entityTypeTitle + ' updated');
                                }
                            });

                    };

                    $scope.makeEnabled = function (id) {
                        (DS.listEnable([id])).then(function (data) {
                            $scope.items = data.DataList;
                            toastr.success($scope.entityTypeTitle + ' updated');
                        });

                    };

                    $scope.makeDisabled = function (id) {
                        (DS.listDisable([id])).then(function (data) {
                            $scope.items = data.DataList;
                            toastr.success($scope.entityTypeTitle + ' updated');
                        });
                    };

                    $scope.toggleSelectAll = function () {
                        //$log.info($scope.allCheckBox);
                        _.each($scope.items, function (x) {
                            x.checked = $scope.allCheckBox;
                        });
                    };

                    // this bit is not required
                    // $scope.$watch('allCheckBox', function(n) {
                    //     _.each($scope.items, function(x) {
                    //         x.checked = n;
                    //     });
                    // });
                    //

                    $scope.$watch('selectListAction', function () {
                        $scope.csvOptionSelected = ($scope.selectListAction == 'csv' || $scope.selectListAction == 'xml');
                    });

                    //
                    $scope.anythingChecked = function () {
                        var si = _.where($scope.items, {
                            checked: true
                        });
                        return si.length > 0;
                    };

                    $scope.itemsChecked = false;
                    $scope.$watch('anythingChecked()', function (x) {
                        $scope.itemsChecked = x;
                    });

                    $scope.isAllChecked = function () {
                        var si = _.where($scope.items, {
                            checked: true
                        });
                        $scope.allCheckBox = $scope.items.length == si.length;
                    };

                    $scope.doListAction = function () {
                        if ($scope.selectListAction === undefined) {
                            return;
                        }

                        var si = _.where($scope.items, {
                            checked: true
                        });

                        var ids = _.pluck(si, 'Id');

                        if ('listDelete' == $scope.selectListAction) {
                            modalDialog.confirmDeleteYesNo()
                                .then(function (usersAnswer) {
                                    if (usersAnswer === true) {
                                        (DS[$scope.selectListAction](ids))
                                        .then(function (data) {
                                            $scope.items = data.DataList;
                                            $scope.allCheckBox = false;
                                            toastr.success($scope.entityTypeTitle + ' updated');
                                        });
                                    }
                                });
                        } else {
                            (DS[$scope.selectListAction](ids))
                            .then(function (data) {
                                $scope.items = data.DataList;
                                $scope.allCheckBox = false;
                                toastr.success($scope.entityTypeTitle + ' updated');
                            });
                        }

                    };

                    $scope.renderElementErrors = function (errors) {
                        if ($scope.form) {
                            for (var property in errors) {
                                if ($scope.form[property]) {
                                    $scope.form[property].$setValidity('validation', false);
                                    $scope.form[property].errorMessage = errors[property];
                                }
                            }
                        }
                    };
                };
            }
        ]);

    app.factory('adminGenericFormController', //
        ['lepApi', 'modalDialog', '$q', 'toastr', 'appConsts',
            function (lepApi, modalDialog, $q, toastr, appConsts) {

                return function adminGenericFormController(entity, entityState, $scope, $state, DS, item) {

                    $scope.item = item || {};

                    $scope.loadData = function (id) {
                        var d = $q.defer();

                        if (id !== null) {
                            DS.get(id)
                                .then(function (result) {
                                    if (result.isSuccess) {
                                        $scope.item = result.Data;
                                        d.resolve($scope.item);
                                    } else {
                                        $scope.cancel();
                                    }
                                });
                        }

                        return d.promise;
                    };

                    $scope.goListPage = function () {
                        //debugger;
                        var canUpdate = $scope.item.CanUpdateItem !== null ? $scope.item.CanUpdateItem : $scope.Permission.canUpdate;

                        if ($scope.form.$dirty && ($scope.item.Id === 0 || canUpdate)) {
                            modalDialog.confirmUnsavedChanges($scope.form.$invalid)
                                .then(function (usersAnswers) {
                                    for (var i in usersAnswers) {
                                        $scope[usersAnswers[i]]();
                                    }
                                });

                        } else {

                            $state.transitionTo(entityState, {
                                reload: true,
                                inherit: false,
                                notify: false
                            });
                        }
                    };

                    $scope.save = function () {

                        DS.save($scope.item)
                            .then(function (result) {
                                if (result.isSuccess) {
                                    toastr.success($scope.entityTypeTitle + ' has been saved successfully');
                                    $scope.cancel();
                                } else {
                                    $scope.SummaryError = result.SummaryError;
                                    $scope.renderElementErrors(result.Errors);
                                }


                            });
                    };

                    $scope.makeDefault = function () {
                        DS.setDefault($scope.item.Id)
                            .then(function (result) {
                                if (result.isSuccess) {
                                    $scope.cancel();
                                } else {
                                    $scope.SummaryError = result.SummaryError;
                                    $scope.renderElementErrors(result.Errors);
                                }
                            });
                    };

                    $scope.remove = function () {
                        modalDialog.confirmDeleteYesNo()
                            .then(function (usersAnswer) {
                                if (usersAnswer === true) {
                                    DS.remove($scope.item.Id)
                                        .then(function (result) {
                                            if (result.isSuccess) {
                                                toastr.success($scope.entityTypeTitle + ' deleted');
                                                $scope.cancel();
                                            } else {
                                                $scope.SummaryError = result.SummaryError;
                                                $scope.renderElementErrors(result.Errors);
                                            }
                                        });
                                }
                            });
                    };

                    $scope.cancel = function () {

                        $state.transitionTo(entityState);
                    };

                    $scope.renderElementErrors = function (errors) {
                        if ($scope.form) {
                            for (var property in errors) {
                                if ($scope.form[property]) {
                                    $scope.form[property].$setValidity('validation', false);
                                    $scope.form[property].errorMessage = errors[property];
                                }
                            }
                        }
                    };


                    $scope.$on('$destroy', function () {
                        $scope.$emit('event:refresh');
                    });

                    $scope.$watch('item.Permission', function () {
                        if ($scope.item.Permission !== null) {
                            return $scope.applyPermisions();
                        } else if ($scope.item.Id === null || $scope.item.Id === 0) { //on create
                            $scope.item.Permission = 15;
                            return $scope.applyPermisions();
                        }
                    });

                    $scope.applyPermisions = function () {
                        /* jshint -W016*/
                        var p;
                        p = $scope.item.Permission || 0;

                        $scope.Permission = {
                            canView: !!(p & appConsts.Permission.VIEW),
                            canCreate: !!(p & appConsts.Permission.CREATE),
                            canUpdate: !!(p & appConsts.Permission.UPDATE),
                            canDelete: !!(p & appConsts.Permission.DELETE)
                        };
                    };
                };

            }
        ]);
    /*
    use where you need
        nextPage()
        search()
    for infinite scroll

    NOTE: use this in addition to adminGenericListController where you need a paged search
    for a use case see broadband-connection-type.js
    in future content.js can be shortened like the code in broadband-connection-type.js

    TODO: in
            1. service member type
            2. content
        use this.... the above 2 got created long before the following was introduced.
    */
    app.factory('adminGenericListControllerPagedSearchMixin', //
        ['lepApi', 'modalDialog', 'toastr', '_', '$log',
            function (lepApi, modalDialog, toastr, _, $log) {

                return function adminGenericListControllerPagedSearchMixin(entity, entityState, $scope, $state, DS) {
                    $scope.totalItemCount = 0;
                    $scope.items = $scope.items || [];
                    $scope.searchCriteria = '';
                    $scope.filters = {};
                    $scope.pageIndex = 0;
                    // prevents multiple searches occuring when clicking the search button or scrolling
                    // debounce was not preventing duplicate results from appearing
                    // tested on about 50 double clicks appears okay
                    $scope.busy = false;
                    $scope.canAddItem = false;

                    $scope.create = function () {
                        //$log.info(entityState + 'Edit');

                        $state.transitionTo(entityState + '.Edit', {
                            id: 0
                        });
                    };

                    $scope.setBusy = function () {
                        $scope.busy = true;
                    };

                    /**
                     * gets called in by infinite scroll, assume $scope.filters is already set
                     */
                    $scope.nextPage = function () {

                        if (!$scope.busy) {
                            $scope.busy = true;

                            if (DS.filters !== null) {
                                $scope.filters = DS.filters;
                            }

                            if ($scope.items.length == $scope.totalItemCount && $scope.totalItemCount > 0) {
                                $scope.busy = false;
                                return;
                            }

                            DS.searchPaged($scope.pageIndex++, $scope.$root.DefaultPageSize, $scope.filters, $scope.sortOrder).then(function (data) {
                                $scope.totalItemCount = data.TotalItemCount;
                                $scope.canAddItem = data.CanAddItem;
                                $scope.canDeleteItem = data.CanDeleteItem;
                                $scope.canUpdateItem = data.CanUpdateItem;
                                for (var i = 0; i <= data.DataList.length - 1; i++) {
                                    $scope.items.push(data.DataList[i]);
                                }
                                $scope.busy = false;
                            });
                        }
                    };

                    /**
                     * Issued by the search button click
                     * sets up search filter and resets the page index to 0
                     */
                    $scope.search = function (filters) {

                        if (filters !== null) {
                            $scope.filters = filters;
                        }

                        // if ($scope.searchCriteria) {
                        //     $scope.filters = $scope.searchCriteria;
                        // } else {
                        //     $scope.filters = null;
                        // }

                        // $scope.sort = sort;

                        $scope.items = [];
                        $scope.pageIndex = 0;
                        $scope.nextPage();
                    };

                    $scope.toggleSort = function (sort) {
                        $scope.sortOrder = sort === $scope.sortOrder ? sort + 'Desc' : sort;
                        $scope.pageIndex = 0;
                        $scope.search();
                    };

                    $scope.reset = function () {
                        $scope.filters = {};
                    };

                    $scope.$on('event:refresh', function () {
                        $scope.search();
                    });
                };
            }
        ]);


    app.factory('adminGenericListControllerCheckList', //
        ['lepApi', 'modalDialog', 'toastr', '_',
            function (lepApi, modalDialog, toastr, _) {

                return function adminGenericListControllerCheckList(entity, entityState, $scope, $state, DS) {

                    $scope.items = $scope.items || [];

                    // $scope.create = function() {
                    //     $state.transitionTo(entityState + 'Edit', {
                    //         id: 0
                    //     });
                    // };

                    // $scope.dlg = function() {
                    //     modalDialog.confirmUnsavedChanges()
                    //         .then(function(usersAnswer) {});
                    // };

                    // $scope.edit = function(id) {

                    //     debugger;
                    //     $state.transitionTo(entityState + 'Edit', {
                    //         id: id
                    //     });
                    // };

                    // $scope.loadData = function() {
                    //     DS.search().then(function(data) {
                    //         $scope.items = data.DataList;
                    //         $scope.allCheckBox = false;
                    //     });
                    // };

                    // $scope.cancel = function() {
                    //     $state.transitionTo(entityState);
                    // };

                    $scope.toggleSelectAll = function () {
                        _.each($scope.items, function (x) {
                            x.checked = $scope.allCheckBox;
                        });
                    };

                    $scope.$watch('allCheckBox', function (n) {
                        _.each($scope.items, function (x) {
                            x.checked = n;
                        });
                    });

                    $scope.anythingChecked = function () {
                        var si = _.where($scope.items, {
                            checked: true
                        });
                        return si.length > 0;
                    };

                    $scope.itemsChecked = false;
                    $scope.$watch('anythingChecked()', function (x) {
                        $scope.itemsChecked = x;
                    });

                    //
                    $scope.isAllChecked = function () {
                        var si = _.where($scope.items, {
                            checked: true
                        });
                        $scope.allCheckBox = $scope.items.length == si.length;
                    };
                };
            }
        ]);



    /*
        put a class iparticipate in all the parent of input/selects
        where you need it to participate in saved friendly search
    */
    app.factory('adminSaveSearchMixin', //
            ['lepApi', 'modalDialog', 'toastr', '_', '$log', '$rootScope',
                function (lepApi, modalDialog, toastr, _, $log, $rootScope) {

                    return function adminSaveSearchMixin(entity, entityState, $scope, $state) {
                        $scope.searchCriteria = $rootScope.SearchParameter || {};
                        $scope.searchLayout = $rootScope.SearchLayout || {};

                        if ($rootScope.SearchParameter) {
                            var f = JSON.parse($rootScope.SearchParameter);

                            if ($scope.filters) {
                                $scope.filters = f || {};
                                $rootScope.SearchParameter = null;
                            }

                            if ($scope.DS.filters) {
                                $scope.DS.filters = f || {};
                                $rootScope.SearchParameter = null;
                            }
                        }
                        if ($rootScope.SearchLayout) {
                            var d = JSON.parse($rootScope.SearchLayout);

                            if ($scope.searchLayout) {
                                $scope.searchLayout = d || {};
                                $rootScope.SearchLayout = null;
                            }

                            if ($scope.DS.searchLayout) {
                                $scope.DS.searchLayout = d || {};
                                $rootScope.SearchLayout = null;
                            }
                        }
                        if ($rootScope.SearchAdv) {
                            $scope.DS.advSearchVisible = $rootScope.SearchAdv;
                            $rootScope.SearchAdv = null;
                        }

                        $scope.generateUIString = function () {
                            var $, strs, result;

                            $ = jQuery;
                            strs = [];

                            $('.iparticipate').each(function (i, x) {
                                var foundInputs, title, val;
                                title = $(x).find('label').html() || 'Text search';
                                val = [];
                                foundInputs = $(x).find('input');
                                if (foundInputs.length === 1) {
                                    val.push(foundInputs[0].value);
                                } else {
                                    foundInputs = $(x).find('option:selected');
                                    var optionCount = foundInputs.length;
                                    if (optionCount > 0) {
                                        for (var pos = 0; pos < optionCount; pos++) {
                                            if (foundInputs[pos].value !== '') {
                                                val.push(foundInputs[pos].text);
                                            }
                                        }
                                    }
                                }
                                if (val.length === 1 && val[0] !== '') {
                                    return strs.push(title + ': ' + val + '');
                                } else if (val.length > 1) {
                                    return strs.push(title + ': (' + val.join(', ') + ')');
                                }
                            });
                            if (strs.length === 0) {
                                result = null;
                            } else {
                                result = (strs.join(', '));
                            }
                            return result;
                        };

                        $scope.saveSearch = function (moduleName) {
                            // $log.info('adminSaveSearchMixin.saveSearch ');
                            var uiString = $scope.generateUIString();

                            var pageName = $state.current.name;

                            // $scope.DS null coalesce
                            var searchParams = JSON.stringify($scope.filters || ($scope.DS !== null ? $scope.DS.filters : void 0));
                            // $log.info(searchParams, $scope.filters, ($scope.DS != null ? $scope.DS.filters : void 0), uiString);
                            var searchLayout;
                            var showAdv;
                            if ($scope.DS !== null) {
                                searchLayout = JSON.stringify($scope.DS.searchLayout ? $scope.DS.searchLayout : void 0);
                                showAdv = $scope.DS.advSearchVisible ? true : false;
                            }

                            var savedSearchVM = {
                                Name: '',
                                StateName: pageName,
                                SearchParameter: searchParams,
                                Favourite: false,
                                Module: moduleName,
                                UIString: uiString,
                                SearchLayout: searchLayout,
                                ShowAdv: showAdv
                            };
                            //$log.info('savedSearchVM');
                            //$log.info(savedSearchVM);

                            modalDialog.saveNewSearch(savedSearchVM);
                        };

                        $scope.getSavedSearches = function (moduleName) {

                            // get all of the saved searches and pass them to the modal dialog
                            lepApi.post('/SavedSearch/ListByModule', '"' + moduleName + '"')
                                .then(function (result) {
                                    if (result.isSuccess) {
                                        // show dialog with search results
                                        modalDialog.moduleSavedSearches(moduleName, result.Data);
                                    } else {
                                        toastr.info(result.SummaryError);
                                        // $scope.SummaryError = result.SummaryError;
                                        // $scope.renderElementErrors(result.Errors);
                                    }
                                });
                        };
                    };
                }
            ]);

})(window, window.angular);
