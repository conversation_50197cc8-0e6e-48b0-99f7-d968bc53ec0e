﻿using NHibernate;
using System;
using Microsoft.Extensions.DependencyInjection;

namespace LepCore
{
	public class NHDIInterceptor : EmptyInterceptor
	{
		private readonly IServiceProvider _serviceProvider;

		public NHDIInterceptor(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public T GetService<T>() => _serviceProvider.GetService<T>();

		public T GetRequiredService<T>() => _serviceProvider.GetRequiredService<T>();
	}
}
