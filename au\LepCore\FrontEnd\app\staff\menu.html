<ul class="nav nav-pills" ng-init="zu=[15476,17965,124175]">
	
	<li ng-if="(zu.indexOf($root.globals.User.Id) > -1) || $root.globals.User.IsSA" ><a ui-sref="staff.audit" ui-sref-active="active-nav">Audit</a></li>
	<li><a ui-sref="staff.orders" ui-sref-active="active-nav">Orders</a></li>
	<li><a ui-sref="staff.runs" ui-sref-active="active-nav">Runs</a></li>
	<li><a ui-sref="staff.customers" ui-sref-active="active-nav">Customers</a></li>
	<li><a ui-sref="staff.job-pricer" ui-sref-active="active-nav">Online Pricer</a></li>


	<li><a ui-sref="staff.dispatcher" 
		ui-sref-active="active-nav">Dispatcher</a></li>



	<li class="dropdown">
		<a href="#" class="dropdown-toggle" data-toggle="dropdown" ui-sref="staff.setup" ui-sref-active="active-nav">
			Setup<b class="caret"></b>
		</a>
		<ul class="dropdown-menu">

			<li ng-if="$root.globals.IsSA"><a ui-sref="staff.setup.staff-list">Staff</a></li>
			<li  ng-if="$root.globals.IsSA || $root.globals.IsA || $root.globals.IsMS"><a ui-sref="staff.setup.setup-promotions">Promotions</a></li>

			<li ng-if="$root.globals.IsSA || $root.globals.IsA || $root.globals.IsMS"><a ui-sref="staff.setup.offers">Customer offers </a></li>

			<li ng-if="$root.globals.IsSA || $root.globals.IsA  || $root.globals.IsMS"><a ui-sref="staff.setup.setup-prices">Price</a></li>

		</ul>
	</li>

	<li class="dropdown">
		<a href="#" class="dropdown-toggle   ng-class:{'active': $state.current.name.contains('staff.jobboard')}" data-toggle="dropdown">
			Job Boards<b class="caret"></b>
		</a>
		<ul class="dropdown-menu">
			<li><a ui-sref="staff.jobboard1fg">Forest Glen</a></li>
			<li><a ui-sref="staff.jobboard1pm">Port Melbourne</a></li>
			<li><a ui-sref="staff.jobboard2fg">Forest Glen - Projector</a></li>
			<li><a ui-sref="staff.jobboard2pm">Port Melbourne - Projector</a></li>

		</ul>
	</li>
	<li>
		<a title="Caton Label Printer" ng-click="cartonLabelPrinter()">
			
		</a>
	</li>
	<li class="dropdown">
		<a  class="dropdown-toggle" data-toggle="dropdown">
			<i class="glyphicon glyphicon-print"></i><b class="caret"></b>
		</a>
		<ul class="dropdown-menu">
			<li><a title="Caton Label Printer" ng-click="cartonLabelPrinter()">Carton Label</a></li>
			<li><a title="Caton Label Printer" ng-click="cartonLabelMailHousePrinter()">MailHouse Carton Label</a></li>
			<li><a title="Logo Label Printer" ng-click="logoLabelPrinter()">Logo Label</a></li>

		</ul>
	</li>
	<!--<li><a href="" ng-click="logout()" ui-sref-active="active-nav">logout</a></li>-->
</ul>
