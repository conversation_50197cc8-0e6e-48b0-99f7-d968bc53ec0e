﻿using System;
using System.Threading;
using System.Threading.Tasks;

namespace LepCore
{
	public class Throttle
	{
		private readonly TimeSpan _maxPeriod;
		private readonly SemaphoreSlim _throttleActions;
		private readonly SemaphoreSlim _throttlePeriods;

		public Throttle(int maxActions, TimeSpan maxPeriod)
		{
			_throttleActions = new System.Threading.SemaphoreSlim(maxActions, maxActions);
			_throttlePeriods = new System.Threading.SemaphoreSlim(maxActions, maxActions);
			_maxPeriod = maxPeriod;
		}

		public Task<T> Enqueue<T>(Func<T> action, System.Threading.CancellationToken cancel)
		{
			return _throttleActions.WaitAsync(cancel).ContinueWith<T>(t =>
			{
				try
				{
					_throttlePeriods.Wait(cancel);

					// Release after period
					// - Allow bursts up to maxActions requests at once
					// - Do not allow more than maxActions requests per period
					Task.Delay(_maxPeriod).ContinueWith((tt) =>
					{
						_throttlePeriods.Release(1);
					});

					return action();
				}
				finally
				{
					_throttleActions.Release(1);
				}
			});
		}
	}
}
