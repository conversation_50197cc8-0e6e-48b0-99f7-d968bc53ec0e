using lep;
using Microsoft.AspNetCore.Http;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace LepCore.Middlewares
{

	public class UserCountMiddleware
	{
		private readonly RequestDelegate _next;



		public UserCountMiddleware(RequestDelegate next)
		{
			_next = next;

		}

		public async Task Invoke(HttpContext context)
		{
			try
			{


				var u = context.User;
				if (u != null)
				{
					var name = u.Identity?.Name ?? "?";
					if (u.Claims != null)
					{
						var role = u.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Role)?.Value ?? "?";
						LepGlobal.Instance.LogAccess(name, role);
					}
				}
			}
			catch (Exception ex)
			{

				var m = ex.Message;
			}

			await _next.Invoke(context);
		}
	}

}
