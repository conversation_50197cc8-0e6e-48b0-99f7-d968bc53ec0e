﻿.container.jobboard {
	width: 100%;
	background-color: white;
}

#jobBoardPage {
	background-color: white;


	.select-board label {
		margin-right: 10px;
		font-size: larger;
	}


	.alertbox {
		font-size: 14pt;
		padding: 4px 14px;
		border-radius: 5px 5px 5px 5px;
		width: 100px;
		font-weight: bold;
		border: 1px solid silver;
		color: white;
		white-space: nowrap;
	}

	.alertbox.amberAlertB {
		background-color: rgb(228,109,10) !important;
	}

	.alertbox.redAlertB {
		background-color: rgb(230,4,0) !important;
	}
}


.nextDayDespatch td {
	background-color: rgb(167,228,176) !important;
}



#jobBoard.normal {
	width: 100%;
	font-size: 8pt;
	font-family: Verdana;
	border-collapse: collapse !important;

	th {
		white-space: nowrap;
	}

	td {
		max-width: 180px;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		border: 1px dotted silver;
		display: table-cell;
		padding: 2px 4px;

		.invalidArtwork {
			background-color: Yellow !important;
		}

		.nextDayDespatch {
			background-color: rgb(167,228,176) !important;
		}

		.sameDayDespatch {
			background-color: rgb(220,208,255) !important;
		}

		.trimsize90x55 {
			background-color: rgb(255,200,255) !important;
		}

		&.JID, &.OID, &.RID {
			cursor: pointer;
			max-width: 71px !important;
			font-weight: bold !important;
		}
	}

	tr:hover {
		background-color: yellowgreen;
	}

	tr.r {
		border-top: 2px solid gray;
	}

	tr.rcollapsed-group .JID {
		background: url("/images/jb_close.gif") no-repeat scroll left center transparent;
		padding-left: 20px !important;
	}

	tr.rexpanded-group-row td {
		background-color: #efefef;
		border-top: none !important;
		border-bottom: none !important;
	}

	tr.rexpanded-group .JID {
		background: url("/images/jb_open.gif") no-repeat scroll left center transparent;
		padding-left: 20px !important;
	}
	/* job production running late....has some sort of issue and should get attention */
	.amberAlert {
		*, a, a:visited, a:active {
			color: rgb(228,109,10) !important;
			text-decoration: none;
		}
	}
	/* job is really late and has passed max threshold of allowable time */
	.redAlert {
		*, a, a:visited, a:active {
			color: rgb(230,4,0) !important;
			text-decoration: none;
		}
	}

	.noAlert {
		background: white;

		*, a, a:visited, a:active {
			color: rgb(0,0,0) !important;
			text-decoration: none;
		}
	}



 

	tr.r {
		border-top: 2px solid gray;
	}

	tr.rexpanded-group .JID {
		padding-left: 20px !important;
	}

	tr.rcollapsed-group .JID {
		padding-left: 20px !important;
	}

	tr.rexpanded-group-row {
		background-color: #F0F0F0;
	}
}




#jobBoard.projector {
	color: black;
	width: 100%;
	font-size: 10pt;
	font-weight: bold;
	font-family: Verdana;
	border-collapse: collapse !important;

	th {
		white-space: nowrap;
		a {
			color: black !important;
		}
	}

	td, th {
		max-width: 150px;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		border: 1px dotted silver;
		display: table-cell;
		padding: 2px 2px;

		.invalidArtwork {
			background-color: Yellow !important;
		}

		.nextDayDespatch {
			background-color: rgb(167,228,176) !important;
		}

		.sameDayDespatch {
			background-color: rgb(220,208,255) !important;
		}

		.trimsize90x55 {
			background-color: rgb(255,200,255) !important;
		}

		&.JID, &.OID, &.RID {
			cursor: pointer;
			max-width: 71px !important;
			font-weight: bold !important;

			a {
				color: black;
			}
		}
	}
	/*next status*/
	td:nth-child(4),
	th:nth-child(4) {
		max-width: 100px !important;
	}
	/*next status*/
	td:nth-child(7),
	th:nth-child(7) {
		max-width: 70px !important;
	}
	/*next status*/
	td:nth-child(8),
	th:nth-child(8) {
		max-width: 70px !important;
		text-align: right;
	}


	tr:hover {
		background-color: yellowgreen;
	}

	tr.r {
		border-top: 2px solid gray;
	}

	tr.rcollapsed-group .JID {
		background: url("/images/jb_close.gif") no-repeat scroll left center transparent;
		padding-left: 20px !important;
	}

	tr.rexpanded-group-row td {
		background-color: #efefef;
		border-top: none !important;
		border-bottom: none !important;
	}

	tr.rexpanded-group .JID {
		background: url("/images/jb_open.gif") no-repeat scroll left center transparent;
		padding-left: 20px !important;
	}
	/* job production running late....has some sort of issue and should get attention */
	.amberAlert {
		*, a, a:visited, a:active {
			color: rgb(228,109,10) !important;
			text-decoration: none;
		}
	}
	/* job is really late and has passed max threshold of allowable time */
	.redAlert {
		*, a, a:visited, a:active {
			color: rgb(230,4,0) !important;
			text-decoration: none;
		}
	}

	.noAlert {
		background: white;

		*, a, a:visited, a:active {
			color: rgb(0,0,0) !important;
			text-decoration: none;
		}
	}



	tr:nth-child(odd) {
		/*background-color: rgba(210, 210, 210, 0.63);*/
	}

	tr:nth-child(even) {
		/*background-color: rgba(232, 232, 232, 0.29);*/
	}

	tr.r {
		border-top: 2px solid gray;
	}

	tr.rexpanded-group .JID {
		padding-left: 20px !important;
	}

	tr.rcollapsed-group .JID {
		padding-left: 20px !important;
	}

	tr.rexpanded-group-row {
		background-color: #F0F0F0;
	}
}
