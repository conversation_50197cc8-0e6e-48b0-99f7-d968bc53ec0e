﻿<!DOCTYPE html>
<!--[if lt IE 7]>
<html lang="en" ng-app="app" class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>
<html lang="en" ng-app="app" class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>
<html lang="en" ng-app="app" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!-->
<html lang="en" ng-app="app" class="no-js">
<!--<![endif]-->
<head>
	<!-- Google Tag Manager -->
	<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window, document, 'script', 'dataLayer', 'GTM-T7BRRXF');</script>

    <!-- End Google Tag Manager -->

    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge, chrome=1" />
    <!--<meta name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no" />-->

    <style>
        .ng-hide {
            display: none !important;
        }

        [ng\:cloak], [ng-cloak], [data-ng-cloak], [x-ng-cloak], .ng-cloak, .x-ng-cloak {
            display: none !important;
        }
    </style>
    <script>

        // Must be first. IE10 mobile viewport fix
        if ("-ms-user-select" in document.documentElement.style && navigator.userAgent.match(/IEMobile\/10\.0/)) {
            var msViewportStyle = document.createElement("style");
            var mq = "@@-ms-viewport{width:auto!important}";
            msViewportStyle.appendChild(document.createTextNode(mq));
            document.getElementsByTagName("head")[0].appendChild(msViewportStyle);
        }
    </script>

    <!-- bower:css -->
    <!-- endinject -->
    <!-- inject:css -->
    <!-- endinject -->

    <style>
        @font-face {
            font-family: Myriad;
            src: url('/fonts/MyriadPro-Regular.otf') format('opentype');
        }

        @font-face {
            font-family: 'Conv_EnzoOT-Medi';
            src: url('/fonts/EnzoOT-Medi.eot');
            src: local('☺'), url('/fonts/EnzoOT-Medi.woff') format('woff'), url('/fonts/EnzoOT-Medi.ttf') format('truetype'), url('/fonts/EnzoOT-Medi.svg') format('svg');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'Conv_EnzoOT-MediIta';
            src: url('/fonts/EnzoOT-MediIta.eot');
            src: local('☺'), url('/fonts/EnzoOT-MediIta.woff') format('woff'), url('/fonts/EnzoOT-MediIta.ttf') format('truetype'), url('/fonts/EnzoOT-MediIta.svg') format('svg');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'Conv_EnzoOT-Bold';
            src: url('/fonts/EnzoOT-Bold.eot');
            src: local('☺'), url('/fonts/EnzoOT-Bold.woff') format('woff'), url('/fonts/EnzoOT-Bold.ttf') format('truetype'), url('/fonts/EnzoOT-Bold.svg') format('svg');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'Conv_EnzoOT-BoldIta';
            src: url('/fonts/EnzoOT-BoldIta.eot');
            src: local('☺'), url('/fonts/EnzoOT-BoldIta.woff') format('woff'), url('/fonts/EnzoOT-BoldIta.ttf') format('truetype'), url('/fonts/EnzoOT-BoldIta.svg') format('svg');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'ui-grid';
            src: url('ui-grid.eot');
            src: url('/fonts/ui-grid.eot#iefix') format('embedded-opentype'), url('/fonts/ui-grid.woff') format('woff'), url('/fonts/ui-grid.ttf') format('truetype'), url('/fonts/ui-grid.svg?#ui-grid') format('svg');
            font-weight: normal;
            font-style: normal;
        }
    </style>
    <title>...</title>
    <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
</head>


<body class="ng-cloak ng-class:{'login': $state.current.class == 'login'}" ng-controller="RootController">
	<div class="container" id="mc">
		<div ui-view>
		</div>
	</div>

	<script src="https://www.paypalobjects.com/api/checkout.js"></script>
	<script type="text/javascript" src="https://checkout.stripe.com/checkout.js"></script>
	<!-- bower:js -->
	<!-- endinject -->
	<!-- inject:js -->
	<!-- endinject -->
	<!-- appcore:js -->
	<!-- endinject -->
	<!-- tplcore:js -->
	<!-- endinject -->
	<div class="iframeContainer"></div>


</body>
</html>
