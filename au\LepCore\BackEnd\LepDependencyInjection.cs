using lep;
using lep.barcode;
using lep.barcode.impl;
using lep.configuration;
using lep.configuration.impl;
using lep.content;
using lep.content.impl;
using lep.courier;
using lep.courier.impl;
using lep.despatch.impl;
using lep.email;
using lep.email.impl;
using lep.freight;
using lep.freight.impl;
using lep.job;
using lep.job.impl;
using lep.jobmonitor;
using lep.jobmonitor.impl;
using lep.macro;
using lep.macro.impl;
using lep.order;
using lep.order.impl;
using lep.pricing;
using lep.pricing.impl;
using lep.promotion;
using lep.promotion.impl;
using lep.run;
using lep.run.impl;
using lep.security;
using lep.security.impl;
using lep.user;
using lep.user.impl;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Concurrent;
using System.IO;
using System.ServiceModel;

namespace LepCore.Setup
{
	/*
	Change the lifestyle of the component to a lifestyle that is as short or shorter than that of the dependency.

	Change the lifestyle of the dependency to a lifestyle as long or longer than that of the component.

	Instead of injecting the dependency, inject a factory for the creation of that dependency and call that
	factory every time an instance is required.
	*/

	public static class LepDependencyInjection
	{
		public static void AddLepDependencyInjection(this IServiceCollection services, IConfigurationRoot configuration)
		{
			Func<string, DirectoryInfo> createIfNotExists = (path) =>
			{
				try
				{
					return new DirectoryInfo(path);
				}
				catch (Exception ex)
				{
					var m = ex.Message;
				}
				return null;
			};

			LepGlobal.Instance.TestBox = bool.Parse(configuration["TestBox"]);
			
			var dataDir = configuration["DataDirectory"];
			LepGlobal.Instance.DataDirectory = createIfNotExists(configuration["DataDirectory"]);
			LepGlobal.Instance.InvoicerPDFFolder = createIfNotExists(configuration["InvoicerPDFFolder"]).FullName;
			LepGlobal.Instance.OldDataDirectory = createIfNotExists(configuration["OldDataDirectory"]);
			LepGlobal.Instance.DataDirectoryPc = createIfNotExists(configuration["DataDirectoryPc"]).FullName;
			LepGlobal.Instance.DataDirectoryMac = configuration["DataDirectoryMac"];
			LepGlobal.Instance.AppRootURL = configuration["AbsolutePathURL"];
			LepGlobal.Instance.AbsolutePathURL = configuration["AbsolutePathURL"];
			LepGlobal.Instance.LepCrmWebserviceUrl = configuration["lepcrm.webservice.url"];
			LepGlobal.Instance.CompDataWebservieUrl = configuration["compdata.webservice.url"];
			LepGlobal.Instance.CustomerLogoDirectory = createIfNotExists(configuration["CustomerLogoDirectory"]);

			
			string jobOptionCSVFolder = Path.Combine(Path.Combine(configuration["StaticAssets"], "jobOptionCSVs"));
			LepGlobal.Instance.ImportSpecFolds = new FileInfo(jobOptionCSVFolder + @"\specs-folds.csv");
			LepGlobal.Instance.ImportSpecType = new FileInfo(jobOptionCSVFolder + @"\specs-type.csv");
			LepGlobal.Instance.ImportSpecPrint = new FileInfo(jobOptionCSVFolder + @"\specs-print.csv");
			LepGlobal.Instance.ImportSpecRange = new FileInfo(jobOptionCSVFolder + @"\specs-range.csv");
			
			LepGlobal.Instance.SupplyMasterConnectionString = configuration["SupplyMaster:Con"];

			LepGlobal.Instance.CourierURL_StarTrack = "https://startrack.com.au/";
			LepGlobal.Instance.CourierURL_FastWay = "https://www.fastway.com.au/tools/track/";
			LepGlobal.Instance.CourierURL_AustPOST = "https://auspost.com.au/mypost/track/#/search";
			LepGlobal.Instance.CourierURL_TNT = "http://www.tnt.com/express/en_au/site/home.html";
			LepGlobal.Instance.CourierURL_CouriersPlease = "https://www.couriersplease.com.au/tools-track";

			LepGlobal.Instance.CourierURL_TOLLNQX = "https://www.mytoll.com/?externalSearchQuery=";

			LepGlobal.Instance.CourierURL_ARAMEX = "https://www.aramex.com.au/tools/track?l=";

			var baseUrl = configuration["AbsolutePathURL"] + "/images/couriers/";
			LepGlobal.Instance.CourierImage_StarTrack = baseUrl + "startrack.png";
			LepGlobal.Instance.CourierImage_FastWay = baseUrl + "fastway.png";
			LepGlobal.Instance.CourierImage_AustPOST = baseUrl + "auspost.png";
			LepGlobal.Instance.CourierImage_TNT = baseUrl + "tnt.png";
			LepGlobal.Instance.CourierImage_CouriersPlease = baseUrl + "COURIERS PLS II.png";
			LepGlobal.Instance.CourierImage_TOLLNQX = baseUrl + "toll nqx.png";
			LepGlobal.Instance.CourierImage_ARAMEX = baseUrl + "aramex.png";

			LepGlobal.Instance.SmtpServer = configuration["email:server"];
			LepGlobal.Instance.SmtpPort = int.Parse(configuration["email:port"]);
			LepGlobal.Instance.SmtpUsername = configuration["email:username"];
			LepGlobal.Instance.SmtpPassword = configuration["email:password"];
			LepGlobal.Instance.SendMail = bool.Parse(configuration["email:sendMail"]);

			LepGlobal.Instance.LabelsLogoLabel = configuration["Labels:LogoLabel"];
			LepGlobal.Instance.LabelsPayMeLabel = configuration["Labels:PayMeLabel"];
			LepGlobal.Instance.LabelsFillingLabel = configuration["Labels:FillingLabel"];
			LepGlobal.Instance.LabelsAddressA4Label = configuration["Labels:AddressA4Label"];
			LepGlobal.Instance.LabelsSampleLabel = configuration["Labels:SampleLabel"];
			LepGlobal.Instance.LabelsPickupLabel = configuration["Labels:PickupLabel"];

			LepGlobal.Instance.GhostScriptPath = configuration["GhostScript"];
			LepGlobal.Instance.PdfTkPath = configuration["PdfTk"];
			LepGlobal.Instance.LibTiffPath = configuration["LibTiff"];
			LepGlobal.Instance.PDF2PrinterPath = configuration["PdfToPrinter"];

			LepGlobal.Instance.GhostscriptDllPath = configuration["StaticAssets"];
			
 
			services.AddSingleton<FileDetector>();
			services.AddSingleton<LepGlobal>(sp => LepGlobal.Instance);
			services.AddSingleton<JobBoard>(sp =>  new JobBoard());

			services.AddSingleton<FoldFactorFinder>(sp => new FoldFactorFinder(jobOptionCSVFolder));
			services.AddSingleton<CartonFinder>(sp => new CartonFinder(jobOptionCSVFolder));
			
			services.AddSingleton<StandardRouting>(sp =>  StandardRouting.Instance);

			services.AddSingleton<WiroMagazineValues>(sp => new WiroMagazineValues(jobOptionCSVFolder));


			// the folowing have been made Scoped as their dependency ISession is scoped
			// Transient is not required

			services.AddScoped<IUserApplication, UserApplication>();

			services.AddScoped<ISecurityApplication>(sp =>
			{
				var _ = new MVCCustomerSecurityApplication(
					 sp.GetRequiredService<IHttpContextAccessor>(),
					 sp.GetRequiredService<NHibernate.ISession>(),
					null
			  );
				return _;
			});

			services.AddScoped<BaseApplication>(sp =>
			{
				var _ = new BaseApplication(
					sp.GetRequiredService<NHibernate.ISession>(),
					sp.GetRequiredService<ISecurityApplication>()
			  );
				return _;
			});

			services.AddScoped<IConfigurationApplication, ConfigurationApplication>();

			services.AddScoped<JobBoardDTOHelper, JobBoardDTOHelper>();
			services.AddScoped<IScheduleApplication, ScheduleApplication>();

			var jobArtworkScripts =  Path.Combine(configuration["StaticAssets"], "jobArtworkScripts");
			services.AddSingleton<LepArtworkScriptFiles>(_ => new LepArtworkScriptFiles(jobArtworkScripts));

			services.AddScoped<IJobApplication, JobApplication>();
			services.AddScoped<IOrderApplication, OrderApplication>();
			services.AddScoped<IRunApplication, RunApplication>();

			services.AddScoped<IPromotionApplication, PromotionApplication>();
			services.AddScoped<IBarcodeService, BarcodeApplication>();
			services.AddScoped<ITemplateApplication, TemplateApplication>();

			services.AddScoped<RunEngine>();

			services.AddScoped<IPricingEngine, PricingEngine>();
			services.AddScoped<IPricePointApplication, PricePointApplication>();
			services.AddScoped<IPackageApplication, PackageApplication>();
			services.AddScoped<IFreightApplication, FreightApplication>();

			services.AddScoped<IEmailApplication, EmailApplication>();

			services.AddScoped<LabelPrinterApplication>();
			services.AddScoped<PrintEngine, PrintEngine>();
			services.AddScoped<IContentApplication, ContentApplication>();

			services.AddScoped<ConsignmentNotesMailerApplication, ConsignmentNotesMailerApplication>();

			services.AddSingleton<CachedAccessTo>();

			switch (configuration["FreightProvider"])
			{
				case "CompData":

					//services.AddSingleton(sp =>
					//{
					//	var url = configuration["compdata.webservice.url"];
					//	return ChannelFactory<lep.CompData.IiFreightChargeEnquiryService>
					//			.CreateChannel(GetBasicWSBinding(), new EndpointAddress(url));
					//});
					//services.AddScoped<ICourierApplication, CourierApplication>();
					break;
				case "SmartFreight":
					services.AddScoped<ICourierApplication, CourierApplicationSF>();
					services.AddSingleton(sp =>
					{
						var url = configuration["SmartFreight:Url:SFOv1"];
						BasicHttpBinding myBinding = new BasicHttpBinding();
						// return ChannelFactory<lep.SmartFreight.SFOv1>
						//          .CreateChannel(GetBasicWSBinding(), new  System.ServiceModel.EndpointAddress(url));

						var factory = new System.ServiceModel.ChannelFactory<lep.SmartFreight.SFOv1>(GetBasicWSBinding(),
							new System.ServiceModel.EndpointAddress(new Uri(url)));

						//factory.Credentials.ServiceCertificate.SslCertificateAuthentication = new System.ServiceModel.Security.X509ServiceCertificateAuthentication
						//{
						//	CertificateValidationMode = System.ServiceModel.Security.X509CertificateValidationMode.None
						//};
						var channel = factory.CreateChannel();

						return channel;


					});

					services.AddSingleton(sp =>
					{
						var url = configuration["SmartFreight:Url:DeliveryOptions"];
						// return ChannelFactory<lep.SmartFreight.ISmartFreightDeliveryOptions>
						//             .CreateChannel(GetBasicWSBinding(), new  System.ServiceModel.EndpointAddress(url));

						var factory = new System.ServiceModel.ChannelFactory<lep.SmartFreight.ISmartFreightDeliveryOptions>(GetBasicWSBinding(),
							new System.ServiceModel.EndpointAddress(new Uri(url)));

						factory.Credentials.ServiceCertificate.SslCertificateAuthentication = new System.ServiceModel.Security.X509ServiceCertificateAuthentication
						{
							CertificateValidationMode = System.ServiceModel.Security.X509CertificateValidationMode.None
						};
						var channel = factory.CreateChannel();

						return channel;
					});
					break;
			}
		}


		private static System.ServiceModel.BasicHttpBinding GetBasicWSBinding()
		{
			System.Net.ServicePointManager.Expect100Continue = false;

			var binding = new System.ServiceModel.BasicHttpBinding()
			{
				Security =
					{
						Mode = BasicHttpSecurityMode.Transport,
						Transport =
						{
							ClientCredentialType = HttpClientCredentialType.None
						}
					},
				OpenTimeout = TimeSpan.FromSeconds(200),
				CloseTimeout = TimeSpan.FromSeconds(100),
				SendTimeout = TimeSpan.FromSeconds(200),
				ReceiveTimeout = TimeSpan.FromSeconds(100),
				MaxBufferPoolSize = long.MaxValue,
				MaxBufferSize = int.MaxValue,
				MaxReceivedMessageSize = int.MaxValue
			};

			return binding;
		}
	}
}
