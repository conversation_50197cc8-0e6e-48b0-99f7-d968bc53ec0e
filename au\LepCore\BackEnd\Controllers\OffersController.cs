using AutoMapper;
using lep.promotion;
using lep.user;
using LepCore.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NHibernate.Linq;
using System;
using System.Collections.Generic;
using System.Linq;

namespace LepCore.src.Controllers
{


	[ApiExplorerSettings(IgnoreApi = true)]
	[Authorize(Roles = LepRoles.Staff)]
	[Produces("application/json")]
	[Route("api/[controller]")]
	public class OffersController : Controller
	{
		private NHibernate.ISession _session;
		private IMapper _mapper;

		public OffersController(NHibernate.ISession session, IMapper mapper)
		{
			_session = session;
			_mapper = mapper;
		}

		[HttpGet("GetPromotions")]
		public dynamic GetPromotions([FromQuery]string search)
		{
			var query = _session.Query<IPromotion>();

			query = query.Where(x => x.CheckCustomerAgainstCampaign);
			if (!string.IsNullOrEmpty(search))
			{
				query = query.Where(x => SqlMethods.Like(x.PromotionCode, $"%{search}%"));
			}

			var result = query
							.OrderBy(x => x.PromotionCode)
							.Select(p => new
							{
								Id = p.Id,
								Name = p.ShortDescription,
								PromotionCode = p.PromotionCode
							});

			return result;
		}

		[HttpGet("GetCustomers")]
		public dynamic GetCustomers([FromQuery]string search)
		{
			var query = _session.Query<ICustomerUser>()
							.Where(x => x.IsEnabled == true);

			if (!string.IsNullOrEmpty(search))
			{
				query = query.Where(x => SqlMethods.Like(x.Name, $"{search}%") ||
											SqlMethods.Like(x.Username, $"{search}%") // ||
																					  //SqlMethods.Like(x.Contact1.Name, $"%{search}%") ||
																					  //SqlMethods.Like(x.Contact2.Name, $"%{search}%")
				);
			}

			var result = query
							.OrderBy(x => x.Name)
							.Take(20)
							.Select(p => new
							{
								Id = p.Id,
								Name = p.Name,
								Username = p.Username
							});

			return result;
		}

		[HttpGet("Search")]
		public dynamic Search([FromQuery] int p, [FromQuery] List<int> c)
		{
			var query = _session.Query<CustomerOffer>();

			if (p != 0)
			{
				query = query.Where(x => x.Promotion.Id == p);
			}

			if (c != null && c.Any())
			{
				query = query.Where(x => c.Contains(x.Customer.Id));
			}

			var result0 = query
							.OrderBy(x => x.Promotion.PromotionCode)
							.ThenBy(x => x.Customer.Name)
							.ToList();
			var result = result0.Select(_ => _mapper.Map<CustomerOfferDto>(_)).ToList();
			return result0;
		}

		[HttpPost("Create2")]
		public IActionResult Save2([FromBody] AddCustomerOfferDto dto)
		{
			var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
			var user = _session.Get<IStaff>(userId);

			var promo = _session.Get<IPromotion>(dto.Promotion.Id);

			foreach (var cx in dto.Customers)
			{
				var custId = cx.Id;

				var cust = _session.Get<ICustomerUser>(custId);

				var offer = new CustomerOffer();

				offer.Customer = cust;
				offer.Promotion = promo;

				var now = DateTime.Now.Date;

				offer.DateCreated = now;
				offer.DateModified = now;
				offer.CreatedBy = user.FirstName + " " + user.LastName;
				offer.ModifiedBy = user.FirstName + " " + user.LastName;

				offer.DateOffered = dto.DateOffered;
				offer.DateOfferEnds = dto.DateOfferEnds;
				offer.DateTakenUp = null;

				offer.AllowReuse = dto.AllowReuse;

				//if (promo.LifeSpan == PromotionLifeSpan.Windowed) {
				//	offer.DateOfferEnds = now.AddDays(promo.Window);
				//} else {
				//	offer.DateOffered = promo.DateValidStart?.Date ?? now;
				//	offer.DateOfferEnds = promo.DateValidEnd?.Date ?? now;

				//}
				_session.SaveOrUpdate(offer);
			}

			return Ok();
		}

		[HttpPost("{id:int}/delete")]
		public IActionResult Delete(int id)
		{
			using (var tx = _session.BeginTransaction())
			{
				_session.Query<CustomerOffer>().Where(o => o.Id == id).Delete();
				tx.Commit();
			}

			return Ok();
		}

		[HttpPost("{id:int}/update")]
		public IActionResult Update([FromRoute]int id, [FromBody] CustomerOfferUpdateDto dto)
		{
			var o = _session.Get<CustomerOffer>(id);

			using (var tx = _session.BeginTransaction())
			{
				_mapper.Map(dto, o);
				_session.SaveOrUpdate(o);
				tx.Commit();
			}

			return Ok();
		}
	}
}
