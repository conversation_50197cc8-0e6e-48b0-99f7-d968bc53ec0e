var app = angular.module('app');

app.constant('templateTree', {

	// str category name -> str path
	image: function(category) {
		var img = {
			'Business card'                          : 'Business-Cards.svg',
			'Brochures and flyers'                   : 'brochure-folded.svg',
			'Magazines/ Booklet'                     : 'Magazines-Booklets.svg',
			'Magazines/ Booklet Separate Cover'      : 'Magazines-Booklets.svg',
			'Wiro Magazines'                         : 'Wiro2.svg',
			'Stationery'                             : 'Stationery.svg',
			'Presentation Folders'                   : 'Presentation-Folders.svg',
			'Calenders'                              : 'calendar.svg',
			'Calendars/Greeting Cards'               : 'calendar.svg',
			'Golf Score Cards'                       : 'Golf-Score-Cards.svg',
			'Notepads/Deskpads'                      : 'Notepads-Deskpads.svg',
			'Postcards'                              : 'Postcards.svg',
			'Envelopes'                              : 'Postcards.svg',
			'Posters'                                : 'Posters.svg',
			'Banners/Pull Ups'                       : 'BannersPullUps.svg',
			'Adhesive Signs, Rigid Signs & Stickers' : 'AdhesiveSignsStickers.svg',
			'NCR Books'                              : 'NCRBooks.svg',
			''                                       : '',
		};
		return function(category) {
			return '/images/ProductIcon2/' + img[category];
		};
	},


	// str category name -> str path
	links: function(tid) {
		var publicSiteShortName = {
			'Business card'                     : 'business-cards',
			'Brochures and flyers'              : 'brochures-flyers',
			'Magazines/ Booklet'                : 'magazines',
			'Magazines/ Booklet Separate Cover' : 'magazines',
			'Wiro Magazines'                    : 'wiro_magazines',
			'Stationery'                        : 'stationery',
			'Presentation Folders'              : 'presentation-folders',
			'Calenders'                         : '',
			'Calendars/Greeting Cards'          : '',
			'Golf Score Cards'                  : 'cards',
			'Notepads/Deskpads'                 : 'stationery',
			'Postcards'                         : 'cards',
			'Posters'                           : 'posters',
			''                                  : '',
		};
		return function(tid) {
			var base = 'http://www.lepcolourprinters.com.au/support-centre/artwork-templates/#';
			var aglink = base + publicSiteShortName[tid];
			var atlink = base + publicSiteShortName[tid];
			var result = [
				{
					link: 'Artwork Guidelines',
					href: aglink
				},
				{
					link: 'Artwork Templates',
					href: atlink
				}
			];
			return result;
		};
	},


	// obj -> job option ids contained within
	Ids: function(obj) {
		if (!obj) return [];
		var r = [];
		JSON.parse(JSON.stringify(obj),
			function(k, v) {
				if (typeof v == 'number') {
					r.push(v);
				}
			});
		return r;
	},

	IdsFromCategory: function(cat) {
		return this.Ids(this.topdown.Category[cat]);
	},

	topdown: {
		'Category': {

			'Business card': {
				'Production': {
					'Standard': 1,
					'Next day': 26,
					'Same day': 27,
				}
			},
			'Brochures and flyers': {
				'Production': {
					'Standard': 7,
					'Next day': 18,
					'Same day': 23,
				}
			},

			'Adhesive Signs, Rigid Signs & Stickers': {
				'Production': {
					'Standard': {
						'Product': {
							'Vinyl Sticker': 41,
							'Removable Wall Decals': 42,
							//'Vinyl Sticker Outdoor': 43,
							'Rigid Signs': 44,
						}
					}
				}
			},

			'Banners/Pull Ups': {
				'Production': {
					'Standard': {
						'Product': {
							'Pull Up Banner (Standard Stand)': 37,
							//'Pull Up Banner (Premium Stand)': 38,
							'Backlit Posters': 32,
							'Mesh Banner with eyelets': 33,
							'Vinyl Outdoor': 34,
							//'Poster Matt Art': 35,
							//'Poster Canvas': 36,
						}
					}
				}
			},


			'Calendars/Greeting Cards':{
				'Production': {
					'Standard': {
						'Product': {
							'A4 Calendar Self Cover': 71,
							'A4 Calendar Separate Cover': 72,
							'Tent Calendars' : 73,
							'DL Calendars': 74,
							'Greeting Cards': 75,
						}
					}
				}
			},


			'Envelopes': {
				'Production': {
					'Standard': {
						'Product': {
							'Envelope Black' : 60,
							'Envelope 1PMS' : 61,
							'Envelope 2PMS'	: 62,
							'Envelope CMYK'	: 63,
						}
					},
				}
			},
			'Golf Score Cards': 15,

			'Notepads/Deskpads': 16,


			'NCR Books': {
				'Production': {
					'Standard': {
						'Product': {
							'Duplicate NCR Books': 51,
							'Triplicate NCR Books ': 52,
							'Quadruplicate NCR Books': 53,
						}
					}
				}
			},

			'Magazines/ Booklet': {
				'Production': {
					'Standard': 8,
					'Next day': 22,
				}
			},
			'Magazines/ Booklet Separate Cover': 14,
			'Wiro Magazines': 76,
			'Stationery': {
				'Production': {
					'Standard': 28,
					'Same day': 29,
				}
			},
			'Presentation Folders': {
				'Production': {
					'Standard': 9,
					'Next day': 21,
				}
			},
			'Posters': 10,
			'Postcards': 11,


			'Other Products': 12,
		}
	},

	// Int JobOptionId -> {Category, Service} hash
	bottomup: {

		1: { 'Category': 'Business card', 'Production': 'Standard' },
		26: { 'Category': 'Business card', 'Production': 'Next day' },
		27: { 'Category': 'Business card', 'Production': 'Same day' },
		7: { 'Category': 'Brochures and flyers', 'Production': 'Standard' },
		18: { 'Category': 'Brochures and flyers', 'Production': 'Next day' },
		23: { 'Category': 'Brochures and flyers', 'Production': 'Same day' },
		8: { 'Category': 'Magazines/ Booklet', 'Production': 'Standard' },
		22: { 'Category': 'Magazines/ Booklet', 'Production': 'Next day' },
		14: { 'Category': 'Magazines/ Booklet Separate Cover' },
		76: { 'Category': 'Wiro Magazines' , 'Production': 'Standard'},
		28: { 'Category': 'Stationery', 'Production': 'Standard' },
		29: { 'Category': 'Stationery', 'Production': 'Same day' },
		9: { 'Category': 'Presentation Folders', 'Production': 'Standard' },
		21: { 'Category': 'Presentation Folders', 'Production': 'Next day' },
		10: { 'Category': 'Posters' },
		11: { 'Category': 'Postcards' },
		15: { 'Category': 'Golf Score Cards' },
		16: { 'Category': 'Notepads/Deskpads' },
		12: { 'Category': 'Other Products' },

		32: { 'Category': 'Banners/Pull Ups', 'Production': 'Standard', 'Product': 'Backlit Posters' },
		33: { 'Category': 'Banners/Pull Ups', 'Production': 'Standard', 'Product': 'Mesh Banner with eyelets' },
		34: { 'Category': 'Banners/Pull Ups', 'Production': 'Standard', 'Product': 'Vinyl Outdoor' },
        //35: { 'Category': 'Banners/Pull Ups', 'Production': 'Standard', 'Product': 'Poster Matt Art' },
		//36: { 'Category': 'Banners/Pull Ups', 'Production': 'Standard', 'Product': 'Poster Canvas' },
		37: { 'Category': 'Banners/Pull Ups', 'Production': 'Standard', 'Product': 'Pull Up Banner (Standard Stand)' },
		//38: { 'Category': 'Banners/Pull Ups', 'Production': 'Standard', 'Product': 'Pull Up Banner (Premium Stand)' },

		41: { 'Category': 'Adhesive Signs, Rigid Signs & Stickers', 'Production': 'Standard', 'Product': 'Vinyl Sticker' },
		42: { 'Category': 'Adhesive Signs, Rigid Signs & Stickers', 'Production': 'Standard', 'Product': 'Removable Wall Decals' },
		//43: { 'Category': 'Adhesive Signs, Rigid Signs & Stickers', 'Production': 'Standard', 'Product': 'Vinyl Sticker Outdoor' },
		44: { 'Category': 'Adhesive Signs, Rigid Signs & Stickers', 'Production': 'Standard', 'Product': 'Rigid Signs' },
		51: { 'Category': 'NCR Books', 'Production': 'Standard', 'Product': 'Duplicate NCR Books' },
		52: { 'Category': 'NCR Books', 'Production': 'Standard', 'Product': 'Triplicate NCR Books ' },
		53: { 'Category': 'NCR Books', 'Production': 'Standard', 'Product': 'Quadruplicate NCR Books' },

		60: { 'Category': 'Envelopes', 'Production': 'Standard', 'Product' : 'Envelope Black'},
		61: { 'Category': 'Envelopes', 'Production': 'Standard', 'Product' : 'Envelope 1PMS'},
		62: { 'Category': 'Envelopes', 'Production': 'Standard', 'Product' : 'Envelope 2PMS'},
		63: { 'Category': 'Envelopes', 'Production': 'Standard', 'Product' : 'Envelope CMYK'},

		71: {'Category': 'Calendars/Greeting Cards', 'Production': 'Standard', 'Product' : 'A4 Calendar Self Cover'},
		72: {'Category': 'Calendars/Greeting Cards', 'Production': 'Standard', 'Product' : 'A4 Calendar Separate Cover'},

		73: {'Category': 'Calendars/Greeting Cards', 'Production': 'Standard', 'Product' : 'Tent Calendars'},
		74: {'Category': 'Calendars/Greeting Cards', 'Production': 'Standard', 'Product' : 'DL Calendars'},
		75: {'Category': 'Calendars/Greeting Cards', 'Production': 'Standard', 'Product' : 'Greeting Cards'},



	}
});
