﻿// using NHibernate.Cfg;
// using System.Reflection;
// using System.IO;
// using System.Runtime.Serialization.Formatters.Binary;

// namespace LepCore
// {
// 	// public class NHConfigurationFileCache
// 	{
// 		private readonly string _cacheFile;
// 		private readonly Assembly _definitionsAssembly;

// 		public NHConfigurationFileCache(Assembly definitionsAssembly)
// 		{
// 			_definitionsAssembly = definitionsAssembly;
// 			_cacheFile = "nh.cfg";
// 			//if (HttpContext.Current != null) //for the web apps
// 			//	_cacheFile = HttpContext.Current.Server.MapPath(
// 			//					string.Format("~/App_Data/{0}", _cacheFile)
// 			//					);
// 			var path = Path.GetDirectoryName(definitionsAssembly.Location);
// 			_cacheFile = Path.Combine(path, _cacheFile);
// 		}

// 		public void DeleteCacheFile()
// 		{
// 			if (File.Exists(_cacheFile))
// 				File.Delete(_cacheFile);
// 		}

// 		public bool IsConfigurationFileValid
// 		{
// 			get
// 			{
// 				if (!File.Exists(_cacheFile))
// 					return false;
// 				var configInfo = new FileInfo(_cacheFile);
// 				var asmInfo = new FileInfo(_definitionsAssembly.Location);

// 				if (configInfo.Length < 5 * 1024)
// 					return false;

// 				return configInfo.LastWriteTime >= asmInfo.LastWriteTime;
// 			}
// 		}

// 		public void SaveConfigurationToFile(Configuration configuration)
// 		{
// 			using (var file = File.Open(_cacheFile, FileMode.Create))
// 			{
// 				var bf = new BinaryFormatter();
// 				bf.Serialize(file, configuration);
// 			}
// 		}

// 		public Configuration LoadConfigurationFromFile()
// 		{
// 			if (!IsConfigurationFileValid)
// 				return null;

// 			using (var file = File.Open(_cacheFile, FileMode.Open, FileAccess.Read))
// 			{
// 				var bf = new BinaryFormatter();
// 				return bf.Deserialize(file) as Configuration;
// 			}
// 		}
// 	}


// 	//public class NHibernateToMicrosoftLogger : INHibernateLogger
// 	//{
// 	//	private readonly ILogger _msLogger;

// 	//	public NHibernateToMicrosoftLogger(ILogger msLogger)
// 	//	{
// 	//		_msLogger = msLogger ?? throw new ArgumentNullException(nameof(msLogger));
// 	//	}

// 	//	private static readonly Dictionary<NHibernateLogLevel, LogLevel> MapLevels = new Dictionary<NHibernateLogLevel, LogLevel>
// 	//	{
// 	//		{ NHibernateLogLevel.Trace, LogLevel.Trace },
// 	//		{ NHibernateLogLevel.Debug, LogLevel.Debug },
// 	//		{ NHibernateLogLevel.Info, LogLevel.Information },
// 	//		{ NHibernateLogLevel.Warn, LogLevel.Warning },
// 	//		{ NHibernateLogLevel.Error, LogLevel.Error },
// 	//		{ NHibernateLogLevel.Fatal, LogLevel.Critical },
// 	//		{ NHibernateLogLevel.None, LogLevel.None },
// 	//	};

// 	//	public void Log(NHibernateLogLevel logLevel, NHibernateLogValues state, Exception exception)
// 	//	{
// 	//		_msLogger.Log(MapLevels[logLevel], 0, new FormattedLogValues(state.Format, state.Args), exception, MessageFormatter);
// 	//	}

// 	//	public bool IsEnabled(NHibernateLogLevel logLevel)
// 	//	{
// 	//		return _msLogger.IsEnabled(MapLevels[logLevel]);
// 	//	}

// 	//	private static string MessageFormatter(object state, Exception error)
// 	//	{
// 	//		return state.ToString();
// 	//	}
// 	//}

// 	//public class NHibernateToMicrosoftLoggerFactory : INHibernateLoggerFactory
// 	//{
// 	//	private readonly Microsoft.Extensions.Logging.ILoggerFactory _loggerFactory;

// 	//	public NHibernateToMicrosoftLoggerFactory(Microsoft.Extensions.Logging.ILoggerFactory loggerFactory)
// 	//	{
// 	//		_loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
// 	//	}

// 	//	public INHibernateLogger LoggerFor(string keyName)
// 	//	{
// 	//		var msLogger = _loggerFactory.CreateLogger(keyName);
// 	//		return new NHibernateToMicrosoftLogger(msLogger);
// 	//	}

// 	//	public INHibernateLogger LoggerFor(System.Type type)
// 	//	{
// 	//		return LoggerFor(
// 	//			Microsoft.Extensions.Logging.Abstractions.Internal.TypeNameHelper.GetTypeDisplayName(type));
// 	//	}
// 	//}
// }
