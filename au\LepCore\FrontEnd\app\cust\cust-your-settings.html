﻿<div class="container your-settings">
	<form class="form-horizontal">
		<div leppane="Basic Info" visible="true">

			<!-- <legend>Basic Information</legend> -->
			<div class="form-group form-group-lg">
				<label class="control-label col-sm-4">Customer * </label>
				<div class="col-sm-5 form-control-static">
					{{vm.Name}}

				</div>
			</div>

			<div class="form-group form-group-sm">
				<label class="control-label col-sm-4">ABN *</label>
				<div class="col-sm-5 form-control-static">
					{{vm.ABN}}

				</div>
			</div>

			<div class="form-group form-group-sm">
				<label class="col-sm-4 control-label" for="BusinessType">Business type</label>
				<div class="col-sm-5">
					<select class="form-control input" ng-model="vm.BusinessType" name="BusinessType"
							ng-options="k  as k for k in BusinessTypes"></select>
				</div>
			</div>

			<div ng-if="vm.AllowedSamples" class="form-group form-group-sm" title="if ticked, on a new job, 'Send samples' will be ticked by default and incur a $5 fee per job">
				<label class="col-sm-4 control-label" for="SendSamples">Send samples</label>
				<div class="col-sm-8">
					<div class="checkbox">
						<label class="">
							<input id="SendSamples" type="checkbox" ng-model="vm.SendSamples" />
						</label>
					</div>
				</div>
			</div>

			<div class="form-group form-group-sm">
				<label class="control-label col-sm-4">Logo File</label>
				<div class="col-sm-5">
					<img ng-src="{{logo}}" style="width: 50%;" no-image="images/no-image.png" />
					<div>
						<a class="btn" ng-click="uploadLogo()"><i class="glyphicon  glyphicon-cloud-upload  "></i> Upload logo</a>
					</div>

				</div>
			</div>

		</div>

		<div leppane="Emails" visible="true">

			<!-- <legend>Email settings</legend> -->
			<div class="form-group form-group-sm">
				<label class="control-label requiredField  col-sm-4 " for="text">
					Send Job Status Notifications to:
					<span class="asteriskField">
						*
					</span>
				</label>
				<div class="col-sm-5">

					<input class="form-control " type="text" ng-model="vm.Email" ng-required="true" />
				</div>
				<span class="help-block col-sm-12 col-md-offset-4" id="hint_text">
					(For LEP Online Job Status Notifications Only)
				</span>
			</div>
			<div class="form-group form-group-sm" for="select">
				<label class="col-sm-4 control-label" for="name-job">
					Email Notifications
				</label>
				<div class="col-sm-5">
					<select class="form-control input" ng-model="vm.NotificationType"
							ng-options="k*1 as v for (k,v) in enums.ValueDesc.NotificationType"></select>
				</div>

			</div>
			<div class="form-group form-group-sm">
				<label class="control-label  col-sm-4" for="text1">Send Accounts to</label>
				<div class="col-sm-5">
					<input class="form-control" type="text" ng-model="vm.AccountEmail" ng-required="true" />
				</div>
			</div>
			<div class="form-group form-group-sm">
				<label class="control-label  col-sm-4" for="text2">Send all other material including promotions to</label>
				<div class="col-sm-5">
					<input class="form-control" type="text" ng-model="vm.OtherEmail" ng-required="true" />
				</div>
			</div>

		</div>

		<!--<div leppane="Contacts" visible="false" >
		<div class="row" style="background-color: darkgray!important">
			<div class="col-sm-12">
				<h4>Contact 1 *</h4>
				<div lep-contact-details contact="vm.Contact1" ></div>
			</div>

			<div class="col-sm-12">
				<h5>Contact 2</h5>
				<div lep-contact-details contact="vm.Contact2" ></div>
			</div>
		</div>
	</div>-->


		<div leppane="Contacts List" visible="true">
			<div lep-contact-list lep-contact-list-details contacts="vm.Contacts"></div>
		</div>

		<div leppane="Address" visible="true">
			<div class="row">
				<div class="col-sm-6">
					<h5>Billing address *</h5>

					<div class="form-group form-group-sm">
						<div class="col-sm-4 control-label" for="order-no"></div>
						<div class="col-sm-8">
							<label class="checkbox">
								&nbsp;
							</label>
						</div>
					</div>
					<div address-details address="vm.BillingAddress"></div>
				</div>



				<div class="col-sm-6">
					<h5>Delivery address</h5>
					<div class="form-group form-group-sm">
						<div class="col-sm-offset-4 col-sm-8">
							<label class="checkbox">
								<input name="checkbox" type="checkbox" ng-model="vm.PostalIsBilling" ng-change="makePostalSameAsBilling(vm.PostalIsBilling)" />
								Delivery address is same as billing address
							</label>
						</div>
					</div>
					<div>
						<div address-details address="vm.PostalAddress" noteditable="vm.PostalIsBilling"></div>
					</div>
				</div>


			</div>
		</div>

		<div leppane="Login" visible="true">
			<form class="form-horizontal" method="post">
				<!-- <legend>Login</legend> -->

				<div class="form-group form-group-sm">
					<label class="control-label col-sm-4">User name</label>
					<div class="col-sm-5">
						<input class="form-control" type="text" ng-model="vm.Username" ng-disabled="true" />
					</div>
				</div>
				<div class="form-group form-group-sm">
					<label class="control-label col-sm-4">Password</label>
					<div class="col-sm-5">
						<input class="form-control" type="text" ng-model="vm.Password" />
					</div>
				</div>
				<div class="form-group form-group-sm">
					<label class="control-label col-sm-4">Confirm Password</label>
					<div class="col-sm-5">
						<input class="form-control" type="text" ng-model="vm.Password1" />
					</div>
				</div>
				<div class="form-group form-group-sm">
					<label class="control-label col-sm-4"></label>
					<div class="col-sm-5">
						<button class="btn btn-default" type="button" ng-click="updatePassword(vm.Password)" ng-disabled="!vm.Password || !(vm.Password == vm.Password1)">Update password</button>
					</div>
				</div>
			</form>
		</div>


		<!--<div leppane="Print Portal" visible="true" ng-if="vm.IsPrintPortalEnabled">
			<div lep-white-label-setup vm="vm"></div>
		</div>-->

		<!--<pre>{{  vm.WhiteLabelProductMarkups | json }}</pre>-->
	
		<div class="row" style="margin-top:10px;">
	 
			
		<div class="col-sm-12">
		<form class="form-horizontal col-sm-12">
		<div class="row">
		<div class="form-actions pull-right">
		<button type="submit" class="btn btn-default" ng-click="save()"> <i class="glyphicon glyphicon-floppy-save"></i> Save my details</button>
							<!--<button type="submit" class="btn" ng-click="patch()">Patch</button>-->
						</div>
					</div>
				</form>
			</div>
		</div>
	</form>
</div>
