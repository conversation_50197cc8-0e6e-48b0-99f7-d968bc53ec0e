{
    "IMP": {
        "HotfolderPath": "\\\\ppp01\\HotFolderRoot\\MyLEP"
    },
    "TestBox": true,
    "HolidayMode": false,
    "Logging": {
        "IncludeScopes": false,
        "LogLevel": {
            "Default": "Debug",
            "System": "Debug",
            "Microsoft": "Debug",
            "Microsoft.AspNetCore.Mvc": "Debug",
            "Microsoft.AspNetCore.Authentication": "Debug",
            "Microsoft.AspNetCore.Routing.RouteBase": "Debug",
            "Microsoft.AspNetCore.Server.Kestrel": "Debug",
            "NHibernate": "Debug",
            "NHibernate.SQL": "Debug",
            "Microsoft.AspNetCore.SignalR": "None",
            "Microsoft.AspNetCore.Hosting": "None",
            "Microsoft.AspNetCore.StaticFiles": "None"
        }
    },
    "jobboard": {
        "mes**gequeue": ".\\Private$\\leponline.jobboardqueue"
    },

    "CustomerLogoDirectory": "E:\\SA\\LEP_Data\\logos",
    "DataDirectory": "E:\\SA\\LEP_Data",
    "StaticAssets": "E:\\SA\\LEP\\au\\StaticAssets",
    "OldDataDirectory": "E:\\SA\\LEP_Data\\oldorders",
    "DataDirectoryPC": "E:\\SA\\LEP_Data",
    "DataDirectoryMac": "E:\\SA\\LEP_Data",
    "InvoicerPDFFolder": "E:\\SA\\LEP_Data\\INVOICER",

    //"AbsolutePathURL": "http://lepold.localtest.me",

    "lepcrm.webservice.url": "http://winston.internal.lepcolourprinters.com.au:1000/MSCRMService.asmx",
    "compdata.webservice.url": "http://harry:8089/iFreightChargeEnquiryService.svc",
    "AbsolutePathURL": "http://localhost:5000",

    "reports": {
        "LepQuote": "E:\\SA\\LEP_Data\\labels2\\t1.frx"
    },

    "email": {
        "sendMail": false,
        "server": "smtp.sendgrid.net",
        "port": "587",
        "username": "<EMAIL>",
        "password": "*********************************************************************"
    },
    "print.mes**gequeue": ".\\Private$\\leponline.mes**geprintqueue",
    "jobboard.mes**gequeue": ".\\Private$\\leponline.jobboardqueue",

    "Nhibernate": {
        "Con": "Data Source=DESKTOP-R1OTS8F\\SQLEXPRESS; user id=**; password=**;Initial Catalog=PRD_AU;MultipleActiveResultSets=true;Max Pool Size=10;App=LepCore",
        "Con2": "Data Source=DESKTOP-R1OTS8F\\SQLEXPRESS; user id=**; password=**;Initial Catalog=PRD_AU_CustNote;MultipleActiveResultSets=true;Max Pool Size=10;App=LepCore"
    },

    "SupplyMaster": {
        "Con": "Data Source=*************; user id=**; password=*************; Initial Catalog=SupplyMaster;MultipleActiveResultSets=true"
    },

    "Seq": {
        "ServerUrl": "http://localhost:5341",
        "MinimumLevel": "Trace",
        "LevelOverride": {
            "Microsoft": "Warning"
        }
    },

    "FreightProvider": "SmartFreight", // or  CompData "current"

    "Labels": {
        "LogoLabel": "E:\\SA\\LEP_Data\\Labels2\\LogoLabel_102x73mm.frx",
        "PayMeLabel": "E:\\SA\\LEP_Data\\Labels2\\PayMeLabel.frx",
        "FillingLabel": "E:\\SA\\LEP_Data\\Labels2\\FillingLabel.frx",
        "SampleLabel": "E:\\SA\\LEP_Data\\Labels2\\SampleLabel.frx",
        "AddressA4Label": "E:\\SA\\LEP_Data\\Labels2\\AddressA4Label.frx",
        "PickupLabel": "E:\\SA\\LEP_Data\\Labels2\\PickupLabel.frx",
        "OneDeliveryOnly": "E:\\SA\\LEP_Data\\Labels2\\OneDeliveryOnly.frx",
        "AddressLabel": "E:\\SA\\LEP_Data\\Labels2\\SampleLabel.frx",
        "AddressLabelOther": "E:\\SA\\LEP_Data\\Labels2\\SampleLabel.frx",
        "CartonLabel": "E:\\SA\\LEP_Data\\labels2\\CartonLabel.frx",
        "CartonLabelMH": "E:\\SA\\LEP_Data\\labels2\\CartonLabelMH.frx"
    },

    "GhostScript": "C:\\Program Files\\gs\\gs10.00.0\\bin\\gswin64c.exe",
    "PdfTk": "C:\\gs952\\bin\\pdftk.exe",
    "LibTiff": "E:\\SA\\LEP_Data\\LibTiff\\tiff2pdf.exe",
    "PdfToPrinter": "c:\\myob\\PdfToPrinter.exe",

    "AutomatedArtworkCheck": {
        "Enabled": false,
        "Timeout": "00:00:40",

        "Method": "DoPitStopViaHotFolder",
        "profiles": {
            "default": "E:\\SA\\LEP_Data\\aac\\CMYK_NEW LEP PROFILE.ppp",

            "spotcolor": "E:\\SA\\LEP_Data\\aac\\LEP FIX_2018_V2 SPOT.ppp",
            "wideformat": "E:\\SA\\LEP_Data\\aac\\LEP Wide Format_V1.ppp"
        },

        "DoPitStopViaCommandLine": {
            "path": "C:\\Program Files\\Enfocus\\Enfocus PitStop Server 13\\PitStopServerCLI.exe",
            "mutator": "E:\\SA\\LEP_Data\\LEP Check 2013-1.ppp"
        },

        "DoPitStopViaHotFolder": {
            "input": "\\\\Henry\\hotfolder\\Input Folder",
            "output": "\\\\Henry\\hotfolder\\Output Folder"
        }
    },

    "Dispatchers": [
        {
            "Name": "FG-DISTRIB-02",
            "DispatchFacility": "FG"
        },
        {
            "Name": "FG-DISTRIB-03",
            "DispatchFacility": "FG"
        },
        {
            "Name": "PM-DISTRIB-01",
            "DispatchFacility": "PM"
        },
        {
            "Name": "PM-BINDERY-05",
            "DispatchFacility": "PM"
        }
    ],

    "SmartFreight": {
        "Url": {
            "SFOv1": "http://api-r1.smartfreight.com/api/soap/classic",
            "DeliveryOptions": "http://api-r1.smartfreight.com/api/soap/deliveryoptions"
        },

        "Senders": {
            "LEPQLD": {
                "Id": "NWM",
                "Key": "RqB0fJOapwP79AVDdzF5Pgg7fESwF4GxHQ"
            },
            "TESTQLD": {
                "Id": "RqB0fJOapwP79AVDdzF5Pgg7fESwF4GxHQ",
                "Key": ""
            },

            "LEPVIC": {
                "Id": "NGU",
                "Key": "JoIJ2bXndxCk0V3ZfkM6VxKuzv1qgEAiA0"
            },

            "TESTVIC": {
                "Id": "NGU",
                "Key": "JoIJ2bXndxCk0V3ZfkM6VxKuzv1qgEAiA0"
            }
        }
    },


    "Serilog": {
        "AllowedHosts": "*",
        "Using": [
            //"Serilog.Sinks.Console",
            //"Serilog.Sinks.File",
            "Serilog.Sinks.Seq"

        ],
        "MinimumLevel": {
            "Default": "Debug",
            "Override": {
                "Microsoft": "Warning",
                "System": "Warning"
            }
        },

        "WriteTo": [
            //{ "Name": "Console" },
            //{
            //    "Name": "File",
            //    "Args": { "path": "Serilog\\logs.txt" }
            //},
            {
                "Name": "Seq",
                "Args": { "serverUrl": "http://localhost:5341" }
            }
        ],

        "Enrich": [ "FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId" ]

    }
}
