(function (angular, window) {
    'use strict';
    var app = angular.module('app');

    // LoginController
    app.controller('LoginController', ['$scope', '$location', '$state', '$rootScope',
        function ($scope, $location, $state, $rootScope) {
            $scope.login = function () {
                $scope.loginFn($scope.username, $scope.password).then(function (r) {
                    if (!r)
                        return;

                    if ($rootScope.postLoginRoute) {
                        $location.url($rootScope.postLoginRoute);
                        $rootScope.postLoginRoute = null;
                    } else


                    if (r.IsCustomer) {
                        $state.go('cust.order.addnewjob', { orderId: 0 });

                        //window.zE(function () {
                        //	zE.setLocale("en-US");
                        //	var uu = {
                        //		name: r.User.Username,
                        //		email: r.User.Email,
                        //		externalId: r.User.Id
                        //	};
                        //	zE.identify(uu);

                        //	window.$zopim(function () {
                        //		window.$zopim.livechat.window.hide();
                        //	});
                        //});

                    } else {

                        //window.zE(function () {
                        //	window.$zopim(function () {
                        //		window.$zopim.livechat.window.hideAll();
                        //	});
                        //});

                        $state.go('staff.orders');
                    }

                });
            };
        }]);





    app.controller('RootController', [
                 '$scope','$rootScope','$location', '$log', '$filter', 'Auth', '$localStorage','$sessionStorage','$state', '$stateParams', '$q', '$animate', 'lepApi2', '$http', 'enums','ngDialog',
        function ($scope, $rootScope, $location, $log, $filter, Auth, $localStorage,$sessionStorage,$state, $stateParams, $q, $animate, lepApi2, $http, enums, ngDialog) {
            $rootScope.enums = enums;
            var urlx = document.location.origin;
            if ((urlx.indexOf('printspecs') > -1) || (urlx.indexOf('localhost') > -1) || (urlx.indexOf('icemedia') > -1)) {
                $rootScope.test = true;
            }


            $scope.$state = $state;
            $scope.isLogin = $state.current.name == 'login';

            $scope.getTheClass = function () {
                return $state.current.class || "";
            };

            $scope.resetPassword = function (username) {
                lepApi2.post("Account/password/reset", JSON.stringify(username))
                    .then(
                        function (d) {
                            toastr.success("Your password has been reset.<br/><br/>Please check your email and try logging in with your new password.");
                            $state.go('login', { reload: true });
                        },
                        function (d) {
                            toastr.error("Could not find a user with  username : " + username);
                        }
                    );
            };

            $scope.logout = function () {
                //$.connection.hub.stop(); //on the client before the user attempts to log on and then call
                //lepApi2.post("Account/logout");
                Auth.ClearCredentials();
                $state.go('login', {});
                // $.connection.disconnect;
            };

            $scope.loginFn = function (username, password) {
                //$.connection.hub.stop(); //on the client before the user attempts to log on and then call
                //$.connection.hub.start(); //after the log on attempt has completed.


                var d = $q.defer();
                $scope.dataLoading = true;
                Auth.Login(username, password, function (response) {
                    toastr.remove();

                    if (!response.data.error) {
                         $scope.$root.globals = response.data;

                        if (response.data.User.PrintPortalSettings) {
                            $scope.$root.ppv = response.data.User.PrintPortalSettings.Version;
                        }

                        d.resolve(response.data);
                    } else {
                        toastr.error("Login failed, please try again.");
                        Auth.ClearCredentials();
                        $state.go('login', { reload: true });
                        d.resolve(false);
                    }
                });
                return d.promise;
            };

            $scope.loginCust = function () {
            };

            $scope.loginStaff = function () {
            };


            $scope.cartonLabelPrinter = function () {
                var dialog = ngDialog.open({
                    template: 'staff/carton-label-printer1.html',
                    controller: 'CartonLabelPrinter1Controller',
                    className: 'ngdialog-theme-default',
                    closeByDocument: false,
                    closeByEscape: false,
                    overlay: false,
                    backdrop: 'static',
                });

            };
            $scope.cartonLabelMailHousePrinter = function () {
                var dialog = ngDialog.open({
                    template: 'staff/carton-label-printer2.html',
                    controller: 'CartonLabelMailHousePrinter1Controller',
                    className: 'ngdialog-theme-default',
                    closeByDocument: false,
                    closeByEscape: false,
                    overlay: false,
                    backdrop: 'static',
                });

            };
            $scope.logoLabelPrinter = function () {
                var dialog = ngDialog.open({
                    template: 'staff/logo-label-printer1.html',
                    controller: 'LogoLabelPrinter1Controller',
                    className: 'ngdialog-theme-default',
                    closeByDocument: false,
                    closeByEscape: false,
                    overlay: false,
                    backdrop: 'static',
                });

            };

            var connection = null;

            function initSR() {
                return;
                // connection = new signalR.HubConnectionBuilder()
                //     .withUrl("/LepHub")
                //     .configureLogging(signalR.LogLevel.Trace)
                //     .build();
                // window.src = connection;

                // connection.onclose(function () {
                // });

                // connection.start().then(function () {
                // });

                // connection.on("HubReceived", function (message) {
                // });
            }

            //// Declare a proxy to reference the hub.
            //$.connection.hub.logging = false;

            //var lepHub = $.connection.lepHub;
            //lepHub.logging = false;

            //// Create a function that the hub can call to recalculate online users.
            //lepHub.client.recalculateOnlineUsers = function (message) {
            //	$scope.$apply(function () {
            //		$scope.userCount = message;
            //	});
            //};

            //lepHub.client.updateJobBoard = function () {
            //	$("body").trigger("buildJobBoard")
            //};

            //try {
            //	$.connection.hub.start().done(function () {
            //		var m = ' bbb Time at client is  ' + new Date();
            //		lepHub.server.send();
            //	});
            //} catch (e) {
            //}

            //if ($localStorage.globals && $localStorage.globals.User && $localStorage.globals.User.PrintPortalSettings) {
            // on a window refresh the  loggedin event will not fire as we want the user to already log in
            // in this screnario we call initSR so the hubs start
            // initSR();

            // on a reload of the page rerequst this and update vars
            // todo: candidate for better imp


            // }


            var isForFrameHostedClients = (document.URL.indexOf("index_r1") > -1) ||
                document.URL.indexOf("index_wl") > -1;

            /*
            // if whitelabel site is being requested as identified by index_wl in url
            // parse the parameters and go to state
            if (false document.URL.indexOf("index_wl") > -1) {
              var sp = new URLSearchParams(document.URL);
              var routeJob = /\/wl\/(\d+)\/o\/(\d+)\/j\/(\d+)/gm;
              var routeOrder = /\/wl\/(\d+)\/o\/(\d+)/gm;
              var p = routeJob.exec(document.URL);
              var po = routeOrder.exec(document.URL);

              if (p) { // if it matches the job page route go there
                var params = {
                  customerId: p[1], orderId: p[2], jobId: p[3]
                };

                var category = sp.get('category');
                if (category) {
                  params.category = category;
                }
                $state.go('whitelabel.job', params);
              } else if (po) { // if it matches the order page route go there
                var params = { customerId: po[1], orderId: po[2] || 0 };
                $state.go('whitelabel.order', params);
              }
            }
            */

            if ( (!$sessionStorage.lepToken && !$localStorage.lepToken ) && !isForFrameHostedClients) {
                Auth.ClearCredentials();
                $state.go('login', { reload: true });
            }

            $scope.userCount = "click here to get user counts.";

            $scope.getUserCount = function () {
                lepApi2.get("Account/UserCount").then(function (v) {
                    $scope.userCount = v.data.Item1 + " Customer and " + v.data.Item2 + " staff online in last 5 minutes";
                });
            };

            $scope.$on('loggedin', function (x) {
                setTimeout(initSR(), 1000);
            });

            $scope.$on('percentage', function (ev, px) {
                if (px) {
                    $scope.percentage = px;
                }

            });

        }
    ]);

})(window.angular, window);
