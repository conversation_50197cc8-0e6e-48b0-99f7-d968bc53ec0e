//
// Glyphicons for Bootstrap
//
// Since icons are fonts, they can be placed anywhere text is placed and are
// thus automatically sized to match the surrounding child. To use, create an
// inline element with the appropriate classes, like so:
//
// <a href="#"><span class="glyphicon glyphicon-star"></span> Star</a>

@at-root {
  // Import the fonts
  @font-face {
    font-family: 'Glyphicons Halflings';
   // src: url(if($bootstrap-sass-asset-helper, twbs-font-path('#{$icon-font-path}#{$icon-font-name}.eot'), '#{$icon-font-path}#{$icon-font-name}.eot'));
    src:// url(if($bootstrap-sass-asset-helper, twbs-font-path('#{$icon-font-path}#{$icon-font-name}.eot?#iefix'), '#{$icon-font-path}#{$icon-font-name}.eot?#iefix')) format('embedded-opentype'),
        // url(if($bootstrap-sass-asset-helper, twbs-font-path('#{$icon-font-path}#{$icon-font-name}.woff2'), '#{$icon-font-path}#{$icon-font-name}.woff2')) format('woff2'),
       //  url(if($bootstrap-sass-asset-helper, twbs-font-path('#{$icon-font-path}#{$icon-font-name}.woff'), '#{$icon-font-path}#{$icon-font-name}.woff')) format('woff'),
         url(if($bootstrap-sass-asset-helper, twbs-font-path('#{$icon-font-path}#{$icon-font-name}.ttf'), '#{$icon-font-path}#{$icon-font-name}.ttf')) format('truetype'),
       //  url(if($bootstrap-sass-asset-helper, twbs-font-path('#{$icon-font-path}#{$icon-font-name}.svg##{$icon-font-svg-id}'), '#{$icon-font-path}#{$icon-font-name}.svg##{$icon-font-svg-id}')) format('svg');
  }
}

// Catchall baseclass
.glyphicon {
  position: relative;
  top: 0px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}



.glyphicon-eye-open               { &:before { content: "\e105"; } }
.glyphicon-arrow-down             { &:before { content: "\e094"; } }
.glyphicon-arrow-left             { &:before { content: "\e091"; } }
.glyphicon-arrow-right            { &:before { content: "\e092"; } }
.glyphicon-arrow-up               { &:before { content: "\e093"; } }
.glyphicon-ban-circle             { &:before { content: "\e090"; } }
.glyphicon-barcode                { &:before { content: "\e040"; } }
.glyphicon-calendar               { &:before { content: "\e109"; } }
.glyphicon-chevron-down           { &:before { content: "\e114"; } }
.glyphicon-chevron-up             { &:before { content: "\e113"; } }
.glyphicon-circle-arrow-down      { &:before { content: "\e134"; } }
.glyphicon-circle-arrow-left      { &:before { content: "\e132"; } }
.glyphicon-circle-arrow-right     { &:before { content: "\e131"; } }
.glyphicon-circle-arrow-up        { &:before { content: "\e133"; } }
.glyphicon-cloud-download         { &:before { content: "\e197"; } }
.glyphicon-cloud-upload           { &:before { content: "\e198"; } }
.glyphicon-download               { &:before { content: "\e026"; } }
.glyphicon-envelope               { &:before { content: "\2709"; } }
.glyphicon-erase                  { &:before { content: "\e221"; } }
.glyphicon-file                   { &:before { content: "\e022"; } }
.glyphicon-filter                 { &:before { content: "\e138"; } }
.glyphicon-floppy-disk            { &:before { content: "\e172"; } }
.glyphicon-floppy-open            { &:before { content: "\e176"; } }
.glyphicon-floppy-remove          { &:before { content: "\e174"; } }
.glyphicon-floppy-save            { &:before { content: "\e175"; } }
.glyphicon-floppy-saved           { &:before { content: "\e173"; } }
.glyphicon-folder-close           { &:before { content: "\e117"; } }
.glyphicon-folder-open            { &:before { content: "\e118"; } }
.glyphicon-inbox                  { &:before { content: "\e028"; } }
.glyphicon-info-sign              { &:before { content: "\e086"; } }
.glyphicon-link                   { &:before { content: "\e144"; } }
.glyphicon-log-in                 { &:before { content: "\e161"; } }
.glyphicon-log-out                { &:before { content: "\e163"; } }
.glyphicon-minus                  { &:before { content: "\2212"; } }
.glyphicon-ok                     { &:before { content: "\e013"; } }
.glyphicon-ok-circle              { &:before { content: "\e089"; } }
.glyphicon-ok-sign                { &:before { content: "\e084"; } }
.glyphicon-pencil                 { &:before { content: "\270f"; } }
.glyphicon-phone                  { &:before { content: "\e145"; } }
.glyphicon-phone-alt              { &:before { content: "\e183"; } }
.glyphicon-plus                   { &:before { content: "\002b"; } }
.glyphicon-print                  { &:before { content: "\e045"; } }
.glyphicon-qrcode                 { &:before { content: "\e039"; } }
.glyphicon-question-sign          { &:before { content: "\e085"; } }
.glyphicon-refresh                { &:before { content: "\e031"; } }
.glyphicon-remove                 { &:before { content: "\e014"; } }
.glyphicon-remove-circle          { &:before { content: "\e088"; } }
.glyphicon-remove-sign            { &:before { content: "\e083"; } }
.glyphicon-save                   { &:before { content: "\e166"; } }
.glyphicon-save-file              { &:before { content: "\e202"; } }
.glyphicon-saved                  { &:before { content: "\e168"; } }
.glyphicon-screenshot             { &:before { content: "\e087"; } }
.glyphicon-search                 { &:before { content: "\e003"; } }
.glyphicon-send                   { &:before { content: "\e171"; } }
.glyphicon-share-alt              { &:before { content: "\e095"; } }
.glyphicon-shopping-cart          { &:before { content: "\e116"; } }
.glyphicon-tasks                  { &:before { content: "\e137"; } }
.glyphicon-trash                  { &:before { content: "\e020"; } }
.glyphicon-upload                 { &:before { content: "\e027"; } }
.glyphicon-usd                    { &:before { content: "\e148"; } }
.glyphicon-user                   { &:before { content: "\e008"; } }
.glyphicon-warning-sign           { &:before { content: "\e107"; } }
.glyphicon-wrench                 { &:before { content: "\e136"; } }

.glyphicon-plus-sign              { &:before { content: "\e081"; } }
.glyphicon-minus-sign             { &:before { content: "\e082"; } }
.glyphicon-star                   { &:before { content: "\e006"; } }
.glyphicon-star-empty             { &:before { content: "\e007"; } }


.glyphicon-chevron-left           { &:before { content: "\e079"; } }
.glyphicon-chevron-right          { &:before { content: "\e080"; } }

.glyphicon-lock                   { &:before { content: "\e033"; } }
.glyphicon-fast-backward          { &:before { content: "\e070"; } }
.glyphicon-fast-forward           { &:before { content: "\e076"; } }
.glyphicon-forward                { &:before { content: "\e075"; } }
.glyphicon-backward               { &:before { content: "\e071"; } }
.glyphicon-paperclip              { &:before { content: "\e142"; } }
.glyphicon-alert                  { &:before { content: "\e209"; } }




.glyphicon-adjust                 { &:before { content: "\e063"; } }
.glyphicon-alert                  { &:before { content: "\e209"; } }
.glyphicon-align-center           { &:before { content: "\e053"; } }
.glyphicon-align-justify          { &:before { content: "\e055"; } }
.glyphicon-align-left             { &:before { content: "\e052"; } }
.glyphicon-align-right            { &:before { content: "\e054"; } }
.glyphicon-apple                  { &:before { content: "\f8ff"; } }
.glyphicon-asterisk               { &:before { content: "\002a"; } }
.glyphicon-baby-formula           { &:before { content: "\e216"; } }

.glyphicon-bed                    { &:before { content: "\e219"; } }
.glyphicon-bell                   { &:before { content: "\e123"; } }
.glyphicon-bishop                 { &:before { content: "\e214"; } }
.glyphicon-bitcoin                { &:before { content: "\e227"; } }
.glyphicon-blackboard             { &:before { content: "\e218"; } }
.glyphicon-bold                   { &:before { content: "\e048"; } }
.glyphicon-book                   { &:before { content: "\e043"; } }
.glyphicon-bookmark               { &:before { content: "\e044"; } }
.glyphicon-briefcase              { &:before { content: "\e139"; } }
.glyphicon-btc                    { &:before { content: "\e227"; } }
.glyphicon-bullhorn               { &:before { content: "\e122"; } }
.glyphicon-camera                 { &:before { content: "\e046"; } }
.glyphicon-cd                     { &:before { content: "\e201"; } }
.glyphicon-certificate            { &:before { content: "\e124"; } }
.glyphicon-check                  { &:before { content: "\e067"; } }
.glyphicon-chevron-left           { &:before { content: "\e079"; } }
.glyphicon-chevron-right          { &:before { content: "\e080"; } }
.glyphicon-cloud                  { &:before { content: "\2601"; } }
.glyphicon-cog                    { &:before { content: "\e019"; } }
.glyphicon-collapse-down          { &:before { content: "\e159"; } }
.glyphicon-collapse-up            { &:before { content: "\e160"; } }
.glyphicon-comment                { &:before { content: "\e111"; } }
.glyphicon-compressed             { &:before { content: "\e181"; } }
.glyphicon-console                { &:before { content: "\e254"; } }
.glyphicon-copy                   { &:before { content: "\e205"; } }
.glyphicon-copyright-mark         { &:before { content: "\e194"; } }
.glyphicon-credit-card            { &:before { content: "\e177"; } }
.glyphicon-cutlery                { &:before { content: "\e179"; } }
.glyphicon-dashboard              { &:before { content: "\e141"; } }
.glyphicon-download-alt           { &:before { content: "\e025"; } }
.glyphicon-duplicate              { &:before { content: "\e224"; } }
.glyphicon-earphone               { &:before { content: "\e182"; } }
.glyphicon-edit                   { &:before { content: "\e065"; } }
.glyphicon-education              { &:before { content: "\e233"; } }
.glyphicon-eject                  { &:before { content: "\e078"; } }

.glyphicon-equalizer              { &:before { content: "\e210"; } }
.glyphicon-eur                    { &:before { content: "\20ac"; } }
.glyphicon-euro,
.glyphicon-exclamation-sign       { &:before { content: "\e101"; } }
.glyphicon-expand                 { &:before { content: "\e158"; } }
.glyphicon-export                 { &:before { content: "\e170"; } }
.glyphicon-eye-close              { &:before { content: "\e106"; } }
.glyphicon-eye-open               { &:before { content: "\e105"; } }
.glyphicon-facetime-video         { &:before { content: "\e059"; } }
.glyphicon-fast-backward          { &:before { content: "\e070"; } }
.glyphicon-fast-forward           { &:before { content: "\e076"; } }
.glyphicon-film                   { &:before { content: "\e009"; } }
.glyphicon-filter                 { &:before { content: "\e138"; } }
.glyphicon-fire                   { &:before { content: "\e104"; } }
.glyphicon-flag                   { &:before { content: "\e034"; } }
.glyphicon-flash                  { &:before { content: "\e162"; } }

.glyphicon-folder-close           { &:before { content: "\e117"; } }
.glyphicon-folder-open            { &:before { content: "\e118"; } }
.glyphicon-font                   { &:before { content: "\e047"; } }
.glyphicon-forward                { &:before { content: "\e075"; } }
.glyphicon-fullscreen             { &:before { content: "\e140"; } }
.glyphicon-gbp                    { &:before { content: "\e149"; } }
.glyphicon-gift                   { &:before { content: "\e102"; } }
.glyphicon-glass                  { &:before { content: "\e001"; } }
.glyphicon-globe                  { &:before { content: "\e135"; } }
.glyphicon-grain                  { &:before { content: "\e239"; } }
.glyphicon-hand-down              { &:before { content: "\e130"; } }
.glyphicon-hand-left              { &:before { content: "\e128"; } }
.glyphicon-hand-right             { &:before { content: "\e127"; } }
.glyphicon-hand-up                { &:before { content: "\e129"; } }
.glyphicon-hd-video               { &:before { content: "\e187"; } }
.glyphicon-hdd                    { &:before { content: "\e121"; } }
.glyphicon-header                 { &:before { content: "\e180"; } }
.glyphicon-headphones             { &:before { content: "\e035"; } }
.glyphicon-heart                  { &:before { content: "\e005"; } }
.glyphicon-heart-empty            { &:before { content: "\e143"; } }
.glyphicon-home                   { &:before { content: "\e021"; } }
.glyphicon-hourglass              { &:before { content: "\231b"; } }
.glyphicon-ice-lolly              { &:before { content: "\e231"; } }
.glyphicon-ice-lolly-tasted       { &:before { content: "\e232"; } }
.glyphicon-import                 { &:before { content: "\e169"; } }
.glyphicon-indent-left            { &:before { content: "\e057"; } }
.glyphicon-indent-right           { &:before { content: "\e058"; } }
.glyphicon-italic                 { &:before { content: "\e049"; } }
.glyphicon-jpy                    { &:before { content: "\00a5"; } }
.glyphicon-king                   { &:before { content: "\e211"; } }
.glyphicon-knight                 { &:before { content: "\e215"; } }
.glyphicon-lamp                   { &:before { content: "\e223"; } }
.glyphicon-leaf                   { &:before { content: "\e103"; } }
.glyphicon-level-up               { &:before { content: "\e204"; } }
.glyphicon-list                   { &:before { content: "\e056"; } }
.glyphicon-list-alt               { &:before { content: "\e032"; } }

.glyphicon-magnet                 { &:before { content: "\e112"; } }
.glyphicon-map-marker             { &:before { content: "\e062"; } }
.glyphicon-menu-down              { &:before { content: "\e259"; } }
.glyphicon-menu-hamburger         { &:before { content: "\e236"; } }
.glyphicon-menu-left              { &:before { content: "\e257"; } }
.glyphicon-menu-right             { &:before { content: "\e258"; } }
.glyphicon-menu-up                { &:before { content: "\e260"; } }

.glyphicon-modal-window           { &:before { content: "\e237"; } }
.glyphicon-move                   { &:before { content: "\e068"; } }
.glyphicon-music                  { &:before { content: "\e002"; } }
.glyphicon-new-window             { &:before { content: "\e164"; } }
.glyphicon-object-align-bottom    { &:before { content: "\e245"; } }
.glyphicon-object-align-horizontal{ &:before { content: "\e246"; } }
.glyphicon-object-align-left      { &:before { content: "\e247"; } }
.glyphicon-object-align-right     { &:before { content: "\e249"; } }
.glyphicon-object-align-top       { &:before { content: "\e244"; } }
.glyphicon-object-align-vertical  { &:before { content: "\e248"; } }
.glyphicon-off                    { &:before { content: "\e017"; } }
.glyphicon-oil                    { &:before { content: "\e238"; } }
.glyphicon-open                   { &:before { content: "\e167"; } }
.glyphicon-open-file              { &:before { content: "\e203"; } }
.glyphicon-option-horizontal      { &:before { content: "\e234"; } }
.glyphicon-option-vertical        { &:before { content: "\e235"; } }

.glyphicon-paste                  { &:before { content: "\e206"; } }
.glyphicon-pause                  { &:before { content: "\e073"; } }
.glyphicon-pawn                   { &:before { content: "\e213"; } }

.glyphicon-picture                { &:before { content: "\e060"; } }
.glyphicon-piggy-bank             { &:before { content: "\e225"; } }
.glyphicon-plane                  { &:before { content: "\e108"; } }
.glyphicon-play                   { &:before { content: "\e072"; } }
.glyphicon-play-circle            { &:before { content: "\e029"; } }

.glyphicon-pushpin                { &:before { content: "\e146"; } }
.glyphicon-queen                  { &:before { content: "\e212"; } }
.glyphicon-random                 { &:before { content: "\e110"; } }
.glyphicon-record                 { &:before { content: "\e165"; } }
.glyphicon-registration-mark      { &:before { content: "\e195"; } }
.glyphicon-repeat                 { &:before { content: "\e030"; } }
.glyphicon-resize-full            { &:before { content: "\e096"; } }
.glyphicon-resize-horizontal      { &:before { content: "\e120"; } }
.glyphicon-resize-small           { &:before { content: "\e097"; } }
.glyphicon-resize-vertical        { &:before { content: "\e119"; } }
.glyphicon-retweet                { &:before { content: "\e115"; } }
.glyphicon-road                   { &:before { content: "\e024"; } }
.glyphicon-rub                    { &:before { content: "\20bd"; } }
.glyphicon-ruble                  { &:before { content: "\20bd"; } }
.glyphicon-scale                  { &:before { content: "\e230"; } }
.glyphicon-scissors               { &:before { content: "\e226"; } }
.glyphicon-sd-video               { &:before { content: "\e186"; } }
.glyphicon-search                 { &:before { content: "\e003"; } }

.glyphicon-share                  { &:before { content: "\e066"; } }
.glyphicon-shopping-cart          { &:before { content: "\e116"; } }
.glyphicon-signal                 { &:before { content: "\e018"; } }
.glyphicon-sort                   { &:before { content: "\e150"; } }
.glyphicon-sort-by-alphabet       { &:before { content: "\e151"; } }
.glyphicon-sort-by-alphabet-alt   { &:before { content: "\e152"; } }
.glyphicon-sort-by-attributes     { &:before { content: "\e155"; } }
.glyphicon-sort-by-attributes-alt { &:before { content: "\e156"; } }
.glyphicon-sort-by-order          { &:before { content: "\e153"; } }
.glyphicon-sort-by-order-alt      { &:before { content: "\e154"; } }
.glyphicon-sound-5-1              { &:before { content: "\e191"; } }
.glyphicon-sound-6-1              { &:before { content: "\e192"; } }
.glyphicon-sound-7-1              { &:before { content: "\e193"; } }
.glyphicon-sound-dolby            { &:before { content: "\e190"; } }
.glyphicon-sound-stereo           { &:before { content: "\e189"; } }

.glyphicon-stats                  { &:before { content: "\e185"; } }
.glyphicon-step-backward          { &:before { content: "\e069"; } }
.glyphicon-step-forward           { &:before { content: "\e077"; } }
.glyphicon-stop                   { &:before { content: "\e074"; } }
.glyphicon-subscript              { &:before { content: "\e256"; } }
.glyphicon-subtitles              { &:before { content: "\e188"; } }
.glyphicon-sunglasses             { &:before { content: "\e240"; } }
.glyphicon-superscript            { &:before { content: "\e255"; } }
.glyphicon-tag                    { &:before { content: "\e041"; } }
.glyphicon-tags                   { &:before { content: "\e042"; } }
.glyphicon-tasks                  { &:before { content: "\e137"; } }
.glyphicon-tent                   { &:before { content: "\26fa"; } }
.glyphicon-text-background        { &:before { content: "\e243"; } }
.glyphicon-text-color             { &:before { content: "\e242"; } }
.glyphicon-text-height            { &:before { content: "\e050"; } }
.glyphicon-text-size              { &:before { content: "\e241"; } }
.glyphicon-text-width             { &:before { content: "\e051"; } }
.glyphicon-th                     { &:before { content: "\e011"; } }
.glyphicon-th-large               { &:before { content: "\e010"; } }
.glyphicon-th-list                { &:before { content: "\e012"; } }
.glyphicon-thumbs-down            { &:before { content: "\e126"; } }
.glyphicon-thumbs-up              { &:before { content: "\e125"; } }
.glyphicon-time                   { &:before { content: "\e023"; } }
.glyphicon-tint                   { &:before { content: "\e064"; } }
.glyphicon-tower                  { &:before { content: "\e184"; } }
.glyphicon-transfer               { &:before { content: "\e178"; } }
.glyphicon-tree-conifer           { &:before { content: "\e199"; } }
.glyphicon-tree-deciduous         { &:before { content: "\e200"; } }
.glyphicon-triangle-bottom        { &:before { content: "\e252"; } }
.glyphicon-triangle-left          { &:before { content: "\e251"; } }
.glyphicon-triangle-right         { &:before { content: "\e250"; } }
.glyphicon-triangle-top           { &:before { content: "\e253"; } }
.glyphicon-unchecked              { &:before { content: "\e157"; } }
.glyphicon-volume-down            { &:before { content: "\e037"; } }
.glyphicon-volume-off             { &:before { content: "\e036"; } }
.glyphicon-volume-up              { &:before { content: "\e038"; } }
.glyphicon-wrench                 { &:before { content: "\e136"; } }
.glyphicon-xbt                    { &:before { content: "\e227"; } }
.glyphicon-yen                    { &:before { content: "\00a5"; } }
.glyphicon-zoom-in                { &:before { content: "\e015"; } }
.glyphicon-zoom-out               { &:before { content: "\e016"; } }

