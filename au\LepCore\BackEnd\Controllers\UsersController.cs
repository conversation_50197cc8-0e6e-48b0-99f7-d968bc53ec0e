using AutoMapper;
using lep.email;
using lep.user;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;

namespace LepCore.Controllers
{
	/// <summary>
	/// </summary>
	[AllowAnonymous]
	[Produces("application/json")]
	[Route("api/Users")]
	[ApiExplorerSettings(IgnoreApi = true)]
	public class UsersController : Controller
	{
		private readonly IMapper _mapper;
		private readonly IUserApplication _userApplication;
		private readonly IEmailApplication _emailApplication;

		private readonly IHttpContextAccessor _contextAccessor;

		public UsersController(
			IHttpContextAccessor contextAccessor,
			IUserApplication userApplication,
			IEmailApplication emailApplication,
			IMapper mapper
		)
		{
			_contextAccessor = contextAccessor;
			_userApplication = userApplication;
			_emailApplication = emailApplication;
			_mapper = mapper;
		}

		[HttpPost("{id:int}/password/reset")]
		public IActionResult PasswordReset([FromRoute] int id, [FromBody]  string newPassword)
		{
			var user = _userApplication.GetUser(id);
			if (user == null) return BadRequest();
			try
			{
				var emailNeedstoBeSent = false;
				if (string.IsNullOrEmpty(newPassword))
				{
					emailNeedstoBeSent = true;
					newPassword = Utils.GeneratePassword(10);
				}
				_userApplication.SetPassword(user, newPassword);
				_userApplication.Save(user);

				if (emailNeedstoBeSent)
				{
					_emailApplication.SendForgottenPassword(user, newPassword);
				}

				return Ok();
			}
			catch (Exception)
			{
				return BadRequest();
			}
		}

		[HttpPost("{id:int}/IsValidUsername")]
		[Produces(typeof(bool))]
		public IActionResult IsValidUsername([FromRoute] int id, [FromBody] string userName)
		{
			var user = _userApplication.GetUser(id);
			if (user == null)
			{
				user = _userApplication.NewCustomerUser();
			}
			var valid = _userApplication.IsValidUsername(userName, user);
			return new OkObjectResult(valid);
		}

		[HttpPost("{id:int}/IsValidBusinessName")]
		[Produces(typeof(bool))]
		public IActionResult IsValidBusinessName([FromRoute] int id, [FromBody] string name)
		{
			var user = _userApplication.GetCustomerUser(id);
			if (user == null)
			{
				user = _userApplication.NewCustomerUser();
				user.Name = name;
			}
			var valid = _userApplication.CheckBusinessName(user);
			return new OkObjectResult(valid);
		}

		[HttpPost("{id:int}/IsValidMYOB")]
		[Produces(typeof(bool))]
		public IActionResult IsValidMYOB([FromRoute] int id, [FromBody] string name)
		{
			var user = _userApplication.GetCustomerUser(id);
			if (user == null)
			{
				user = _userApplication.NewCustomerUser();
				user.Name = name;
			}
			var valid = _userApplication.IsUniqueMYOB(name, user);
			return new OkObjectResult(valid);
		}
	}
}
