﻿<style>
	.tab-content {
		height: 0px;
	}

	#tx {
		display: table;
		width: 100%;
		border-collapse: collapse;
	}

		#tx tr {
			border-top: 10px solid #ffffff;
			border-bottom: 10px solid #f3f4f7;
			line-height: 4;
		}

		#tx td {
			padding: 4px;
			background-color: transparent
		}
</style>

<div class="row">
	<div class="col-sm-12 col-md-3">
		<h3 style="margin-top:0px;">Your Customers</h3>

		<div class="blue-box">
			<h5><b>Search</b></h5>
			<div class="input-group">
				<label class="control-label" for="customer">Customer</label>

				<input id="customer" type="text" class="form-control " ng-model="vm.Customer"
					   ng-model-options="{debounce:500}"
					   placeholder="Customer name, number, contact name number" />
			</div>

			<div class="input-group">
				<label class="control-label" for="order-no">Order #</label>
				<input id="order-no" type="text" class="form-control" ng-model="vm.OrderNr"
					   ng-model-options="{debounce:500}" />
			</div>

			<div class="input-group">
				<label class=" control-label" for="Job-num">Job #</label>
				<input id="Job-num" type="text" class="form-control " ng-model="vm.JobNr"
					   ng-model-options="{debounce:500}" />
			</div>





			<div class="input-group">
				<label class="control-label" for="payment-terms">Payment terms</label>

				<select id="payment-terms" name="templateId" class="form-control " ng-model="vm.PaymentTerms">
					<option selected="selected" value="">-- Any --</option>
					<option value="Account">On Account</option>
					<option value="COD">Cash Before Dispatch</option>
				</select>

			</div>

			<div class="input-group">
				<label class="control-label" style="font-weight:100;"  ><input type="checkbox" ng-model="vm.ShowArchived">Show archived customers</label>
			</div>
			

			<div style="height:20px"></div>
			<div class="right">
				<button type="reset" class="btn " ng-click="clear()"><i class="glyphicon glyphicon-erase"></i> Clear</button>
				<button id="cust-btn" class="btn btn-default" ng-click="search()"><i class="glyphicon glyphicon-search"></i> Search</button>
			</div>
			<div style="height:20px"></div>
		</div>

	</div>

	<div class="col-sm-12 col-md-9">
		
		<a class="btn btn-default pull-right" ui-sref="cust.sub-customers-view({id: 0})">
				<i class="glyphicon glyphicon-plus-sign"></i>
				Create new account

		</a>


		<div class="row">
			<div class="col-sm-12">

				<table id="tx">
					<tbody>
						<tr ng-repeat="c in r.List" class="white">
							<td>
								<a class="bold" ui-sref="cust.sub-customers-view({id: c.Id})">
									{{c.Name}}

                                 
								</a>
							</td>
							<td>{{::enums.ValueKey.PaymentTermsOptions[c.PaymentTerms]}}</td>
                            <td>

                                <a ng-if="ppv >= 2"   ng-click="createOrderAs(c.Id)"> <span class="glyphicon glyphicon-shopping-cart"> </span></a>
                              
                                <!--<a ng-click="findOrdersFor(c.Id)"> <span class="glyphicon glyphicon-search"> </span> </a>-->

                            </td>
						</tr>
					</tbody>
				</table>
				{{r.Summary}}
				<div paging
					 page="r.Page"
					 page-size="r.PageLength"
					 total="r.Total"
					 paging-action="goPage(page)"
					 scroll-top="false"
					 hide-if-empty="true"
					 show-prev-next="true"
					 show-first-last="true"
					 text-next-class="glyphicon glyphicon-chevron-right"
					 text-prev-class="glyphicon glyphicon-chevron-left"
					 text-first-class="glyphicon glyphicon-backward"
					 text-last-class="glyphicon glyphicon-forward">
				</div>
			</div>
		</div>


	</div>

</div>





