(function (angular, window) {
    var appStaff = angular.module('app.staff');

    var nextDayDespatchTemplateIds = [4,13,18,19,20,21,22,26];
    var sameDayDespatchTemplateIds = [23,27,29];

  appStaff.directive('keycheck', function () {
        return {
            restrict: 'E',
            replace: true,
            scope: true,
            link: function postLink(scope, iElement, iAttrs) {
                $(document).bind('keydown', function (e) { scope.$apply(scope.keyPress(e)); });
                $(document).bind('keyup', function (e) { scope.$apply(scope.keyUp(e)); });
            }
        };
    });

  appStaff.controller('RunCtrl', [
        '$scope', '$interval', '$http', 'ngDialog', 'enums', '$rootScope', '$timeout', 'userRunSearchCriteriaOld', 'JobService','$localStorage', 'templateTree',
        function ($scope, $interval, $http, ngDialog, enums, $rootScope, $timeout, userRunSearchCriteriaOld, JobService, $localStorage, templateTree) {

            // Make templateTree available to the view
            $scope.templateTree = templateTree;

            $scope.allStocksKind = ['Uncoated', 'Matt', 'Gloss', 'Artboard'];
            $scope.openJobS = function (oid, jid) {
                var url = window.location.origin + '#!/staff/order/' + oid + '/job/' + jid;
                window.open(url);
            };

            function openOrderS(oid) {
                var url = window.location.origin + '#!/staff/jobs/' + jid;
            }


            $scope.enums = enums;

            $scope.runSearchOptions = angular.copy(enums.ValueDesc.RunSearchOptions);
            $scope.runSearchOptions["0"] = "-- Any --";

            $scope.runCelloOptions = angular.copy(enums.ValueDesc.RunCelloglazeOptions);
            //$scope.runCelloOptions["0"] = "-- Any --";


            JobService.getAllSizes().then(function (d) {
                $scope.allSizes = d;
            });

            JobService.getAllStocksForRuns().then(function (d) {
                $scope.allStocks = d;
            });

            // Load templates for the Template dropdown
            JobService.getTemplates().then(function (d) {
                $scope.templates = d;
            });

            $scope.searchcriteria = userRunSearchCriteriaOld;

            $scope.allowOperation = true;

            $scope.lastcriteria = null;
            $scope.showUneffected = false;
            $scope.savedRunIds = [];
            $scope.savedJobIds = [];
            $scope.runs = [];
            $scope.jobs = [];

            var LastJobRetrieve = null;
            var LastRunRetrieve = null;
            var intervalCheck;
            var isProcessing = false;
            var altkeypressed = false;

            function updateRunProperties(run) {

                run.uneffectedCss = "";
                run.selected = false;
                run.showjobCss = run.Open ? "showjob" : "hidejob";
                run.jobnr = "";

                var rindex = $scope.savedRunIds.indexOf("R" + run.Id);
                if (rindex > -1) {
                    uneffectedCss = $scope.showUneffected ? "uneffected" : "";
                    run.selected = true;
                    $scope.savedRunIds.splice(rindex, 1);
                }
                var countOfDigitals = _.filter(run.Jobs, function( _) { return _.PrintType == 'D';}).length;
                if(countOfDigitals > 0){
                    run.uneffectedCss += " DigitalStyle "
                }

                //cd (run.Id, countOfDigitals)
                run.TimeTillDispatch = secondsToString(run.HD);
                for (var i = 0; i < run.Jobs.length; i++) {
                    run.Jobs[i].TimeTillDispatch = secondsToString(run.Jobs[i].HD);

                    run.Jobs[i].uneffectedCss = $scope.showUneffected ? "uneffected" : "";

                    if (nextDayDespatchTemplateIds.indexOf(run.Jobs[i].TemplateId) > -1) {
                        run.uneffectedCss += " nextDayDespatch ";
                        run.Jobs[i].uneffectedCss  += " nextDayDespatch ";

                    }

                    if (sameDayDespatchTemplateIds.indexOf(run.Jobs[i].TemplateId) > -1) {
                        run.uneffectedCss += " sameDayDespatch ";
                        run.Jobs[i].uneffectedCss  += " sameDayDespatch ";
                    }

                    //cd(run.Jobs[i].Id, run.Jobs[i].PrintType)
                    if (run.jobnr.length > 0) {
                        run.jobnr += "\n";
                    }
                    run.jobnr += run.Jobs[i].JobNr;
                    run.Jobs[i].searchedCss = run.Jobs[i].Searched ? "searched " : "";

                    var rjindex = $scope.savedRunIds.indexOf("R" + run.Id + " J" + run.Jobs[i].Id + " " + run.Jobs[i].Job);
                    if (rjindex > -1) {


                        run.Jobs[i].selected = true;
                        $scope.savedRunIds.splice(rjindex, 1);
                    } else {
                        if (run.Jobs[i].Size == "Business Card (90 x 55)") {
                            run.Jobs[i].uneffectedCss += " trimsize90x55 ";
                        } else {
                            run.Jobs[i].uneffectedCss += "";
                        }
                        run.Jobs[i].selected = false;
                    }

                    if(run.Jobs[i].PrintType == 'D')
                        run.Jobs[i].uneffectedCss += ' DigitalStyle ';


                }
            }

            function LoadingWait() {
                //toastr.info('Loading data, please wait.');
            }

            $scope.$on('ngDialog.closed', function (e, $dialog) {
                if ($dialog.hasClass("errorDialog")) {
                    intervalCheck = $interval(function () { $scope.List(); }, 10000);
                }
            });

            function secondsToString(seconds) {
                var result = "";
                if (!seconds) return result;

                var value = Math.abs(seconds);

                var units = {
                    "d": 24 * 60 * 60,
                    "h": 60 * 60,
                    "m": 60,
                }

                var result = [];

                for (var name in units) {
                    var p = Math.floor(value / units[name]);
                    if (p)
                        result.push("" + p + "" + name);
                    value %= units[name];
                }
                result = result.join(" ");
                if (seconds && seconds < 0) result = '-' + result;
                return result;

            }
            function SendRequest(data, showmsg) {

                if (isProcessing) {
                    LoadingWait();
                    return;
                } else {
                    isProcessing = true;
                }

                if (angular.isDefined(intervalCheck)) {
                    $interval.cancel(intervalCheck);
                    intervalCheck = undefined;
                }

                $scope.lastcriteria = data;

                if (showmsg) {
                    //  ngDialog.open({ template: 'processingTemplate', showClose: false, closeByEscape: false, closeByDocument: false, className: 'ngdialog-theme-default processDialog' });
                }

                if (data.hasconfirm) {
                    $scope.lastcriteria = null;
                }
                data.LastJobRetrieve = LastJobRetrieve;
                data.LastRunRetrieve = LastRunRetrieve;

                //if (!data.Search) {
                //    data.Search = angular.copy($scope.searchcriteria)
                //}


                $http({
                    method: 'POST',
                    url: "/api/runs/process",
                    data: data
                }).then(
                    function (response, status, headers, config) {

                        var data = response.data;
                        isProcessing = false;
                        if (data.LastJobRetrieve) {
                            LastJobRetrieve = data.LastJobRetrieve;
                        }
                        if (data.LastRunRetrieve) {
                            LastRunRetrieve = data.LastRunRetrieve;
                        }

                        $scope.AutoRunAllocationEnabled = data.AutoRunAllocationEnabled;

                        ngDialog.close();

                        if (data.confirm) {
                            intervalCheck = $interval(function () { $scope.List(); }, 10000);
                            if (window.confirm(data.confirm)) {
                                $scope.lastcriteria.hasconfirm = true;

                                SendRequest($scope.lastcriteria, true);

                            }
                            return;
                        }

                        $scope.showUneffected = false;
                        $scope.messages = [];
                        var showUneffected = false;
                        var showItemChange = data.action == "search" || data.action == "list";
                        if (data.error) {
                            $scope.messages = ['Process fail:', data.error];
                            showUneffected = true;
                            showItemChange = false;
                        }

                        if (data.runs) {

                            $scope.savedRunIds = [];
                            if (showItemChange) {
                                angular.forEach($scope.runs, function (run, rindex) {
                                    if (run.selected) {
                                        $scope.savedRunIds.push("R" + run.Id);
                                    }
                                    if (run.Open) {
                                        angular.forEach(run.Jobs, function (job, jindex) {
                                            if (job.selected) {
                                                $scope.savedRunIds.push("R" + run.Id + " J" + job.Id + " " + job.Job);
                                            }
                                        });
                                    }
                                });
                            }
                            angular.forEach(data.runs, function (r) {
                                updateRunProperties(r);
                            });
                            $scope.runs = data.runs;
                            if (showItemChange) {
                                angular.forEach($scope.savedRunIds, function (rid, rindex) {
                                    $scope.messages.push(rid + ' not selectable');
                                });
                            }
                        }

                        if (data.jobs) {
                            $scope.savedJobIds = [];
                            if (showItemChange) {
                                angular.forEach($scope.jobs, function (job, jindex) {
                                    if (job.selected) {
                                        $scope.savedJobIds.push('J' + job.Id + ' ' + job.Job);
                                    }
                                });
                            }
                            angular.forEach(data.jobs, function (job) {


                                job.TimeTillDispatch =   secondsToString(job.HD);

                                var uneffectedCss = "";
                                var jindex = $scope.savedJobIds.indexOf('J' + job.Id + ' ' + job.Job);
                                if (jindex > -1) {
                                    uneffectedCss = $scope.showUneffected ? "uneffected" : "";
                                    job.selected = true;
                                    $scope.savedJobIds.splice(jindex, 1);
                                }

                                job.rowCss = "";

                                if (sameDayDespatchTemplateIds.indexOf(job.TemplateId) > -1) {
                                    job.rowCss = " sameDayDespatch ";
                                }

                                if (nextDayDespatchTemplateIds.indexOf(job.TemplateId) > -1) {
                                    job.rowCss = " nextDayDespatch ";
                                }

                                    if (job.Size == 'Business Card (90 x 55)') {
                                        job.rowCss += " trimsize90x55 ";
                                    }
                                    else {
                                        job.rowCss += uneffectedCss + " " + (job.Health||'');
                                }

                                if (job.PrintType =='D') {
                                    job.rowCss += " DigitalStyle ";
                                }


                            });

                            $scope.jobs = data.jobs;

                            if (showItemChange) {
                                angular.forEach($scope.savedJobIds, function (jid, rindex) {
                                    $scope.messages.push(jid + ' not selectable');
                                });
                            }
                        }

                        if ($scope.messages.length > 0) {
                            ngDialog.open({ template: 'warningTemplate', closeByDocument: false, scope: $scope, className: 'ngdialog-theme-default errorDialog' });
                        } else {
                            //  intervalCheck = $interval(function () { $scope.List(); }, 10000);
                        }
                    },

                    function (data, status, headers, config) {
                        ngDialog.close();
                        toastr.error('Error when process request');
                        isProcessing = false;

                    }

                    );
                //.error();
            } // end send request

            $scope.keyPress = function (e) {
                if (e.keyCode == 17) {
                    altkeypressed = true;
                }
            };

            $scope.keyUp = function (e) {
                if (e.keyCode == 17) {
                    altkeypressed = false;
                }
            };

            $scope.SearchClick = function (forceSave) {
                if (isProcessing) {
                    //toggle back

                    LoadingWait();
                    return;
                }
                var isShow = true;
                if (forceSave) {
                    SendRequest({ Action: "panel", Search: { searchopen: !isShow } }, false);
                }
            };

            $scope.clear = function () {
                var x = $scope.searchcriteria;
                x.RunStatus = [];
                x.Customer = '';
                x.OrderNr = '';
                x.JobNr = '';
                x.RunNr = '';
                x.IsUrgent = false;
                x.IsOnHold = false;
                x.RunSearchOption = 0;
                x.Size   = null;
                x.Cello   = null;
                x.Side   = null;
                x.Stock   = null;
                x.Ordering = '';
                // Clear the new filters
                x.JobTypes = null;
                x.JobType = null;
                $scope.Search();
            };

            $scope.RAEnable = function () {
                var dd = { Action: "RAEnable", Search: $scope.searchcriteria };
                SendRequest(dd, true);
            }
            $scope.RADisable = function () {
                var dd = { Action: "RADisable", Search: $scope.searchcriteria };
                SendRequest(dd, true);
            }

            $scope.Search = function (ordering, runordering) {
                var x = $scope.searchcriteria;
                if (isProcessing) {
                    LoadingWait();
                    return;
                }
                if (ordering) {
                    if (x.Ordering == ordering) {
                        x.Ordering = ordering + "-";
                    } else if (x.Ordering == (ordering + "-")) {
                        x.Ordering = ordering;
                    } else {
                        x.Ordering = ordering;
                    }
                }

                if (runordering) {

                    if (x.RunOrdering == runordering) {
                        x.RunOrdering = runordering + "-";
                    } else if (x.RunOrdering == (runordering + "-")) {
                        x.RunOrdering = runordering;
                    } else {
                        x.RunOrdering = runordering;
                    }

                }
                var dd = { Action: "search", Search: $scope.searchcriteria };
                //console.debug(dd);
                SendRequest(dd, true);
            };


            //todo
            $scope.SetRunJobCtrlClick = function (run, jobindex) {
                if (!altkeypressed) {
                    return;
                }
                for (var i = jobindex - 1; i >= 0; i--) {
                    if (run.Jobs[i].selected) {
                        break;
                    } else {
                        run.Jobs[i].selected = true;
                    }
                }
            };

            $scope.OpenRun = function (run) {
                if (isProcessing) {
                    LoadingWait();
                    return;
                }
                run.Open = !run.Open;
                run.showjobCss = run.Open ? "showjob" : "hidejob";
                if (run.Open) {
                    SendRequest({ action: "runopen", RunOpenClose: run.Id }, false);
                } else {
                    SendRequest({ action: "runclose", RunOpenClose: run.Id }, false);
                }
            };

            $scope.RemoveRun = function (run) {
                if (isProcessing) {
                    LoadingWait();
                    return;
                }

                if (run.selected) {
                    SendRequest({ Action: "removerun", RunId: run.Id }, true);
                } else {
                    var jobs = [];

                    angular.forEach(run.Jobs, function (job, index) {
                        if (job.selected) {
                            jobs.push(job.Id);
                        }
                    });
                    if (jobs.length === 0) {
                        return false;
                    }
                    SendRequest({ Action: "removerun", RunId: run.Id, JobIds: jobs }, true);
                }
            };

            $scope.CreateNewRun = function () {
                var selectedJobIds = _($scope.jobs).filter({ selected: true }).map('Id').value();
                if (selectedJobIds.length === 0) {
                    toastr.info("Please choose the jobs from which you want to create a run and try again");
                    return;
                }

                SendRequest({ Action: "addnewrun", JobIds: selectedJobIds }, true);
            };

            $scope.toggleJobSelected = function (run) {
                angular.forEach(run.Jobs, function (j) {
                     j.selected = run.selected;
                });
            }

            $scope.MoveToRun = function (run) {
                if (isProcessing) {
                    LoadingWait();
                    return;
                }

                var jobs = [];

                angular.forEach($scope.runs, function (r) {
                    if (r.Open && r.Id != run.Id) {
                        angular.forEach(r.Jobs, function (j) {
                            if (j.selected) {
                                jobs.push({ jobid: j.Id, runid: r.Id });
                            }
                        });
                    }
                });
                angular.forEach($scope.jobs, function (j) {
                    if (j.selected) {
                        jobs.push({ jobid: j.Id });
                    }
                });

                if (jobs.length > 0) {

                    SendRequest({ Action: "movetorun", RunId: run.Id, JobIdRunIds: jobs , HasConfirm: false}, true);
                }
            };


            $scope.MoveToNewRun = function () {
                if (isProcessing) {
                    LoadingWait();
                    return;
                }

                var jobs = [];
                angular.forEach($scope.runs, function (r) {
                    if (r.Open) {
                        angular.forEach(r.Jobs, function (j) {
                            if (j.selected) {
                                jobs.push({ jobid: j.Id, runid: r.Id });
                            }
                        });
                    }
                });

                SendRequest({ Action: "movetorun", RunId: 0, JobIdRunIds: jobs }, true);
            };

            $scope.List = function () {
                SendRequest({ Action: "list" }, false);
            };

            // <% if (ShowSearch) { %>
            $scope.SearchClick(false);
            //    <% } %>

            intervalCheck = $interval(function () { $scope.List(); }, 40000);
            $scope.List();


            $scope.$watchCollection('searchcriteria.RunStatus', function () { $scope.Search(null, null); }, true);

            $scope.$on ('visibilitychange', function(v, e){
                if(e === 'visible')
                $scope.Search();
            });

            $scope.allocate = function(){
                $http.get('/api/runs/allocate').then(function(){
                    $scope.Search(null, null);
                });

            }
            //$scope.toggleRunSort = function(sort) {
            //    $scope.vm.SortDir =  sort == $scope.vm.SortField and !$scope.vm.SortDir
            //    $scope.vm.SortField = sort
            //}

            $scope.jobToDPC = function(jobId) {
                $http.post('/api/orders/JobToDpc/' + jobId).then(function(){
                    $scope.Search(null, null);
                    window.toastr.info("Job " + jobId + " moved to DPC Pre Production" )
                });

            }

            $scope.jobToIMP = function(jobId) {
                $http.post('/api/runs/SendToImp/job/' + jobId).then(function(response){
                    window.toastr.info("Job " + jobId + " sent to IMP" );
                }, function(error) {
                    window.toastr.error("Error sending job to IMP: " + error.data.message);
                });
            }

            $scope.runToIMP = function(runId) {
                $http.post('/api/runs/SendToImp/run/' + runId).then(function(response){
                    window.toastr.info("Run " + runId + " sent to IMP" );
                }, function(error) {
                    window.toastr.error("Error sending run to IMP: " + error.data.message);
                });
            }




            var favRunSearches;

            favRunSearches = $localStorage.favRunSearches || [];

            $scope.favRunSearches = favRunSearches;

            $scope.setAsCurrentSearch = function(fs) {
                $scope.searchcriteria = fs.filters;
                isProcessing = false;

                $scope.Search(null, null)
            };

            $scope.saveAsFavSearch = function() {
                bootbox.prompt({
                title: "Enter a name for this search!",
                centerVertical: true,
                value: "",
                closeButton: false,
                callback: function(name) {
                    if (!name) {
                    return;
                    }
                    $timeout(function() {
                        var newSearch;
                        newSearch = {
                            Name: name,
                            filters: angular.copy($scope.searchcriteria)
                        };
                        newSearch.page = 1;
                        favRunSearches.push(newSearch);
                        $localStorage.favRunSearches = angular.copy(favRunSearches);
                        $scope.favRunSearches = angular.copy(favRunSearches);
                        // Force localStorage to persist the data immediately
                        try {
                            localStorage.setItem('ngStorage-favRunSearches', JSON.stringify($localStorage.favRunSearches));
                        } catch (e) {
                            console.error('Error saving to localStorage:', e);
                        }
                    }, 10);
                }
                });
            };

            $scope.removeFavSearch = function(fs) {
                _.remove(favRunSearches, {
                Name: fs.Name
                });
                $localStorage.favRunSearches = angular.copy(favRunSearches);
                $scope.favRunSearches = angular.copy(favRunSearches);
                // Force localStorage to persist the data immediately
                try {
                    localStorage.setItem('ngStorage-favRunSearches', JSON.stringify($localStorage.favRunSearches));
                } catch (e) {
                    console.error('Error saving to localStorage:', e);
                }
            };

            $scope.ifAnyJobHasSpecialInstructions = function(run) {
                var result = false;
                angular.forEach(run.Jobs, function (j) {
                    if (j.SpecialInstructions) {
                        result = true;
                        return false;
                    }
                });
                return result;
            }

            $scope.ifAnyJobHasProductionInstructions = function(run) {
                var result = false;
                angular.forEach(run.Jobs, function (j) {
                    if (j.ProductionInstructions) {
                        result = true;
                        return false;
                    }
                });
                return result;
            }

        }]);

})(window.angular, window);
