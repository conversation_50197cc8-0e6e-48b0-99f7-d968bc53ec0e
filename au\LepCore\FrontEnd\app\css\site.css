
body {
    height: 138vh !important;
   
}
.ngdialog-open {
    height: auto !important;
}

.ngdialog.ngdialog-theme-default.custom-width-800 .ngdialog-content {
    width: 800px;
}

.ngdialog.ngdialog-theme-default.custom-width-900 .ngdialog-content {
    width: 940px;
}

.ngdialog.custom-width-full {
    width: 90% !important;
    width: 90% !important;
    padding: 20px !important;
    background-color: white !important;
}


.custom-width-900 {
    width: 95%;
    line-height: 1.2;
}

    .custom-width-900 .modal-body {
        max-height: 65%;
        overflow-y: scroll;
    }

    .custom-width-900 * {
        font-size: 10pt;
    }

.modal-body .alert {
    padding: 4px;
}

.table-wrapper-scroll-y {
    max-height: 200px;
    overflow-y: auto;
    -ms-overflow-style: -ms-autohiding-scrollbar;
}

.highlight-search {
    font-weight: bold;
    color: #ff145c !important;
    border: 2px solid #ff0b0b !important;
    background-color: #ea2d2d;
}

.white {
    background: white;
}

.error-redish {
    color: #d9534f;
}

.col-centered {
    float: none;
    margin: 0 auto;
    display: table;
    text-align: center;
}

.vcenter {
    vertical-align: middle;
}

.scale2 {
    transform: scale(2);
}

@font-face {
    font-family: 'barcode';
    src: url("/fonts/3OF9_NEW.TTF") format("truetype");
}

html,
body {
    height: 100%;
}

option {
    color: black !important;
}

.u {
    text-decoration: underline;
}

.barcode {
    font-family: 'barcode';
    font-size: 48pt !important;
}

ul.nav li {
    display: block;
}

.lpad0 {
    padding-left: 0 !important;
}

.rpad0 {
    padding-right: 0 !important;
}

.content.container.staffView {
   width: 98% !important;
}
.content.container.customerView {

}

.container-fluid {
    width: 100% !important;
}

.block {
    display: block;
}

.inline-block {
    display: inline-block;
}

p {
    font-size: 12px;
}

.readonly-text {
    /* padding-top: 5px; */
}

/* body {
    padding-bottom: 0px;
    font-family: 'Myriad';
} */

a {
    outline: 0;
}

.content.container {
    min-height: 70%;
}

select#freights,
select.monospace, select.monospace option {
    font-family: monospace !important;
    white-space: pre !important;
}

.navbar-brand > img {
    display: inline-block;


}

.light-grey {
    color: #8E8E8E;
}

/* Set padding to keep content from hitting the edges */
.body-content {
    padding-left: 15px;
    padding-right: 15px;
}

.pad0 {
    padding: 0;
}

.input-group-addon *[class^="col-"] {
    /* padding-left: 0;
    padding-right: 0; */
}

/*Form Styling*/
.form-control, .form-group-sm .form-control {
    /* border-radius: 0; */
}

.form-actions {
    margin-bottom: 15px;
    text-align: right;
}

/*
.form-control.ng-invalid {
	border-color: #d9534f;
}

*/
.form-control.pristine.ng-invalid::after {
    content: "\e091";
}

.help-block2 {
    position: absolute;
    top: 10px;
    right: -50px;
}

.ng-invalid.ng-dirty.form-control {
    color: #d9534f;
    border: 1px solid #d9534f;
}

.input-error span {
    color: #d9534f;
    width: 100%;
    display: block;
    padding: 7px 1px;
}

/*Log in Styling
#login-row {
  width: 420px;
  margin: auto; }

#login-row form {
  margin-top: 20px; }

login-row p {
  color: #577D93; }
*/
/*nav bar styles*/
.navbar-inverse {
    background-color: white !important;
    border: none;
}

/*.navbar .container {
  border-bottom: 1px solid #DDE5EB; }*/
.navbar-nav {
    float: right;
}

.navbar-brand span {
    color: #8CA5B9;
    font-weight: bold;
    font-style: italic;
    text-decoration: none;
    float: left;
    font-size: 20px;
    position: relative;
    left: 138px;
    bottom: 30px;
}

#branding {
    width: 215px;
    font-size: 18px;
    height: 170px;
    margin: auto;
}

    #branding span {
        color: #8CA5B9;
        font-weight: bold;
        font-style: italic;
        text-decoration: none;
        font-size: 20px;
        position: relative;
        left: 13px;
        bottom: -19px;
    }

/*Side bar styles*/
.blue-box {
    padding: 10px;
    background-color: #DDE5EB;
    margin: 2px 0;
    color: #474D54;
}

    .blue-box a {
        color: black;
    }

.dark-blue-bck,
.dark-blue-bck a {
    background-color: #577C93 !important;
    color: #fff !important;
}

.box-num {
    font-size: 30px;
    font-weight: bold;
    line-height: 30px;
}

.box-title {
    font-size: 17px;
    line-height: 22px;
}

.blue-box .input-group {
    margin-top: 7px;
}

.blue-box .form-control, .blue-box .input-group-addon {
    box-shadow: none;
    border-color: #98AFC0;
    background-color: #FFFFFF;
    border-radius: 2px;
}

.blue-box .input-group-addon {
    border-left: none;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    padding: 4px 12px;
}

.blue-box .form-control.with-addon {
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.blue-box label.col-sm-12.control-label {
    margin-top: 10px;
    margin-bottom: 2px;
}

.blue-box ul {
    padding: 0;
    margin-bottom: 0;
}

    .blue-box ul li {
        list-style: none;
        font-size: 12px;
    }

        .blue-box ul li span {
            font-weight: bold;
        }

order-jobs {
    font-size: 12px;
}

/*form styles*/
.large-form-fields .form-control {
    padding: 10px;
    font-size: 18px;
    height: auto;
}

.form-horizontal .form-group-sm.large-form-fields .control-label {
    /* font-size: 18px;
    line-height: 18px;
    padding-top: 15px; */
}

.hr-seperator {
    margin: 5px 0;
}

.collapse-btn {
    color: #8CA5B9;
    text-decoration: none;
    display: block;
    padding: 5px;
    width: 30px;
    height: 30px;
    float: right;
    cursor: pointer;
    text-align: center;
    vertical-align: middle;
}

    .collapse-btn :hover {
        animation: 0.5s tada ease;
    }

.order-box h3 {
    margin: 10px 0;
}

.job-box {
    border-bottom: 1px solid #f9f9f9;
    padding: 2px 0 2px;
}

.paste-popover {
    cursor: pointer;
}

.popover-content ul {
    padding: 5px;
    margin-bottom: 0;
}

    .popover-content ul li {
        list-style: none;
    }

        .popover-content ul li a {
            font-size: 12px;
            cursor: pointer;
        }

.job-box:last-child {
    border: none;
}

/**/
.job-box div.two-story-col {
    height: 33px;
}

    .job-box div.two-story-col span {
        line-height: 12px;
        display: block;
        position: relative;
    }

        .job-box div.two-story-col span:last-child {
            top: -16px;
        }

/*ORDERS PAGE*/
.form-horizontal div.checkbox:first-child, .form-horizontal div.radio:first-child {
    /* padding-top: 7px; */
}

.form-horizontal .checkbox, .form-horizontal .radio {
    /* min-height: 22px;
    padding-top: 0; */
}

/*Your settings*/
.your-settings .nav.nav-pills {
    width: 25%;
    float: left;
}

.tab-content {
    width: 75%;
    float: left;
}

.nav-pills > li.active > a, .nav-pills > li.active > a:hover, .nav-pills > li.active > a:focus {
    background-color: #577C93;
}

.tab-content > .tab-pane {
    padding-top: 20px;
}

.help-block {
    font-size: 12px;
}

a.active,
.blue-box.active a {
    padding: 5px;
    display: block;
}

.blue-box a {
    padding: 5px;
    display: block !important;
}

a.active-nav, a.active-nav:hover, a.active-nav:active {
    font-weight: bold;
    color: #d1eeff;
    background-color: #DDE5EB !important;
    text-decoration: none !important;
}

.price {
    font-weight: 800;
    font-size: larger;
}

.template-img1 {
    /* text-align: center; */
    height: 57px;
    position: relative;
    /* filter: contrast(70%); */
    top: 0px;
}

.template-matrix-row {
    margin-bottom: 15px;
    padding-top: 15px;
    border-top: 1px solid #DDE5EB;
}

    .template-matrix-row .checkbox {
        margin: 0;
    }

    .template-matrix-row .alltoggle {
        color: gray;
    }

        .template-matrix-row .alltoggle input {
            border: none;
        }

.form-horizontal .control-label {
    padding-right: 0;
}

.right {
    text-align: right;
}

label {
    cursor: pointer;
}

.notice {
    animation: 1s slideInRight ease;
}

.order-box {
    background-color: #fff;
    padding: 0px;
    padding-left: 15px;
    /* padding-bottom: 0px; */
    margin-bottom: 10px;
}

    .order-box .dbg {
        background-color: #e9e9e9;
    }

.we-are-printing .order-box {
    background-color: #faebdf;
}

    .we-are-printing .order-box .dbg {
        background-color: #f2e4d8;
    }

.order-box a {
    color: #474d54;
    text-decoration: none;
}

.order-box h3 {
    display: inline-block;
}

.order-box a:hover {
    text-decoration: underline;
}

.job-box {
    border-top: 1px solid #f9f9f9;
    padding: 15px 0 15px;
    display: block !important;
}

.line {
    border-top: 1px solid #e5e5e5;
    margin-left: -15px;
    margin-bottom: 5px;
}

/**/
.animate-enter,
.animate-leave {
    -webkit-transition: 400ms cubic-bezier(0.25, 0.25, 0.75, 0.75) all;
    -moz-transition: 400ms cubic-bezier(0.25, 0.25, 0.75, 0.75) all;
    -ms-transition: 400ms cubic-bezier(0.25, 0.25, 0.75, 0.75) all;
    -o-transition: 400ms cubic-bezier(0.25, 0.25, 0.75, 0.75) all;
    transition: 400ms cubic-bezier(0.25, 0.25, 0.75, 0.75) all;
    position: relative;
    display: block;
}

    .animate-enter.animate-enter-active,
    .animate-leave {
        opacity: 1;
        top: 0;
        height: 30px;
    }

        .animate-leave.animate-leave-active,
        .animate-enter {
            opacity: 0;
            top: -50px;
            height: 0px;
        }

.debug {
    white-space: pre;
    font-family: monospace;
    font-size: 10px;
}

a {
    cursor: pointer;
}

.form-group {
    margin-bottom: 5px;
}

.rlabel {
    text-align: right;
    padding-right: 0;
}

input-group-addon {
    border-radius: 0px;
}

.nowrap {
    white-space: pre;
}

.pre {
    white-space: pre;
}

.form-control {
    padding: 4px 6px;
    height: auto;
}

#u4185-2 {
    font-size: 18px;
    line-height: 22px;
}

.container-fluid {
    margin: 15px;
    margin-top: 0;
}

.usermenu {
    float: right;
    clear: right;
}
  .usermenu * {
    font-weight: bold;
     font-size: 12pt !important;
  }
  .usermenu .glyphicon {
    top:3px;
    font-size: 14pt !important;
  }

.mainmenu {
    float: right;
    clear: both;
}

.nextDayDespatch td {
    background-color: #a7e4b0 !important;
}

.sameDayDespatch td {
    background-color: pink !important;
}

ul.footer-links li {
    display: inline-block;
    margin: 10px 10px;
}

    ul.footer-links li:after {
        margin: 10px 0px 0 30px;
        content: "|";
        white-space: nowrap;
    }

/* when hiding the thing */
.ng-hide-add.flash {
    -webkit-animation: 0.2s slideOutUp ease;
    animation: 0.2s slideOutUp ease;
    display: block !important;
}

/* when showing the thing */
.ng-hide-remove.flash {
    -webkit-animation: 0.3s slideInDown ease;
    animation: 0.3s slideInDown ease;
    display: block !important;
}

.row.flash.content {
    z-index: -10;
}

.input-group-addon {
    border-radius: 0px;
}

.tp4, .pt4, .justtext {
    padding-top: 4px;
}

.form-group-lg .form-control {
    border-radius: 0px;
}

.textbox {
    display: inline-block;
    width: 50px;
    text-align: center;
    border: 1px solid #c3d421;
}

.td180 {
    max-width: 180px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

/* job production running late....has some sort of issue and should get attention */
.amberAlert, .amberAlert *, .amberAlert * a, .amberAlert * a:visited, .amberAlert * a:active {
    color: #e46d0a !important;
    text-decoration: none;
}

/* job is really late and has passed max threshold of allowable time */
.redAlert,
.redAlert *,
.redAlert * a,
.redAlert * a:visited,
.redAlert * a:active {
    color: #e60400 !important;
    text-decoration: none;
}

a {
    text-decoration: dotted;
}

.updateMessage {
    font: 13px Arial,sans-serif;
    color: #333333;
    padding: 1px 6px 1px 6px;
    margin: 0 0 10px 0;
    border: solid 2px #df3939;
    background: #94c83f;
    font-weight: bold;
    line-height: 160%;
}

.instructionMessage {
    background-color: #bee5e4;
    border: 2px solid #9fd9da;
    font: 13px Arial,sans-serif;
    color: #333333;
    padding: 1px 6px 1px 6px;
    margin: 0 0 10px 0;
    font-weight: bold;
    line-height: 160%;
}

.expander {
    margin-bottom: 20px;
}

.total, .bold {
    font-weight: bold !important;
}

.mainmenu .nav-pills > li > a {
    border-radius: 4px;
    font-weight: bold;
    color: #337ab7;
    font-size: 18px;
}

a.action {
    text-decoration: underline;
}

    a.action:not(.first):before {
        content: '  ';
        padding: 0px 8px;
        text-decoration: none !important;
        color: #bdb8b8;
    }

@keyframes shake {
    10%, 90% {
        transform: translate3d(-1px, 0, 0);
    }

    20%, 80% {
        transform: translate3d(2px, 0, 0);
    }

    30%, 50%, 70% {
        transform: translate3d(-4px, 0, 0);
    }

    40%, 60% {
        transform: translate3d(4px, 0, 0);
    }
}

.shake {
    animation-name: shake;
    animation-duration: 3s;
    transform-origin: 50% 50%;
    animation-iteration-count: infinite;
    animation-timing-function: ease-in-out;
}

@keyframes pulse_animation {
    0% {
        transform: scale(1, 1);
    }

    30% {
        transform: scale(1, 1);
    }

    40% {
        transform: scale(1.08, 1.08);
    }

    50% {
        transform: scale(1, 1);
    }

    60% {
        transform: scale(1, 1);
    }

    70% {
        transform: scale(1.02, 1.02);
    }

    80% {
        transform: scale(1, 1);
    }

    100% {
        transform: scale(1, 1);
    }
}

.pulse {
    animation-name: pulse_animation;
    animation-duration: 3s;
    transform-origin: 50% 50%;
    animation-iteration-count: infinite;
    animation-timing-function: ease-in-out;
}

/* Cart */
.number {
    transition: .3s all ease;
    -webkit-transition: .3s all ease;
    font-size: 20px;
    display: inline-block;
}

.number--increment {
    color: green;
    transform: scale(1.6);
}

.number--decrement {
    color: red;
    transform: scale(0.8);
}

.bgx {
    background-color: blue;
}

.rzslider .rz-pointer {
    top: -14px;
    z-index: 3;
    width: 32px;
    height: 32px;
    cursor: pointer;
    background-color: #337ab7;
    border-radius: 16px;
}

.rzslider .rz-limit {
    font-size: x-small;
}

.angularjs-datetime-picker {
    z-index: 1000;
}

label, .expander-title {
    -webkit-user-select: none;
    /* webkit (safari, chrome) browsers */
    -moz-user-select: none;
    /* mozilla browsers */
    -khtml-user-select: none;
    /* webkit (konqueror) browsers */
    -ms-user-select: none;
    /* IE10+ */
}

.expander-title {
    cursor: pointer;
}

.qa {
    transition: all ease-in-out 0.1s;
}

.rotate180 {
    transform-origin: 50% 50%;
    transform: rotate(180deg);
}

@keyframes slideInRight {
    from {
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }

    to {
        transform: translate3d(0, 0, 0);
    }
}

.slideInRight {
    animation-name: slideInRight;
}

@keyframes slideInLeft {
    from {
        transform: translate3d(-100%, 0, 0);
        visibility: visible;
    }

    to {
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideOutRight {
    from {
        transform: translate3d(0, 0, 0);
    }

    to {
        visibility: hidden;
        transform: translate3d(100%, 0, 0);
    }
}

/* when hiding the picture */
.jobDetailsForm0 .ng-hide-add {
    animation: 0.2s slideOutRight ease;
    display: block !important;
}

/* when showing the picture */
.jobDetailsForm0 .ng-hide-remove {
    animation: 0.2s slideInLeft ease;
}

/* expander ------------------------------------------------ */
.expander {
    margin-bottom: 20px;
    clear: both;
    position: relative;
}

    .expander hr.g {
        border: none;
        margin: 1px 1px 10px 1px;
        height: 1px;
        background: #385678;
    }

    .expander .rightbtn {
        float: right;
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 2em;
    }

        .expander .rightbtn i {
            width: 32px;
            height: 32px;
            margin: 0px;
            padding: 0px;
            left: 0px;
            transform-origin: 46% 50%;
            transition: all ease-in-out .1s;
        }

    .expander.closed .rightbtn i {
        transform: rotate(180deg);
    }

@keyframes eopen {
    from {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 0.3);
    }

    to {
        opacity: 1;
        max-height: auto;
        overflow: visible;
    }
}

@keyframes eclose {
    from {
        opacity: 1;
    }

    50% {
        transform: scale3d(0.3, 0.3, 0.3);
    }

    to {
        opacity: 0;
        max-height: 0;
        overflow: hidden;
    }
}

.content00.eopen {
    animation: .2s eopen forwards;
}

.content00.eclosed {
    animation: .2s eclose forwards;
}

.content.eopen {
    max-height: auto;
    overflow: visible;
}

.content.eclosed {
    max-height: 0;
    overflow: hidden;
}

.form-group.ng-invalid > div:first-of-type:after {
    position: absolute;
    top: 23%;
    left: 98%;
    white-space: nowrap;
    color: #d9534f;
}

.form-group.ng-valid.ngdirty > div:first-of-type:after {
    content: "good";
}

.form-group.ng-invalid.ng-dirty > div:first-of-type:after {
    font-weight: bold;
    color: #d9534f;
}

.form-group.ng-invalid-required > div:first-of-type:after {
    content: '* required';
}

.form-group.ng-invalid-minlength > div:first-of-type:after {
    content: '* too short';
}

.form-group.ng-invalid-maxlength > div:first-of-type:after {
    content: '* too long';
}

.form-group.ng-invalid-email > div:first-of-type:after {
    content: '* not an email';
}

.form-group.ng-invalid-unique > div:first-of-type:after {
    content: '* already taken';
}

.has-error > div:first-of-type:after {
    position: absolute;
    top: 23%;
    left: 98%;
    content: '* required';
    font-weight: bold;
    white-space: nowrap;
    color: #d9534f;
}

/*

.required > div:first-of-type:after,
.ng-required > div:first-of-type:after {
	position: absolute;
	top: 23%;
	left: 98%;
	content: '* required';
	font-weight: bold;
	white-space: nowrap;
	color: #d9534f;
}

.minlength > div:first-of-type:after {
	position: absolute;
	top: 23%;
	left: 98%;
	content: '* too short';
	font-weight: bold;
	white-space: nowrap;
	color: #d9534f;
}

.maxlength > div:first-of-type:after {
	position: absolute;
	top: 23%;
	left: 98%;
	content: '* too long';
	font-weight: bold;
	white-space: nowrap;
	color: #d9534f;
}

.unique > div:first-of-type:after {
	position: absolute;
	top: 23%;
	left: 98%;
	content: '* already taken';
	font-weight: bold;
	white-space: nowrap;
	color: #d9534f;
}

.pwdDoesNotMatch > div:first-of-type:after {
	position: absolute;
	top: 23%;
	left: 98%;
	content: '* passwords do not match';
	font-weight: bold;
	white-space: nowrap;
	color: #d9534f;
}


.invalid-email > div:first-of-type:after {
	position: absolute;
	top: 23%;
	left: 98%;
	content: '* not valid email';
	font-weight: bold;
	white-space: nowrap;
	color: #d9534f;
}

.pristine.invalid-email > div:first-of-type:after,
.pristine.has-error > div:first-of-type:after,
.pristine.required > div:first-of-type:after,
.pristine.ng-required > div:first-of-type:after,
.pristine.minlength > div:first-of-type:after,
.pristine.maxlength > div:first-of-type:after,
.pristine.unique > div:first-of-type:after,
.pristine.pwdDoesNotMatch > div:first-of-type:after {
	color: #2196f3;
	font-weight: normal;
}*/
/*
.has-error > div:first-of-type:after {
	font-family: 'Glyphicons Halflings';
	font-size: 30px;
	position: absolute;
	bottom: 0%;
	left: 98%;
	content: '\e128';
	font-weight: bold;
	white-space: nowrap;
	color: #d9534f;
	animation: shake;
	animation-duration: 3s;
	transform-origin: 50% 50%;
	animation-iteration-count: infinite;
	animation-timing-function: ease-in-out;
}
*/
.status-bar {
    font-size: smaller;
}

    .status-bar .status {
        padding: 2px;
        margin: 0px;
    }

        .status-bar .status .done {
            background-color: limegreen;
            color: white;
            padding: 2px;
        }

.actions a {
    padding: 0 2px;
    font-weight: bold;
    text-decoration: underline;
}

.progress-cust {
    height: 12px !important;
    margin-bottom: 0px !important;
    background-color: rgba(216, 216, 216, 0.63) !important;
    border-radius: 6px !important;
    -webkit-box-shadow: inset 0 1px !important 2px rgba(0, 0, 0, 0.1);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-right: 10px !important;
}

    .progress-cust .tooltip-inner {
        text-align: center;
        background-color: #474d54 !important;
        border-radius: 1px;
        height: 21px;
        font-size: 13px;
    }

    .progress-cust .tooltip-arrow {
        border-top-color: #474d54 !important;
    }

.problem {
    padding: 4px 2px;
    color: red;
    cursor: pointer;
}

    .problem i {
        font-size: 20px;
    }

a, button {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

ul {
    list-style-type: none;
}

.footer {
    padding: 85px 0;
    background: #184156;
    /*
	background: -moz-linear-gradient(-45deg, #264977 0%, #182c54 100%);
	background: -webkit-linear-gradient(-45deg, #264977 0%,#182c54 100%);
	background: linear-gradient(135deg, #264977 0%,#182c54 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#264977', endColorstr='#182c54',GradientType=1 );
*/
}

.footer_list .logo {
    position: relative;
    top: -40px;
    width: 145px;
    height: 119px;
    margin-bottom: -25px;
}

.footer_list h4 {
    color: #fff;
}

.footer_list > li {
    min-height: 207px;
    padding: 0 20px 20px;
    border-right: 1px solid #132241;
    color: #fff;
}

    .footer_list > li:first-child {
        padding-left: 0;
    }

    .footer_list > li:last-child {
        border-right: none;
    }

.footer_list h4 {
    margin: 0 0 30px;
    font-size: 20px;
    font-family: 'Conv_EnzoOT-Bold';
}

.footer_list p {
    padding-left: 54px;
    font-size: 24px;
    background: url("/images/telephone.png") no-repeat center left 14px;
}

.footer_list ul a {
    line-height: 27px;
    font-size: 15px;
    color: #fff;
    font-family: 'Conv_EnzoOT-Medi';
}

.footer_list .australia {
    width: 217px;
    height: 151px;
}

.footer_list .printing_logo {
    width: 100px;
    height: 100px;
}

.footer_list .sgp_logo {
    width: 84px;
    height: 100px;
}

.control-label {
    /* font-weight: bold; */
}

.Absolute-Center {
    margin: auto;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

    .Absolute-Center.is-Responsive {
        width: 50%;
        height: 100%;
        min-width: 250px;
        max-width: 450px;
        padding: 40px;
    }


#logo-container {
    margin: auto;
    margin-bottom: 0px;
    margin-top: 60px;
    width: 268px;
    height: 120px;
    background: url(/images/lep.png) no-repeat center center transparent;
    background-size: 52% auto;
}

#footer {
    background-color: white;
    padding: 20px;
}

    #footer span {
        padding: 0 4px;
    }

    #footer .glyphicon-phone-alt {
        margin-right: 10px;
    }

.btn.Standard {
    /*background-color: #1c9a279e;*/
    color: #666666;
    border: 2px solid #1c9a27;
    border-radius: 2px;
}

.btn.Next.day {
    /*background-color: #fd9d318a;*/
    color: #565656;
    border: 2px solid #fd9d31;
    border-radius: 2px;
}

.btn.Same.day {
    /*background-color: #fb21299e;*/
    color: #666666;
    border: 2px solid #fb2129;
    border-radius: 2px;
}

.radio-inline.dm {
    /*transform: scale(1.5);
	margin-left: 8px;*/
}

.tooltip-wide + .tooltip > .tooltip-inner {
    max-width: 100%;
}

body.login {
    background-image: url(/images/login/000.jpg);
    background-position: top center;
    background-size: 2560px;
    background-repeat: no-repeat;
}

.nav-tabs {
    font-size: 18px;
}

.inputfile {
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;
}

    .inputfile + label {
        font-weight: 700;
        display: inline-block;
        padding: 5px 15px;
        width: 100%;
        border: 1px solid #666666;
        border-radius: 4px;
    }

.has-error >
.inputfile + label {
    border: 1px solid #d9534f;
}

.inputfile:focus + label,
.inputfile + label:hover {
    background-color: #0970b5;
    color: white;
}

.inputfile + label {
    cursor: pointer;
    /* "hand" cursor */
}

.inputfile:focus + label {
    outline: 1px dotted #000;
    outline: -webkit-focus-ring-color auto 5px;
}

.inputfile + label * {
    pointer-events: none;
}

.cf {
    zoom: 1;
}

    .cf:before,
    .cf:after {
        content: "";
        display: table;

    }

    .cf:after {
        clear: both;
    }

.login-wrapper h2 {
    color: #474d54;
    padding: 20px 0;
    font-size: 26px;
    text-align: center;
}

.login-block {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    opacity: 0.75;
}

    .login-block h3 {
        text-align: center;
        margin-top: 0;
        margin-bottom: 0;
        padding-bottom: 20px;
        border-bottom: 1px solid;
        margin: 0 -15px 20px;
        color: #999;
    }

    .login-block label {
        font-size: 13px;
        margin-bottom: 0;
    }

    .login-block input {
        margin-bottom: 10px;
    }

    .login-block #login {
        margin: 15px auto 0;
        width: 50%;
        background: #ff9e1b;
        color: #fff;
    }

    .login-block a {
        font-size: 14px;
        color: #999;
    }

.DigitalStyle {
    font-weight: bold !important;
    font-style: italic !important;
}

.HasSDD {
    background-color: pink !important;
}

.HasNDD {
    background-color: #a7e4b0 !important;
}

.staffView  tr.HasSpotFoil{
    background-color: #00846345 !important;
}

.staffView  tr.HasSpotFoil.HasNDD,
.staffView  tr.HasSpotFoil.HasSDD
{
    background-color: #00846345 !important;
}

.staffView  .HasSpotFoil select{
    background-color: #00846345 !important;
}
.row.order-row a.bold {
    color: #3f6ab3;
    text-decoration: underline;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.state-waiting:after {
    content: "\e031";
    display: inline-block;
    position: relative;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    font-weight: bold;
    width: 20px;
    height: 20px;
    margin-left: 20px;
    color: #81a71b;
    z-index: 100;
    -webkit-animation: spin .6s infinite linear;
    animation: spin .6s infinite linear;
    -webkit-font-smoothing: antialiased;
}

.state-waiting2:after {
    z-index: 100;
    content: "\e031";
    position: absolute;
    background-color: none;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    font-size: 80px;
    top: -6px;
    font-weight: bold;
    line-height: 85px;
    margin-left: 10px;
    padding: 0px;
    margin: 0px;
    -webkit-animation: spin .7s infinite linear;
    animation: spin .7s infinite linear;
    -webkit-font-smoothing: antialiased;
    color: #81a71b;
}

.jobrow.over {
    border: 2px dashed green !important;
    animation-name: shake;
    animation-duration: 3s;
    transform-origin: 50% 50%;
    animation-iteration-count: infinite;
    animation-timing-function: ease-in-out;
}

/* .jobrow.over1 {
    background-color: rgba(226, 238, 225, 0.62) !important;
    border: 2px dashed #ccc;
} */

.grow {
    /*filter: grayscale(100%);*/
    transform: scale(1);
    transition: all .1s ease-in-out;
}

    .grow:hover {
        /*filter:none;*/
        z-index: 100;
        transform-origin: 50% 50%;
        transform: scale(8);
        box-shadow: 1px 1px 15px #000;
    }

.template-img1:after {
    content: "\a";
    white-space: pre;
}

.axxx {
    margin: 0px;
    max-height: 40px;
    max-width: 54px;
}

/*# sourceMappingURL=maps/site.css.map */
.grow.axxx:hover {
    /*filter:none;*/
    z-index: 100;
    transform-origin: 50% 50%;
    transform: translate(100px, 0) scale(8);
    box-shadow: 1px 1px 15px #000;
}

.hand {
    cursor: pointer;
    /* "hand" cursor */
}

.ui-grid-viewport {
    overflow-anchor: none;
}

.ngCell {
    /*overflow: hidden;*/
    position: absolute;
    top: 0;
    bottom: 0;
    background-color: inherit;
}

.ui-grid-cell {
    overflow: visible;
    z-index: 99999;
}

.compact-category
* {
    background-color: white !important;
    color: #555555 !important;
}

.extra-info:before {
    position: absolute;
    left: 84%;
    height: auto;
    content: attr(data-title);
    display: inline-block;
    white-space: pre;
    font-size: small;
}

.smaller {
    font-size: smaller;
}

.percentage {
    font-size: 3em;
    position: fixed;
    bottom: 10%;
    right: 0%;
    z-index: 10;
    color: #474c47;
    text-shadow: 2px 2px #d8caca;
    background-color: #dedede;
    border-radius: 28px;
    padding: 2px 85px;
    opacity: 0.9;
}

.ui-select-choices {
    opacity: 1 !important;
    min-height: 30px;
}

.toast-bottom-center {
    bottom: 30%;
    margin: 0 auto;
    left: 0%;
}

.ngdialog-content {
    padding: 0;
}

    .ngdialog-content login-block {
        opacity: 1;
    }

.stripped-cols td:nth-of-type(odd),
.stripped-cols th:nth-of-type(odd) {
    background: rgba(0, 0, 0, 0.02);
}

.disable-animations *,
.disable-animations *:after,
.disable-animations *:before {
    transition: none !important;
}

h5.bar {
background: lightblue;
padding: 10px 0 16px 10px;
margin: -10px -10px 10px;

}

.precss {
    display: block;
    unicode-bidi: embed;
    font-family: monospace;
    white-space: pre;
    overflow: auto;
}

table.tableBodyScroll500 tbody {
    display: block;
    max-height: 300px;
    overflow-y: scroll;
}

    table.tableBodyScroll500 thead,
    table.tableBodyScroll500 tbody tr {
        display: table;
        width: 100%;
        table-layout: fixed;
    }

.errorrow {
    border: 4px solid #fb0000;
    border-radius: 4px;
}


.breakword{
    overflow-wrap: break-word;
}

/*body, .expander {
    background: linear-gradient(90deg, #ffffff 0%, #ffffff  66%, #f3f4f7 100%);
  }

:checked + span {
    font-weight: bold;
}

*/


.filepos {
    padding:2px;
}
.filepos.ng-invalid {
    border-color:red;
    border-width: 5px;
    border-radius: 2px;
  }



.modal {
    text-align: center;
}

@media screen and (min-width: 768px) {
    .modal:before {
        display: inline-block;
        vertical-align: middle;
        content: " ";
        height: 100%;
    }
}

.modal-dialog {
    display: inline-block;
    text-align: left;
    vertical-align: middle;
}



a.tip {
    white-space: nowrap;
    text-decoration: none;
    padding: 5px;
    cursor: pointer;
}
a.tip:hover {
    background: #84f06e;
    position: relative
}
a.tip span {
    display: none
}
a.tip:hover span {
    border: #c0c0c0 1px dotted;
    padding: 5px 20px 5px 5px;
    display: block;
    z-index: 100;
    background: url(../images/status-info.png) #f0f0f0 no-repeat 100% 5%;
    left: 0px;
    margin: 10px;

    font-size: larger;
    position: absolute;
    top: 10px;
    text-decoration: none;
    white-space: pre;
}




                        .ot {
                            width:23% !important;
                            white-space: pre;
                            display: inline-block;
                            margin-top: 5px;
                            margin-left: 5px;
                        }
                        .ot input {
                            border: 1px solid #ccc;
                        }
                        .ot select {
                            margin-left: 2px;
                            display: block;
                            padding: 0px 0px;
                            font-size: 16px;
                            color: #555555;
                            width: 100%;
                        }

                        .ot input.ng-invalid {
                            border-color: red;
                        }



.rewardtable {
    margin-top: 40px;
    border-collapse: collapse !important;
}

.rewardtable  caption {
    text-align: center;
    font-size: 14pt;
    font-weight: bold;
}
.rewardtable td {
    vertical-align: middle;
    font-size: 10pt;
    text-align: center;
    font-weight: bold;
    color: rgb(20,31,41)
  }

 .rewardtable.horizontal tr td:nth-of-type(2){ background-color:#FCD5B4 !important; }
 .rewardtable.horizontal tr td:nth-of-type(3){ background-color:#E6B8B7 !important; }
 .rewardtable.horizontal tr td:nth-of-type(4){ background-color:#C5D9F1 !important; }
 .rewardtable.horizontal tr td:nth-of-type(5){ background-color:#8DB4E2 !important; }
 .rewardtable.horizontal tr td:nth-of-type(6){ background-color:#D8E4BC !important; }
 .rewardtable.horizontal tr td:nth-of-type(7){ background-color:#C4D79B !important; }
 .rewardtable.horizontal tr td:nth-of-type(8){ background-color:#C4BD97 !important; }
 .rewardtable.horizontal tr td:nth-of-type(9){ background-color:#FFC000 !important; }
 .rewardtable.horizontal tr td.currentReward {
       filter: drop-shadow(0px 20px 5px #aecf1c);
        color:#c7bb4d;
        font-size: larger;
    }


    .rewardtable.vertical tr:nth-of-type(2){ background-color: rgb(94, 160, 218) !important; }
    .rewardtable.vertical tr:nth-of-type(3){ background-color: rgb(105,164,217) !important; }
    .rewardtable.vertical tr:nth-of-type(4){ background-color: rgb(156,194,229) !important; }
    .rewardtable.vertical tr:nth-of-type(5){ background-color: rgb(189,214,238) !important; }
    .rewardtable.vertical tr:nth-of-type(6){ background-color: rgb(222,234,246) !important; }

    .rewardtable.vertical tr.currentReward {
          font-size: 14pt!important;
          font-weight: bold;
          background-color: rgb(255, 204, 41)!important;
       }

    .rewardtable.vertical .currentReward ::before {
        content: '➧';
        font-size: 20px;
     }
    .rewardtable.vertical tr td:nth-of-type(2){ font-size: larger; }




   .fav-search {
      }
   .fav-search .r {
       color:rgba(216, 216, 216, 0.63);
        opacity:0;
        transition:opacity 10s linear;
        -moz-transition: opacity 10s linear;
        -webkit-transition: opacity 10s linear;
    }

    .fav-search:hover .r {
        opacity: 1;
   }













    .custom_button_3 {
        background-color: #0149CC;
        background-image: linear-gradient(to right,#0149CC,#00C6FF,#0149CC);
        display: inline-block !important;
        cursor: pointer;
        position: relative;
        transition: color 0.35s ease, background-position 0.3s ease-in-out, background-color 0.35s ease, box-shadow 0.3s ease-in-out;
        z-index: 1;
        overflow: hidden;
        background-size: 200% 100%;
        background-position: 0 0;
    }

    .custom_button_3:hover {
        box-shadow: -0.25em 0.25em 2.25em rgba(0,0,0,0.35);
        background-position: 100% 100%;
    }

    /* ### CUSTOM BUTTON 3 GREEN ### */

    .custom_button_3_green {
        background-color: #478212;
        background-image: linear-gradient(to right,#478212,#74cc27,#478212);
        display: inline-block !important;
        cursor: pointer;
        position: relative;
        transition: color 0.35s ease, background-position 0.3s ease-in-out, background-color 0.35s ease, box-shadow 0.3s ease-in-out;
        z-index: 1;
        overflow: hidden;
        background-size: 200% 100%;
        background-position: 0 0;
    }

    .custom_button_3_green:hover {
        box-shadow: -0.25em 0.25em 2.25em rgba(0,0,0,0.35);
        background-position: 100% 100%;
    }

    /* ### CUSTOM BUTTON 3 RED ### */

    .custom_button_3_red {
        background-color: #e02022;
        background-image: linear-gradient(to right,#e02022,#f35656,#e02022);
        display: inline-block !important;
        cursor: pointer;
        position: relative;
        transition: color 0.35s ease, background-position 0.3s ease-in-out, background-color 0.35s ease, box-shadow 0.3s ease-in-out;
        z-index: 1;
        overflow: hidden;
        background-size: 200% 100%;
        background-position: 0 0;
    }

    .custom_button_3_red:hover {
        box-shadow: -0.25em 0.25em 2.25em rgba(0,0,0,0.35);
        background-position: 100% 100%;
    }



.staff .paginaconroltion { margin: 0;}


input.nospinner{
    -moz-appearance:textfield;
}

input.nospinner::-webkit-outer-spin-button,
input.nospinner::-webkit-inner-spin-button {
    -webkit-appearance: none;
}



.form-control-static {
/*     
    padding-bottom: 0px;
    margin-bottom: 0; */
 
}



/*

 lepcp\rebecca.mcarthur

job-deatails-component   */
/* CSS talk bubble */




.visibleUnderRedAlert {display: hidden !important;}
.redAlert > .visibleUnderRedAlert {display: inline-block !important;}



.btn-success {
    filter: drop-shadow(2px 2px 4px #84ca64);

}


/* Division */
#crpdiv {
    text-align: center;
    z-index: 1;
    float: right;
    margin: 0px;
    position: relative;
    top: -180px;
    height: 0px;
}


/* Division */
#pricecalc #crpdiv {
  
}





/* The switch - the box around the slider */
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}


.bg-orange{
    background-color:#f79840  !important;
    color:white;
    padding: 2px;

}

label.fcl {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    transition: 0.2s;
}



input:focus+label.fcl {
    top: 100%;
    margin-top: -16px;
}


html {
    overflow: -moz-scrollbars-vertical;
    overflow-y: scroll;
}
body {
    min-height: 101%;
}


form-group-sm .form-control-static {
    /* height: 38px;
    min-height: 36px;
    padding: 5px 10px;
    font-size: 14px;
    line-height: 1.33333; */
}

.aa {
    text-decoration:  underline;
    cursor: pointer;
}
.aa:hover {
    text-decoration:  underline;
    font-weight: bold;
}

#price-list {
    float: right;
    height: 0;
}

#price-list table {
    font-family: Verdana;
    display: block;
    height: 220px !important;
    overflow-y: scroll;
    font-size: 11pt;
    left: 180px;
    position: relative;
    top: -100px; 
    border-radius: 4px;
}

#price-list table tr td {
    border: 1px solid #CCC;
    padding: 2px 8px;
    text-align: right;
}
#price-list table tr:first-child{
    position: sticky;
    top:0;
    background-color: #f3f4f7;
    font-weight: bold;
    
  }
  #price-list table a {
    text-decoration: underline !important;
  }


label.col-xs-3.control-label {
    /* font-size: 15px !important  ; */
}

.input-group-addon {
    /* font-size: 14px !important; */
    /* font-family: monospace; */
}

.jobDetailsForm .form-group {
    /* margin: 8px 0px;
    border-bottom: 1px dotted #eaeaea;
    padding-bottom: 8px; */
  }
  .form-group .radio-inline {
    /* margin-top: 0;
    margin-bottom: 0;
    padding-top: 2px;  */
} 

.input-group-addon.x {
    font-family: Courier, monospace;
    font-size: smaller;
}
