﻿(function (angular) {
    'use strict';

    var app = angular.module('app');

    app.factory('backendHubProxy', ['$rootScope',
      function ($rootScope) {
          function backendFactory(hubName) {
              var connection = $.hubConnection('http://localhost:5000');
              var proxy = connection.createHubProxy(hubName);
              connection.start().done(function () {
                  //console.log('connected....');
              });

              return {
                  on: function (eventName, callback) {
                      proxy.on(eventName, function (result) {
                          //console.log(eventName);
                          $rootScope.$apply(function () {
                              if (callback) {
                                  callback(result);
                              }
                          });
                      });
                  },
                  invoke: function (methodName, callback) {
                      proxy.invoke(methodName)
                      .done(function (result) {
                          $rootScope.$apply(function () {
                              if (callback) {
                                  callback(result);
                              }
                          });
                      });
                  }
              };
          }
          return backendFactory;
      }]);
})(window.angular);
