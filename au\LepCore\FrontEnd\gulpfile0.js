const { src, dest, series, parallel, watch, gulp } = require('gulp');
const del = require('del');
const browserSync = require('browser-sync').create();
const reload = browserSync.reload;
const gulpSass = require('gulp-sass');
const sourcemaps = require('gulp-sourcemaps');
const concat = require('gulp-concat');
const uglify = require('gulp-uglify');
const rename = require('gulp-rename');
const order = require('gulp-order');
const mainBowerFiles = require('main-bower-files');
const jshint = require('gulp-jshint');
const htmlhint = require('gulp-htmlhint');
const htmlmin = require('gulp-htmlmin');
const ngHtml2Js = require('gulp-ng-html2js');
const nodemon = require('gulp-nodemon');
const inject = require('gulp-inject');
var childProcess = require("child_process");
const $ = require('gulp-load-plugins')({ lazy: true });
const es = require('event-stream');
var bowerFiles = require('main-bower-files');
// Define paths
const paths = {
	coffee_scripts: 'app/**/*.coffee',
	coffee_scripts_e2e: 'e2e/**/*.coffee',
	scripts: 'app/**/*.js',
	styles: ['./app/css/*.css', './app/css/*.scss'],
	images: './images/**/*',
	index: ['./app/index.html', './app/index2.html', './app/index_r1.html', './app/staff.html', './app/index_wl.html'],
	partials: ['app/**/*.html', '!app/index*.html', '!./app/staff.html', '!./app/index_wl.html'],
	appfonts: './fonts/*',
	distDev: '../wwwroot',
	distProd: '../wwwroot',
	distScriptsProd: './../wwwroot/scripts',
	scriptsDevServer: 'devServer/**/*.js'
};

var defaultOrder = [
	'browser-polyfill.js', 'es5-shim.js',
	'jquery.js', 'moment.js', 'jquery.signalR.js', 'toastr.js',
	'jquery.dataTables.js', 'bootstrap.js', 'tv4.js', 'bower.json',
	'angular.js', 'ocLazyLoad.js', 'angular-loader', 'angular-sanitize', 'angular-resource', 'angular-animate', 'angular-ui-router', 'ng-animate-model-change.js'
];

function preseveLicence(node, comment) {
	var text = comment.value;
	var type = comment.type;
	if (type == "comment2") {
		// multiline comment
		return /@preserve|@license|@cc_on/i.test(test);
	}
}

function swallowError(error) {
	// If you want details of the error in the console
	console.log(error.toString());
	this.emit('end');
}

var p = {
	core: {
		coffee: ['app/**/*.coffee', '!app/staff/**'],
		js: ['app/**/*.js', '!app/staff/**'],
		html: ['app/**/*.html', '!app/staff/**'],
	},
	staff: {
		coffee: ['app/staff/**/*.coffee'],
		js: ['app/staff/**/*.js'],
		html: ['app/staff/**/*.html'],
	},


	coffee_scripts: 'app/**/*.coffee',
	coffee_scripts_e2e: 'e2e/**/*.coffee',
	scripts: 'app/**/*.js',
	styles: ['./app/css/*.css', './app/css/*.scss'],
	images: './images/**/*',
	index: ['./app/index.html', './app/index2.html', './app/index_r1.html', './app/staff.html', './app/index_wl.html'],
	partials: ['app/**/*.html', '!app/index*.html', '!./app/staff.html', '!./app/index_wl.html'],
	appfonts: './fonts/*',   // LEP's fonts, bar code fonts etc here

	//distDev: './dist.dev',
	//distProd: './dist.prod',
	//distScriptsProd: './dist.prod/scripts',

	distDev: './../wwwroot',
	distProd: './../wwwroot',
	distScriptsProd: './wwwroot/scripts',
	scriptsDevServer: 'devServer/**/*.js'
};



var jsScriptsRegex = /.js$/i;
var cssStylesRegex = /.css$/i;
var lessStylesRegex = /.less$/i;
var fontsStylesRegex = /.(ttf)$/i; //|woff|eot|svg|woff2

// == PIPE SEGMENTS ========

var pipes = {};


process.on('unhandledRejection', error => {
	// Will print "unhandledRejection err is not defined"
	console.log('PUNTUI unhandledRejection', error.message);
});



var defaultOrder = [
	'browser-polyfill.js', 'es5-shim.js',
	'jquery.js', 'moment.js', 'jquery.signalR.js', 'toastr.js',
	'jquery.dataTables.js', 'bootstrap.js', 'tv4.js', 'bower.json',
	'angular.js', 'ocLazyLoad.js', 'angular-loader', 'angular-sanitize', 'angular-resource', 'angular-animate', 'angular-ui-router', 'ng-animate-model-change.js'

];

function builtStylesDev() {
	return src(paths.styles, { allowEmpty: true })
		.pipe($.sass().on('error', $.sass.logError))
		.pipe($.sourcemaps.init({ loadMaps: true }))
		.pipe($.sass({ indentedSyntax: false, outputStyle: 'expanded' }))
		.pipe($.sourcemaps.write('./maps'))
		.pipe(dest(paths.distDev + '/css'));
};


function orderedVendorScripts() {
	return $.order(defaultOrder)
	//.pipe($.print())
	;
};

function orderedAppScripts() {
	return $.angularFilesort();
};

function minifiedFileName() {
	return $.rename(function (path) {
		path.extname = '.min' + path.extname;
	});
};

function validatedAppScripts(done) {
	src(paths.scripts)
		.pipe($.jshint())
		.pipe($.jshint.reporter('jshint-stylish'))
		.pipe(dest(paths.distDev));
	done();
};

function compiledAppCS0(done) {
	var coreCS = src(p.core.coffee)
		//.pipe($.print())
		.pipe($.sourcemaps.init())
		.pipe($.plumber())
		.pipe($.coffee({ bare: true }).on('error', function (error) {
			console.error("Compilation error: " + error.stack);
			childProcess.exec("powershell.exe [console]::beep(2500,1000)");
		}))
		.pipe($.sourcemaps.write());
	var coreJS = src(p.core.js);
	//.pipe($.jshint())
	//.pipe($.jshint.reporter('jshint-stylish', { beep:true, verbose: false }));
	// unicode for beep
	childProcess.exec("powershell.exe [console]::beep(500,100)");
	process.stdout.write('\x07');
	$.util.log(new Date().toLocaleTimeString() + '\x07');

	es.merge(coreCS, coreJS).pipe(dest(p.distDev));
	//		.pipe($.concat('app.core.min.js'))
	//		//.pipe($.uglify({ compress: { hoist_funs: false, hoist_vars: false, drop_console: true } }))
	//		.pipe($.rev())
	//		.pipe(dest(paths.distDev));
	done();
};

function compiledAppCS1(done) {
	var staffCS = src(p.staff.coffee)
		.pipe($.sourcemaps.init())
		.pipe($.coffee({ bare: true })
			.on('error', $.util.log))
		//.pipe($.print())
		.pipe($.sourcemaps.write());

	var staffJS = src(p.staff.js);


	es.merge(staffCS, staffJS)
		.pipe(dest(p.distDev));
	done();
};

/*
function builtAppScriptsProd () {
	var compiledAppCS0 = compiledAppCS0()
			.pipe($.concat('app.core.min.js'))
			.pipe($.uglify({ compress: { hoist_funs: false, hoist_vars: false, drop_console: true } }))
			.pipe($.rev());

	var compiledAppCS1 = compiledAppCS1()
			.pipe($.concat('app.staff.min.js'))
			.pipe($.uglify({ compress: { hoist_funs: false, hoist_vars: false, drop_console: true } }))
			.pipe($.rev());

	return es.merge(compiledAppCS0, compiledAppCS1)
			.pipe($.print())
			.pipe(dest(p.distDev));
};
*/

function builtAppScriptsProd0() {
	return compiledAppCS0()
		.pipe($.concat('app.core.min.js'))
		.pipe($.uglify({ compress: { hoist_funs: false, hoist_vars: false, drop_console: true } }).on('error', function (e) { console.log(e); }))
		.pipe($.rev())
		.pipe(dest(p.distDev));


};

function builtAppScriptsProd1() {
	return compiledAppCS1()
		.pipe($.concat('app.staff.min.js'))
		.pipe($.uglify({ compress: { hoist_funs: false, hoist_vars: false, drop_console: true } }).on('error', function (e) { console.log(e); }))
		.pipe($.rev())
		.pipe(dest(p.distDev));


};

function builtVendorScriptsProd() {
	return src(bowerFiles({
		filter: function (e) {
			return jsScriptsRegex.test(e);
		}
	}))
		.pipe(orderedVendorScripts())
		//.pipe($.print())
		.pipe($.concat('vendor.min.js'))
		.pipe($.uglify({
			preserveComments: /^!|@preserve|@license|@cc_on/i,
			compress: { hoist_funs: false, hoist_vars: false, drop_debugger: true, drop_console: false }
		}).on('error', function (e) { console.log(e); }))
		.pipe(dest(paths.distProd));
};




function validatedDevServerScripts() {
	return src(paths.scriptsDevServer)
		.pipe($.jshint())
		.pipe($.jshint.reporter('jshint-stylish'));
};

function validatedPartials() {
	return src(paths.partials, { base: 'app' })
		.pipe($.htmlhint({ 'doctype-first': false }))
		.pipe($.htmlhint.reporter());
};

function builtPartialsDev() {
	return validatedPartials()
		.pipe(dest(paths.distDev));
};

function scriptedPartialsDev() {
	return validatedPartials()
		.pipe($.htmlhint.failReporter())
		.on('error', swallowError)
		.pipe($.htmlmin({ collapseWhitespace: true, removeComments: true }))
		//.pipe($.ngHtml2js({
		//    moduleName: "app"
		//}))
		.pipe(dest(paths.distDev));
};

/*
function scriptedPartialsProd () {
	return validatedPartials()
		.pipe($.htmlhint.failReporter())
		.pipe($.htmlmin({ collapseWhitespace: true, removeComments: true }))
		.pipe($.ngHtml2js({
			moduleName: "app", declareModule: false
		}))
		.pipe($.concat('templates.min.js'))
		.pipe($.rev())
		.pipe($.uglify())
		.pipe(dest(paths.distProd));
};
*/

function scriptedPartialsProd0() {
	return src(p.core.html, { base: 'app' })
		.pipe($.htmlhint({ 'doctype-first': false }))
		.pipe($.htmlhint.reporter())
		.pipe($.htmlhint.failReporter())
		.pipe($.htmlmin({ collapseWhitespace: true, removeComments: true }))
		.pipe($.ngHtml2js({
			moduleName: "app", declareModule: false
		}))
		.pipe($.concat('templates.core.min.js'))
		.pipe($.rev())
		.pipe($.uglify())
		.pipe(dest(paths.distProd));

};
function scriptedPartialsProd1() {
	return src(p.staff.html, { base: 'app' })
		.pipe($.htmlhint({ 'doctype-first': false }))
		.pipe($.htmlhint.reporter())
		.pipe($.htmlhint.failReporter())
		.pipe($.htmlmin({ collapseWhitespace: true, removeComments: true }))
		.pipe($.ngHtml2js({
			moduleName: "app", declareModule: false
		}))
		.pipe($.concat('templates.staff.min.js'))
		.pipe($.rev())
		.pipe($.uglify())
		.pipe(dest(paths.distProd));
};


function buildVendorStylesLess() {
	return src(bowerFiles({
		filter: function (e) {
			return lessStylesRegex.test(e);
		}
	})
	).pipe($.less({}))
}

function builtVendorCssStyles() {
	return src(bowerFiles({
		filter: function (e) {
			return cssStylesRegex.test(e);
		}
	})
	)
}

function builtVendorStylesDev() {
	return es.merge(buildVendorStylesLess(), builtVendorCssStyles())
		.pipe($.htmlmin({ removeComments: true }))
		.pipe(dest(paths.distDev + "/styles"));
}

function builtVendorStylesProd() {
	return es.merge(buildVendorStylesLess(), builtVendorCssStyles())
		.pipe($.concat('vendor.min.css'))
		.pipe($.minifyCss())
		.pipe(dest(paths.distProd));
}

function builtStylesProd() {
	return src(paths.styles)
		// .pipe($.sourcemaps.init())
		.pipe($.sass())
		.pipe($.minifyCss())
		//.pipe($.sourcemaps.write())
		.pipe($.concat('app.min.css'))
		.pipe($.rev())
		.pipe(dest(paths.distProd));
};

function processedFonts(base_path) {
	return src(bowerFiles({
		filter: function (e) {
			return fontsStylesRegex.test(e);
		}
	}),
		{ base: 'bower_components' }
	)//.pipe($.print())
		.pipe($.rename(function (path) {
			var arrayPath = path.dirname.split("/");
			if (arrayPath.length > 1) {
				arrayPath.splice(0, 1);
				new_path = "../" + arrayPath.join('/');
			} else {
				new_path = "./"
			}
			path.dirname = new_path;
		}))
		.pipe(dest(base_path + "/fonts"));
};


function processedAppFonts(base_path) {
	return src(paths.appfonts)
		//.pipe($.print())
		.pipe(dest(base_path + "/fonts"));
};


function processedImagesDev() {
	return src(paths.images)
		.pipe(dest(paths.distDev + '/images/'));
};

function processedImagesProd() {
	return src(paths.images)
		.pipe(dest(paths.distProd + '/images/'));
};

function validatedIndex() {
	return src(paths.index)
		.pipe($.htmlhint())
		.pipe($.htmlhint.reporter());
};

function buildIndexDev() {

	var bowerLibs = builtVendorScriptsDev();

	var appScripts = buildAppScriptsDev()
		.pipe(orderedAppScripts());

	var partials = scriptedPartialsDev();

	var appStyles = builtStylesDev();
	var vendorStyles = builtVendorStylesDev();

	//var vendorScripts = builtVendorScriptsProd();

	return validatedIndex()
		.pipe(dest(paths.distDev)) // write first to get relative path for inject
		.pipe($.inject(bowerLibs, { relative: true, name: 'bower' }))
		.pipe($.inject(partials, { relative: true, name: 'templates' }))
		.pipe($.inject(appScripts, { relative: true }))
		.pipe($.inject(appStyles, { relative: true }))
		.pipe($.inject(vendorStyles, { relative: true, name: 'bower' }))
		.pipe(dest(paths.distDev));
};

function buildIndexProd() {
	var vendorScripts = builtVendorScriptsProd();
	//var appScripts = builtAppScriptsProd();
	var tpl0 = scriptedPartialsProd0();
	var tpl1 = scriptedPartialsProd1();

	var app0 = builtAppScriptsProd0();
	var app1 = builtAppScriptsProd1();

	var appStyles = builtStylesProd();
	var vendorStyles = builtVendorStylesProd();

	var r = validatedIndex()
		//.pipe($.print())
		.pipe(dest(paths.distProd)) // write first to get relative path for inject
		.pipe($.inject(vendorScripts, { relative: true, name: 'bower' }))
		//.pipe($.inject(scriptedPartialsProd, { relative: true, name: 'templates' }))

		.pipe($.inject(app0, { name: 'appcore', relative: true }))
		.pipe($.inject(app1, { name: 'appstaff', relative: true }))

		.pipe($.inject(tpl0, { name: 'tplcore', relative: true }))
		.pipe($.inject(tpl1, { name: 'tplstaff', relative: true }))

		.pipe($.inject(appStyles, { relative: true }))
		.pipe($.inject(vendorStyles, { relative: true, name: 'bower' }))
		//.pipe($.htmlmin({ collapseWhitespace: true, removeComments: true }))
		.pipe(dest(paths.distProd))


		;


	return r;
};



function builtAppProd() {
	return es.merge(
		builtIndexProd(),
		processedFonts(paths.distProd),
		processedAppFonts(paths.distProd),
		processedImagesProd()
	);
};
// Define jsFileFilter function
function jsFileFilter(file) {
	return file.match(/.*\.js$/i);
}
// Task to clean the development directory
function cleanDev(done) {
	del([paths.distDev], { force: true, read: false }, done);
}

// Task to clean the production directory
async function cleanProd(done) {
	del([paths.distProd], { force: true , read: false});
	done();
}

// Task to validate index.html
function validateIndex() {
	return src(paths.index, { allowEmpty: true })
		.pipe(htmlhint({ 'doctype-first': false }))
		.pipe(htmlhint.failOnError());
}

function validatePartials() {
	return src(paths.partials, { base: 'app'})
		//.pipe($.print())
		.pipe(htmlhint({ 'doctype-first': false }))
		.pipe(htmlhint.failOnError());
}
// Task to build partials for development
function buildPartialsDev() {
	return validatePartials()
		.pipe(dest(paths.distDev));
}

// Task to convert partials to JavaScript using html2js for development
function convertPartialsToJsDev() {
	return validatePartials()
		.pipe(htmlmin({ collapseWhitespace: true, removeComments: true }))
		.pipe(ngHtml2Js({ moduleName: "app" }))
		.pipe(concat('templates.min.js'))
		.pipe(dest(paths.distDev));
}

// Task to validate dev server scripts
function validateDevServerScripts() {
	return src(paths.scriptsDevServer)
		.pipe(jshint())
		.pipe(jshint.reporter('jshint-stylish'));
}

// Task to validate app scripts
function validateAppScripts() {
	//console.trace("validateAppScripts");
	return src(paths.scripts)
		//.pipe(jshint())
		//.pipe($.print())
		//.pipe(jshint.reporter('jshint-stylish'))
		.pipe(dest(p.distDev));
}



// Task to build vendor scripts for development
function buildVendorScriptsDev() {
	return src(mainBowerFiles({ filter: jsFileFilter }))
		.pipe(order(defaultOrder))
		.pipe(concat('vendor.min.js'))
		.pipe(dest(paths.distDev + '/libs'));
}

// Task to build styles for development
function buildStylesDev() {
	return src(paths.styles)
		.pipe(sourcemaps.init())
		.pipe(gulpSass().on('error', gulpSass.logError))
		.pipe(sourcemaps.write('./maps'))
		.pipe(dest(paths.distDev + '/css'));
}
function builtVendorScriptsDev() {
	return src(bowerFiles({
		filter: function (e) {
			return jsScriptsRegex.test(e);
		}
	})
	)
	.pipe(orderedVendorScripts())
	//.pipe($.print())
	//.pipe($.uglify( {  preserveComments: /^!|@preserve|@license|@cc_on/i,       compress: { hoist_funs: false, hoist_vars: false, drop_debugger: true, drop_console: false  }0        }))
	.pipe($.concat('vendor.min.js'))
	.pipe(dest(paths.distDev + '/libs'));

	//return src(bowerFiles({
	//	filter: function (e) {
	//		return jsScriptsRegex.test(e);
	//	}
	//}))
	//	.pipe(orderedVendorScripts())
	//	.pipe($.print())
	//	.pipe($.concat('vendor.min.js'))
	//	.pipe($.uglify({
	//		preserveComments: /^!|@preserve|@license|@cc_on/i,
	//		compress: { hoist_funs: false, hoist_vars: false, drop_debugger: true, drop_console: false }
	//	}))
	//	.pipe(dest(paths.distDev + '/libs'));
};
// Task to process images for development
function processImagesDev() {
	return src(paths.images)
		.pipe(dest(paths.distDev + '/images/'));
}




// function buildAppDev (done) {
// 	es.merge(
// 		builtIndexDev(),
// 		processedFonts(paths.distDev),
// 		processedAppFonts(paths.distDev),
// 		processedImagesDev());
//   done();
// };
// Task to build a complete development environment
function buildAppDev(cb) {
	series(
		cleanDev,
		//parallel(
			// buildPartialsDev,
			// validateAppScripts,
			// compiledAppCS0,
			//compiledAppCS1,
			// buildVendorScriptsDev,
			// buildStylesDev,
			// processImagesDev
		//),
		//buildIndexDev
	);
	cb();
	
}
// Task to watch live changes to the development environment
function watchDev() {
	// Start nodemon to auto-reload the dev server
	nodemon({ script: 'server.js', ext: 'js', watch: ['devServer/'], env: { NODE_ENV: 'development' } });

	// Start browser-sync server
	browserSync.init({
		server: {
			baseDir: paths.distDev
		}
	});

	// Watch index
	watch(paths.index, buildIndexDev);

	// Watch app coffee scripts
	watch(paths.coffee_scripts, buildAppScriptsDev);

	// Watch app scripts
	watch(paths.scripts, buildAppScriptsDev);

	// Watch html partials
	watch(paths.partials, convertPartialsToJsDev);

	// Watch styles
	watch(paths.styles, buildStylesDev);
	watch("./app/css/**/*.*", buildStylesDev);

	// Watch images
	watch(paths.images, processImagesDev);
}

exports.cleanDev = cleanDev;
exports.cleanProd = cleanProd;
exports.validatePartials = validatePartials;
exports.validateIndex = validateIndex;
exports.convertPartialsToJsDev = convertPartialsToJsDev;
exports.validateDevServerScripts = validateDevServerScripts;
exports.validateAppScripts = validateAppScripts;
 
exports.buildVendorScriptsDev = buildVendorScriptsDev;
exports.buildStylesDev = buildStylesDev;
exports.processImagesDev = processImagesDev;
exports.buildIndexDev = buildIndexDev;
exports.buildAppDev = buildAppDev;
exports.watchDev = watchDev;
 

// Default task
exports.default = buildAppDev;// watchDev;

exports.compiledAppCS0 = compiledAppCS0