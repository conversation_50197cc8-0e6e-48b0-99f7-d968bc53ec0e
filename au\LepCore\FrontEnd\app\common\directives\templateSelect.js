/* jshint -W069 */
/* jshint -W030 */


if (!Object.keys) {
    Object.keys = function (obj) {
        if (typeof obj !== 'object') {
            return [];
        }
        var keys = [];

        for (var i in obj) {
            if (obj.hasOwnProperty(i)) {
                keys.push(i);
            }
        }

        return keys;
    };
}
if (!Object.values) {
    Object.values = function (obj) {
        var res = [];
        for (var i in obj) {
            if (obj.hasOwnProperty(i)) {
                res.push(obj[i]);
            }
        }
        return res;
    };
}

function removeProps(obj, keys) {
    if (obj instanceof Array) {
        obj.forEach(function (item) {
            removeProps(item, keys);
        });
    }
    else if (typeof obj === 'object') {
        var own = Object.getOwnPropertyNames(obj);

        own.forEach(function (key) {
            if (keys.indexOf(key) !== -1) {
                delete obj[key];
            }
            else {
                removeProps(obj[key], keys);
            }
        });
    }
}

function removePropsByVal(obj, vals) {
    if (obj instanceof Array) {
        obj.forEach(function (item) {
            removePropsByVal(item, vals);
        });
    }
    else if (typeof obj === 'object') {
        var own = Object.getOwnPropertyNames(obj);

        own.forEach(function (key) {
            if (vals.indexOf(obj[key]) !== -1) {
                delete obj[key];
            }
            else {
                removePropsByVal(obj[key], vals);
            }
        });
    }
}

(function (window, angular) {
    function clean(el) {
        function internalClean(el) {
            return _.transform(el, function (result, value, key) {
                var isCollection = _.isObject(value);
                var cleaned = isCollection ? internalClean(value) : value;

                if (isCollection && _.isEmpty(cleaned)) {
                    return;
                }

                _.isArray(result) ? result.push(cleaned) : (result[key] = cleaned);
            });
        }

        return _.isObject(el) ? internalClean(el) : el;
    }

    var app = angular.module('app');

   

    app.directive('templateSelect', ['templateTree', 'JobService',  '$sessionStorage',function (templateTree, JobService, $sessionStorage) {
        var tree, dcats ;
        return {
            restrict: 'EA',
            scope: {
                jobOptionId: '=',
                removeOtherProducts: '=',
                rop: '=',
                dcats: '=', // denied categories that should not be in the drop down
                jobCategory: '=',
                isWhiteLabel: '=',
                removeSameDay: '='
                //
            },
            templateUrl: 'common/directives/templateSelect.html',
            link: function ($scope, $element, $attr, parentDirectCtrl) {
				//tree = angular.copy(templateTree);
				//$scope.tree = tree;
			},
            controller: ['$scope', function ($scope) {
                var vm = {};
                $scope.vm = vm;

                $scope.keys = function (x) {
                    if (!x || typeof x != 'object')
                        return [];
                    return Object.keys(x);
                };
                $scope.key0 = function (x) {
                    if (!x || typeof x != 'object')
                        return '';
                    return Object.keys(x)[0];
                };

                $scope.values = function (x) {
                    if (!x || typeof x != 'object') return [];
                    return Object.values(x);
                };

                $scope.values0 = function (x) {
                    if (!x || typeof x != 'object') return [];
                    return Object.values(x)[0];
                };

                $scope.tryExtractValue = function (n) {
                    if (!n) return;

                    if (n.constructor === Number) {
                        $scope.jobOptionId = n;
                    }
                };

                $scope.$watch("vm.Category", function (n, o) {
                    if (!n) return;
                    $scope.tryExtractValue(n);

                    if (n["Production"]) {
                        // see if n is an object containing only 1 service, just use it
                        var ov = Object.values(n["Production"]);
                        if (ov.length == 1) {
                            var xxx = ov[0];
                            vm.Category2 = xxx;
                            $scope.tryExtractValue(xxx);
                        }
                    }
                    //if (n != o) {
                    //	$scope.$emit('clear-price');
                    //}
                });

                // SET CONTROLS on EXISTING JOB
                // do a reverse looup on tree and set controls
                $scope.$watch("jobOptionId", function (n, o) {
                    if (!n) { return; }

                    var f = tree.bottomup[n];
                    var y = tree.topdown.Category[f.Category];
                    $scope.jobCategory = f.Category;

                    vm.Category = y;
                    if (typeof vm.Category === 'object' && Object.values(f).length == 2) {
                        vm.Category2 = n;
                    } else if (typeof vm.Category === 'object' && Object.values(f).length == 3) {
                        if (f['Production']) {
                            vm.Category2 = y.Production[f.Production];
                        }
                        vm.Category3 = n;
                    }
                    if (n !== o) $scope.$emit('category-changed');
                });

                $scope.$on("set-category", function (evt, category) {
                    if (!category) { return; }

                    var y = tree.topdown.Category[category];
                    vm.Category = y;
                });

                $scope.$on("set-category-force", function (evt, category) {
                    if (!category) { return; }
                    var y = tree.topdown.Category[category];

                    vm.Category = y;
                    $scope.tryExtractValue(y);
                    $scope.$emit('category-changed');
                });

                $scope.getStockOptionsOnTemplate = function (tid) {
                    if (typeof tid === 'object') return;
                    JobService.getStockOptionsOnTemplate(tid).then(function (r) {
                        $scope.listOfStockOptionsOnTemplate = r;
                    });
                };

                function init() {

                    tree = angular.copy(templateTree);
                    
                    if (window.IsBarneys) removeOtherProducts = true;

                    if ($scope.removeOtherProducts) {
                        delete tree.topdown.Category['Other Products'];
                    }

                    var removeSameDay = $scope.$eval($scope.removeSameDay);
                    if (window.IsBarneys) removeSameDay = true;

                    if (removeSameDay) {
                        removeProps(tree, 'Same day');
                    }

                    dcats = $scope.$root.globals.DeniedTemplates;
                    if (dcats && dcats.length) {
                        removePropsByVal(tree.topdown, dcats);
                        tree.topdown = clean(tree.topdown);
                    }
                    

                    // var wideFormatTemplateIds = [32,33,34,36,37,38,41,42,43,44]
                    // removePropsByVal(tree.topdown, wideFormatTemplateIds );
                    // tree.topdown = clean(tree.topdown);


                    $scope.tree = tree;

                }
                init();
                $scope.$on('update-login-details', function () {
                    //cd('re init template tree')
                    init();
                });
                
               // setInterval( init, 5000);
               $scope.$on("set-tree", function (evt, category) {
                    //cd('set-tree');
                    init();
                });

            }]
        };
    }]);
})(window, angular);
