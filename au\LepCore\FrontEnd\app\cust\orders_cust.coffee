﻿do (window, angular, toastr, bootbox) ->
    app = angular.module('app')

    app.directive 'lepCustOrdersList', [ '$timeout', ($timeout)->
            {
            templateUrl: 'cust/order-box.html'
            restrict: 'EA'
            scope: ordersList: '=', open: '=', pp: '='

            controller : [
              '$scope', '$state','$http', 'imgLookup',
              ($scope, $state, $http, imgLookup) ->
                $scope.imgLookup = imgLookup
                $scope.limit = 10
                $scope.changeLimit = (limit) ->
                    if $scope.limit is 10
                        $scope.limit = 10000
                    else if $scope.limit is 10000
                        $scope.limit = 10
                    $timeout  () ->
                            $('[data-toggle="tooltip"]')
                            .tooltip({trigger: 'manual'})
                            .tooltip('show')
                    , 10

                $scope.openCustJob = (o,j) ->
                    $scope.$emit("open-job",{orderId: o , jobId: j})

                $scope.openCustOrder = (o) ->
                    $scope.$emit("open-order",{orderId: o})

                $scope.checkSteps = (id) ->
                    $http.get("/api/Orders/Job/#{id}/RoutingChain").then (d) ->
                        r = d.data
                        toastr.remove()
                        toastr.info(JSON.stringify(r.routesAllList, true))
            ]

            }
    ]

    s = {}
    s.YOU_HAVE_STARTED    = "You've Started"
    s.SUBMITTED_IN_PORTAL = "Submitted in Portal"
    s.WE_ARE_PRINTING     = "We're Printing"
    s.RECENT              = "Recent"
    s.HISTORY             = "History"



    app.controller 'CustOrdersListController', [
        '$scope', 'OrderService', 'JobService', '$sessionStorage', '$location', '$state','$timeout', 'templateTree',   '$stateParams', 'lepApi2',
        ($scope,   OrderService,   JobService,  $sessionStorage, $location, $state, $timeout, templateTree , $stateParams, lepApi2 ) ->
            $scope._ = 'CustOrdersListController'
            $scope.templateTree = templateTree
            lastTab = $sessionStorage.lastTab || s.WE_ARE_PRINTING
            $scope.bOrderStatusUsed = true
            $scope.tabs =
                list: [
                    { title: s.YOU_HAVE_STARTED }
                    #{ title: s.SUBMITTED_IN_PORTAL  }
                    { title: s.WE_ARE_PRINTING }
                    { title: s.RECENT }
                    { title: s.HISTORY }
                ]
                current:  lastTab

            $scope.current =  lastTab

            # $scope.$watch "vm", (n,o) ->
            #     $scope.search()
            # , true

            $sessionStorage.search = $sessionStorage.search || {}
            $scope.vm = angular.copy($sessionStorage.search[$state.current.name]) || {Page: 1}
            vm = $scope.vm

            vm.JobTypes =  null
            $scope.cTab = vm.Tab ||   s.WE_ARE_PRINTING

            if $stateParams.justSubmitted then $scope.cTab =  s.WE_ARE_PRINTING

            $scope.$watch "tabs.current", (n,o) ->
                delete vm.IsWLOrderPaidFor
                vm.Tab = n
                vm.Page = 1
                if n !=  s.YOU_HAVE_STARTED
                    delete vm.IsRejected
                    delete vm.IsWaitingApproval
                    delete vm.IsQuoteRequired
                statuses = []
                if n is s.YOU_HAVE_STARTED    then statuses = ['Open']
                if n is s.SUBMITTED_IN_PORTAL then statuses = ['SubmittedInPortal']
                if n is s.WE_ARE_PRINTING     then statuses = ['Submitted','Ready', 'Finished']
                if n is s.RECENT              then statuses = ['Dispatched']
                if n is s.HISTORY             then statuses = ['Archived']
                vm.OrderStatuses = statuses
                $sessionStorage.lastTab = n
                $scope.search()
            , true


            JobService.getTemplates().then (d) ->
                $scope.templates = d

            $scope.search = () ->
                $scope.msg = "searching..."
                $sessionStorage.search[$state.current.name] =  angular.copy($scope.vm)
                OrderService.getOrders(vm).then (d) ->
                    $scope.list = d.list
                    $scope.bOrderStatusUsed = d.bOrderStatusUsed
                    $scope.msg = ""
                    if  d.list.List.length > 0  then $scope.msg  = "#{ d.list.List.length} orders<br/>"
                    if  d.list.List.length == 0
                        $scope.msg  = "no orders found."

            $scope.goPage = (p) ->
                vm.Page = p
                $scope.search()

            $scope.clear = () ->
                vm.Page = 1
                delete vm.OrderNr
                delete vm.JobTypes
                delete vm.IsRejected
                delete vm.IsWaitingApproval
                delete vm.IsQuoteRequired
                delete vm.Customer
                $scope.bOrderStatusUsed = true
                $sessionStorage.search[$state.current.name] =  angular.copy(vm)
                $scope.search()

            $scope.$on "open-job",     (evt,data) -> 
                $state.go('cust.order.job', data)

            $scope.$on "open-order",   (evt,data) -> 
                $state.go('cust.order.view', data)

            $scope.customers = []
            $scope.searchCust = (s) ->
                lepApi2.get("cust/SubCustomer/SubCustomers", {s: s}, true).then (list) ->
                    $scope.customers = list

    ]
