do (window, angular, toastr, bootbox) ->
    app = angular.module('app')

    app.directive 'connotes', [ ()->
            {
            templateUrl: 'common/connotes.html'
            restrict: 'A'
            scope: order: '='

            controller : ['$scope', '$state',($scope, $state) ->

                $scope.fgnotes = []
                $scope.pmnotes = []
                urls = {
                    'TNT'             : 'https://www.tntexpress.com.au/interaction/trackntrace.aspx?con='
                    'AusPost'         : 'https://auspost.com.au/mypost/track/#/details/'
                    'AUSTRALIA POST'  : 'https://auspost.com.au/mypost/track/#/details/'
                    'StarTrack'       : 'https://auspost.com.au/mypost/track/#/details/'
                    'STARTRACK EXP'   : 'https://auspost.com.au/mypost/track/#/details/'
                    'FASTWAY'         : 'https://www.fastway.com.au/tools/track?l='
                    'COURIERS PLS II' : 'https://www.couriersplease.com.au/tools-track?no='
                    'TOLL NQX'        : 'https://www.mytoll.com/?externalSearchQuery='
                    'ARAMEX'          : 'https://www.aramex.com.au/tools/track?l='
                }

                $scope.$watch 'order', () ->
                    cns = $scope.order.ConNotes
                    g = _.groupBy(cns, 'CarrierName')

                    n2 = []
                    for carrier, notes of g
                        carrier_ =  carrier
                        if carrier == 'AUSTRALIA POST'
                            carrier_ = 'AusPost'
                        if carrier == 'TOLL NQX'
                            carrier_ = 'toll'

                        n = {}
                        n.carrier =  carrier_
                        n.logo    = "images/couriers/#{carrier_}.png"

                        if carrier is 'FASTWAY' or carrier is 'ARAMEX' 
                            n.notes   = _.flatten(_.map(notes, (i) -> i.TrackingLabels));
                        else
                            n.notes   = _.map(notes,'ConNote')

                        n2.push(n)

                    $scope.gnotes = n2
                , true

                trackAustraliaPost = (conNote) -> urls['AusPost'] + conNote
                trackStarTrack     = (conNote) -> urls['STARTRACK EXP'] + conNote
                trackFastway       = (conNote) -> urls['FASTWAY'] + conNote
                trackTNT           = (conNote) -> urls['TNT']     + conNote
                trackCouriersPls   = (conNote) -> urls['COURIERS PLS II'] + conNote
                trackTollNQX       = (conNote) -> urls['TOLL NQX'] + conNote
                trackAramex        = (conNote) -> urls['ARAMEX'] + conNote

                $scope.track = (connote, courier) ->
                    url = "#"

                    if courier is 'FASTWAY'
                        url = trackFastway(connote)
                    if courier is 'AusPost' or courier is 'AUSTRALIA POST'
                        url = trackAustraliaPost(connote)
                    if courier is 'TNT'
                        url = trackTNT(connote)
                    if courier is 'COURIERS PLS II'
                        url = trackCouriersPls(connote)
                    if courier is 'STARTRACK EXP' or courier is 'STARTRACK'
                        url = trackStarTrack(connote)
                    if courier is 'TOLL NQX'
                        url = trackTollNQX(connote)
                    if courier is 'ARAMEX'
                        url = trackAramex(connote)
                    if(url != "#")
                        window.open(url)

                openPopupWriteDoc = (windowContent) ->
                    w = window.open('', '', '')
                    w.document.write windowContent
                    w.focus()
                    return

            ]

            }
    ]
