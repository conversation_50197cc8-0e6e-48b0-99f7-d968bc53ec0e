#jobStatusesToScan = "Open,Submitted,PreflightDone,DPCPreProduction,WideFormatProduction,InRun,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,Celloglazed,Cut,Scored,Perforated,Letterpressed,Folded,Stitched,Drilled,Rounded,Finished,DPCPrinted,DPCComplete,WideFormatComplete,ShrinkWrapped,Packed,Dispatched,Complete,Outwork".split(",")
appStaff = angular.module('app.staff')

appStaff.directive 'jobScan', ->
	{
	restrict: 'EA'
	scope: { id: '=', routes: '='}
	templateUrl: 'staff/job-scan.html'
	link: ($scope, $elem, $attrs) ->

	controller : ['$scope',  '$element', '$attrs', '$state', '$http', 'enums',	($scope,  $element, $attrs, $state, $http, enums) ->
			id = $scope.id

			statusOutwork = enums.KeyVal.JobStatusOptions.Outwork
			statusPayMe   = enums.KeyVal.JobStatusOptions.PayMe

			if(!$scope.routes)
				$http.get("/api/Orders/Job/#{id}/RoutingChain").then (result) ->
					r = result.data.routesAllList
					if r.indexOf(statusOutwork) == -1 then r.push(statusOutwork)
					if r.indexOf(statusPayMe)   == -1 then r.push(statusPayMe)
					$scope.jobStatusesToScan =	r
			else
				r = $scope.routes
				if r.indexOf(statusOutwork) == -1 then r.push(statusOutwork)
				if r.indexOf(statusPayMe)   == -1 then r.push(statusPayMe)
				$scope.jobStatusesToScan = r

			$scope.scanJob = (status) ->
				statusStr = enums.ValueKey.JobStatusOptions[status]
				$http.post('/api/Barcode/scan', {Scanner: statusStr, Barcode: "J"+id}).then(
					(d) ->
						$state.reload()
				,	(d) ->
						toastr.error(d.data.data)
				)
		]
	}

appStaff.directive 'jobPrintLabel', ->
	{
	restrict: 'EA'
	scope: { jid: '='}
	templateUrl: 'staff/job-print-label.html'
	link: ($scope, $elem, $attrs) ->

	controller : ['$scope',  '$element', '$attrs', '$state', '$http', 'enums',	($scope,  $element, $attrs, $state, $http, enums) ->
			id = $scope.jid
			$scope.labelsToPrint = ['FurtherProcessing','Carton','Freight','Filing','FilingPayMe','FreightMe','ImmediateFreightMe','FreightPickList','Pickup','DPCProcessing','WideFormatProcessing']
			$scope.printJob = (label) ->
				$http.get("/api/Orders/job/#{id}/print/#{label}").then (d) ->

		]
	}

appStaff.directive 'runScan', ->
	{
	restrict: 'EA'
	scope: { id: '=', printType: '='}
	templateUrl: 'staff/runs/run-scan.html'
	link: ($scope, $elem, $attrs) ->

	controller : ['$scope',  '$element', '$attrs', '$state', '$http', 'enums',	
	($scope,  $element, $attrs, $state, $http, enums) ->
			id = $scope.id

			if $scope.printType is 'O'
				$scope.runStatusesToScan  = [0,1,2,3,5,6,7,8,9]
			else if $scope.printType is 'D'
				$scope.runStatusesToScan  = [0,1,2,4,6,7,8,9]
			else 
				$scope.runStatusesToScan  = [0,1,2,3,4,5,6,7,8,9]
			
			$scope.jobStatusesToScan  = [12,13]

			$scope.scanRun = (status) ->
				statusStr = enums.ValueKey.RunStatusOptions[status]
				if !statusStr 
					statusStr = enums.ValueKey.JobStatusOptions[status]
				$http.post('/api/Barcode/scan', {Scanner: statusStr, Barcode: "R" + id}).then (d) ->
					$state.reload()
		]
	}
