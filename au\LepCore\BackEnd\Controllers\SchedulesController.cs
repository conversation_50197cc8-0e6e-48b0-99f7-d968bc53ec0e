//using System;
//using System.ComponentModel.DataAnnotations;
//using AutoMapper;
//using lep;
//using lep.user;
//using lep.user.impl;
//using LepCore.Dto;
//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Http;
//using Microsoft.AspNetCore.JsonPatch;
//using Microsoft.AspNetCore.Mvc;
//using NHibernate.Criterion;
//using System.IO;
//using Microsoft.Net.Http.Headers;
//using System.Linq;
//using lep.email;
//using lumen.csv;
//using System.Text;
//using System.Collections;
//using System.Collections.Generic;
//using lep.job;
//using lep.jobmonitor;
//using LepCore.DTOs;

//namespace LepCore.Controllers
//{
//	/// <summary>
//	/// </summary>
//	[Produces("application/json")]
//	[Route("api/Staff/[controller]")]
//	[Authorize(Roles = LepRoles.Staff)]
//	public class SchedulesController : Controller
//	{
//		private readonly IMapper _mapper;
//		private readonly IScheduleApplication _scheduleApplication;
//		private readonly IEmailApplication _emailApplication;
//		private NHibernate.ISession _session;
//		private readonly IHttpContextAccessor _contextAccessor;
//		public SchedulesController (
//			IHttpContextAccessor contextAccessor,
//			IScheduleApplication scheduleApplication,
//			IEmailApplication emailApplication,
//			IMapper mapper,
//			NHibernate.ISession session

//		)
//		{
//			_contextAccessor = contextAccessor;
//			_scheduleApplication = scheduleApplication;
//			_mapper = mapper;
//			_session = session;
//		}

//		// called from staff pages to get a list of orders
//		[HttpGet("Get/{facility}/{template:int}/{folding:bool}")]
//		//[ValidateActionParameters]

//		public IActionResult GetByFaciltiyTemplate ([FromQuery]  Facility facility, JobTypeOptions template , bool folding)
//		{
//			var list = _scheduleApplication.GetSchedulesByTemplate(facility, template, ProductionTiming.Standard, folding);
//			var list2 = _mapper.Map<List<ScheduleDto>>(list);
//			return new OkObjectResult(list2);
//		}

//	}
//}
