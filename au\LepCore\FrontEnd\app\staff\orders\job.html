﻿<!--
cref:  Order<PERSON>obController,   common\orders.coffee
-->
<form name="jobDetailsForm">
    <div>
        <div leppane="Job details: {{job.Template.Name}}" visible="true">
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-horizontal">
                        <div class="form-group" ng-hide="job.Id == 0">
                            <label class="col-xs-3 control-label">Job#   </label>
                            <div class="col-xs-7 form-control-static">
                                {{::(job.Id || 'New Job')}}

                                &nbsp;&nbsp;&nbsp;
                                <span job-scan id="job.Id" routes="::job.Visibility.Progress.routesAllList"></span>
                                <span job-print-label jid="job.Id"></span>

                                <span ng-if="job.RunIds.length>0">
                                    <a ui-sref="staff.run-edit({runId: job.RunIds[0]})">( run# {{job.RunIds[0]}})</a>
                                </span>
                                
                                &nbsp;&nbsp;<a href="#" style="white-space: nowrap; text-decoration: none;"
                                         title="Nominate this job for DPC production."   ng-click="jobToDPC(job.Id)">DPC</a>

                                <span ng-if="job.ReOrderSourceJobId != 0" style="font-size: small">
                                    <!--&nbsp;&nbsp;&nbsp;<a ui-sref="^.job({jobId: job.ReOrderSourceJobId})" class="bold ng-binding" target="_blank">(Original job J{{::job.ReOrderSourceJobId}}) </a>-->
                                    &nbsp;&nbsp;&nbsp;<a ng-click="goJob(job.ReOrderSourceJobId)" class="bold ng-binding" target="_blank">(Original job J{{::job.ReOrderSourceJobId}}) </a>
                                    
                                </span>
                            </div>
                        </div>
                        <div class="form-group" ng-hide="job.Id == 0">
                            <label class="col-xs-3 control-label">Created</label>
                            <div class="col-xs-7 form-control-static">
                                {{job.DateCreated  | date:'dd-MMM-yy HH:mm'}}, <span class="text-muted" am-time-ago="job.DateCreated"></span>
                                
                                <span ng-if="job.CreatedBy && job.CreatedBy.IsStaff">
                                    <br /> by LEP / {{job.CreatedBy.FirstName}} {{job.CreatedBy.LastName}}
                                </span>
                                <span ng-if="job.CreatedBy && !job.CreatedBy.IsStaff">
                                    <br /> by Customer {{job.CreatedBy.Username}} 
                                </span>
                            </div>
                        </div>
                        <div class="form-group" ng-hide="job.Id == 0">
                            <label class="col-xs-3 control-label">Status</label>
                            <div class="col-xs-7  form-control-static">
                                {{::enums.ValueDesc.JobStatusOptions[job.Status]}}
                            </div>
                        </div>

                        <div class="form-group ng-class:{'has-error': !job.Name}">
                            <label class="col-xs-3 control-label" for="JobName">Job Name</label>
                            <div class="col-xs-7">
                                <input id="JobName" name="JobName" type="text" class="form-control input"
                                       ng-model="job.Name" ng-required="true" />
                            </div>
                        </div>
                    </div>

                    <div lep-job-details job="job" price-calc="false" vnp="1" vpp="1">
                    </div>


                    <div class="form-horizontal">
                        <!-- -- <pre>{{job.artspecs}}</pre> -- -->
                        <div class="form-group " ng-if="job.artspecs.length" style="font-weight: bold !important;">
                            <label class="col-xs-3 control-label" for="">Artwork requirements</label>
                            <div class="col-xs-7" style="padding: 6px;">
                                 <a ng-repeat="a in job.artspecs" 
                        target="_blank" class="error-redish " style="display: block;"
                        href="/images/art-specs/{{a}}" > Refer attached guide for assistance <br/> {{a}}&nbsp;&nbsp;<i class="glyphicon glyphicon-cloud-download"></i><br/><br/></a>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="col-sm-6  form-horizontal">
                    <div class="form-group">
                        <div class="col-sm-11 col-sm-offset-1">
                            <label>Special instructions</label> <br />
                            <textarea class="form-control input" ng-model="job.SpecialInstructions"
                                      rows="8" maxlength="300" maxlines="8" maxlines-prevent-enter="true"></textarea>
                            <div style="text-align: left; font-size: 13px; ">
                                {{(300 - job.SpecialInstructions.length)}}  chars  or {{8 - job.SpecialInstructions.split("\n").length}} lines left.
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-11 col-sm-offset-1">
                            <label>Production instructions</label> <br />
                            <textarea class="form-control input" ng-model="job.ProductionInstructions"
                                      rows="8" maxlength="300" maxlines="8" maxlines-prevent-enter="true"></textarea>
                            <div style="text-align: left; font-size: 13px; ">
                                {{(300 - job.ProductionInstructions.length)}}  chars  or {{8 - job.ProductionInstructions.split("\n").length}} lines left.
                            </div>
                        </div>
                    </div>

                    <!-- MYOB
                    <div class="form-group">
                        <label class="col-xs-3 control-label" for="myob"> MYOB</label>
                        <div class="col-xs-7">
                            <input id="myob" name="myob" type="text" class="form-control input" ng-model="job.MYOB" />
                        </div>
                    </div>
                    -->

                    <div class="form-group">
                        <label class="col-xs-3 control-label" for="PrintByDate">Print by</label>
                        <div class="col-xs-7">
                            <div class="input-group">
                                <div class="col-sm-6 col-xs-6 lpad0 rpad0">
                                    <input type="text" size="10" class="form-control" ng-model="job.PrintByDate" data-date-format="dd-MMM-yy" data-autoclose="1" placeholder="Date" bs-datepicker>
                                </div>
                                <div class="col-sm-6 col-xs-6 lpad0 rpad0">
                                    <input type="text" size="8" class="form-control" ng-model="job.PrintByDate" data-time-format="HH:mm" data-autoclose="1" placeholder="Time" data-default-time="false" bs-timepicker>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-xs-3 control-label" for="DispatchDate">Despatch by</label>
                        <div class="col-xs-7">
                            <div class="input-group">
                                <div class="col-sm-6 col-xs-6 lpad0 rpad0">
                                    <input type="text" size="10" class="form-control" ng-model="job.RequiredByDate" data-date-format="dd-MMM-yy" data-autoclose="1" placeholder="Date" bs-datepicker>
                                </div>
                                <div class="col-sm-6 col-xs-6 lpad0 rpad0">
                                    <input type="text" size="8" class="form-control" ng-model="job.RequiredByDate" data-time-format="HH:mm" data-autoclose="1" placeholder="Time" data-default-time="false" bs-timepicker>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- <pre> {{job.Facility}} - {{job.IsCustomFacility}}</pre> -->
                    <div class="form-group">
                        <label class="col-xs-3 control-label" for="facility">Production facility</label>
                        <div class="col-xs-7">
                            <select name="facility" ng-options="k*1 as v for (k,v) in enums.ValueDesc.Facility" ng-model="job.Facility" class="form-control">
                                <option value="">Auto</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-xs-3 control-label">Other flags</label>
                        <div class="col-xs-7">
                            <div>
                                <label><input id="previewChk" ng-model="job.PreviewChk" type="checkbox" /> Preview requires approval by customer</label>
                            </div>
                            <div>
                                <label><input id="proofChk" ng-model="job.ProofChk" type="checkbox" /> Send proof to customer before printing</label>
                            </div>
                            <div>
                                <label><input id="onholdChk" ng-model="job.OnHoldChk" type="checkbox" /> On Hold</label>
                            </div>

                            <div>
                                <label><input ng-model="job.InvolvesOutwork" type="checkbox" /> Job Involves Outwork</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group ng-class:{'has-error': !job.CustomSlot}" ng-if="showSlotEntryInput">
                        <label class="col-xs-3 control-label" for="slots"> Custom Slot</label>
                        <div class="col-xs-2">
                            <input id="slots" name="slots" type="number" class="form-control input" ng-model="job.CustomSlot" ng-required="true" ng-min="1" />
                        </div>
                    </div>

                    <div class="form-group" ng-show="::(job.Visibility.Thumbs.length>0)">
                        <label class="col-xs-3 control-label">Job thumbnails:</label>
                        <span class="col-xs-12" style="text-align: center">
                            <img ng-repeat="t in job.Visibility.Thumbs track by $index"
                                 ng-src="/api/orders/Job/{{::job.Id}}/thumb/{{t}}"
                                 style="max-width: 240px;" />
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-6">
                <div leppane="Art work" visible="true">
                    <a  id="artwork-link"  target="_self" >
                        <i class="glyphicon glyphicon-folder-open"></i> Open artwork 
                    </a>
                     <br/>{{::job.Visibility.artworkFolderNavigateUrl}}  
                    <div lep-job-artwork-uploader-async job="job"></div>
                </div>
            </div>
            <div class="col-sm-6">
                <div leppane="Extra files">
                    <button class="btn btn-sm btn-default" ng-click="uploadExtraFile(job.Id)">Upload extra files <i class="glyphicon glyphicon-upload"></i></button>

                    <table class="table table-condensed">
                        <tr ng-repeat="t in extraFiles track by $index">
                            <td>
                                <a ng-click="downloadExtraFile(job.Id, t.Name)">{{t.Name}}</a>
                            </td>
                            <td nowrap>{{::t.CreationTime | date:'dd-MMM-yy HH:mm'}} </td>
                            <td style="white-space: pre-wrap">
                                <a ng-click="deleteExtraFile(job.Id, t.Name)"> <i class="glyphicon glyphicon-remove-circle"></i></a>
                            </td>
                        </tr>
                    </table>
                </div>
                <div ng-if="$root.globals.IsA || $root.globals.User.Role == 3">
                    <div leppane="Quote" visible="false">
                        <div class="row">
                            <div class="form-horizontal">


                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="slots"> Estimator</label>
                                    <div class="col-xs-7">


                                        <select name="QuoteEstimator" ng-options="v as v for v in estimators" ng-model="job.QuoteEstimator" class="form-control">
                                            <option value=""></option>
                                        </select>

                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="QuoteCOGS"> COGS</label>
                                    <div class="col-xs-7">
                                        <input name="QuoteCOGS" type="number" class="form-control input" ng-model="job.QuoteCOGS" />
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="QuoteOutworkCost">Outwork Cost </label>
                                    <div class="col-xs-7">
                                        <input name="QuoteOutworkCost" type="number" class="form-control input" ng-model="job.QuoteOutworkCost" />
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="QuoteComments">  Comments </label>
                                    <div class="col-xs-7">
                                        <textarea name="QuoteComments" class="form-control input" ng-model="job.QuoteComments" rows="4" maxlength="300" maxlines="4" maxlines-prevent-enter="true"></textarea>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="QuotePrimary">  Primary </label>
                                    <div class="col-xs-7">
                                        <select name="QuotePrimary" ng-options="v as v for v in ['Primary', 'Variation']" ng-model="job.QuotePrimary" class="form-control">
                                            <option value=""></option>
                                        </select>
                                    </div>
                                </div>



                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="QuoteOutcome">  Outcome </label>
                                    <div class="col-xs-7">
                                        <select name="QuoteOutcome" ng-options="v as v for v in ['Requested',  'Pending', 'WonPendingSubmitted','Won','Lost', 'Rejected']" ng-model="job.QuoteOutcome" class="form-control">
                                            <option value=""></option>
                                        </select>
                                    </div>
                                </div>
                            

                                <div class="form-group" ng-show="job.QuoteOutcome == 'Lost'">
                                    <label class="col-xs-3 control-label" for="QuoteLostReason">Reason for Lost</label>
                                    <div class="col-xs-7">
                                        <select name="QuoteLostReason" ng-options="v as v for v in ['Customer did not win Job', 'Customer gave job to competitor', 'Customer’s customer changed mind', 'Customer didn’t follow-up', 'Unknown']" ng-model="job.QuoteLostReason" class="form-control">
                                            <option value=""></option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="form-group" ng-show="job.QuoteOutcome == 'Lost'">
                                    <label class="col-xs-3 control-label" for="QuoteLostComments">Comments</label>
                                    <div class="col-xs-7">
                                        <textarea name="QuoteLostComments" class="form-control input" ng-model="job.QuoteLostComments" rows="2" maxlength="32"></textarea>
                                    </div>
                                </div>


                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="QuoteFollowUpNotes">  Follow Up Notes </label>
                                    <div class="col-xs-7">
                                        <div class="pre">{{job.QuoteFollowUpNotes}}</div>
                                        
                                        <textarea name="QuoteFollowUpNotes" class="form-control input"
                                         ng-model="job.QuoteFollowUpNotesAdd" rows="4" maxlength="1000" maxlines="4" maxlines-prevent-enter="true"></textarea>
                                    </div>
                                </div>


                            </div>
                        </div>

                    </div>
                </div>
                
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <div class="pull-right">
                    <a class="btn" ng-click="openOrder()">
                        <i class="glyphicon glyphicon-chevron-left"></i>
                        back to order
                    </a>

                    <a class="btn" ng-click="nextJob()" ng-show="::($parent.order.Jobs.length > 1)">
                        next job
                        <i class="glyphicon glyphicon-chevron-right"></i>
                    </a>

                    <button class="btn " ng-show="::job.Visibility.clearButton" ng-click="reload()">
                        <i class="glyphicon glyphicon-refresh"></i>
                        Clear job
                    </button>

                    <button class="btn  " ng-show="::job.Visibility.deleteButton" ng-click="jobCommand('Delete')">
                        <i class="glyphicon glyphicon-trash"></i>
                        Delete job
                    </button>

                    <button class="btn " id="rejectButton" ng-show="job.Visibility.rejectButton" ng-click="jobCommand('Reject')">
                        Reject Job
                    </button>


                    <button class="btn " id="unableToMeetPrice" 
                        ng-show="job.Visibility.rejectButton && job.CustomerRequestedPrice" ng-click="jobCommand('UnableToMeetPrice')">
                        Unable to meet price
                    </button>

                    
                    <button class="btn " id="rejectionOfVariationRequest" 
                        ng-show="job.Visibility.rejectButton && job.CustomerRequestedPrice" ng-click="jobCommand('RejectionOfVariationRequest')">
                        Reject Variation
                    </button>

                    <!--<button class="btn btn-default" id="rejectButton" ng-show="::job.Visibility.reprintButton" ng-click="jobCommand('Reprint')">
        Reprint Job
    </button>-->

                    <button class="btn btn-default" ng-show="::job.Visibility.moveArtButtonVisible" ng-click="jobCommand('moveArtwork')">
                        Supplied --&gt; Ready Art
                    </button>
                    <button type="submit" class="btn " ng-show="(job.Id != 0 && job.Visibility.saveButton) || $root.globals.IsSA" click-and-wait="saveJob2('copy')"
                        ng-disabled="jobForm.$invalid">
                        <i class="glyphicon  glyphicon-floppy-save   "></i>
                        Save a copy
                    </button>


                    <button class="btn btn-default" ng-show="job.Visibility.saveButton  || $root.globals.IsSA" ng-click="saveJob2()" ng-disabled="jobDetailsForm.$invalid">
                        <i class="glyphicon ng-class:{ 'glyphicon-floppy-save': job.Id != 0,   'glyphicon-plus': job.Id == 0 }"></i>
                        Save job
                    </button>

                    <button id="acceptButton" class="btn btn-default" ng-show="::job.Visibility.acceptButton"
                            ng-click="jobCommand('AcceptSupplyArtwork')">
                        Proceed with Existing Art
                    </button>

                    <a class="btn btn-default" ng-show="::job.Visibility.restart" ui-sref="staff.job-ncr({orderId: job.OrderId, jobId: job.Id,  ncrCmd: 'Restart'})">Restart Job</a>
                    <a class="btn btn-default" ng-show="::job.Visibility.reprintButton" ui-sref="staff.job-ncr({orderId: job.OrderId,  jobId: job.Id, ncrCmd:'Reprint'})">Reprint job</a>
                </div>
            </div>
        </div>
        <br />
        <div leppane="Comments">
            <div class="row">
                <div class="col-sm-8">
                    <table class="table table-condensed table-striped" style="font-size: smaller">
                        <tr ng-repeat="c in (job.Comments | reverse)" ng-class="{'bold': c.LepOnly}">
                            <td>
                                <span ng-if="::c.Author.FirstName">
                                    {{::c.Author.FirstName}} {{::c.Author.LastName}}
                                </span>
                                <span ng-if="::(!c.Author.FirstName)">
                                    {{::c.Author.Username}}
                                </span>
                            </td>
                            <td nowrap>{{::c.CreationDate | date:'dd-MMM-yy HH:mm'}} <span class="text-muted" am-time-ago="::c.CreationDate"></span>   </td>
                            <td style="white-space: pre-wrap">{{::c.CommentText}}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-sm-4">
                    <label class="control-label">New Comment</label>
                    <textarea class="form-control" ng-model="job.newComment.CommentText" rows="5"></textarea>
                    <label ng-if="job.newComment.CommentText"
                           title="">
                        LEP Internal comment (not visible to Customers) ? <input type="checkbox" ng-model="job.newComment.LepOnly" ng-value="true" />
                    </label>
                    <br />

                    <button value="add comment" class="btn" ng-click="addComment()">Add comment</button>

                    <!--{{job.newComment | json}}-->
                </div>
            </div>
        </div>
    </div>


</form>
