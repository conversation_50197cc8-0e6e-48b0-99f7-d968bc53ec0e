﻿<div ng-form="totalJobForm">
    <form name="jobDetailsForm">
        <div id="quoteExpireInst" class="alert alert-error" ng-show="job.Visibility.quoteExpireInst">
            Our NamedPrices are valid for up to 60 days. Please contact us to confirm if this NamedPrice is still valid.
        </div>
        <div id="priceExpireInst" class="alert alert-error" ng-show="job.Visibility.priceExpireInst">
            Our prices are valid for up to 30 days. We have updated your job price to reflect our current pricing
        </div>

        <div class="alert alert-danger" ng-show="job.FoldedSize.PaperSize.Id == 12 && !job.SpecialInstructions && job.Visibility.saveButton">
            Please also supply special instruction
        </div>

        <div leppane="Product details">
            <div class="row">
                <div class="col-md-9">

                    <div class="form-horizontal">

                        <div class="form-group" ng-hide="job.Id == 0">
                            <label class="col-xs-3 control-label">Job #</label>
                            <div class="col-xs-7 form-control-static price">{{::(job.Id || 'New Job')}}</div>

                            <div class="help-block pull-right">



                            </div>

                        </div>

                        <div class="form-group" ng-hide="job.Id == 0">
                            <label class="col-xs-3 control-label">Created</label>
                            <div class="col-xs-7 form-control-static">
                                {{job.DateCreated | date:'dd-MMM-yy HH:mm'}}, <span class="text-muted" am-time-ago="job.DateCreated"></span>

                                <span ng-if="job.CreatedBy && job.CreatedBy.IsStaff">
                                <br /> by LEP / {{job.CreatedBy.FirstName}} {{job.CreatedBy.LastName}}
                            </span>
                                <span ng-if="job.CreatedBy && !job.CreatedBy.IsStaff">
                                <br /> by Customer {{job.CreatedBy.Username}}
                            </span>

                            </div>
                        </div>


                        <div class="form-group" ng-hide="job.Id == 0">
                            <label class="col-xs-3 control-label">Status</label>
                            <div class="col-xs-7  form-control-static">
                                <span ng-style="{color: job.StatusCss}">{{::job.StatusC}}</span>
                            </div>
                        </div>

                        <div class="form-group ng-class:{'has-error': !job.Name}">
                            <label class="col-xs-3 control-label" for="JobName">Job Name</label>
                            <div class="col-xs-7">
                                <input id="JobName" name="JobName" type="text" class="form-control input" ng-model="job.Name" ng-required="!priceCalc" maxlength="80" />
                            </div>
                        </div>
                    </div>

                    <div lep-job-details job="job" price-calc="false" dcats="dcats" vnp="1" vpp="1"></div>

                    <div class="form-horizontal">
                        <!--Special instructions-->
                        <div class="form-group " ng-if="!priceCalc">

                            <label class="col-xs-3  control-label">Special instructions</label>
                            <div class="col-xs-7 form-control-static">
                                <div class="bold" style="color: blue; white-space: pre-wrap; overflow-wrap: break-word;">{{job.SpecialInstructions}}</div>
                                <a ng-show="job.Visibility.optionPnlEnabled && !job.Visibility.approvalButton && job.ReOrderSourceJobId==0  " ng-click="addSpecialInstructions()">
                                    <i class="glyphicon glyphicon-pencil"></i> Add instructions
                                </a>
                                <div ng-show="job.Visibility.optionPnlEnabled " class="help-block">Note: Quoting will require review by our print team</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-horizontal">
                        <!--Special instructions-->
                        <div class="form-group ng-class:{'has-error' : (!job.Copies || job.Copies<=0 ||job.Copies>99)}" ng-if="!priceCalc && job.Id == 0" ng-init="job.Copies = 1">

                            <label class="col-xs-3  control-label" title="Enter a value from 1 to 99">Number of Kinds</label>
                            <div class="col-xs-2 form-control-static">
                                <input type="number" class="form-control input" ng-model="job.Copies" ng-min="1" ng-max="99" maxlength="2" ng-required="true" title="Enter a value from 1 to 99" />
                            </div>
                            <div class="col-xs-6 ">
                                <span class="help-block">
                                Enter the number of Jobs you want to create from the above template.
                                This can be 1 upto 99.
                            </span>
                            </div>
                        </div>
                    </div>

                    <div class="form-horizontal">
                        <!-- -- <pre>{{job.artspecs}}</pre> -- -->
                        <div class="form-group " ng-if="job.artspecs.length" style="font-weight: bold !important;">
                            <label class="col-xs-3 control-label" for="">Artwork requirements</label>
                            <div class="col-xs-7" style="padding: 6px;">
                                <a ng-repeat="a in job.artspecs" target="_blank" class="error-redish " style="display: block;" href="/images/art-specs/{{a}}"> Refer attached guide for assistance <br/> {{a}}&nbsp;&nbsp;<i class="glyphicon glyphicon-cloud-download"></i><br/><br/></a>
                            </div>
                        </div>
                    </div>

                    <div ng-hide="job.Copies>1" lep-job-artwork-uploader-async job="job"></div>
                </div>
            </div>
        </div>
    </form>

    <div id="crpdiv" ng-if="crp.visible" class="ng-class:{'red': !job.CustomerRequestedPrice}">
        <div class="talk-bubble tri-right btm-right-in">
            <div class="info " style="margin:5px">
                Don’t know the price you wish to pay? put $1 and we will Quote.
            </div>

            <div class="">
                <span class="inline-block" style="margin-left: 5px;position: relative;top: 7px;">Price I want to pay
                <span style="font-size: x-small; display:block">(Excluding Freight &amp; GST)</span>
                </span>
                <input id="crp" name="crp" type="number" ng-model="job.CustomerRequestedPrice" class="form-control form-control-sm" style="width: 40%; display: inline-block; border-radius: 4px;margin:5px" length="6" inputmode="numeric" ng-required="{{false}}" ng-min="1"
                    ng-value="1" />
            </div>

            <div style="text-align: right;margin-top: 10px;">
                Click <span style="padding: 5px;margin: 2px 4px;">
                <i class="glyphicon {{crp.btnIcon}}"></i> {{crp.btnText}}</span> .
            </div>
        </div>

    </div>

    <div class="row">
        <div class="col-sm-12">
            <div class="form-actions">

                <a ng-if="job.OrderId != 0" class="btn" ng-click="openOrder()">
                    <i class="glyphicon glyphicon-chevron-left"></i> back to order</a>

                <a ng-if="job.OrderId != 0" class="btn" ng-show="job.Visibility.clearButton" ng-click="reload()"><i class="glyphicon glyphicon-refresh"></i> Clear</a>

                <a class="btn " ng-if="job.Visibility.splitDeliveryPossible" ui-sref="^.jobsplit({orderId:job.OrderId, jobId: job.Id})"> Split delivery </a>

                <a class="btn" ng-show="job.Visibility.deleteButton" ng-click="jobCommand('Delete')"><i class="glyphicon glyphicon-trash"></i> Delete</a>
                <!--<a class="btn" ng-show="job.Visibility.withdraw" ng-click="jobCommand('Withdraw')">Withdraw</a>-->

                <button type="submit" class="btn" ng-show="job.Id != 0 && job.Visibility.saveButton" click-and-wait="saveJob2('copy')" ng-disabled="jobForm.$invalid">
                <i class="glyphicon  glyphicon-floppy-save   "></i>
                Save a copy
            </button>

                <button type="submit" class="btn btn-success" ng-show="job.Visibility.saveButton || job.AttachmentsToProcess.length" click-and-wait="saveJob2()" ng-disabled="totalJobForm.$invalid">

                <i class="glyphicon ng-class:{ 'glyphicon-floppy-save':  job.Id != 0, 'glyphicon-plus':  job.Id == 0 }  "></i>
                {{ job.Price ?  "Next"  :"Continue"}}
            </button>

                <button id="acceptButton" class="btn btn-default" ng-show="job.Visibility.acceptButton" ng-click="jobCommand('AcceptSupplyArtwork')">
                Proceed with Existing Art
            </button>
                <button id="acceptButtonWithSubmit" class="btn btn-success bold" ng-show="job.Visibility.acceptButtonWithSubmit" ng-click="jobCommand('AcceptSupplyArtworkWithSubmit')">
                Proceed with Existing Art and Submit Order
            </button>



                <button class="btn btn-default" ng-show="job.Visibility.approvalButton" ng-click="jobCommand('ApproveQuote', true)">Approve</button>


            </div>
        </div>
    </div>


</div>
<!--<div ng-show="job.Visibility.addOrderHint" class="help-block">
        To price with freight or to proceed with order then click on the "Add to Order" button
    </div>-->


<div leppane="Comments">

    <table class="table  table-striped form-group-sm">
        <tr ng-repeat="c in job.Comments  | reverse">
            <td>
                {{ (c.Author.IsStaff==true)? "LEP": "You"}}
            </td>

            <td>{{::c.CreationDate | date:'dd-MMM-yy HH:mm'}} <span class="text-muted" am-time-ago="::c.CreationDate"></span> </td>

            <td style="white-space: pre-line; max-width: 300px">
                {{::c.CommentText}}
            </td>
        </tr>
    </table>
</div>


<!--
<div visual-diff n="job" o="jobVmo"></div>

<pre>
{{job | json}}
</pre>
<br /><ul>
    <li ng-repeat="(key, errors) in jobDetailsForm.$error track by $index">
        <ul>
            <li ng-repeat="e in errors">{{ e.$name }} has an error: <strong>{{ key }}</strong>.</li>
        </ul>
    </li>
</ul>
-->