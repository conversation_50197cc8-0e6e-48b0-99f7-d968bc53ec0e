do (window, angular, bootbox, toastr) ->
    app = angular.module('app')


    sure = "Are you sure you want to "
    jobCommandPrompts = {
        "Copy"                 : "create a copy of this job into a new order?"
        "Delete"               : "delete this job?"
        "RemoveArtworks"       : "remove this artwork?"
        }


    #
    # Angular controllers for White labeled pages
    #

    #
    # WhitelabelBaseController - base controller for all white labeled pages
    # See cust/base.js for all states for whitelabels
    # has logging in popup dialog and WLCustomerId maintainging code
    # that is common across all pages.
    #
    app.controller 'WhitelabelBaseController', [
        '$scope','ngDialog', 'ppc' , '$stateParams', '$state',  '$ocLazyLoad', '$sessionStorage','Auth','$http', '$rootScope',
        ($scope, ngDialog,	  ppc ,   $stateParams,   $state,   $ocLazyLoad,  $sessionStorage, Auth, $http, $rootScope) ->

            $scope.ppc = ppc
            $scope.$root.ppv = ppc.ppv


            $scope.customerId = $stateParams.customerId
            cid = $stateParams.customerId

            # if anonymous user has logged in then WLCustomerId will exist in session storage.
            $scope.WLCustomerId =  $sessionStorage?.WLCustomerId || null

            if ppc.CustomCssURL
                m = { name: 'custCss', files: [{ type: 'css', path: ppc.CustomCssURL }]}
                $ocLazyLoad.load(m)

            $scope.showLogo = window.location == window.parent.location
            $scope.logo = "/api/staff/customers/#{cid}/logo?r=" + (+new Date())

            #
            # when the web user clicks the login link, show a login dialog box,
            # try Authenticateing him and if that passes
            # send an event downsteam so either job  or order pages associate
		    # this guys Id as the WLCustomerId of the order or job
            #
            $scope.popupLogin = ->
                dialog = ngDialog.open(template   : 'cust/wl/login.html')
                dialog.closePromise.then (data) ->
                    xx = data.value
                    xx.Extra =  $scope.customerId
                    $http.post('/api/Account/Authenticate',xx)
                    .then (r) ->
                            t = r.data.token
                            id = r.data.userId
                            if t
                                $sessionStorage.lepToken	 = t
                                $sessionStorage.WLCustomerId = id
                                $scope.WLCustomerId			 = id
                                $scope.$broadcast('WLCustomerId',id)
                                $http.get('/api/Account/LoginDetails').then (response) ->
                                    if !response.data.error
                                        $rootScope.globals = response.data
                                        $rootScope.$broadcast('set-tree')
                            dialog.close()

                    , () ->
                        toastr.error 'Login failed, please try again.'
                return


            #
            # various state switching functions
            # ui-sref not used in order to hide urls on mouse hover
            $scope.logout = ->
                Auth.ClearCredentials()
                window.location = window.location.origin + '/wl/'+ $stateParams.customerId

            $scope.goOrdersList			= () -> $state.go('whitelabel.orderslist')
            $scope.goNewJob				= () -> $state.go('whitelabel.job', { orderId:0, jobId:0}, {reload:true})
            $scope.goRegisterAccount	= () -> $state.go('whitelabel.registerAccount')
            $scope.goMyDetails       	= () -> $state.go('whitelabel.editAccount')
    ]





    app.controller 'OrderViewControllerWL', [
        '$scope', '$state', '$timeout', 'Utils','OrderService', '$location', '$http', '$stateParams',   'order', '$rootScope','templateTree',  'ngDialog',   '$log',  'enums','lepApi2','$q', 'cfpLoadingBar' ,
        ($scope ,  $state,   $timeout,   utils,  OrderService,   $location,   $http,   $stateParams,     orderVmo, $rootScope , templateTree,    ngDialog,     $log,   enums,  lepApi2,  $q,   cfpLoadingBar) ->

            #$scope.dcats =   $scope.ppc.DeniedTemplates
            $scope.saveButtonText = "Proceed"
            if $scope.WLCustomerId
                $scope.saveButtonText = "Save Order"



            window.isWL = true
            window.ignoreErrorAndAttachAnyways  = !$scope.ppc.IsAacEnabled

            #if(!orderVmo.WLAnonymousUserId)
            #	orderVmo.WLAnonymousUserId = $sessionStorage.SessionId

            $scope._ = "OrderViewControllerWL"

            $scope.updatingCourier = false

            order = angular.copy(orderVmo)
            $scope.orderVmo =orderVmo
            $scope.order = order

            $rootScope.title = "O#{order.Id}"

            $scope.addressx = {}
            $scope.fileAttachmentPending = false


            orderState = "whitelabel"
            jobState   = "whitelabel"

            orderbase = "/api/Orders/order/#{$scope.order.Id}"
            $scope.qoText = 'order'

            $scope.$watch 'order.PriceOfJobs', (p,p0) ->
                if order.Status == 0
                    if p is null
                        $scope.qoText = 'quote'
                    else if p is 0
                        $scope.qoText = 'order'

            # if the courier changes,update order.PackDetail.Price and save order
            $scope.$watch 'order.Courier', (f,o) ->
                if f is o then return

                #if !f   then return
                so = _.find($scope.freights, (x) -> x.Value == f)
                if so
                    window.toastr.remove()
                    $scope.order.Courier = so.Value
                    $scope.order.PackDetail.Price =  so.CustomerCharge
                    $scope.saveOrder()
                if !so
                    $scope.order.Courier = ''
                    $scope.order.PackDetail.Price =  0
            , true


            # get list of freights for this order on page load
            getFreights = (id) ->
                if !order?.DeliveryAddress then return
                postcode = $scope.order?.DeliveryAddress.Postcode + "-" + $scope.order.DeliveryAddress.City
                freightFields = ""
                freightFields += order.Jobs.length
                for j in order.Jobs
                    freightFields += j.DateModified
                $scope.updatingCourier = true
                OrderService.getFreights(id, postcode, freightFields).then (f) ->
                    _.remove(f, { "CustomerCharge": 0})
                    $scope.freights  =  f
                    $scope.updatingCourier = false
                    # after getting freights see if the selected customer charge matches packing details price
                    # if not matches that means post code was updated and we need to update it

                    so = _.find($scope.freights, (x) -> x.Value == order.Courier)
                    if  so and  so.CustomerCharge != order.PackDetail.Price and so.Value != "None ~ None"
                        order.PackDetail.Price = so.CustomerCharge
                        window.toastr.remove()
                        $scope.order.PackDetail.Price =  so.CustomerCharge
                        #$scope.saveOrder()
                    if !so
                        order.Courier =   ''
                        order.PackDetail.Price = 0




            if !order?.PackDetail?.IsCustom
                getFreights(order.Id)

            # update the list of freights for this order when the delivery address changes
            # but dont do it on every keystroke, debounce 2 seconds
            getFreightsThrottled = _.debounce(getFreights, 3000, {trailing:true})

            $scope.deliveryDetailsChanged = false

            w1 = $scope.$watch('order.DeliveryAddress',(n,o) ->
                if angular.equals(n,o)  then return
                $scope.deliveryDetailsChanged = true
                $scope.addressDefault = angular.equals($scope.order.DeliveryAddress, $scope.order.Visibility.DefaultAddress)
                #job.DeliveryAddress = angular.copy(n)
            , true)

            w2 = $scope.$watchGroup ['order.RecipientName', 'order.RecipientPhone'], (n,o) ->
                if angular.equals(n,o)  then return
                $scope.deliveryDetailsChanged = true

            ###
            w3 = $scope.$watch 'order.DeliveryAddress.Postcode',(n,o) ->
                if n == o then return
                dto = angular.copy(order)
                delete dto.Visibility
                $http.post(orderbase,  dto).then (r) ->
                    getFreights(order.Id)
            ###



            success1 = (d) ->
                if d?.data
                    window.toastr.success(d.data)
                orderId = parseInt(d?.headers('OrderId'), 10)
                params = angular.copy($stateParams)
                params.scrollY = window.scrollY
                #$state.reload()
                $state.transitionTo($state.current, params, {reload: true,inherit: false,notify: true})
                return

            fail1 = (d) ->
                window.toastr.warning(d.data)
                return

            $scope.saveOrder = () ->
                dto = angular.copy(order) # first we copy the order to a temp
                delete dto.Visibility     # no need to post this
                delete dto.Jobs           # no need to post this
                $scope.deliveryDetailsChanged = false
                $http.post(orderbase,  dto).then(success1, fail1)

            $scope.freightChange = () ->

            $scope.submitOrder = () ->
                dto = angular.copy(order)
                delete dto.Visibility
                $http.post orderbase,  dto
                .then ->
                    $http.post orderbase + "/submit", {}
                    .then ->
                        window.toastr.remove()
                        window.toastr.success(submitMsg)

            $scope.deleteOrder = () ->
                $http.post "#{orderbase}/delete", {}
                .then ->
                        $scope.backToOrders()
                , fail1

            $scope.orderCommand = (command) ->
                $http.post "#{orderbase}/#{command}", {}
                .then success1, fail1


            $scope.jobCommand = (command, jobId) ->
                url = "#{orderbase}/job/#{jobId}/#{command}"
                prompt =  jobCommandPrompts[command]
                if prompt
                    prompt = sure + prompt
                    bootbox.confirm prompt, (r) ->
                        if not r then return
                        $http.post(url, {}).then(success1, fail1)
                else
                    $http.post(url, {}).then(success1, fail1)


            priceO = $scope.order?.PriceWL  || 0         # price of order
            priceStr    = priceO.toFixed(2).toString()   # price of order in string with 2 decimal places for display
            priceOCents = parseInt(parseFloat(priceStr) * 100)  # price of order in cents for paypal or stripe

            $scope.orderCanBePaidForNow = false          # start of assuming the order is not ready for payment yet


            if order and order.Jobs?.length	and !order.IsWLOrderPaidFor and order.Visibility.submitButton
                $scope.orderCanBePaidForNow = true

            # region PayPal
            if $scope.orderCanBePaidForNow and $scope.ppc.PayPalClientId
                $scope.showPaypal = true
                payPalClientId = $scope.ppc.PayPalClientId || ''
                $scope.paypal = {
                    env			: 'production'
                    sandbox		: payPalClientId
                    production	: payPalClientId
                    price		: priceStr
                    currency	:'AUD'
                    symbol		:'$'
                    id			:'example'
                    show		: true
                    description		: "Payment for #{order.Id}"
                    invoice_number  : "#{order.Id}"
                }

                paypalTxn =
                    amount:
                        total: priceStr
                        currency: 'AUD'
                    description   : "Order #{order.Id}"
                    invoice_number: "Order #{order.Id}",
                    #item_list: {items:items}

                paypalTxns = {transactions: [paypalTxn] }
                $scope.paypalTxns = paypalTxns


            if $scope.orderCanBePaidForNow and $scope.ppc.StripePublishableKey
                $scope.showStripe = true

            $scope.orderPaidForInPaypal = (data,action) ->
                delete data.returnUrl
                delete data.intent
                d = JSON.stringify data
                $http.post "#{orderbase}/WLOrderPayment", {
                    IsWLOrderPaidFor: true,
                    WLOrderPaymentDetails: d }
                .then (r) ->
                    window.toastr.success "Thank you for your payment!"
                    success1()
                return
            # end

            # region Stripe
            $scope.onToken = (token) ->
                url = "#{orderbase}/StripePayment"
                charge = {stripeToken: token.id, amount: priceOCents}
                $http.post url, charge
                .then (r) ->
                        toastr.success "Payment Successful."
                        $state.reload(true)
                    , (e) ->
                        toastr.error "Payment problem"

            $scope.onStripe = () ->
                apiKey    = $scope.ppc.StripePublishableKey
                handler = StripeCheckout.configure
                    key: apiKey
                    locale: 'auto'
                    token: $scope.onToken
                handler.open
                    panelLabel      : 'Pay '
                    amount          : priceOCents
                    currency        : 'aud'
                    name            : "Order: #{order.Id}"
                    description     : "Payment for order #{order.Id}"
                    email           : ''
                    zipCode         : false
                    allowRememberMe : false
                return
            # end

            $scope.chargeToMyAccount = ()->
                url = "#{orderbase}/WL/AccountPay"
                $http.post url
                .then (r) ->
                        toastr.success "Success..."
                        $state.reload(true)


            $scope.$on 'WLCustomerId', (evt, id) ->
                order.WLCustomerId = id
                if order.Id
                    $http.post "#{orderbase}/associate"
                return


            $scope.backToOrders = (justSubmitted) ->
                p = {}
                if justSubmitted then p =  {justSubmitted:true}

                if $scope.WLCustomerId
                    $state.go('whitelabel.orderslist', p)
                else
                    $state.go('whitelabel.job', {orderId: 0, jobId: 0})


            custContacts = "cust/Contacts"
            $scope.setOrderSubCustReipientDetails = (rd) ->
                    $scope.order.WLContact = rd
                    $scope.saveOrder()

            $scope.getSubCustomersRecipients = () ->
                lepApi2.get(custContacts + "/List", null, true).then (r) ->
                    $scope.ContactsList = r

            return
    ]


    app.controller 'OrderJobControllerWL', [
        '$scope', '$state', '$timeout', 'Utils','OrderService', '$location', '$http', '$stateParams', 'job', 'order', '$rootScope','templateTree',  'ngDialog', '$log',  'enums','lepApi2','$q', 'cfpLoadingBar' , '$sessionStorage',
        ($scope ,  $state,   $timeout,   utils,  OrderService,   $location,   $http,   $stateParams,   job, order, $rootScope , templateTree,    ngDialog,     $log,   enums,  lepApi2,  $q,   cfpLoadingBar,   $sessionStorage) ->

            
            #$scope.dcats =    $scope.ppc.DeniedTemplates
            #cd $rootScope.globals?.User?.PrintPortalSettings?.DeniedTemplates
            $scope.dcats =   $rootScope.globals?.User?.PrintPortalSettings?.DeniedTemplates

            window.isWL = true
            window.ignoreErrorAndAttachAnyways  = !$scope.ppc.IsAacEnabled

            job.IsWhiteLabel = true

            if $scope.WLCustomerId
                job.WLCustomerId = $scope.WLCustomerId

            if(!job.WLAnonymousUserId)
                job.WLAnonymousUserId = $sessionStorage.SessionId

            $scope._ = "OrderJobController"
            $scope.t = $stateParams.t
            $scope.files = []
            $scope.requiredPositions = []
            $scope.artwork = {}
            $scope.job = job
            $scope.jobVmo = angular.copy(job)

            $rootScope.title = "O#{$stateParams.orderId} > J#{$stateParams.jobId}"

            orderbase = "/api/Orders/order/#{$stateParams.orderId}"
            jobbase   = "/api/Orders/order/#{$stateParams.orderId}/job/#{$stateParams.jobId}"

            $scope.$watchGroup ['job.UploadType', 'job.Template.Id'], (nv,ov) ->
                n = job.UploadType
                if !n? then return

                # if Upload type changed, then empty the AttachmentsToProcess
                if nv[0] != ov[0] then job.AttachmentsToProcess = []

                if n is 'multiart'
                    $scope.requiredPositions = utils.getRequiredPositions($scope.job)
                else if n is 'singleart'
                    $scope.requiredPositions = ['multiart']
                else if n is 'later'
                    $scope.files = []
                    $scope.requiredPositions = []

            #if $stateParams.templateId
            #    job.Template =  _.find(window.custTemplates, {Id:  parseInt($stateParams.templateId) })

            if $stateParams.category

                $timeout(() ->
                    $rootScope.$broadcast('set-category-force', $stateParams.category)
                ,100)
                #job.Template =  _.find(window.custTemplates, {Id:  parseInt($stateParams.templateId) })


            $scope.setCategory = (c) ->
                $scope.$broadcast('set-category', c)

            if !job.IsCustomFacility
                job.Facility = null

            $scope.jobVmo = angular.copy(job)
            $scope.job = job


            $scope.$watch "job.Copies", (n,o) ->
                if n && parseInt(n,10) > 1
                    job.UploadType = "later"

            #IsCustomer = $scope.$root.globals.IsCustomer
            #orderState = if IsCustomer then "cust.order.view" else "staff.order.view"
            #jobState   = if IsCustomer then "cust.order.job"  else "staff.order.job"

            $scope.reload = () ->
                $state.reload()

            $scope.saveJobBasic = () ->
                d = $q.defer()
                isNewJobBeingSaved = $stateParams.jobId == 0
                job = angular.copy($scope.job)

                job.Copies = parseInt(job.Copies, 10) || 1
                delete job.Visibility
                delete job.Comments

                formData = new FormData
                formData.append 'request',  angular.toJson(job, true)

                #now add all of the assigned files
                ###
                i = 0
                while i < $scope.files.length
                    #add each file to the form data and iteratively name them
                    formData.append $scope.requiredPositions[i], $scope.files[i].file
                    i++
                ###
                jobId = $scope.job.Id

                $rootScope.$broadcast("percentage", 1)
                $http(
                    method          : 'POST'
                    url             : jobbase
                    headers         : {'Content-Type': undefined}
                    transformRequest: angular.identity
                    data: formData
                    #ignoreLoadingBar: true
                    uploadEventHandlers:
                        progress: (e) ->
                                if (e.lengthComputable)
                                    px = (e.loaded / e.total)
                                    cfpLoadingBar.set(px)
                                    $rootScope.$broadcast("percentage",  Math.floor(px * 100))
                                    #if e.loaded == e.total and $scope.files.length > 0
                                    #	window.toastr.info("File upload finished! Server will perform extra work now...", "please wait...", {timeout:10000})

                ).then((result, status, headers, config) ->
                    r = result.data
                    d.resolve(r)
                    #$state.go(orderState, {orderId: r.Id}, {reload:true})
                    return
                ,(data, status, headers, config) ->
                    d.reject(data, status, headers, config)
                    $rootScope.$broadcast("percentage",  -1)
                    return
                )
                d.promise

            $scope.saveJob2 = () ->
                dx = $q.defer()
                contFn = () ->
                    $scope.saveJobBasic().then (r)->
                        $state.go('whitelabel.order', { orderId: r.Id, customerId: $stateParams.customerId, t: $stateParams.t  }, { queryParamsHandling: 'preserve', reload: true, inherit: false, notify: true})
                        dx.resolve(true)
                        return

                artworkConfirm.callback = (agreed)->
                        if not agreed
                            dx.resolve(true)
                            return
                        contFn()

                if $scope.job.AttachmentsToProcess.length
                    bootbox.confirm artworkConfirm 
                else
                    contFn()
                dx.promise

            $scope.$on 'WLCustomerId', (evt,id) ->
                job.WLCustomerId = id
                job.DateModified2 = + new Date()
                if(job.Id)
                    $http.post("#{orderbase}/associate",{})

            $scope.job.newComment = {LepOnly:true}
            $scope.addComment = () ->
                url = "/api/Orders/Job/#{$stateParams.jobId}/comments"
                $http.post(url, $scope.job.newComment).then ()->
                    $scope.job.newComment =  {LepOnly:true}
                    $http.get(url).then (r) ->
                        $scope.job.Comments = r.data


            $scope.jobCommand = (command, needsSaveFirst, extra) ->
                body = JSON.stringify(extra || '')
                if $scope.job.newComment.CommentText
                    body = JSON.stringify($scope.job.newComment.CommentText)

                JSON.stringify($scope.job.newComment.CommentText)

                commandFn = () ->
                    url = "#{jobbase}/#{command}"
                    prompt = jobCommandPrompts[command]
                    if prompt then prompt = sure + prompt
                    bootbox.confirm prompt, (rr)->
                        if !rr then return
                        $http.post url, body
                        .then (d) ->
                                window.toastr.success(d.data)
                                if command == 'Delete'
                                    $state.go($state.$current.parent.name + '.view', {}, {reload:true})
                                else
                                    $state.reload()
                            , (d) ->
                                window.toastr.error(d.data)

                if needsSaveFirst
                    $scope.saveJobBasic().then(()-> commandFn())
                else
                    commandFn()
            ###





            $scope.copyJobToNewOrder = (jobId) ->
                command = "Copy"
                url = "#{orderbase}/job/#{jobId}/Copy"
                prompt =  jobCommandPrompts[command]
                if prompt then prompt = sure + prompt
                bootbox.confirm prompt, (rr) ->
                    if !rr then return
                    $http.post(url, {}).then((r) ->
                            orderId = parseInt(r.headers('OrderId'), 10)
                            jobId = parseInt(r.headers('jobId'), 10)
                            window.toastr.remove()
                            $state.go('cust.order.job', {orderId: orderId, jobId: jobId}, {reload:true})
                            window.toastr.info(r.data)
                            return
                        fail1
                    )
                return

            $scope.copyJobNTimes = (jobId) ->
                dialog =  ngDialog.open({template: 'common/jobcopydialog.html'})
                dialog.closePromise.then((r) ->
                    n = r.value
                    if not n then return
                    if isNaN(n) then return
                    n = parseInt(n,10)
                    if n >= 100 then n = 99
                    url = "#{orderbase}/job/#{jobId}/ncopy/#{n}"
                    $http.post(url, {}).then(success1, fail1)
                )
                return
            ###


            #toastr.remove()
            if job.Visibility?.QuoteInst        then toastr.warning "Supply quote."
            if job.Visibility?.RdyArtworkInst   then toastr.warning "Upload ready artwork."
            if job.Visibility?.inPrintInst      then toastr.warning "Job sheet has already been printed."
            if job.Visibility?.priceExpireInst  then toastr.error   "Our prices are valid for up to 30 days. We have updated your job price to reflect our current pricing."
            if job.Visibility?.quoteExpireInst  then toastr.error   "Our quotes are valid for up to 60 days. Please contact us to confirm if this quote is still valid."
            #if job.Visibility.NoActionInst then toastr.success " No action required."

            $scope.rrLabel = $stateParams.ncrCmd
            $scope.showSlotEntryInput = false
            jto   = enums.KeyVal.JobTypeOptions
            jco   = enums.KeyVal.JobCelloglazeOptions

            if $stateParams.jobId is 0
                #if $stateParams.templateId
                #    job.Template =  _.find(window.custTemplates, {Id:  parseInt($stateParams.templateId) })

                if $stateParams.category
                    $timeout(() ->
                        $rootScope.$broadcast('set-category-force', $stateParams.category)
                    ,100)
                    #job.Template =  _.find(window.custTemplates, {Id:  parseInt($stateParams.templateId) })

            return
    ]

    s = {}
    s.YOU_HAVE_STARTED    = "You've Started"
    s.SUBMITTED_IN_PORTAL = "Submitted in Portal"
    s.WE_ARE_PRINTING     = "We're Printing"
    s.RECENT              = "Recent"
    s.HISTORY             = "History"


    app.controller 'SubCustOrdersListController', [
        '$scope', 'OrderService', 'JobService', '$sessionStorage', '$location', '$state','$timeout', 'templateTree',   '$stateParams','imgLookup',
        ($scope,   OrderService,   JobService,  $sessionStorage, $location, $state, $timeout, templateTree , $stateParams, imgLookup) ->

            $scope.WLCustomerId = $sessionStorage.WLCustomerId || null
            $scope.imgLookup = imgLookup
            $scope.list = []
            $scope._ = 'SubCustOrdersListController'

            $scope.templateTree = templateTree
            lastTab = $sessionStorage.lastTab || s.WE_ARE_PRINTING

            $scope.bOrderStatusUsed = true
            $scope.tabs =
                list: [
                    { title: s.YOU_HAVE_STARTED }
                    { title: s.WE_ARE_PRINTING }
                    { title: s.RECENT }
                    { title: s.HISTORY }
                ]
                current:  lastTab

            $scope.current =  lastTab

            $scope.$watch "vm", (n,o) ->
                $scope.search()
            , true

            $sessionStorage.search = $sessionStorage.search || {}
            $scope.vm = angular.copy($sessionStorage.search[$state.current.name]) || {Page: 1}
            vm = $scope.vm

            vm.JobTypes =  null
            vm.WLCustomerId = $scope.WLCustomerId

            $scope.cTab = vm.Tab ||   s.WE_ARE_PRINTING
            if $stateParams.justSubmitted then $scope.cTab =  s.WE_ARE_PRINTING


            $scope.$watch "tabs.current", (n,o) ->
                vm.Tab = n
                vm.Page = 1
                delete vm.IsWLOrderPaidFor
                if n !=  s.YOU_HAVE_STARTED
                    delete vm.IsRejected
                    delete vm.IsWaitingApproval
                    delete vm.IsQuoteRequired
                statuses = []
                if n is s.YOU_HAVE_STARTED    then statuses = ['Open']
                if n is s.WE_ARE_PRINTING     then statuses = ['Open','Submitted', 'Ready',  'Finished' ]
                if n is s.RECENT              then statuses = ['Dispatched']
                if n is s.HISTORY             then statuses = ['Archived']
                vm.OrderStatuses = statuses

                if n is s.WE_ARE_PRINTING then vm.IsWLOrderPaidFor = true
                if n is s.YOU_HAVE_STARTED then vm.IsWLOrderPaidFor = false

                $sessionStorage.lastTab = n
            , true

            $scope.$root.imgLookup = {}
            JobService.getTemplates().then (d) ->
                $scope.templates = d
                lookup = {}
                for ti in d
                    lookup[ti.Id] = ti.img
                $scope.$root.imgLookup = lookup

            $scope.search = () ->
                $scope.msg = "searching..."
                $sessionStorage.search =  $sessionStorage.search  || {}
                $sessionStorage.search[$state.current.name] =  angular.copy(vm)
                OrderService.getOrders(vm).then (d) ->
                    $scope.list = d.list
                    $scope.bOrderStatusUsed = d.bOrderStatusUsed
                    $scope.msg = ""
                    if  d.list.List.length > 0  then $scope.msg  = "#{ d.list.List.length} orders<br/>"
                    if  d.list.List.length == 0
                        $scope.msg  = "no orders found."

            $scope.goPage = (p) ->
                vm.Page = p

            $scope.clear = () ->
                vm.Page = 1
                delete vm.OrderNr
                delete vm.JobTypes
                delete vm.IsRejected
                delete vm.IsWaitingApproval
                delete vm.IsQuoteRequired
                $scope.bOrderStatusUsed = true
                $sessionStorage.search[$state.current.name] =  angular.copy(vm)
                $scope.search()

            $scope.$on "open-job",     (evt,data) ->
                $state.go('whitelabel.job', data)

            $scope.$on "open-order",   (evt,data) ->
                $state.go('whitelabel.order', data)
    ]

    artworkConfirmmessage =
            """
            <ol><b style="color: red; font-weight: 800;"> I have </b>
            <li>Checked my Artwork files to make sure they are Press Ready</li>
            <li>Understood any Artwork warnings and fixes and accept responsibility for any fixes made or not made</li>
            </ol>
            """
    artworkConfirm =
        centerVertical: true
        closeButton: false
        message:   artworkConfirmmessage
        buttons:
            confirm:
                label: 'I confirm the above'
                className: 'btn-success pull-right'
            cancel:
                label: 'Cancel'
                className: 'btn-warning pull-left'
