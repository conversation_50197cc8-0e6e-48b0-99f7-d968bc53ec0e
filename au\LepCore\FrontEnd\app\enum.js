(function (angular, window) {
    'use strict';

    var app = angular.module('app');
    var enums = {
      "ValueKey": {
        "Align": {
          "0": "left",
          "1": "center",
          "2": "right"
        },
        "ArtworkStatusOption": {
          "0": "None",
          "1": "FTP",
          "2": "CDROM",
          "3": "LATER"
        },
        "ArtworkTypeOptions": {
          "0": "TIFF",
          "1": "JPG",
          "2": "PDF",
          "3": "EPS",
          "4": "Corel",
          "5": "Misc",
          "6": "Quark",
          "7": "InDesign",
          "8": "PNG"
        },
        "ColourSides": {
          "0": "None",
          "1": "OneSide",
          "2": "BothSides"
        },
        "ContentType": {
          "3": "FooterNotification",
          "4": "BodyNotification",
          "5": "JobQuoteRequestSubmit",
          "6": "JobQuoteRequestAttention",
          "7": "OrderSubmit",
          "8": "JobReject",
          "9": "JobArtWorkApproval",
          "10": "OrderPrePayment",
          "11": "JobGonePlate",
          "12": "JobGoneFinish",
          "13": "JobAwaitingCourier",
          "14": "JobPaymentAwaitingCourier",
          "15": "CustomerOnHold",
          "16": "CustomerDetailChange",
          "18": "OnlinePaymentSuccessful",
          "19": "OnlinePaymentFail",
          "21": "Reprint",
          "22": "Restart",
          "24": "PasswordReset",
          "25": "CourierDispatch",
          "28": "WebOrderRaised",
          "29": "WebOrderRaisedWithPaypalPayment",
          "30": "WebOrderRaisedWithStripePayment",
          "31": "WebOrderRaisedWithAccountPayment",
          "32": "PayMeOrder",
          "33": "OrderReadyForPickup",
          "34": "ReprintOrder",
          "35": "UnableToMeetPrice",
          "36": "RejectionOfVariationRequest"
        },
        "CutOptions": {
          "0": "None",
          "1": "SingleGusset",
          "41": "SingleGussetNoBCSlits",
          "2": "DoubleGusset",
          "42": "DoubleGussetNoBCSlits",
          "3": "Custom",
          "4": "StyleA",
          "5": "StyleB",
          "6": "StyleC",
          "7": "StyleD",
          "8": "StyleE",
          "9": "StyleF",
          "10": "StyleG",
          "11": "StyleH",
          "12": "StyleI",
          "13": "StyleJ",
          "14": "StyleK",
          "15": "StyleL",
          "16": "StyleM",
          "17": "StyleN",
          "18": "StyleO",
          "19": "StyleP",
          "20": "StyleQ",
          "21": "StyleR",
          "22": "StyleS",
          "23": "StyleT",
          "24": "StyleU",
          "25": "StyleV",
          "26": "StyleW",
          "31": "LEPStandard"
        },
        "Envelope": {
          "0": "White",
          "1": "Red",
          "2": "Green"
        },
        "EnvelopeType": {
          "0": "WindowPeelNSeal",
          "1": "PlainFacePeelNSeal",
          "2": "WindowSelfSeal",
          "3": "PlainFaceSelfSeal"
        },
        "Facility": {
          "0": "FG",
          "1": "PM"
        },
        "HoleDrilling": {
          "0": "None",
          "1": "Three",
          "2": "Five",
          "3": "Six",
          "4": "Seven",
          "5": "Eight"
        },
        "JobApprovalOptions": {
          "0": "NotNeeded",
          "1": "NeedsApproval",
          "3": "Rejected"
        },
        "JobBoardTypes": {
          "-1": "None",
          "0": "All",
          "1": "Exceptions",
          "2": "PreFlight",
          "3": "PrePress",
          "4": "Plateroom",
          "5": "PressRoom",
          "6": "Celloglaze",
          "7": "Guillotine",
          "8": "Folding",
          "9": "LetterPress",
          "10": "Stitching",
          "11": "Finishing",
          "12": "Despatch",
          "13": "DPCProduction",
          "14": "WideFormatProduction",
          "15": "Outwork",
          "16": "PayMe",
          "17": "OnHold"
        },
        "JobBoundEdgeOptions": {
          "0": "None",
          "1": "SideEdge",
          "2": "TopEdge"
        },
        "JobCelloglazeOptions": {
          "0": "None",
          "1": "Gloss",
          "2": "Matt",
          "3": "Velvet",
          "4": "Emboss",
          "5": "Foil",
          "6": "SpotUV",
          "7": "EmbossFoil",
          "8": "EmbossedGlossFront",
          "9": "EmbossedMattFront",
          "10": "SpotUVFrontMattFront",
          "11": "MattAntiScuff"
        },
        "JobPrintOptions": {
          "0": "Unprinted",
          "1": "Printed",
          "2": "BW",
          "3": "Black",
          "4": "ReflexBlue"
        },
        "JobProofStatus": {
          "0": "None",
          "1": "OnHold"
        },
        "JobStatusOptions": {
          "0": "Open",
          "1": "Submitted",
          "2": "PreflightDone",
          "3": "DPCPreProduction",
          "4": "WideFormatProduction",
          "5": "InRun",
          "6": "Filling",
          "7": "LayoutRequired",
          "8": "LayoutDone",
          "9": "ApprovedForPlating",
          "10": "PlatingDone",
          "11": "PressDone",
          "12": "Celloglazed",
          "13": "Cut",
          "14": "Scored",
          "15": "Perforated",
          "16": "Letterpressed",
          "17": "Folded",
          "18": "Stitched",
          "19": "Drilled",
          "20": "Rounded",
          "21": "Finished",
          "22": "DPCPrinted",
          "23": "DPCComplete",
          "24": "WideFormatComplete",
          "25": "ShrinkWrapped",
          "26": "Outwork",
          "27": "PayMe",
          "28": "Packed",
          "29": "Dispatched",
          "30": "Complete",
          "31": "UnableToMeetPrice",
          "32": "RejectedVariation"
        },
        "JobTypeOptions": {
          "1": "BusinessCard",
          "2": "DoubleBusinessCard",
          "3": "DL",
          "4": "DLSpecial",
          "5": "Letterhead",
          "6": "Compliments",
          "7": "Brochure",
          "8": "Magazine",
          "9": "PresentationFolder",
          "10": "Poster",
          "11": "Postcard",
          "12": "Custom",
          "13": "BrochureSpecial",
          "14": "MagazineSeparate",
          "15": "GolfScoreCards",
          "16": "Notepads",
          "18": "BrochureNDD",
          "19": "LetterheadNDD",
          "20": "ComplimentsNDD",
          "21": "PresentationFolderNDD",
          "22": "MagazineNDD",
          "23": "BrochureSDD",
          "24": "LetterheadSDD",
          "25": "ComplimentsSDD",
          "26": "BusinessCardNdd",
          "27": "BusinessCardSdd",
          "28": "Stationery",
          "29": "StationerySDD",
          "30": "MagazineCover",
          "32": "BacklitPosters",
          "33": "MeshBanner",
          "34": "VinylOutdoor",
          "35": "PosterMattArt",
          "36": "PosterCanvas",
          "37": "PullUpBannerStandardStand",
          "38": "PullUpBannerPremiumStand",
          "41": "VinylSticker",
          "42": "RemovableWallDecals",
          "43": "VinylStickerOutdoor",
          "44": "RigidSigns",
          "51": "DuplicateNCRBooks",
          "52": "TriplicateNCRBooks",
          "53": "QuadruplicateNCRBooks",
          "60": "EnvelopeBlack",
          "61": "Envelope1Pms",
          "62": "Envelope2Pms",
          "63": "EnvelopeCmyk",
          "71": "A4CalendarSelfCover",
          "72": "A4CalendarSeparateCover",
          "73": "TentCalendars",
          "74": "DLCalendars",
          "75": "GreetingCards",
          "76": "WiroMagazines",
          "6020": "FridgeMagnet"
        },
        "LabelType": {
          "0": "Undetermined",
          "1": "BusinessCardReferenceLayout",
          "2": "FurtherProcessing",
          "3": "Carton",
          "4": "Freight",
          "5": "Filing",
          "6": "FilingPayMe",
          "7": "FreightMe",
          "8": "ImmediateFreightMe",
          "9": "FreightPickList",
          "10": "Pickup",
          "11": "DPCProcessing",
          "12": "WideFormatProcessing",
          "13": "FurtherProcessingList"
        },
        "LayoutType": {
          "0": "FrontLayout",
          "1": "BackLayout",
          "3": "OutsideFront",
          "4": "OutsideBack",
          "5": "InsideFront",
          "6": "InsideBack",
          "7": "Page"
        },
        "NotificationType": {
          "0": "All",
          "1": "None",
          "2": "Brief"
        },
        "OnlineTxSorts": {
          "0": "AscPaymentDate",
          "1": "DescPaymentDate",
          "2": "AcsCustomer",
          "3": "DescCustomer"
        },
        "OrderPaymentStatusOptions": {
          "0": "NotNeeded",
          "1": "AwaitingPayment",
          "2": "Paid"
        },
        "OrderStatusOptions": {
          "0": "Open",
          "1": "Submitted",
          "2": "Ready",
          "3": "Finished",
          "4": "Dispatched",
          "5": "Archived"
        },
        "PackingInstruction": {
          "0": "Standard",
          "1": "Banding",
          "2": "ShrinkWrapping",
          "3": "BulkPackCarton",
          "4": "BulkPackPallet"
        },
        "PadDirection": {
          "0": "None",
          "1": "Head",
          "2": "Foot",
          "3": "Left",
          "4": "Right"
        },
        "PaymentTermsOptions": {
          "0": "Account",
          "1": "COD",
          "2": "PrePay",
          "3": "OnHold"
        },
        "PricingModel": {
          "0": "Unspecified",
          "1": "CategoryMarkup",
          "2": "PriceMarkup"
        },
        "PrintType": {
          "0": "B",
          "1": "O",
          "2": "D",
          "3": "W",
          "4": "N"
        },
        "ProductionTiming": {
          "0": "Standard",
          "1": "TurnaroundGurantee",
          "2": "PrioritySevice"
        },
        "PromotionLifeSpan": {
          "0": "Absolute",
          "1": "Windowed"
        },
        "PromotionSalesCategory": {
          "0": "None",
          "1": "Lead",
          "2": "Prospect",
          "4": "Lapsed",
          "8": "Customer"
        },
        "QuoteStatus": {
          "0": "Requested",
          "1": "Pending",
          "2": "WonPendingSubmitted",
          "3": "Won",
          "4": "Lost",
          "5": "Rejected"
        },
        "RectangleCorners": {
          "0": "None",
          "1": "TopLeft",
          "2": "TopRight",
          "4": "BottomLeft",
          "8": "BottomRight",
          "15": "All"
        },
        "Role": {
          "0": "Prepress",
          "1": "PrepressFinishing",
          "2": "Accounts",
          "3": "CustomerService",
          "4": "Administrator",
          "5": "MarketingSpecialist",
          "6": "System",
          "7": "Packing",
          "8": "Dispatch",
          "9": "Invoicing",
          "10": "Scanner",
          "11": "SuperAdministrator"
        },
        "RotationOption": {
          "0": "Portrait",
          "1": "Landscape"
        },
        "RoundDetailOption": {
          "0": "None",
          "1": "Standard6mm1",
          "2": "Standard6mm2",
          "3": "Standard6mm3",
          "4": "Standard6mm4",
          "5": "Standard10mm1",
          "6": "Standard10mm2",
          "7": "Standard10mm3",
          "8": "Standard10mm4",
          "9": "DieCut3mm4",
          "10": "DieCut3mmTopLBottomR",
          "11": "DieCut6mm4",
          "12": "DieCut6mmTopLBottomR",
          "13": "DieCut24mmBottomR",
          "14": "Standard35mm1",
          "15": "Standard35mm2",
          "16": "Standard35mm3",
          "17": "Standard35mm4"
        },
        "RoundOption": {
          "0": "None",
          "1": "Standard",
          "2": "DieCut",
          "3": "Custom",
          "4": "Round",
          "5": "RoundMagnet"
        },
        "RunCelloglazeOptions": {
          "0": "None",
          "1": "MattFront",
          "2": "MattBoth",
          "3": "GlossFront",
          "4": "GlossBoth",
          "5": "VelvetFront",
          "6": "VelvetBoth",
          "7": "EmbossGlossFront",
          "8": "EmbossMattFront",
          "9": "FoilFrontMattBoth",
          "10": "SpotUVFrontMattBoth",
          "11": "SpotUVFrontMattFront",
          "12": "GlossFrontMattBack",
          "13": "MattAntiScuffFront",
          "14": "MattAntiScuffBoth"
        },
        "RunSearchOptions": {
          "0": "None",
          "1": "NonBC",
          "2": "BC",
          "3": "BC310",
          "4": "BC420",
          "5": "BC360",
          "6": "BCLoyalty",
          "7": "BC350Recycled"
        },
        "RunStatusOptions": {
          "0": "Filling",
          "1": "LayoutRequired",
          "2": "LayoutDone",
          "3": "ApprovedForPlating",
          "4": "DPCPreProduction",
          "5": "PlatingDone",
          "6": "PressDone",
          "7": "Finishing",
          "8": "FinishingDone",
          "9": "PackingDone",
          "10": "Outwork"
        },
        "ShellChangeNotificationEvents": {
          "8192": "SHCNE_UPDATEITEM"
        },
        "ShellChangeNotificationFlags": {
          "4096": "SHCNF_FLUSH"
        },
        "SiteLocation": {
          "0": "AU",
          "1": "NZ"
        }
      },
      "ValueDesc": {
        "Align": {
          "0": "left",
          "1": "center",
          "2": "right"
        },
        "ArtworkStatusOption": {
          "0": "None",
          "1": "FTP",
          "2": "CDROM",
          "3": "LATER"
        },
        "ArtworkTypeOptions": {
          "0": "TIFF",
          "1": "JPG",
          "2": "PDF",
          "3": "EPS",
          "4": "Corel",
          "5": "Misc",
          "6": "Quark",
          "7": "InDesign",
          "8": "PNG"
        },
        "ColourSides": {
          "0": "None",
          "1": "One Side",
          "2": "Both sides"
        },
        "ContentType": {
          "3": "FooterNotification",
          "4": "BodyNotification",
          "5": "JobQuoteRequestSubmit",
          "6": "JobQuoteRequestAttention",
          "7": "OrderSubmit",
          "8": "JobReject",
          "9": "JobArtWorkApproval",
          "10": "OrderPrePayment",
          "11": "JobGonePlate",
          "12": "JobGoneFinish",
          "13": "JobAwaitingCourier",
          "14": "JobPaymentAwaitingCourier",
          "15": "CustomerOnHold",
          "16": "CustomerDetailChange",
          "18": "OnlinePaymentSuccessful",
          "19": "OnlinePaymentFail",
          "21": "Reprint",
          "22": "Restart",
          "24": "PasswordReset",
          "25": "CourierDispatch",
          "28": "WebOrderRaised",
          "29": "WebOrderRaisedWithPaypalPayment",
          "30": "WebOrderRaisedWithStripePayment",
          "31": "WebOrderRaisedWithAccountPayment",
          "32": "PayMeOrder",
          "33": "OrderReadyForPickup",
          "34": "ReprintOrder",
          "35": "UnableToMeetPrice",
          "36": "RejectionOfVariationRequest"
        },
        "CutOptions": {
          "0": "None",
          "1": "Single gusset",
          "41": "Single gusset (No Business Card slits)",
          "2": "Double gusset",
          "42": "Double gusset (No Business Card slits)",
          "3": "Custom",
          "4": "Style A - A5",
          "5": "Style B - A5",
          "6": "Style C - A4",
          "7": "Style D - A4",
          "8": "Style E - A4",
          "9": "Style F - A4",
          "10": "Style G - A4",
          "11": "Style H - A4",
          "12": "Style I - A4",
          "13": "Style J - A4",
          "14": "Style K - A4",
          "15": "Style L - A4",
          "16": "Style M - A4",
          "17": "Style N - A4",
          "18": "Style O - A4",
          "19": "Style P - A4",
          "20": "Style Q - A4",
          "21": "Style R - A4",
          "22": "Style S - A4",
          "23": "Style T - A4",
          "24": "Style U - A4",
          "25": "Style V - A4",
          "26": "Style W - A4",
          "31": "LEP Standard"
        },
        "Envelope": {
          "0": "White",
          "1": "Red",
          "2": "Green"
        },
        "EnvelopeType": {
          "0": "Window/Peel & Seal",
          "1": "Plain Face/Peel & Seal",
          "2": "Window/Self Seal",
          "3": "Plain Face/Self Seal"
        },
        "Facility": {
          "0": "Forest Glen",
          "1": "Melbourne"
        },
        "HoleDrilling": {
          "0": "None",
          "1": "3 mm",
          "2": "5 mm",
          "3": "6 mm",
          "4": "7 mm",
          "5": "8 mm"
        },
        "JobApprovalOptions": {
          "0": "NotNeeded",
          "1": "NeedsApproval",
          "3": "Rejected"
        },
        "JobBoardTypes": {
          "-1": "None",
          "0": "All",
          "1": "Exceptions",
          "2": "PreFlight",
          "3": "PrePress",
          "4": "Plateroom",
          "5": "PressRoom",
          "6": "Celloglaze",
          "7": "Guillotine",
          "8": "Folding",
          "9": "LetterPress",
          "10": "Stitching",
          "11": "Finishing",
          "12": "Despatch",
          "13": "DPCProduction",
          "14": "WideFormatProduction",
          "15": "Outwork",
          "16": "PayMe",
          "17": "OnHold"
        },
        "JobBoundEdgeOptions": {
          "0": "None",
          "1": "Side Edge",
          "2": "Top Edge"
        },
        "JobCelloglazeOptions": {
          "0": "None",
          "1": "Gloss",
          "2": "Matt",
          "3": "Velvet",
          "4": "Emboss",
          "5": "Foil",
          "6": "SpotUV",
          "7": "EmbossFoil",
          "8": "EmbossedGlossFront",
          "9": "EmbossedMattFront",
          "10": "SpotUVFrontMattFront",
          "11": "MattAntiScuff"
        },
        "JobPrintOptions": {
          "0": "Unprinted",
          "1": "Printed",
          "2": "Black & White",
          "3": "Black",
          "4": "Reflex Blue"
        },
        "JobProofStatus": {
          "0": "None",
          "1": "OnHold"
        },
        "JobStatusOptions": {
          "0": "Not Submitted",
          "1": "Submitted",
          "2": "Preflight Done",
          "3": "DPC Pre Production",
          "4": "In Wide Format Production",
          "5": "In Run ",
          "6": "Filling",
          "7": "Layout In Progress",
          "8": "Layout Done",
          "9": "Approved For Plating",
          "10": "Plating Done",
          "11": "Press Done",
          "12": "Celloglazed",
          "13": "Cut",
          "14": "Scored",
          "15": "Perforated",
          "16": "Letterpressed",
          "17": "Folded",
          "18": "Stitched",
          "19": "Drilled",
          "20": "Rounded",
          "21": "Finished",
          "22": "DPC Printed",
          "23": "DPC Complete",
          "24": "Wide Format Complete",
          "25": "Shrink Wrapped",
          "26": "Outwork",
          "27": "PayMe",
          "28": "Packed",
          "29": "Dispatched",
          "30": "Complete",
          "31": "Unable to meet price",
          "32": "Rejected Variation"
        },
        "JobTypeOptions": {
          "1": "Business Card",
          "2": "Double Business Card",
          "3": "DL",
          "4": "DL Special",
          "5": "Letterhead",
          "6": "With Comps Slip",
          "7": "Brochures & Flyers",
          "8": "Magazine/Booklet",
          "9": "Presentation Folder",
          "10": "Poster",
          "11": "Postcard",
          "12": "Custom",
          "13": "Brochure Special",
          "14": "Magazine/Booklet Separate Cover",
          "15": "Golf Score Cards",
          "16": "Notepads",
          "18": "Brochure (next day dispatch)",
          "19": "Letterhead (next day dispatch)",
          "20": "With Comps Slips (next day dispatch)",
          "21": "Presentation Folder (next day dispatch)",
          "22": "Magazine/Booklet (next day dispatch)",
          "23": "Brochure (same day dispatch)",
          "24": "Letterhead (same day dispatch)",
          "25": "With Comps Slips (same day dispatch)",
          "26": "Business Card (next day dispatch)",
          "27": "Business Card (same day dispatch)",
          "28": "Stationery",
          "29": "Stationery (same day dispatch)",
          "30": "Magazine Cover",
          "32": "Backlit Posters",
          "33": "Mesh Banner with eyelets",
          "34": "Vinyl Outdoor",
          "35": "Poster Matt Art",
          "36": "Poster Canvas",
          "37": "Pull Up Banner (Standard Stand)",
          "38": "Pull Up Banner (Premium Stand)",
          "41": "Vinyl Sticker",
          "42": "Removable Wall Decals",
          "43": "Vinyl Sticker Outdoor",
          "44": "Rigid Signs",
          "51": "Duplicate NCR Books",
          "52": "Triplicate NCR Books ",
          "53": "Quadruplicate NCR Books",
          "60": "Envelope Black",
          "61": "Envelope 1PMS",
          "62": "Envelope 2PMS",
          "63": "Envelope CMYK",
          "71": "A4 Calendar Self Cover",
          "72": "A4 Calendar Separate Cover",
          "73": "Tent Calendars",
          "74": "DL Calendars",
          "75": "Greeting Cards",
          "76": "Wiro Magazines",
          "6020": "Fridge Magnet"
        },
        "LabelType": {
          "0": "Undetermined",
          "1": "Business Card Reference Layout",
          "2": "",
          "3": "Carton Label",
          "4": "Freight Label",
          "5": "Filing Pegionhole Label",
          "6": "Filing Pegionhole Payme Label",
          "7": "Ready to Freight now",
          "8": "Immediate Freight Slip",
          "9": "Freight Label Pick List",
          "10": "Pick up only, no freight needed",
          "11": "DPC Processing Label",
          "12": "Wide Format Processing Label",
          "13": "FurtherProcessing Labels in strict print order"
        },
        "LayoutType": {
          "0": "FrontLayout",
          "1": "BackLayout",
          "3": "OutsideFront",
          "4": "OutsideBack",
          "5": "InsideFront",
          "6": "InsideBack",
          "7": "Page"
        },
        "NotificationType": {
          "0": "All",
          "1": "None",
          "2": "Submitted & completed only"
        },
        "OnlineTxSorts": {
          "0": "AscPaymentDate",
          "1": "DescPaymentDate",
          "2": "AcsCustomer",
          "3": "DescCustomer"
        },
        "OrderPaymentStatusOptions": {
          "0": "NotNeeded",
          "1": "AwaitingPayment",
          "2": "Paid"
        },
        "OrderStatusOptions": {
          "0": "Not Submitted",
          "1": "New Order",
          "2": "Ready",
          "3": "Finished",
          "4": "Dispatched",
          "5": "Archived"
        },
        "PackingInstruction": {
          "0": "Standard",
          "1": "Banding",
          "2": "Shrink Wrapping",
          "3": "Bulk Pack Carton",
          "4": "Bulk Pack Pallet"
        },
        "PadDirection": {
          "0": "None",
          "1": "Pad at Head",
          "2": "Pad at Foot",
          "3": "Pad at Left",
          "4": "Pad at Right"
        },
        "PaymentTermsOptions": {
          "0": "On account",
          "1": "Pay before dispatch",
          "2": "Pay before pre-press",
          "3": "On hold"
        },
        "PricingModel": {
          "0": "Unspecified/Customer default",
          "1": "Category based Markup %",
          "2": "Price Range based Markup %"
        },
        "PrintType": {
          "0": "Both",
          "1": "Offset",
          "2": "Digital",
          "3": "Wide Format",
          "4": "N/A"
        },
        "ProductionTiming": {
          "0": "Standard",
          "1": "TurnaroundGurantee",
          "2": "PrioritySevice"
        },
        "PromotionLifeSpan": {
          "0": "Promotions is valid from a specific date range",
          "1": "Promotion is valid in a window after the customer was assigned in a campaign"
        },
        "PromotionSalesCategory": {
          "0": "",
          "1": "Lead",
          "2": "Prospect",
          "4": "Lapsed",
          "8": "Customer"
        },
        "QuoteStatus": {
          "0": "Requested",
          "1": "Pending",
          "2": "WonPendingSubmitted",
          "3": "Won",
          "4": "Lost",
          "5": "Rejected"
        },
        "RectangleCorners": {
          "0": "None",
          "1": "TopLeft",
          "2": "TopRight",
          "4": "BottomLeft",
          "8": "BottomRight",
          "15": "All"
        },
        "Role": {
          "0": "Prepress",
          "1": "Press and Finshing",
          "2": "Accounts",
          "3": "Customer Service",
          "4": "Administrator",
          "5": "Marketing Specialist",
          "6": "System",
          "7": "Packing",
          "8": "Dispatch",
          "9": "Invoicing",
          "10": "Scanner",
          "11": "Super Administrator"
        },
        "RotationOption": {
          "0": "Portrait",
          "1": "Landscape"
        },
        "RoundDetailOption": {
          "0": "None",
          "1": "6mm x 1 corner",
          "2": "6mm x 2 corners",
          "3": "6mm x 3 corners",
          "4": "6mm x 4 corners",
          "5": "10mm x 1 corners",
          "6": "10mm x 2 corners",
          "7": "10mm x 3 corners",
          "8": "10mm x 4 corners",
          "9": "3mm x 4 corners",
          "10": "3mm x Top L Bottom R",
          "11": "6mm x 4 corners",
          "12": "6mm x Top L Bottom R",
          "13": "24mm Bottom R",
          "14": "3.5mm x 1 corner",
          "15": "3.5mm x 2 corners",
          "16": "3.5mm x 3 corners",
          "17": "3.5mm x 4 corners"
        },
        "RoundOption": {
          "0": "None",
          "1": "RC Standard",
          "2": "RC Die Cut (86 x 50mm)",
          "3": "RC Customised Die Cut",
          "4": "Round Corners 3.5/6 mm",
          "5": "6mm x 4 corners"
        },
        "RunCelloglazeOptions": {
          "0": "None",
          "1": "M-f",
          "2": "M-f/b",
          "3": "G-f",
          "4": "G-f/b",
          "5": "V-f",
          "6": "V-f/b",
          "7": "EMBOSS G-f",
          "8": "EMBOSS M-f",
          "9": "FOIL M-f/b",
          "10": "SpotUV M-f/b",
          "11": "SpotUV M-f",
          "12": "G-f/M-b",
          "13": "M-AntiScuff-f",
          "14": "M-AntiScuff-f/b"
        },
        "RunSearchOptions": {
          "0": "None",
          "1": "Non Business Card Runs",
          "2": "Business Card Runs",
          "3": "310gsm Business Card Runs",
          "4": "420gsm Business Card Runs",
          "5": "360gsm Business Card Runs",
          "6": "LoyaltyCard Business Card Runs",
          "7": "350gsm Recycled  Business Card Runs"
        },
        "RunStatusOptions": {
          "0": "Filling",
          "1": "Layout required",
          "2": "Layout done",
          "3": "Approved for plating",
          "4": "DPC Pre Production",
          "5": "Plating done",
          "6": "Press done",
          "7": "Finishing",
          "8": "Finishing done",
          "9": "Packing done",
          "10": "Outwork"
        },
        "ShellChangeNotificationEvents": {
          "8192": "SHCNE_UPDATEITEM"
        },
        "ShellChangeNotificationFlags": {
          "4096": "SHCNF_FLUSH"
        },
        "SiteLocation": {
          "0": "Australia",
          "1": "New Zealand"
        }
      },
      "KeyVal": {
        "Align": {
          "left": 0,
          "center": 1,
          "right": 2
        },
        "ArtworkStatusOption": {
          "None": 0,
          "FTP": 1,
          "CDROM": 2,
          "LATER": 3
        },
        "ArtworkTypeOptions": {
          "TIFF": 0,
          "JPG": 1,
          "PDF": 2,
          "EPS": 3,
          "Corel": 4,
          "Misc": 5,
          "Quark": 6,
          "InDesign": 7,
          "PNG": 8
        },
        "ColourSides": {
          "None": 0,
          "OneSide": 1,
          "BothSides": 2
        },
        "ContentType": {
          "FooterNotification": 3,
          "BodyNotification": 4,
          "JobQuoteRequestSubmit": 5,
          "JobQuoteRequestAttention": 6,
          "OrderSubmit": 7,
          "JobReject": 8,
          "JobArtWorkApproval": 9,
          "OrderPrePayment": 10,
          "JobGonePlate": 11,
          "JobGoneFinish": 12,
          "JobAwaitingCourier": 13,
          "JobPaymentAwaitingCourier": 14,
          "CustomerOnHold": 15,
          "CustomerDetailChange": 16,
          "OnlinePaymentSuccessful": 18,
          "OnlinePaymentFail": 19,
          "Reprint": 21,
          "Restart": 22,
          "PasswordReset": 24,
          "CourierDispatch": 25,
          "WebOrderRaised": 28,
          "WebOrderRaisedWithPaypalPayment": 29,
          "WebOrderRaisedWithStripePayment": 30,
          "WebOrderRaisedWithAccountPayment": 31,
          "PayMeOrder": 32,
          "OrderReadyForPickup": 33,
          "ReprintOrder": 34,
          "UnableToMeetPrice": 35,
          "RejectionOfVariationRequest": 36
        },
        "CutOptions": {
          "None": 0,
          "SingleGusset": 1,
          "SingleGussetNoBCSlits": 41,
          "DoubleGusset": 2,
          "DoubleGussetNoBCSlits": 42,
          "Custom": 3,
          "StyleA": 4,
          "StyleB": 5,
          "StyleC": 6,
          "StyleD": 7,
          "StyleE": 8,
          "StyleF": 9,
          "StyleG": 10,
          "StyleH": 11,
          "StyleI": 12,
          "StyleJ": 13,
          "StyleK": 14,
          "StyleL": 15,
          "StyleM": 16,
          "StyleN": 17,
          "StyleO": 18,
          "StyleP": 19,
          "StyleQ": 20,
          "StyleR": 21,
          "StyleS": 22,
          "StyleT": 23,
          "StyleU": 24,
          "StyleV": 25,
          "StyleW": 26,
          "LEPStandard": 31
        },
        "Envelope": {
          "White": 0,
          "Red": 1,
          "Green": 2
        },
        "EnvelopeType": {
          "WindowPeelNSeal": 0,
          "PlainFacePeelNSeal": 1,
          "WindowSelfSeal": 2,
          "PlainFaceSelfSeal": 3
        },
        "Facility": {
          "FG": 0,
          "PM": 1
        },
        "HoleDrilling": {
          "None": 0,
          "Three": 1,
          "Five": 2,
          "Six": 3,
          "Seven": 4,
          "Eight": 5
        },
        "JobApprovalOptions": {
          "NotNeeded": 0,
          "NeedsApproval": 1,
          "Rejected": 3
        },
        "JobBoardTypes": {
          "None": -1,
          "All": 0,
          "Exceptions": 1,
          "PreFlight": 2,
          "PrePress": 3,
          "Plateroom": 4,
          "PressRoom": 5,
          "Celloglaze": 6,
          "Guillotine": 7,
          "Folding": 8,
          "LetterPress": 9,
          "Stitching": 10,
          "Finishing": 11,
          "Despatch": 12,
          "DPCProduction": 13,
          "WideFormatProduction": 14,
          "Outwork": 15,
          "PayMe": 16,
          "OnHold": 17
        },
        "JobBoundEdgeOptions": {
          "None": 0,
          "SideEdge": 1,
          "TopEdge": 2
        },
        "JobCelloglazeOptions": {
          "None": 0,
          "Gloss": 1,
          "Matt": 2,
          "Velvet": 3,
          "Emboss": 4,
          "Foil": 5,
          "SpotUV": 6,
          "EmbossFoil": 7,
          "EmbossedGlossFront": 8,
          "EmbossedMattFront": 9,
          "SpotUVFrontMattFront": 10,
          "MattAntiScuff": 11
        },
        "JobPrintOptions": {
          "Unprinted": 0,
          "Printed": 1,
          "BW": 2,
          "Black": 3,
          "ReflexBlue": 4
        },
        "JobProofStatus": {
          "None": 0,
          "OnHold": 1
        },
        "JobStatusOptions": {
          "Open": 0,
          "Submitted": 1,
          "PreflightDone": 2,
          "DPCPreProduction": 3,
          "WideFormatProduction": 4,
          "InRun": 5,
          "Filling": 6,
          "LayoutRequired": 7,
          "LayoutDone": 8,
          "ApprovedForPlating": 9,
          "PlatingDone": 10,
          "PressDone": 11,
          "Celloglazed": 12,
          "Cut": 13,
          "Scored": 14,
          "Perforated": 15,
          "Letterpressed": 16,
          "Folded": 17,
          "Stitched": 18,
          "Drilled": 19,
          "Rounded": 20,
          "Finished": 21,
          "DPCPrinted": 22,
          "DPCComplete": 23,
          "WideFormatComplete": 24,
          "ShrinkWrapped": 25,
          "Outwork": 26,
          "PayMe": 27,
          "Packed": 28,
          "Dispatched": 29,
          "Complete": 30,
          "UnableToMeetPrice": 31,
          "RejectedVariation": 32
        },
        "JobTypeOptions": {
          "BusinessCard": 1,
          "DoubleBusinessCard": 2,
          "DL": 3,
          "DLSpecial": 4,
          "Letterhead": 5,
          "Compliments": 6,
          "Brochure": 7,
          "Magazine": 8,
          "PresentationFolder": 9,
          "Poster": 10,
          "Postcard": 11,
          "Custom": 12,
          "BrochureSpecial": 13,
          "MagazineSeparate": 14,
          "GolfScoreCards": 15,
          "Notepads": 16,
          "BrochureNDD": 18,
          "LetterheadNDD": 19,
          "ComplimentsNDD": 20,
          "PresentationFolderNDD": 21,
          "MagazineNDD": 22,
          "BrochureSDD": 23,
          "LetterheadSDD": 24,
          "ComplimentsSDD": 25,
          "BusinessCardNdd": 26,
          "BusinessCardSdd": 27,
          "Stationery": 28,
          "StationerySDD": 29,
          "MagazineCover": 30,
          "BacklitPosters": 32,
          "MeshBanner": 33,
          "VinylOutdoor": 34,
          "PosterMattArt": 35,
          "PosterCanvas": 36,
          "PullUpBannerStandardStand": 37,
          "PullUpBannerPremiumStand": 38,
          "VinylSticker": 41,
          "RemovableWallDecals": 42,
          "VinylStickerOutdoor": 43,
          "RigidSigns": 44,
          "DuplicateNCRBooks": 51,
          "TriplicateNCRBooks": 52,
          "QuadruplicateNCRBooks": 53,
          "EnvelopeBlack": 60,
          "Envelope1Pms": 61,
          "Envelope2Pms": 62,
          "EnvelopeCmyk": 63,
          "A4CalendarSelfCover": 71,
          "A4CalendarSeparateCover": 72,
          "TentCalendars": 73,
          "DLCalendars": 74,
          "GreetingCards": 75,
          "WiroMagazines": 76,
          "FridgeMagnet": 6020
        },
        "LabelType": {
          "Undetermined": 0,
          "BusinessCardReferenceLayout": 1,
          "FurtherProcessing": 2,
          "Carton": 3,
          "Freight": 4,
          "Filing": 5,
          "FilingPayMe": 6,
          "FreightMe": 7,
          "ImmediateFreightMe": 8,
          "FreightPickList": 9,
          "Pickup": 10,
          "DPCProcessing": 11,
          "WideFormatProcessing": 12,
          "FurtherProcessingList": 13
        },
        "LayoutType": {
          "FrontLayout": 0,
          "BackLayout": 1,
          "OutsideFront": 3,
          "OutsideBack": 4,
          "InsideFront": 5,
          "InsideBack": 6,
          "Page": 7
        },
        "NotificationType": {
          "All": 0,
          "None": 1,
          "Brief": 2
        },
        "OnlineTxSorts": {
          "AscPaymentDate": 0,
          "DescPaymentDate": 1,
          "AcsCustomer": 2,
          "DescCustomer": 3
        },
        "OrderPaymentStatusOptions": {
          "NotNeeded": 0,
          "AwaitingPayment": 1,
          "Paid": 2
        },
        "OrderStatusOptions": {
          "Open": 0,
          "Submitted": 1,
          "Ready": 2,
          "Finished": 3,
          "Dispatched": 4,
          "Archived": 5
        },
        "PackingInstruction": {
          "Standard": 0,
          "Banding": 1,
          "ShrinkWrapping": 2,
          "BulkPackCarton": 3,
          "BulkPackPallet": 4
        },
        "PadDirection": {
          "None": 0,
          "Head": 1,
          "Foot": 2,
          "Left": 3,
          "Right": 4
        },
        "PaymentTermsOptions": {
          "Account": 0,
          "COD": 1,
          "PrePay": 2,
          "OnHold": 3
        },
        "PricingModel": {
          "Unspecified": 0,
          "CategoryMarkup": 1,
          "PriceMarkup": 2
        },
        "PrintType": {
          "B": 0,
          "O": 1,
          "D": 2,
          "W": 3,
          "N": 4
        },
        "ProductionTiming": {
          "Standard": 0,
          "TurnaroundGurantee": 1,
          "PrioritySevice": 2
        },
        "PromotionLifeSpan": {
          "Absolute": 0,
          "Windowed": 1
        },
        "PromotionSalesCategory": {
          "None": 0,
          "Lead": 1,
          "Prospect": 2,
          "Lapsed": 4,
          "Customer": 8
        },
        "QuoteStatus": {
          "Requested": 0,
          "Pending": 1,
          "WonPendingSubmitted": 2,
          "Won": 3,
          "Lost": 4,
          "Rejected": 5
        },
        "RectangleCorners": {
          "None": 0,
          "TopLeft": 1,
          "TopRight": 2,
          "BottomLeft": 4,
          "BottomRight": 8,
          "All": 15
        },
        "Role": {
          "Prepress": 0,
          "PrepressFinishing": 1,
          "Accounts": 2,
          "CustomerService": 3,
          "Administrator": 4,
          "MarketingSpecialist": 5,
          "System": 6,
          "Packing": 7,
          "Dispatch": 8,
          "Invoicing": 9,
          "Scanner": 10,
          "SuperAdministrator": 11
        },
        "RotationOption": {
          "Portrait": 0,
          "Landscape": 1
        },
        "RoundDetailOption": {
          "None": 0,
          "Standard6mm1": 1,
          "Standard6mm2": 2,
          "Standard6mm3": 3,
          "Standard6mm4": 4,
          "Standard10mm1": 5,
          "Standard10mm2": 6,
          "Standard10mm3": 7,
          "Standard10mm4": 8,
          "DieCut3mm4": 9,
          "DieCut3mmTopLBottomR": 10,
          "DieCut6mm4": 11,
          "DieCut6mmTopLBottomR": 12,
          "DieCut24mmBottomR": 13,
          "Standard35mm1": 14,
          "Standard35mm2": 15,
          "Standard35mm3": 16,
          "Standard35mm4": 17
        },
        "RoundOption": {
          "None": 0,
          "Standard": 1,
          "DieCut": 2,
          "Custom": 3,
          "Round": 4,
          "RoundMagnet": 5
        },
        "RunCelloglazeOptions": {
          "None": 0,
          "MattFront": 1,
          "MattBoth": 2,
          "GlossFront": 3,
          "GlossBoth": 4,
          "VelvetFront": 5,
          "VelvetBoth": 6,
          "EmbossGlossFront": 7,
          "EmbossMattFront": 8,
          "FoilFrontMattBoth": 9,
          "SpotUVFrontMattBoth": 10,
          "SpotUVFrontMattFront": 11,
          "GlossFrontMattBack": 12,
          "MattAntiScuffFront": 13,
          "MattAntiScuffBoth": 14
        },
        "RunSearchOptions": {
          "None": 0,
          "NonBC": 1,
          "BC": 2,
          "BC310": 3,
          "BC420": 4,
          "BC360": 5,
          "BCLoyalty": 6,
          "BC350Recycled": 7
        },
        "RunStatusOptions": {
          "Filling": 0,
          "LayoutRequired": 1,
          "LayoutDone": 2,
          "ApprovedForPlating": 3,
          "DPCPreProduction": 4,
          "PlatingDone": 5,
          "PressDone": 6,
          "Finishing": 7,
          "FinishingDone": 8,
          "PackingDone": 9,
          "Outwork": 10
        },
        "ShellChangeNotificationEvents": {
          "SHCNE_UPDATEITEM": 8192
        },
        "ShellChangeNotificationFlags": {
          "SHCNF_FLUSH": 4096
        },
        "SiteLocation": {
          "AU": 0,
          "NZ": 1
        }
      }
    }

    app.constant('enums', enums);


})(window.angular, window);
