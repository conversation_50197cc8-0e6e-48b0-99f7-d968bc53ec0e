do (window, angular, toastr)->
    appCore = angular.module('app.core')

    appCore.directive 'lepContactDetails', () ->
        restrict    : 'EA'
        scope       : { vm : '=contact', disabled: '=?disabled', emailRequired: '=', mobileRequired: '='}
        templateUrl : 'common/directives/contact.component.html'
        controller  : 'ContactDetailsDirectiveController'


    appCore.controller 'ContactDetailsDirectiveController', [
        '$scope', ($scope) ->
            $scope.disabled  = $scope.disabled || false


    ]
