(function (angular, window) {
    'use strict';

    var blankJobResolver = [
        'JobService', function (JobService) {
            return JobService.getJob(0);
        }
    ];

    var orderResolver = [
        'OrderService', '$stateParams', function (OrderService, p) {
            if (p.orderId === 0) return {};
            return OrderService.getOrder(p.orderId);
        }
    ];
    var orderLargeResolver = [
        'OrderService', '$stateParams', function (OrderService, p) {
            if (p.orderId === 0) return {};
            return OrderService.getOrderLarge(p.orderId);
        }
    ];

    var jobResolver = [
        'JobService', '$stateParams', function (JobService, p) {
            return JobService.getJob(p.jobId, p.orderId);
        }
    ];
    var dcatsCustResolver = [
        '$rootScope', function ($rootScope) {
            var x = $rootScope.globals.IsCustomer ? $rootScope.globals.User.DeniedTemplates : [];
            return x;
        }
    ];
    var appStaff = angular.module('app.staff');






    appStaff.config(['$stateProvider', function ($stateProvider) {

        var staffStates = [{
            name: 'staff',
            url: '/staff',
            templateUrl: 'staff/base.html'
        }, {
            name: 'staff.dispatcher',
            url: '/dispatcher',
            templateUrl: 'staff/dispatcher.html',
            controller: 'DispatcherController',
            title: 'Dispatcher'
        }, {
            name: 'staff.orders',
            url: '/orders',
            templateUrl: 'staff/orders/orders-list.html',
            controller: 'StaffOrdersListController',
            title: 'Orders'
        }, {
            name: 'staff.audit',
            url: '/audit',
            templateUrl: 'staff/audit.html',
            controller: 'StaffAuditListController',
            title: 'Audit'
        },{
            class: 'runs',
            name: 'staff.order',
            url: '/order/{orderId:int}',
            template: '<ui-view />',
            controller: 'OrderController',
            resolve: { order: orderLargeResolver },

            //abstract: true
        }, {
            // add job under existing order
            name: 'staff.order.addnewjob',
            url: '/add-a-job',
            templateUrl: 'cust/cust-templates.html',
            controller: 'TemplatesController',
            resolve: {
                dcatsCust: function () { return [] }
            },

        }, {
            name: 'staff.order.view',
            url: '/open',
            templateUrl: 'staff/orders/order-edit.html',
            controller: 'OrderViewController',
        },
        {
            name: 'staff.order.job',
            url: '/job/{jobId:int}',
            templateUrl: 'staff/orders/job.html',
            controller: 'OrderJobController',
            resolve: {
                job: jobResolver, dcatsCust: function () { return [] }
            },

        }, 
        
        {
            // split job delivery address
            name: 'staff.order.jobsplit',
            url: '/job/{jobId:int}/splits',
            template: '<div lep-job-split-delivery job="job" />',
            onEnter: ['$stateParams', 'job', function ($stateParams, job) {
                //cd ('cust.order.jobsplit', job)
            }],
            controller: 'OrderJobController1',
            resolve: { job: jobResolver, dcatsCust: dcatsCustResolver },
            // params: { templateId: null, category: null },
          },
          
        
        
        {
            name: 'staff.job-ncr',
            url: '/order/{orderId:int}/job/{jobId:int}/ncr/{ncrCmd}',
            templateUrl: 'staff/orders/job-reprint.html',
            controller: 'OrderJobController',
            resolve: { job: jobResolver, dcatsCust: function () { return [] } },
        },
        {
            name: 'staff.order-ncr',
            url: '/order/{orderId:int}/ncr/{ncrCmd}',
            templateUrl: 'staff/orders/order-reprint.html',
            controller: 'OrderReprintController',
            resolve: { order: orderLargeResolver, dcatsCust: function () { return [] } },
        },

        {
            name: 'staff.order-freight',
            url: '/order/{orderId:int}/freight',
            templateUrl: 'staff/orders/order-freight.html',
            controller: 'OrderFreightController',
        }, {
            name: 'staff.customers',
            url: '/customers',
            templateUrl: 'staff/customers/list.html',
            controller: 'StaffCustomersListController',
            title: 'Customers'
        }, {
            name: 'staff.customers-view',
            url: '/customers/{id:int}',
            templateUrl: 'staff/customers/edit1.html',
            controller: 'StaffCustomersViewController'
        }, {
            name: 'staff.customers-view-wl',
            url: '/customers/wl/{id:int}',
            templateUrl: 'staff/customers/print-portal.html',
            controller: 'StaffCustomersViewWLController',
            resolve: {
                customer: ['$stateParams', 'lepApi2',
                    function ($stateParams, lepApi2) {
                        return lepApi2.get("staff/customers/" + $stateParams.id);
                    }]
            }

        }, {
            class: 'runs',
            name: 'staff.runs',
            url: '/runs',
            templateUrl: 'staff/runs/runs-list.html',
            controller: 'RunCtrl',
            resolve: {
                'userRunSearchCriteriaOld': ['$q', '$rootScope', 'lepApi2', function ($q, $rootScope, lepApi2) {
                    return lepApi2.get('runs/RunsearchCriteria');
                }]
            },
            title: 'Runs'
        }, {
            name: 'staff.run-edit',
            url: '/run/{runId:int}',
            templateUrl: 'staff/runs/run-edit.html',
            controller: 'StaffRunsEditController',
            resolve: {
                'run': ['$stateParams', 'lepApi2', function ($stateParams, lepApi2) {
                    return lepApi2.get('runs/' + $stateParams.runId);
                }]
            }
        }, {
            name: 'staff.run-print-bc-run-sheet',
            url: '/run/{runId:int}/print-bc-run-sheet',
            templateUrl: 'staff/runs/run-print-bc-run-sheet.html',
            controller: 'StaffRunsPrintBCRunSheetController',
            resolve: {
                'run': ['$stateParams', 'lepApi2', function ($stateParams, lepApi2) {
                    return lepApi2.get('runs/' + $stateParams.runId);
                }]
            }
        }, {
            name: 'staff.run-edit-jobspec',
            url: '/run/{runId:int}/jobs-spec/{jobId:int}?{srcJobId:int}?',
            templateUrl: 'staff/runs/run-edit-job-spec.html',
            controller: 'StaffRunsJobspecController',
            resolve: {
                'job': ['$stateParams', 'lepApi2', function ($stateParams, lepApi2) {

                    if ($stateParams.srcJobId) {
                        return lepApi2.get('runs/' + $stateParams.runId + '/jobspec/' + $stateParams.jobId + '/from/' + $stateParams.srcJobId);

                    }
                    return lepApi2.get('runs/' + $stateParams.runId + '/jobspec/' + $stateParams.jobId);
                }]
            }
        }, {
            name: 'staff.setup',
            url: '/setup',
            template: '<div ui-view></div>',

        }, {
            name: 'staff.setup.staff-list',
            url: '/staff',
            templateUrl: 'staff/setup/staff/staff-list.html',
            controller: 'StaffStaffListController'
        }, {
            name: 'staff.setup.staff-view',
            url: '/staff/{id:int}',
            templateUrl: 'staff/setup/staff/staff-view.html',
            controller: 'StaffStaffViewController',

        }, {
            name: 'staff.setup.setup-prices',
            url: '/prices?{tId:int}&{sId:int}&{psId:int}',
            templateUrl: 'staff/setup/price/setup-prices.html',
            controller: 'StaffSetupPricesController',
            resolve: { blankJob: blankJobResolver   },
            onEnter: function () {
            }
        }, {
            name: 'staff.setup.setup-steps',
            url: '/steps',
            templateUrl: 'staff/setup/price/setup-steps.html',
            controller: 'StaffSetupStpesController',
        }, {
            name: 'staff.setup.setup-promotions',
            url: '/promotions',
            templateUrl: 'staff/setup/promotion/setup-promotions.html',
            controller: 'StaffSetupPromotionsController'
        }, {
            name: 'staff.setup.setup-promotions-edit',
            url: '/promotions/{id:int}',
            templateUrl: 'staff/setup/promotion/setup-promotions-edit.html',
            controller: 'StaffSetupPromotionsEditController'
        }, {
            name: 'staff.job-pricer',
            url: '/job-pricer',
            templateUrl: 'common/directives/price-calculator.html',
            controller: 'PriceCalculatorController',
            resolve: { blankJob: blankJobResolver, dcatsCust: dcatsCustResolver },
            title: 'Online Pricer'
        }, {
            name: 'staff.customerPickup',
            class: '',
            url: '/PickupCounter',
            templateUrl: 'staff/customer-Pickup.html',
            controller: 'PickupCounterController',
            title: 'Pickup Counter',
        }, {
            name: 'staff.jobboard1fg',
            class: 'jobboard',
            url: '/jobboard1/FG',
            templateUrl: 'staff/jobboards/jobboards1_fg.html',
            controller: 'StaffJobBoardsController',
            title: 'Job Board',

        },
        {
            name: 'staff.jobboard1pm',
            class: 'jobboard',
            url: '/jobboard1/PM',
            templateUrl: 'staff/jobboards/jobboards1_pm.html',
            controller: 'StaffJobBoardsController',
            title: 'Job Board',

        },

        {
            name: 'staff.jobboard2fg',
            class: 'jobboard2',
            url: '/jobboard2/FG',
            templateUrl: 'staff/jobboards/jobboards2_fg.html',
            controller: 'StaffJobBoardsController',
            title: 'Job Board',

        },
        {
            name: 'staff.jobboard2pm',
            class: 'jobboard2',
            url: '/jobboard2/PM',
            templateUrl: 'staff/jobboards/jobboards2_pm.html',
            controller: 'StaffJobBoardsController',
            title: 'Job Board',

        },





        {
            name: 'staff.setup.offers',
            url: '/offers',
            templateUrl: 'staff/setup/offers/setup-offers.html',
            controller: 'StaffSetupOffersController'
        }
            , {
            name: 'staff.setup.productionTiming',
            url: '/productionTiming',
            templateUrl: 'staff/prodtiming/setup-production-timing.html',
            controller: 'StaffSetupProductionTimingController'
        }
        ];

        angular.forEach(staffStates, function (state) {
            $stateProvider.state(state);
        });

    }]);
})(window.angular, window);
