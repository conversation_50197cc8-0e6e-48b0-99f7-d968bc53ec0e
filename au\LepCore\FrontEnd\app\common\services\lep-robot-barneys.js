

app.service('lepRobot', ['$rootScope', '$location', '$http', '$sessionStorage', 'enums', '$state', '$window', 'Auth', 'lepApi2', 'cfpLoadingBar',
	function ($rootScope, $location, $http, $sessionStorage, enums, $state, $window, Auth, lepApi2, cfpLoadingBar) {
		if (!window.parent) {
			return;
		}
		window.parent.postMessage({ command: 'loaded' }, '*');
		window.addEventListener("message", function receiveMessage(ev) {

			var data = ev.data;
			var command = ev.data.command;

            if (command == 'usekey') {
                Auth.ClearCredentials();
				$sessionStorage.lepToken = data.key;
			} else if (command == 'openjob') {
				var m = {};
				// if new job requested and some category or template is specified
				// pass that along to router as parameters so category can be preselected
				if (data.jobId == 0 && (data.category || data.templateId)) {
					m = { orderId: data.orderId, jobId: data.jobId };

					m.category = data.category || undefined;
					m.templateId = data.templateId || undefined;
				} else {
					m = { orderId: data.orderId, jobId: data.jobId };
				}
				$state.go('remote.job', m, { reload: true, inherit: false });
			} else if (command == 'savejob') {
				$rootScope.$broadcast('savejob');
			}
			else if (command == 'reload') {
				$state.reload();
			}
		}, false);

		// every one second send out the size
		var lastH = 0;
		var mc = document.querySelector('#mc');
		if (mc) {
			setInterval(function () {
				if (!window.parent) return;
				var mc = document.querySelector('#mc');
				if (!mc)
					return;

				var w = mc.scrollWidth || 600;
				var h = (mc.scrollHeight + 100) || 600;
				if (h == lastH)
					return;

				lastH = h;
				var m = {
					command: 'resizeframe',
					w: w,
					h: h
				};
				window.parent.postMessage(m, '*');

			}, 1000);




		}


		return {};
	}
]);



