/*!
 * Bootstrap v3.3.7 (http://getbootstrap.com)
 * Copyright 2011-2016 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */


// @font-face {
// 	font-family: 'Conv_Myriad Pro Regular';
// 	src: url('/fonts/Myriad Pro Regular.eot');
// 	src: local('?'), url('/fonts/Myriad Pro Regular.woff') format('woff'),
// 					 url('/fonts/Myriad Pro Regular.ttf') format('truetype');
// 	font-weight: normal;
// 	font-style: normal;
// }
// @font-face {
// 	font-family: 'Conv_Myriad Pro Light';
// 	src: url('/fonts/Myriad Pro Light.eot');
// 	src: local('?'), url('/fonts/Myriad Pro Light.woff') format('woff'),
// 					 url('/fonts/Myriad Pro Light.ttf') format('truetype');
// 	font-weight: normal;
// 	font-style: normal;
// }

@font-face {
	font-family: 'Conv_EnzoOT-Medi';
	//src: url('/fonts/EnzoOT-Medi.eot');
	src: local('?'), //url('/fonts/EnzoOT-Medi.woff') format('woff'),
					 url('/fonts/EnzoOT-Medi.ttf') format('truetype');
	font-weight: normal;
	font-style: normal;
}
 @font-face {
 	font-family: 'Conv_EnzoOT-MediIta';
    //src: url('/fonts/EnzoOT-MediIta.eot');
 	src: local('?'), //url('/fonts/EnzoOT-MediIta.woff') format('woff'),
 					 url('/fonts/EnzoOT-MediIta.ttf') format('truetype');
 	font-weight: normal;
 	font-style: normal;
}
@font-face {
	font-family: 'Conv_EnzoOT-Bold';
	//src: url('/fonts/EnzoOT-Bold.eot');
	src: local('?'), //url('/fonts/EnzoOT-Bold.woff') format('woff'),
					 url('/fonts/EnzoOT-Bold.ttf') format('truetype');
	font-weight: normal;
	font-style: normal;
}
@font-face {
 	font-family: 'Conv_EnzoOT-BoldIta';
 	//src: url('/fonts/EnzoOT-BoldIta.eot');
 	src: local('?'), //url('/fonts/EnzoOT-BoldIta.woff') format('woff'),
 					 url('/fonts/EnzoOT-BoldIta.ttf') format('truetype');
 	font-weight: normal;
 	font-style: normal;
 }





// Core variables and mixins
@import "bootstrap/variables";
@import "bootstrap/mixins";

// Reset and dependencies
@import "bootstrap/normalize";
//@import "bootstrap/print";
@import "bootstrap/glyphicons";

// Core CSS
@import "bootstrap/scaffolding";
@import "bootstrap/type";
@import "bootstrap/code";
@import "bootstrap/grid";
@import "bootstrap/tables";
@import "bootstrap/forms";
@import "bootstrap/buttons";

// Components
//@import "bootstrap/component-animations";
@import "bootstrap/dropdowns";
@import "bootstrap/button-groups";
@import "bootstrap/input-groups";
@import "bootstrap/navs";
@import "bootstrap/navbar";
//@import "bootstrap/breadcrumbs";
@import "bootstrap/pagination";
@import "bootstrap/pager";
@import "bootstrap/labels";
@import "bootstrap/badges";
//@import "bootstrap/jumbotron";
@import "bootstrap/thumbnails";
@import "bootstrap/alerts";
@import "bootstrap/progress-bars";
//@import "bootstrap/media";
@import "bootstrap/list-group";
//@import "bootstrap/panels";
@import "bootstrap/responsive-embed";
@import "bootstrap/wells";
//@import "bootstrap/close";

// Components w/ JavaScript
@import "bootstrap/modals";
@import "bootstrap/tooltip";
@import "bootstrap/popovers";
//@import "bootstrap/carousel";

// Utility classes
@import "bootstrap/utilities";
@import "bootstrap/responsive-utilities";

.datepicker.dropdown-menu,
.timepicker.dropdown-menu
{

  width: 360px;
  height: 320px;
  button {
    outline: none;
    border: 0px;
	padding:5px;
  }
  tbody {
    height: 250px;
  }
  tbody button {

  }
  &.datepicker-mode-1, &.datepicker-mode-2 {
    tbody button {

    }
  }
}

@media print {
    .footer, .navbar, footer, #footer,
    #non-printable {
        display: none !important;
    }
    #printable {
        display: block;
    }
}