using AutoMapper;
using lep;
using lep.email;
using lep.user;
using LepCore.Dto;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace LepCore.Controllers
{
	[Produces("application/json")]
	public class AccountController : Controller
	{
		private IHttpContextAccessor _httpContextAccessor;
		private readonly IEmailApplication _emailApplication;
		private const string LoginHistory = "INSERT INTO LepUserLoginHistory(Name, Action) VALUES (:Name, :Action)";
		public IUserApplication _userApplication { get; set; }
		private IConfigurationRoot _config;
		private bool _currentUserIsCust;
		private bool _currentUserIsStaff;
		private bool _currentUserIsAnonymousWLCustomer;
		private bool _currentUserIsLoggedInWLCustomer;

		public AccountController(IUserApplication userApplication, IEmailApplication emailApplication, IHttpContextAccessor httpContextAccessor
			, IConfigurationRoot config)
		{
			_userApplication = userApplication;
			_emailApplication = emailApplication;
			_httpContextAccessor = httpContextAccessor;
			_config = config;
		}

		[ApiExplorerSettings(IgnoreApi = true)]
		public override void OnActionExecuting(ActionExecutingContext context)
		{
			var host = _httpContextAccessor.HttpContext.Request.Host;

			var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
			var roles = User.Claims.Where(c => c.Type == ClaimTypes.Role).Select(c => c.Value).ToList();

			_currentUserIsStaff =	roles.Contains(LepRoles.SuperAdministrator) ||	roles.Contains(LepRoles.Administrator) || roles.Contains(LepRoles.Staff); // TODO
			_currentUserIsCust = roles.Contains(LepRoles.Customer);
			_currentUserIsAnonymousWLCustomer = roles.Contains(LepRoles.AnonymousWLCustomer);
			_currentUserIsLoggedInWLCustomer = roles.Contains(LepRoles.LoggedInWLCustomer);
		}

		/// <summary>
		/// Client side SPA make a call to this to check it it is still loggged in.
		/// </summary>
		/// <returns></returns>
		[HttpGet("/api/Account/Pulse")]
		[Authorize]
		[ApiExplorerSettings(IgnoreApi = true)]
		public IActionResult Pulse()
		{
			try
			{
				var u = _httpContextAccessor.HttpContext.User;
				if (u != null)
				{
					var name = u.Identity?.Name ?? "?";
					if (u.Claims != null)
					{
						var role = u.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Role)?.Value ?? "?";
						LepGlobal.Instance.LogAccess(name, role);
					}
				}
			}
			catch (Exception ex)
			{
				var m = ex.Message;
			}

			return Ok();
		}

		/// <summary>
		/// Logs a user in the system
		/// Deprecated: as SPA just uses JWT from LORD-780 and coookies are being deprecated
		/// Kept here for Dispatcher and some other moving parts
		/// </summary>
		/// <param name="loginDetails"></param>
		/// <returns></returns>
		[HttpPost("/api/Account/Login")]
		[AllowAnonymous]
		public async Task<IActionResult> Login([FromBody] LoginDetails loginDetails, [FromServices] NHibernate.ISession session)
		{
			if (loginDetails.Username == "lepdemo1")
			{
				throw new Exception("bla bla");
			}

			IUser user = _userApplication.AttemptLogin(loginDetails.Username ?? "", loginDetails.Password ?? "");

			if (user != null)
			{
				var role = "";
				session.Refresh(user);

				if (user is IStaff)
				{
					role = (user as IStaff).Role.ToString();
					if ((user as IStaff).Role != Role.Scanner)
					{
						(user as IStaff).IPAddress = Request.HttpContext.Connection.RemoteIpAddress.ToString();
					}
				}

				if ((user is ICustomerUser) || ((user is IStaff) && (user as IStaff).Role != Role.Scanner))
				{
					user.LastLogin = DateTime.Now;
				}

				_userApplication.Save(user);

				var authProperties = new Microsoft.AspNetCore.Authentication.AuthenticationProperties();
				var userClaims = new List<Claim> {
					new Claim("UserId", user.Id.ToString()),
					new Claim(ClaimTypes.Name , user.Username),
				};

				if (user.IsStaff)
				{
					userClaims.Add(new Claim(ClaimTypes.Role, LepRoles.Staff));
				}

				var principal = new ClaimsPrincipal(new ClaimsIdentity(userClaims, "Cookies"));
				await HttpContext.SignInAsync(
					CookieAuthenticationDefaults.AuthenticationScheme,
					principal, authProperties);
				_httpContextAccessor.HttpContext.Items["CurrentUser"] = user;

				var userIsStaff = user is IStaff;
				var userIsCustomer = user is ICustomerUser;

				// TODO: var userIsWLCustomerLoggedIn =(user is ICustomerUser) &&
				var userIsSuperAdministrator = userIsStaff && (user as IStaff).Role == Role.SuperAdministrator;
				var userIsAdministrator = userIsStaff && (user as IStaff).Role == Role.Administrator;
				var userIsPrepress = userIsStaff && ((user as IStaff).Role == Role.Prepress);
				var userIsAccounts = userIsStaff && ((user as IStaff).Role == Role.Accounts);
				session.CreateSQLQuery(LoginHistory).SetParameter("Name", user.Username).SetParameter("Action", "Login").ExecuteUpdate();
				return Ok(new
				{
					success = true,
					IsStaff = userIsStaff,
					IsCustomer = userIsCustomer,
					IsSA = userIsSuperAdministrator,
					IsA = userIsAdministrator || userIsSuperAdministrator,
					IsAccounts = userIsAccounts,
					IsPrepress = userIsPrepress,
					// Todo: Trim this
					User = user, // this will either be a IStaff or ICustomerUser
					TemplatesDenied = userIsCustomer ? (user as ICustomerUser).DeniedTemplates : new List<int>(),
					DeniedTemplates = userIsCustomer ? (user as ICustomerUser).PrintPortalSettings?.DeniedTemplates : new List<int>(),
					IsPrintPortalEnabled = userIsCustomer && (user as ICustomerUser).IsPrintPortalEnabled,
				});
			}
			else
			{
				session.CreateSQLQuery(LoginHistory).SetParameter("Name", loginDetails.Username ?? "").SetParameter("Action", "Failed Login attempt").ExecuteUpdate();
				return new ContentResult { StatusCode = StatusCodes.Status401Unauthorized, Content = "Username or password is incorrect" };
			}
		}

		/// <summary>
		/// Just like Login, just lightweight
		/// </summary>
		/// <param name="loginDetails"></param>
		/// <param name="session"></param>
		/// <returns></returns>
		[HttpPost("/api/Account/Login2")]
		[AllowAnonymous]
		[ApiExplorerSettings(IgnoreApi = true)]
		public async Task<IActionResult> Login2([FromBody] LoginDetails loginDetails, [FromServices] NHibernate.ISession session)
		{
			IUser user = _userApplication.AttemptLogin(loginDetails.Username ?? "", loginDetails.Password ?? "");
			if (user != null)
			{
				var authProperties = new Microsoft.AspNetCore.Authentication.AuthenticationProperties();
				var userClaims = new List<Claim>
				{
					new Claim("UserId", user.Id.ToString()),
					new Claim(ClaimTypes.Name , user.Username)
				};
				if (user.IsStaff)
				{
					userClaims.Add(new Claim(ClaimTypes.Role, LepRoles.Staff));
				}

				var principal = new ClaimsPrincipal(new ClaimsIdentity(userClaims, "Cookies"));
				await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal, authProperties);

				_httpContextAccessor.HttpContext.Items["CurrentUser"] = user;
				return Ok();
			}
			else
			{
				return new ContentResult { StatusCode = StatusCodes.Status401Unauthorized, Content = "Username or password is incorrect" };
			}
		}

		/// <summary>
		/// Authenticates a user and if successfull returns a JWT Bearer Token
		/// </summary>
		/// <param name="loginDetails"></param>
		/// <returns></returns>
		[HttpPost("/api/Account/authenticate")]
		[AllowAnonymous]
		public IActionResult Authenticate([FromBody] LoginDetails loginDetails, [FromServices] NHibernate.ISession session)
		{
			if (loginDetails == null) return BadRequest();
			IUser user = _userApplication.AttemptLogin(loginDetails.Username ?? "", loginDetails.Password ?? "");

			if (!string.IsNullOrEmpty(loginDetails.Extra))
			{
				if (user != null && (user is ICustomerUser) )
				{
					var portalId = (user as ICustomerUser).ParentCustomer.Id.ToString();
					if ((portalId != loginDetails.Extra))
					return new ContentResult { StatusCode = StatusCodes.Status401Unauthorized, Content = "Username or password is incorrect" };
				}
			}
			else
			{
				if(user is ICustomerUser)
				{
					if( (user as ICustomerUser).ParentCustomer != null)
					{
						return new ContentResult { StatusCode = StatusCodes.Status401Unauthorized, Content = "Username or password is incorrect" };
					}
				}
			}

			if (user == null)
			{
				session.CreateSQLQuery(LoginHistory).SetParameter("Name", loginDetails.Username ?? "").SetParameter("Action", "Failed Login attempt").ExecuteUpdate();
				return new ContentResult { StatusCode = StatusCodes.Status401Unauthorized, Content = "Username or password is incorrect" };
			}

			session.Refresh(user);

			// start with claims containing UserId and username...
			var claims = new List<Claim>
			{
			};
			claims.Add(new Claim(ClaimTypes.Name, user.Username));
			bool userIsIStaff = user is IStaff;

			if (userIsIStaff) // then add Staffs roles
			{
				var role = (user as IStaff).Role.ToString();
				if ((user as IStaff).Role != Role.Scanner)
				{
					(user as IStaff).IPAddress = Request.HttpContext.Connection.RemoteIpAddress.ToString();
				}
				claims.Add(new Claim(ClaimTypes.Role, LepRoles.Staff));
				claims.Add(new Claim(ClaimTypes.Role, role));
				claims.Add(new Claim("UserId", user.Id.ToString()));
			}
			else // (user is ICustomerUser) its Customer or White Label customers trying to log in
			{
				if (string.IsNullOrEmpty(loginDetails.Extra)) // Normal customers
				{
					claims.Add(new Claim("UserId", user.Id.ToString()));

					claims.Add(new Claim(ClaimTypes.Role, LepRoles.Customer));
				}
				else
				{
					//TODO: and check if parent cusomer Id was passed in extra data
					var parentCust = (user as ICustomerUser).ParentCustomer;
					if (parentCust != null && loginDetails.Extra == parentCust.Id.ToString()) // means a sub customer trying to log in
					{
						claims.Add(new Claim(ClaimTypes.Role, LepRoles.LoggedInWLCustomer));
						claims.Add(new Claim("ParentId", parentCust.Id.ToString()));
						claims.Add(new Claim("SubCustId", user.Id.ToString()));
						claims.Add(new Claim("UserId", user.Id.ToString()));
					}
				}
			}

			if ((user is ICustomerUser) || ((user is IStaff) && (user as IStaff).Role != Role.Scanner))
			{
				user.LastLogin = DateTime.Now;
			}
			_userApplication.Save(user);
			string encryptedJWT = GenerateGWEToken(claims);
			return Ok(new { token = encryptedJWT, userId = user.Id , IsStaff = (user is IStaff) });
		}

		private static string GenerateGWEToken(List<Claim> claims)
		{
			var now = DateTime.UtcNow;
			// authentication successful so generate jwt token
			var tokenHandler = new JwtSecurityTokenHandler();
			var issuer = "Issuer";
			var audience = "Audience";
			var key = Encoding.ASCII.GetBytes(Program.secretKey);
			var signingCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256);
			EncryptingCredentials encryptingCredentials = new EncryptingCredentials(new SymmetricSecurityKey(key), JwtConstants.DirectKeyUseAlg, SecurityAlgorithms.Aes256CbcHmacSha512);

			var jwtSecurityToken = new JwtSecurityTokenHandler().CreateJwtSecurityToken(issuer, audience,
										   new ClaimsIdentity(claims), null,
										   expires: DateTime.UtcNow.AddDays(5),
										   null, signingCredentials, encryptingCredentials);
			var encryptedJWT = new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);
			return encryptedJWT;
		}

		/// <summary>
		/// Meant for WhiteLabel Iframe source
		///
		///
		///
		/// </summary>
		/// <param name="userId"></param>
		/// <param name="orderId"></param>
		/// <param name="jobId"></param>
		/// <param name="templateId"></param>
		/// <param name="category"></param>
		/// <returns></returns>
		[HttpGet]
		[AllowAnonymous]
		[Route("WL/{userId:int}/{orderId:int?}/{jobId:int?}")]
		[Route("WL/{userId:int}/o/{orderId:int?}/j/{jobId:int?}")]
		[ApiExplorerSettings(IgnoreApi = true)]
		public IActionResult WhiteLabelIndex([FromRoute] int userId, [FromRoute] int? orderId, [FromRoute] int? jobId, [FromQuery] string templateId, [FromQuery] string category)
		{
			ICustomerUser parentCust = (ICustomerUser)_userApplication.GetUser(userId);
			if (parentCust == null)
				return BadRequest();

			if (!parentCust.IsPrintPortalEnabled)
				return BadRequest("Please contact us for quote");

			var claims = new List<Claim>
				{
					new Claim("ParentId",      parentCust.Id.ToString()),
					new Claim("UserId",        parentCust.Id.ToString()),
					new Claim(ClaimTypes.Name , parentCust.Username),

					// Important: we are saying  WhiteLabelSubCustomer who is anonymous uptill now.
					// if he chooses to log in that will go to normal /account/authenticate and
					// we will put WhiteLabelSubCustomerLoggedIn over there
					new Claim(ClaimTypes.Role, LepRoles.AnonymousWLCustomer),
				};

			string encryptedJWT = GenerateGWEToken(claims);
			var extra = "";

			if (!string.IsNullOrEmpty(templateId)) { extra += $"&templateId={templateId}"; }
			if (!string.IsNullOrEmpty(category)) { extra += $"&category={category}"; }
			//_httpContextAccessor.HttpContext.Response.Headers.Add("t", encryptedJWT);
			return Redirect($"~/index_wl.html#!/wl/{userId}/o/{orderId ?? 0}/j/{jobId ?? 0}?t={encryptedJWT}{extra}");//index.html/#!/login
		}

		[HttpGet]
		[AllowAnonymous]
		[Route("shop/{orderId:int?}/{jobId:int?}")]
		[Route("shop/o/{orderId:int?}/j/{jobId:int?}")]
		[ApiExplorerSettings(IgnoreApi = true)]
		public IActionResult WhiteLabelIndex2([FromRoute] int? orderId, [FromRoute] int? jobId, [FromQuery] string templateId, [FromQuery] string category
			 , [FromServices] NHibernate.ISession session)
		{
			var a = _httpContextAccessor.HttpContext.Request.Host.Host.Split('.');
			if (a.Count() != 3)
				return new BadRequestResult();

			var name = a[0];
			var userId = session.CreateSQLQuery($@"select CustomerId from Customer
										where[WhiteLabelEnabled] = 'Y'
										And Replace(Name, ' ', '') = '{name}'").List<int>().FirstOrDefault();
			if (userId == 0)
				return new BadRequestResult();

			ICustomerUser parentCust = (ICustomerUser)_userApplication.GetUser(userId);
			if (parentCust == null)
				return BadRequest();

			if (!parentCust.IsPrintPortalEnabled)
				return BadRequest("Please contact us for quote");

			var claims = new List<Claim>
				{
					new Claim("ParentId",      parentCust.Id.ToString()),
					new Claim("UserId",        parentCust.Id.ToString()),
					new Claim(ClaimTypes.Name , parentCust.Username),

					// Important: we are saying  WhiteLabelSubCustomer who is anonymous uptill now.
					// if he chooses to log in that will go to normal /account/authenticate and
					// we will put WhiteLabelSubCustomerLoggedIn over there
					new Claim(ClaimTypes.Role, LepRoles.AnonymousWLCustomer),
				};

			string encryptedJWT = GenerateGWEToken(claims);
			var extra = "";

			if (!string.IsNullOrEmpty(templateId)) { extra += $"&templateId={templateId}"; }
			if (!string.IsNullOrEmpty(category)) { extra += $"&category={category}"; }
			//_httpContextAccessor.HttpContext.Response.Headers.Add("t", encryptedJWT);
			return Redirect($"~/index_wl.html#!/wl/{userId}/o/{orderId ?? 0}/j/{jobId ?? 0}?t={encryptedJWT}{extra}");//index.html/#!/login
		}

		[HttpPost("/api/Account/Logout")]
		public async Task<IActionResult> Logout([FromServices] NHibernate.ISession session)
		{
			var name = _httpContextAccessor.HttpContext.User.Identity.Name;

			if (name != null)
				session.CreateSQLQuery(LoginHistory).SetParameter("Name", name).SetParameter("Action", "Logout").ExecuteUpdate();

			await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
			return Redirect("/");
		}

		/// <summary>
		/// Returns user object associated with current user
		/// </summary>
		/// <param name="loginDetails"></param>
		/// <returns></returns>
		[HttpGet("/api/Account/LoginDetails")]
		[Authorize] // important
		public IActionResult LoginDetails([FromServices] IMapper mapper)
		{
			var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
			var _currentUser = _userApplication.GetUser(userId);
			_userApplication.Refresh(_currentUser);

			var user = _currentUser;

			if (user == null)
			{
				return new ContentResult { StatusCode = StatusCodes.Status401Unauthorized, Content = "User not logged in" };
			}

			var role = "";
			if (user is IStaff)
			{
				role = (user as IStaff).Role.ToString();
				if ((user as IStaff).Role != Role.Scanner)
				{
					(user as IStaff).IPAddress = Request.HttpContext.Connection.RemoteIpAddress.ToString();
				}
			}
			else
			{
				role = "Client";
			}

		 

			var userIsStaff = user is IStaff;
			var userIsAdministrator = userIsStaff && (user as IStaff).Role == Role.Administrator;
			var userIsPrepress = userIsStaff && ((user as IStaff).Role == Role.Prepress);
			var userIsSuperAdministrator = userIsStaff && (user as IStaff).Role == Role.SuperAdministrator;
			var userIsAccounts = userIsStaff && ((user as IStaff).Role == Role.Accounts);
			var userIsMarketingSpecialist = userIsStaff && ((user as IStaff).Role == Role.MarketingSpecialist);
			dynamic r = new ExpandoObject();
			r.success = true;
			if (userIsStaff)
			{
				r.IsStaff = true;
				r.IsAdministrator = userIsAdministrator;
				r.IsPrepress = userIsPrepress;
				r.IsSA = userIsSuperAdministrator;
				r.IsA = userIsAdministrator  || userIsSuperAdministrator;
				r.IsAccounts = userIsAccounts;
				r.IsMS = userIsMarketingSpecialist;
			}

			if (_currentUserIsCust)  // eg lepdemo
			{
				var u = (user as ICustomerUser);

				var lepdemo = u.Username;

				r.IsCustomer = true;
				//r.DeniedTemplates = u.DeniedTemplates;

				var DeniedTemplates = new List<int>();
				DeniedTemplates.AddRange(u.DeniedTemplates ?? new List<int>());
				r.DeniedTemplates = DeniedTemplates.Distinct().ToList();
				r.IsPrintPortalEnabled = u.IsPrintPortalEnabled;
			}

			if (_currentUserIsAnonymousWLCustomer)
			{
				var u = (user as ICustomerUser); // make sure this is still lepdemo
				var lepdemo = u.Username;

				r.IsSubCustomer = true;
				//r.DeniedTemplates = u.CombinedDeniedTemplates();
				r.IsPrintPortalEnabled = u.IsPrintPortalEnabled;

				//Anonymous Sub Cust->
				//  trim Customer.Parent(lepdemo).PrintPortalSettings.DeniedTemplates
				//  lepdemo.DeniedTemplates
				// 
				var DeniedTemplates = new List<int>();

				DeniedTemplates.AddRange(u.DeniedTemplates ?? new List<int>());
				DeniedTemplates.AddRange(u.PrintPortalSettings?.DeniedTemplates ?? new List<int>() );
				r.DeniedTemplates = DeniedTemplates.Distinct().ToList();
			}

			if (_currentUserIsLoggedInWLCustomer)
			{
				var u = (user as ICustomerUser);
				ICustomerUser parent = _userApplication.GetCustomerUser(u.ParentCustomer.Id);
				_userApplication.Refresh(parent);

				var testtest = u.Username;
				var lepdemo = parent.Username;

				r.IsSubCustomer = true;

				//r.DeniedTemplates = u.CombinedDeniedTemplates();
				r.IsPrintPortalEnabled = false;
				r.PaymentTerms = u.PaymentTerms;

				var DeniedTemplates = new List<int>();
				DeniedTemplates.AddRange(parent.DeniedTemplates ?? new List<int>());
				DeniedTemplates.AddRange(parent.PrintPortalSettings?.DeniedTemplates ?? new List<int>());
				DeniedTemplates.AddRange(u.PrintPortalSettings?.DeniedTemplates ?? new List<int>());
				r.DeniedTemplates = DeniedTemplates.Distinct().ToList();
			}

			if (user is ICustomerUser)
			{
				var ux = mapper.Map<LoginDetailsDto>(user);
				if (ux.PrintPortalSettings != null)
				{
					ux.PrintPortalSettings.PriceRangeMarkups = null;
					ux.PrintPortalSettings.CategoryMarkups = null;
					ux.PrintPortalSettings.FavouriteCategoryMarkups = null;
					ux.PrintPortalSettings.FavouritePriceRangeMarkups = null;
				}

				r.User = ux;
			}
			else
			{
				r.User = user;
			}

			bool inHolidays = bool.Parse(_config["HolidayMode"]);
			if (inHolidays)
			{
				r.inHolidays = true;
			}

			return Ok(r);
		}

		/// <summary>
		/// return the current White Label Sites configuration  owner customers details
		/// </summary>
		/// <returns></returns>
		[HttpGet, Route("api/Account/ppc")]
		[Authorize(Roles = "AnonymousWLCustomer,LoggedInWLCustomer,Customer")]
		[ApiExplorerSettings(IgnoreApi = true)]
		public IActionResult GetPrintPortalConfig()
		{
			// most of the settings obvisously comes from from the Main / parent customers profile
			var parentCustId = User.Claims.Where(_ => _.Type == "ParentId").Select(_ => Convert.ToInt32(_.Value)).FirstOrDefault();
			var parentCust = (ICustomerUser)_userApplication.GetUser(parentCustId);
			_userApplication.Refresh(parentCust);

			if (parentCust == null)
				return BadRequest();

			if (!parentCust.IsPrintPortalEnabled)
				return BadRequest("Please contact us for quote");

			// then we fill in the setting we need
			var pc = parentCust;
			dynamic o = new ExpandoObject();
			o.pcName = parentCust.Name;
			o.ppv = pc.PrintPortalSettings.Version;
			o.PayPalClientId = pc.PrintPortalSettings.PayPalClientId;
			o.StripePublishableKey = pc.PrintPortalSettings.StripePublishableKey;
			o.CustomCssURL = pc.PrintPortalSettings.CustomCssURL;

			o.DeniedTemplates = pc.CombinedDeniedTemplates();

			o.IsAacEnabled = pc.PrintPortalSettings.IsAacEnabled;
			o.PaymentTerms = PaymentTermsOptions.COD;

			// if this is a logged in sub customer then we need their payment terms as well to see if they can
			if (_currentUserIsLoggedInWLCustomer)
			{
				var subCustId = User.Claims.Where(_ => _.Type == "SubCustId").Select(_ => Convert.ToInt32(_.Value)).FirstOrDefault();
				var subCust = (ICustomerUser)_userApplication.GetUser(subCustId);
				o.PaymentTerms = subCust.PaymentTerms;
			}

			return Ok(o);
		}


		

		[HttpPost("/api/Account/Password/Reset")]
		[AllowAnonymous]
		public IActionResult PasswordReset([FromBody]  string username)
		{
			var users = _userApplication.GetUserByName(username);
			try
			{
				var newPassword = Utils.GeneratePassword(10);
				if (users != null && users.Count > 0)
				{
					var user = users[0];
					_userApplication.SetPassword((IUser)user, newPassword);
					_userApplication.Save((IUser)user);
					_emailApplication.SendForgottenPassword((IUser)user, newPassword);
				}
				else
				{
					return BadRequest();
				}
				return Ok();
			}
			catch (Exception ex)
			{
				var m = ex.Message;
				return BadRequest();
			}
		}

		[HttpGet("/api/Account/UserCount")]
		[Authorize]
		[ApiExplorerSettings(IgnoreApi = true)]
		public IActionResult UserCount()
		{
			return Ok(LepGlobal.Instance.GetUsersCount());
		}

		[HttpPost("/api/Account/WL/Create")]
		[Authorize]
		[ApiExplorerSettings(IgnoreApi = true)]
		public IActionResult RegisterWhiteLabelCustomer([FromBody]  CustomerUserDto dto, [FromServices] IMapper mapper)
		{
			if (!_currentUserIsAnonymousWLCustomer)
				return BadRequest();

			var parentCustomerId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
			var parentCustomer = _userApplication.GetCustomerUser(parentCustomerId);

			var customer = _userApplication.NewCustomerUser();
			customer.Username = dto.Username;
			customer = mapper.Map(dto, customer);

			customer.ParentCustomer = parentCustomer;
			customer.PaymentTerms = PaymentTermsOptions.COD;

			_userApplication.SetPassword(customer, dto.Password);
			customer.PreferredCourier = new lep.courier.CourierType(lep.courier.CourierType.None);
			_userApplication.Save(customer);

			var Id = customer.Id;

			return new StatusCodeResult(StatusCodes.Status201Created);
		}

		[HttpGet("/api/Account/WL/blank")]
		[ApiExplorerSettings(IgnoreApi = true)]
		[Authorize]
		public IActionResult GetBlankWhiteLabelCustomer([FromServices] IMapper mapper)
		{
			if (!_currentUserIsAnonymousWLCustomer)
				return BadRequest();

			var parentCustomerId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
			var parentCustomer = _userApplication.GetCustomerUser(parentCustomerId);

			var customer = _userApplication.NewCustomerUser();
			customer.ParentCustomer = parentCustomer;
			customer.PaymentTerms = PaymentTermsOptions.COD;
			customer.PreferredCourier = new lep.courier.CourierType(lep.courier.CourierType.None);
			customer.IsEnabled = true;
			var dto = mapper.Map<CustomerUserDto>(customer);
			return Ok(dto);
		}
	}
}
