app = angular.module('app')


app.directive 'lepJobArtworkUploader', () ->
	restrict		: 'EA'
#	scope			 : { job : '=', files : '='}
	templateUrl : 'common/directives/upload-artwork.html'
	controller	: 'JobArtworkUploaderController'


app.controller 'JobArtworkUploaderController', [
	'$scope', '$rootScope', 'OrderService', 'JobService', '$location', 'enums', 'Utils', '$log', '$http', '$state', 'cfpLoadingBar',
	($scope , $rootScope,    OrderService,   JobService,   $location,   enums,   utils,   $log,   $http,   $state, cfpLoadingBar) ->
		##console.debug('JobArtworkUploaderController', $scope.job.Quantity)

		#		if $scope.job.Id != 0
		#			OrderService.getJobArtworks($scope.job.Id).then (artworks) ->
		#				$scope.artworks = artworks
		#
		#
		if window.IsBarneys
			$scope.IsBarneys = true

		$scope.suppliedArtworks = _.filter($scope.job.Artworks, (a)-> a.Supplied && !a.Ready)

		$scope.btn_upload = () ->
			if !$scope.files.length then return

			uploadRequest =
				Id: $scope.job.Id
				UploadType : $scope.job.UploadType

			formData = new FormData
			formData.append 'vm', angular.toJson(uploadRequest)
				#now add all of the assigned files
			i = 0
			while i < $scope.files.length
				#add each file to the form data and iteratively name them
				formData.append $scope.requiredPositions[i], $scope.files[i].file
				i++
			$rootScope.$broadcast("percentage", 1)
			jobId = $scope.job.Id
			$http(
				method          : 'POST'
				url             : "/Api/Orders/Job/#{jobId}/artworks"
				headers         : {'Content-Type': undefined}
				transformRequest: angular.identity
				data: formData
				uploadEventHandlers: 
					progress: (e) ->
						if (e.lengthComputable)
								px = (e.loaded / e.total)
								cfpLoadingBar.set(px)
								$rootScope.$broadcast("percentage",  Math.floor(px * 100))
			).then((data, status, headers, config) ->
					$scope.artworks = data
					#console.debug(arguments)
					window.toastr.info('success!')
					$scope.upload_artwork = false
					$state.reload()
					#$scope.$emit("upload_artwork") ## required artwork message should go away or state shroud refresh
				,(data, status, headers, config) ->
					alert 'failed!'
			)
#
		$scope.init = () ->

		$scope.init()

		$scope.downloadArtwork = OrderService.downloadArtwork

		$scope.pos2label = utils.pos2label

		return @
	]

