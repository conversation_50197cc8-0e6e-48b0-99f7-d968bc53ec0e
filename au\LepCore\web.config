﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <system.web>
        <httpRuntime maxRequestLength="4097152" executionTimeout="3600" targetFramework="4.5" />
    </system.web>

    <system.webServer>

        <handlers>
            <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2"
                resourceType="Unspecified" />
        </handlers>

        <aspNetCore processPath=".\LepCore.exe" arguments="" stdoutLogEnabled="true"
            stdoutLogFile=".\logs\stdout" forwardWindowsAuthToken="false" hostingModel="inprocess"
            requestTimeout="00:20:00" />

        <security>
            <requestFiltering allowDoubleEscaping="true">
                <requestLimits maxAllowedContentLength="4147483648" />  <!-- 1 GB-->
                <verbs>
                    <remove verb="OPTIONS" />
                    <add verb="OPTIONS" allowed="true" />
                </verbs>
            </requestFiltering>
        </security>

        <staticContent>
            <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="1.00:00:00" />
        </staticContent>

        <httpProtocol>
            <customHeaders>
                <remove name="Access-Control-Allow-Origin" />
                <add name="Access-Control-Allow-Origin" value="*" />
                <add name="Access-Control-Allow-Headers" value="*" />
                <add name="Access-Control-Allow-Methods" value="GET, POST,PUT,DELETE, OPTIONS" />
            </customHeaders>
        </httpProtocol>

    </system.webServer>

</configuration>
