appCore = angular.module('app.core')
#
#{{ enums.ValueDesc.RoundOption[job.RoundOption.toString()]}} <br/>
#{{ enums.ValueDesc.RoundDetailOption[job.RoundDetailOption.toString()]}} <br/>
#
appCore.directive 'roundingBox', ['enums',(enums)->
    restrict : 'A'
    scope : {job : '=job'}
    template: """
            <div id="roundingbox" ng-model="roundCornerError" value-needs-to-pass="vFn">
                <div ng-if="job.RoundOption!=2">Click corners to modify</div>
                <div id="box" class="box bc r3"  >
                    <span class="cnr" style="top: 0; left: 0" title="click to change top left rounding"></span>
                    <span class="cnr" style="top: 0; right: 0" title="click to change top right rounding"></span>
                    <span class="cnr" style="bottom: 0; left: 0" title="click to change bottom left rounding"></span>
                    <span class="cnr" style="bottom: 0; right: 0" title="click to change bottom right rounding"></span>
                    <div style="left: 0; width: 100%; position: absolute; top: 0;">Front top</div>
                    <div style="left: 0; width: 100%; position: absolute; top: 42%;" class="title" id="roundOptionTxt">
                        {{enums.ValueDesc.RoundOption[job.RoundOption]}} <br/>
                        {{enums.ValueDesc.RoundDetailOption[job.RoundDetailOption]}}


                    </div>
                    <div style="left: 0; width: 100%; position: absolute; bottom: 0;">Front bottom</div>
                    <div style="left: 0; width: 100%; position: absolute; top: 68%;color:red!important; font-weight: bold"
                         ng-show="roundCornerError" >{{roundCornerError}}!</div>
                </div>


            </div>
            """
    controller: [ '$scope', ($scope) ->
                    $scope.enums = enums
                ]
    link : ($scope, elem, attr, ctrl) ->
        x0 = document.getElementById('box')
        x = angular.element(x0)


        corners = 0
        $scope.corners = corners



        if $scope.job.TRround then x.addClass('tr')
        if $scope.job.TLround then x.addClass('tl')
        if $scope.job.BRround then x.addClass('br')
        if $scope.job.BLround then x.addClass('bl')

        validate = () ->
            if $scope.job.RoundOption == 0 or  $scope.job.RoundOption == 3
                $scope.roundCornerError = false
                return

            rd = enums.ValueKey.RoundDetailOption[$scope.job.RoundDetailOption]
            expected = 0
            if rd.indexOf('mm1') > -1 then expected = 1
            if rd.indexOf('mm2') > -1 then expected = 2
            if rd.indexOf('mm3') > -1 then expected = 3
            if rd.indexOf('mm4') > -1 then expected = 4
            if rd.indexOf('mmTopLBottomR') > -1 then expected = 2
            if rd.indexOf('mmBottomR') > -1 then expected = 1
            if corners != expected
                $scope.roundCornerError = "Please select #{expected} corners"
            else
                $scope.roundCornerError = false


        $scope.vFn = () ->
            $scope.roundCornerError == false

        $scope.$watch 'job.RoundOption' , (n,o) ->
            if n == 0 || n == 3
                $scope.job.TRround = false
                $scope.job.TLround = false
                $scope.job.BRround = false
                $scope.job.BLround = false
            if n != o
                $scope.job.RoundDetailOption = 0
                $scope.job.CustomDieCut = ''
                updateCornersCount()


        updateCornersCount = () ->

            

            c = 0
            if $scope.job.TRround then c++
            if $scope.job.TLround then c++
            if $scope.job.BRround then c++
            if $scope.job.BLround then c++
            corners = c

        $scope.$watch 'job.TRround+job.TLround+job.BRround+job.BLround' , () ->
            updateCornersCount()

        $scope.$watch 'job.RoundDetailOption' , (n) ->
            rd = enums.ValueKey.RoundDetailOption[n]
            if !rd then return
            if rd is 'DieCut3mm4' or  rd is 'DieCut6mm4'
                $scope.job.TRround = true
                $scope.job.TLround = true
                $scope.job.BRround = true
                $scope.job.BLround = true
            else if rd is 'DieCut3mmTopLBottomR' or rd is 'DieCut6mmTopLBottomR'
                $scope.job.TRround = false
                $scope.job.TLround = true
                $scope.job.BRround = true
                $scope.job.BLround = false
            else if rd is 'DieCut24mmBottomR'
                $scope.job.TRround = false
                $scope.job.TLround = false
                $scope.job.BRround = true
                $scope.job.BLround = false


            if n && $scope.enums.ValueDesc.RoundDetailOption[n]?.indexOf('4 corners') > -1
                $scope.job.TRround = true
                $scope.job.TLround = true
                $scope.job.BRround = true
                $scope.job.BLround = true
                x.addClass('tl tr bl br')

            updateCornersCount()

            if rd.indexOf('3mm')  > -1 then x.removeClass('r3 r6 r10 r24').addClass('r3')
            if rd.indexOf('6mm')  > -1 then x.removeClass('r3 r6 r10 r24').addClass('r6')
            if rd.indexOf('6mm')  > -1 then x.removeClass('r3 r6 r10 r24').addClass('r6')
            if rd.indexOf('10mm') > -1 then x.removeClass('r3 r6 r10 r24').addClass('r10')
            if rd.indexOf('24mm') > -1 then x.removeClass('r3 r6 r10 r24').addClass('r24')

            if(rd == "DieCut3mm4" )
                x.removeClass('tl tr bl br')
                x.addClass('tl tr bl br')

            if(rd == "DieCut3mmTopLBottomR" )
                x.removeClass('tl tr bl br')
                x.addClass('tl br')

            if(rd == "DieCut6mm4" )
                x.removeClass('tl tr bl br')
                x.addClass('tl tr bl br')

            if(rd == "DieCut6mmTopLBottomR" )
                x.removeClass('tl tr bl br')
                x.addClass('tl br')

            if(rd == "DieCut24mmBottomR" )
                x.removeClass('tl tr bl br')
                x.addClass('br')

            validate()

        x.on('click', (e) ->
            ro =  $scope.job.RoundOption 
            if ro is 2
                return

            o = offset(x)
            posX = e.pageX - o.left
            posY = e.pageY - o.top

            w = x[0].clientWidth
            h = x[0].clientHeight

            xx = posX / w
            yy = posY / h
            if (xx < 0.3 && yy < 0.3) then x.toggleClass('tl')
            if (xx > 0.7 && yy > 0.7) then x.toggleClass('br')
            if (xx < 0.3 && yy > 0.7) then x.toggleClass('bl')
            if (xx > 0.7 && yy < 0.3) then x.toggleClass('tr')

            $scope.job.TRround =  x.hasClass('tr')
            $scope.job.TLround =  x.hasClass('tl')
            $scope.job.BRround =  x.hasClass('br')
            $scope.job.BLround =  x.hasClass('bl')

            updateCornersCount()
            validate()
            $scope.$apply()

            return
        )

        offset = (elm) ->
            try
                return elm.offset()
            catch e
            rawDom = elm[0]
            _x = 0
            _y = 0
            body = document.documentElement or document.body
            scrollX = window.pageXOffset or body.scrollLeft
            scrollY = window.pageYOffset or body.scrollTop
            _x = rawDom.getBoundingClientRect().left + scrollX
            _y = rawDom.getBoundingClientRect().top + scrollY
            {left: _x, top: _y}

        return
]

