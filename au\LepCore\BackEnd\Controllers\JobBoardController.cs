using lep.job;
using lep.jobmonitor;
using lep.jobmonitor.impl;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;

namespace LepCore.Controllers
{

	[Produces("application/json")]
	[Route("api/[controller]")]
	[Authorize(Roles = LepRoles.Staff)]
	[ApiExplorerSettings(IgnoreApi = true)]
	public class JobBoardController : Controller
	{
		public JobBoardController(JobBoard globalJobBoard)
		{
			GlobalJobBoard = globalJobBoard;
		}

		private JobBoard GlobalJobBoard { get; set; }

		[HttpGet("Get")]
		public IActionResult GetJobboard(string facilityStr = "FG", string board = "All")
		{
			if (GlobalJobBoard.Entries.Any())
			{
				Facility facility = Facility.PM;
				try
				{
					facility = (Facility)Enum.Parse(typeof(Facility), facilityStr);
				}
				catch (Exception)
				{
				}

				JobBoardTypes jobboard = JobBoardTypes.All;
				try
				{
					jobboard = (JobBoardTypes)Enum.Parse(typeof(JobBoardTypes), board);
				}
				catch (Exception)
				{
				}

				dynamic dataBeingSentToBrower = new
				{
					version = GlobalJobBoard.Version,
					data = GlobalJobBoard.GetEntiesAA(facility, jobboard)
				};

				return new OkObjectResult(dataBeingSentToBrower);
			}

			return new OkObjectResult(new { version = 0 });
		}

		[HttpGet("Version")]
		public string Version()
		{
			return GlobalJobBoard.Version.ToString();
		}


		[HttpGet("create")]
		[AllowAnonymous]
		public IActionResult Create([FromServices] JobBoardDTOHelper _)
		{
			_.CreateJobBoard();
			return Ok();
		}
	}
}
