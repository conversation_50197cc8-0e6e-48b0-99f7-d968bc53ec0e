appStaff = angular.module('app.staff')
###
FG-DISTRIB-02     ->  FG-BusinessCard
FG-DISTRIB-03     ->  FG-Brochure
PM-DISTRIB-01    -> PM-Despatch
PM-BINDARY-05    -> PM-Finishing

FG
FG.Compdata-02    for Connote/FreightLabel
FG.Compdata-03    for Connote/FreightLabel
FG.Dispatch_BW-02       for Menifest
FG.Dispatch_BW-03       for Menifest


PM
PM.Compdata label    for Connote/FreightLabel
PM.BusinesCardFurtherProcessingLabel
    on PM-BINDERY-05.internal.lepcolourprinters.com.au  for Connote/FreightLabel
PM.Dispatch_BW      for Menifest
TEST Print t mac address 94:dd:f8:14:da:5a
Brother HL-L5210DW series Printer

FG.Dispatch.Brochure
FG.Dispatch_BW-03

Brother HL-L5210DW series Printer (Copy 1)
FG.Dispatch.BusinessCard
FG.Dispatch_BW-02
###

appStaff.constant 'dispatchers', dispatchers =
    "FG-BusinessCard":
        Facility: "FG"
        Printers:
            PickUpLabel       : "FG.Compdata-02"
            AddressA4Label    : "FG.Dispatch.BusinessCard"
            AddressLabel      : "FG.Compdata-02"
            AddressLabelOther : "FG.Compdata-02"
            SampleLabel       : "FG.Compdata-02"
            LogoLabel         : "FG.Logo_Label"
            PayMe             : "FG.Compdata-02"
            AustraliaPost     : "FG.Compdata-02"
            PayMeLabel        : "FG.Compdata-02"
            FillingLabel      : "FG.Compdata-02"
            OneDeliveryOnly   : "FG.Compdata-02"
            Consignment       : "FG1"
    "FG-Brochure":
        Facility: "FG"
        Printers:
            PickUpLabel       : "FG.Carton_Label"
            AddressA4Label    : "FG.Dispatch.Brochure"
            AddressLabel      : "FG.Carton_Label"
            AddressLabelOther : "FG.Carton_Label"
            SampleLabel       : "FG.Carton_Label"
            LogoLabel         : "FG.Logo_Label"
            PayMe             : "FG.Carton_Label"
            AustraliaPost     : "FG.Carton_Label"
            PayMeLabel        : "FG.Carton_Label"
            FillingLabel      : "FG.Carton_Label"
            OneDeliveryOnly   : "FG.Carton_Label"
            Consignment       : "FG2"
    "PM-Despatch":
        Facility: "PM"
        Printers:
            PickUpLabel       : "PM.Compdata-02"
            AddressA4Label    : "PM.Despatch_BW"
            AddressLabel      : "PM.Compdata-02"
            AddressLabelOther : "PM.Compdata-02"
            SampleLabel       : "PM.Compdata-02"
            LogoLabel         : "PM.ColourLabel"
            PayMe             : "PM.Compdata-02"
            AustraliaPost     : "PM.Compdata-02"
            PayMeLabel        : "PM.Compdata-02"
            FillingLabel      : "PM.Compdata-02"
            OneDeliveryOnly   : "PM.Compdata-02"
            Consignment       : "PM1"
    "PM-Finishing":
        Facility: "PM"
        Printers:
            PickUpLabel       : "PM.Compdata-03"
            AddressA4Label    : "PM.Despatch_BW"
            AddressLabel      : "PM.Compdata-03"
            AddressLabelOther : "PM.Compdata-03"
            SampleLabel       : "PM.Compdata-03"
            LogoLabel         : "PM.ColourLabel"
            PayMe             : "PM.Compdata-03"
            AustraliaPost     : "PM.Compdata-03"
            PayMeLabel        : "PM.Compdata-03"
            FillingLabel      : "PM.Compdata-03"
            OneDeliveryOnly   : "PM.Compdata-03"
            Consignment       : "PM2"
    "DEV":
        Facility: "FG"
        Printers:
            PickUpLabel       : "Microsoft Print to PDF"
            AddressA4Label    : "Microsoft Print to PDF"
            AddressLabel      : "Microsoft Print to PDF"
            AddressLabelOther : "Microsoft Print to PDF"
            SampleLabel       : "Microsoft Print to PDF"
            LogoLabel         : "Microsoft Print to PDF"
            PayMe             : "Microsoft Print to PDF"
            AustraliaPost     : "Microsoft Print to PDF"
            PayMeLabel        : "Microsoft Print to PDF"
            FillingLabel      : "Microsoft Print to PDF"
            OneDeliveryOnly   : "Microsoft Print to PDF"
            Consignment       : "P1"


appStaff.controller 'DispatcherController', [
    '$scope','lepApi2',	'$location','enums', '$stateParams', 'Customers', 'Utils', '$http', '$state', 'JobService',	'OrderService', '$localStorage', 'ngDialog', '$q', '$window','dispatchers',
    ($scope,   lepApi2,   $location,  enums,  $stateParams,   Customers,   Utils,   $http,   $state,   JobService,	 OrderService, $localStorage, ngDialog, $q, $window, dispatchers ) ->

        #setInterval ()->
        #    angular.element('#edtSearch').focus()
        #, 5000
        $scope.dispatchers = dispatchers
        #$scope.edtSearch = 'J1730375'
        #$scope.edtSearch = 'J1430105'
        jobId = 0
        jobName = ""
        $scope.jobId = jobId
        $scope.jobName = jobName
        $scope.jobSendSample = false
        order = {}
        $scope.order     = order
        currDisp  = {}
        $scope.currDisp  = currDisp
        $scope.currDispName = $localStorage.currDispName || null
        $scope.currFacility = null
        $scope.sIdx = null
        $scope.$watch 'currDispName', (n,o) ->
            if !n then return
            $localStorage.currDispName = n
            $scope.currDisp = dispatchers[n]
            $scope.currFacility = currDisp.Facility
            focusJobScanInput()

        #if $scope.currDispName is 'DEV'
        #  $scope.edtSearch = 'J1103196'

        setStatus = (strId, scanner) ->
            sr =
                Scanner : scanner
                Barcode :strId
                Time : new DateTime()
            lepApi2.post('barcode/scan', sr)
            .then (r) ->

        $scope.setStatus = setStatus

        focusJobScanInput = () ->
            e = angular.element('#edtSearch')
            e.focus()
            e.select()

        $scope.scan = (id, bypassPopup) ->
            dispatchJobRequest =
                JobId   : id
                Printers: $scope.currDisp.Printers
                Facility: $scope.currDisp.Facility
            $localStorage.lastScannedJobId = id
            lepApi2.post("orders/DispatchJob2", dispatchJobRequest)
            .then (o) ->
                $scope.sIdx = null
                jobId = parseInt(id)
                $scope.jobId = id
                order = o
                p  = o.PackDetail
                isFG = $scope.currDisp.Facility == 'FG'
                $scope.totalWeight     = if isFG then p.FGWeight else p.PMWeight
                $scope.overrideCourier =  if isFG
                                              if p.IsFGCourierCustom then p.FGCourier else ''
                                          else
                                              if p.IsPMCourierCustom then p.PMCourier else ''
                $scope.packingStr2 = if isFG then p.FGPackageStr2 else p.PMPackageStr2
                $scope.order         = o
                job                  = _.find(o.Jobs, {Id: jobId})
                jobName              = job.Name
                $scope.jobName       = jobName
                $scope.jobSendSample = job.SendSamples
                if bypassPopup
                    focusJobScanInput()
                    return
                dialogOptions =
                    template        : 'dlgWhatToDo'
                    scope           : $scope
                    #className       : 'custom-width-900',
                    showClose       : true
                    closeByEscape   : true
                    closeByDocument : false
                    overlay         : false
                    width : '90%'
                    height: '90%'
                    onOpenCallback  : () ->
                        setTimeout () ->
                            angular.element('.current.bold')[0].scrollIntoView()
                        , 10
                dialog = ngDialog.open(dialogOptions)
                dialog.closePromise.then (r) ->
                    focusJobScanInput()
                return
            , (e) ->
                return

        $scope.scan2 = (id) ->
            d = $q.defer()
            dispatchJobRequest =
                JobId   : id
                Printers: $scope.currDisp.Printers
                Facility: $scope.currDisp.Facility
            $localStorage.lastScannedJobId = id
            lepApi2.post("orders/DispatchRefresh", dispatchJobRequest).then (o) ->
                jobId = parseInt(id)
                $scope.jobId = id
                order = o
                p  = o.PackDetail
                isFG = $scope.currDisp.Facility == 'FG'
                $scope.totalWeight     = if isFG then p.FGWeight else p.PMWeight
                $scope.overrideCourier = if isFG
                                            if p.IsFGCourierCustom then p.FGCourier else ''
                                        else
                                            if p.IsPMCourierCustom then p.PMCourier else ''
                $scope.packingStr2 = if isFG then p.FGPackageStr2 else p.PMPackageStr2
                $scope.order         = o
                job                  = _.find(o.Jobs, {Id: jobId})
                jobName              = job.Name
                $scope.jobName       = jobName
                $scope.jobSendSample = job.SendSamples
                d.resolve(true)
            d.promise

        $scope.showFreight = () ->
            $scope.orderIdPopup  = $scope.order.Id
            dialogOptions =
                template        : 'staff/orders/order-freight.html'
                controller: 'OrderFreightController',
                scope           : $scope
                className       : 'custom-width-900',
                showClose       : true
                #appendTo        :
                #closeByEscape   : true
                #closeByDocument : false
                #overlay         : false
            dialog = ngDialog.open(dialogOptions)
            dialog.closePromise.then (r) ->
                focusJobScanInput()

        $scope.$watch 'edtSearch', (n,o) ->
            if !n then return
            id = n.slice(1)
            if n[0].toLowerCase() is 'j'
                $scope.scan(id)

        $scope.print = (label, askHowmany, copies) ->
            printCore = (n) ->
                d = $q.defer()
                printerName = $scope.currDisp.Printers[label]
                url =  "orders/Order/DispatcherPrint"
                printReq =
                    label       : label
                    printerName : printerName
                    orderId     : order.Id
                    jobId       : jobId
                    sIdx        : $scope.sIdx
                    copies      : n
                    courier     : $scope.overrideCourier || order.Courier
                lepApi2.post(url, printReq).then ->
                    toastr.info  "#{label} printed on #{printerName}"
                    d.resolve(true)
                d.promise
            # sIdx = null
            # if order.HasSplitDelivery
            #     job = _.find(order.Jobs, {Id: jobId})
            #     if job.Splits.length
            #         sIdx = prompt("Which split to print? ")
            #         sIdx = parseInt(sIdx) - 1
            if !askHowmany
                printCore(copies || 1)
            else
                n = prompt("How many #{label} would you like to print?",copies)
                printCore(n)
            focusJobScanInput()


        $scope.setJobsInOrderToDispatched = (orderId, facility) ->
            d = $q.defer()
            url  = "orders/Order/#{orderId}/SetJobsInOrderToDispatched/#{facility}"
            lepApi2.post(url, {}).then ->
                toastr.success("Jobs marked Dispatched")
                d.resolve(true)
            d.promise


        $scope.sendViaSmartFreight = (carrierName, carrierService, pubDispatchFacility, n) ->
            d = $q.defer()
            if !carrierName or !carrierService or !pubDispatchFacility
                return $q.reject('no carrier')
            url = "orders/Order/CreateConsignment"
            nnn = carrierName.split(" ").join("").toUpperCase()
            pCfgName = $scope.currDisp.Printers.Consignment + "_" + nnn
            req =
                OrderId                : order.Id
                Facility               : pubDispatchFacility
                ConsignmentPrinterName : pCfgName
                JobId      : order.Jobs[0].Id
                SplitIdx   : gIdx
            lepApi2.post(url, req).then (r1) ->
                if !r1.Errors
                    if r1.Connote and r1.Connote.length
                        connote = r1.Connote[0]
                        toastr.success("Consignment # #{connote} created")
                        d.resolve(true)
                else
                    toastr.error(r1.Errors)
                    d.reject(false)
            d.promise


        $scope.sendViaPickup = () ->
            count = $scope.order.DispatchLabels.length
            $scope.print('PickUpLabel', true, count)
            url  = "orders/Order/#{$scope.order.Id}/sendPickupReadyEmail"
            lepApi2.post(url, {}).then (r1) ->
                $q.resolve(true)


        $scope.sendViaAusPostPrePayService = () ->
            d = $q.defer()
            auspostPrepaidLabelNumber = prompt('Australia Post Bag No:')
            orderConNoteDto =
                Id               : 0
                OrderId          : $scope.order.Id
                ConNote          : auspostPrepaidLabelNumber
                IsEmailGenerated : 0
                DispatchFacility : currDisp.Facility
                CarrierName      : 'AUSTRALIA POST'
                CarrierService   : 'PREPAID'
            lepApi2.post("orders/Order/AttachConnote", orderConNoteDto).then (r) ->
                if !r
                    d.reject("failed to create connote with AustraliaPost Prepaid label number")
                else
                    count = $scope.order.DispatchLabels.length
                    $scope.print('AddressLabel', false, count).then (r) ->
                        d.resolve(true)
            d.promise


        $scope.sendViaOtherService = () ->
            $scope.print("AddressLabelOther", false, 1)
            $q.resolve(true)
            ###
            d = $q.defer()
            auspostPrepaidLabelNumber = prompt('Australia Post Bag No:')
            orderConNoteDto =
              Id               : 0
              OrderId          : $scope.order.Id
              ConNote          : auspostPrepaidLabelNumber
              IsEmailGenerated : 0
              DispatchFacility : currDisp.Facility
              CarrierName      : 'AUSTRALIA POST'
              CarrierService   : 'PREPAID'

            lepApi2.post("orders/Order/AttachConnote", orderConNoteDto).then (r) ->
                if !r
                  d.reject("failed to create connote with AustraliaPost Prepaid label number")
                else
                  count = $scope.order.DispatchLabels.length
                  $scope.print('AddressLabel', false, count).then (r) ->
                    d.resolve(true)
            d.promise
            ###

        $scope.btnGenerateConsignmentForOrderClick = () ->
            count = $scope.order.DispatchLabels.length
            ########################## now print logo
            if order.CustomerLogoRequired
               $scope.print('LogoLabel', true, count)
            ################### now print send samples
            if _.some(order.Jobs, {SendSamples: true})
                prompt = 'A job in this order requires samples to be sent.\nWould you like to print the Sample Label now?'
                if confirm(prompt, count)
                    $scope.print('SampleLabel', true, count)
            #
            if $scope.overrideCourier.indexOf('None ~ None') > -1
                $scope.overrideCourier = null
            #
            courier = $scope.overrideCourier || $scope.order.Courier
            sendViaPromise = null
            if /pickup/i.test(courier)
                sendViaPromise = $scope.sendViaPickup()
            else if /australia post/i.test(courier) and (/prepaid/i.test(courier))
                sendViaPromise = $scope.sendViaAusPostPrePayService()
            else if /Other /i.test(courier)
                sendViaPromise = $scope.sendViaOtherService()
            else
                ss = courier.split(" ~ ")
                sendViaPromise = $scope.sendViaSmartFreight(ss[0], ss[1], $scope.currDisp.Facility,0)

            sendViaPromise.then () ->
                $scope.setJobsInOrderToDispatched(order.Id, $scope.currDisp.Facility).then (rr) ->
                    #UpdateOrder_DispatchLabel(pubOrderID);
                    $scope.scan2(jobId, true).then ()->
                        order.Visibility.btnGenerate = false
            focusJobScanInput()


        gIdx = 0
        $scope.btnGenerateConsignmentForJobWithSplitDeliveryClick = () ->
            if !order.HasSplitDelivery then return
            job = order.Jobs[0]
            count = job.Splits.length

            ########################## now print logo
            # if order.CustomerLogoRequired
            #    $scope.print('LogoLabel', true, count)


            ################### now print send samples
            # if _.some(order.Jobs, {SendSamples: true})
            #     prompt = 'A job in this order requires samples to be sent.\nWould you like to print the Sample Label now?'
            #     if confirm(prompt, count)
            #         $scope.print('SampleLabel', true, count)
            #
            # for s,gIdx in job.Splits
            #     courier = s.Courier
            #     sendViaPromise = null
            #     if /pickup/i.test(courier)
            #         sendViaPromise = $scope.sendViaPickup()
            #     else if /australia post/i.test(courier) and (/prepaid/i.test(courier))
            #         sendViaPromise = $scope.sendViaAusPostPrePayService()
            #     else if /Other /i.test(courier)
            #         sendViaPromise = $scope.sendViaOtherService()
            #     else
            #         ss = courier.split(" ~ ")
            #         sendViaPromise = $scope.sendViaSmartFreight(ss[0], ss[1], $scope.currDisp.Facility,0, gIdx)
            d = $q.defer()
            url = "orders/Order/CreateConsignment"
            pCfgName = $scope.currDisp.Printers.Consignment
            req =
                OrderId                : order.Id
                Facility               : $scope.currDisp.Facility
                JobId                  : order.Jobs[0].Id
                ConsignmentPrinterName : pCfgName
            lepApi2.post(url, req).then (r1) ->
                if !r1.Errors
                    if r1.Connote and r1.Connote.length
                        connote = r1.Connote[0]
                        toastr.success("Consignment # #{connote} created")
                        $scope.setJobsInOrderToDispatched(order.Id, $scope.currDisp.Facility).then (rr) ->
                            $scope.scan2(jobId, true).then ()->
                                order.Visibility.btnGenerate = false
                        focusJobScanInput()
                        d.resolve(true)
                else
                    toastr.error(r1.Errors)
                    d.reject(false)
            d.promise

        angular.element($window).on  "focus", (e) ->
            if !jobId then return
            $scope.scan2(jobId, true)


        $scope.syncConnotes = () ->
            url  = "orders/Order/#{order.Id}/SyncConnotes"
            lepApi2.post(url, {}).then (r2) ->
                $scope.scan2(jobId)
                toastr.success("Connotes Synchronised")


        return
    ]

