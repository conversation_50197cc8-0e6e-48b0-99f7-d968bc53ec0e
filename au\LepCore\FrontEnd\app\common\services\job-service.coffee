do(window, angular) ->
    app = angular.module('app')

    tOrderId = do () ->
        x = [1, 26, 27, 7, 18, 23, 61, 62, 63, 15, 8, 22, 14, 28, 29, 9, 21, 10, 11, 16, 37, 32, 33, 34, 35, 41, 42, 43, 44, 51, 52, 53]
        (id) ->
            i = x.indexOf(id)
            if i == -1 then i = 100
            i

        ###
        app.directive 'checkForUnfilled', ->
            {
            restrict: 'EA'
            link: (scope, element, attr) ->
                    observer = new MutationObserver((mutations) ->
                        mutations.forEach (mutation) ->
                            #cd mutation.target.nodeName, mutation.type, mutation
                        )
                    config =
                        childList: false
                        attributes: true
                        characterData: false
                        subtree: true
                        attributeOldValue: false
                        characterDataOldValue: false
                    observer.observe element[0], config
                    return
            }
        ###

    app.factory 'Utils',[ 'enums', 'ngDialog', (enums, ngDialog) ->
        jto = enums.KeyVal.JobTypeOptions

        @isTemplateMagazine = (templateId) ->
            [jto.MagazineSeparate, jto.Magazine, jto.MagazineNDD, jto.A4CalendarSelfCover, jto.A4CalendarSeparateCover, jto.WiroMagazines].indexOf(templateId) > -1

        @isTemplateNCR = (templateId) ->
            [jto.DuplicateNCRBooks, jto.TriplicateNCRBooks, jto.QuadruplicateNCRBooks].indexOf(templateId) > -1

        @isTemplateBrochure = (templateId) ->
            [jto.Brochure, jto.BrochureSpecial , jto.BrochureSDD, jto.TentCalendars, jto.DLCalendars, jto.GreetingCards ].indexOf(templateId) > -1

        @isTemplatePresentationFolder = (templateId) ->
            [jto.Brochure, jto.BrochuPresentationFolder, jto.PresentationFolderNDD].indexOf(templateId) > -1

        @getRequiredPositions = (job) ->
            positions = []
            if [jto.Magazine,jto.MagazineNDD, jto.MagazineSeparate].indexOf(job.Template?.Id || job.TemplateId) > -1
                positions.push("Cover")
                positions.push("Text")

                #if job.BindingOption?.Id > 1
                if job.FrontCelloglaze and job.FrontCelloglaze is 10 #EmbossedMattFront
                    positions.push("Spot UV")
                #if job.Template.Id == jto.MagazineSeparate
                #	positions.push 'front_outside'
                #	positions.push 'back_outside'
                #	if job.BackPrinting != enums.KeyVal.JobPrintOptions.Unprinted
                #		positions.push 'front_inside'
                #		positions.push 'back_inside'
                #i = 1
                #while i <= job.Pages
                #	positions.push i
                #	i++
            else
                if job.DieCutType == enums.KeyVal.CutOptions.Custom
                    positions.push 'diecut'
                if job.FrontPrinting != enums.KeyVal.JobPrintOptions.Unprinted
                    positions.push 'front'
                if job.BackPrinting != enums.KeyVal.JobPrintOptions.Unprinted
                    positions.push 'back'
                if [jto.VinylSticker].indexOf(job.Template?.Id || job.TemplateId) > -1 and job.FinishedSize?.PaperSize?.Name is 'Custom'
                    positions.push 'Cut Contour'
            positions

        @browseForFile = () -> ngDialog.openConfirm(template: 'common/filebrowser.html')
        @browseForLogoFile = () -> ngDialog.openConfirm(template: 'common/filebrowser-logo.html')
        @pos2label = (str) ->
            display = ""
            switch str
                when 'multiart'
                    display = 'Multi page'
                when 'front_outside'
                    display = 'Outside front'
                when 'back_outside'
                    display = 'Outside back'
                when 'front_inside'
                    display = 'Inside front'
                when 'back_inside'
                    display = 'Inside back'
                when 'front'
                    display = 'Front'
                when 'back'
                    display = 'Back'
                when 'Cover'
                    display = 'Cover'
                when 'Text'
                    display = 'Text'
                when 'Spot UV'
                    display = 'Spot UV'
                when 'diecut'
                    display = 'Custom die cut'
                when 'Cut Contour'
                    display = 'Cut Contour'
                else
                    display = 'Page ' + str
                    break
            display

        @ #important
    ]

    ###
    app.factory 'blankJob', ['OrderService' ,(OrderService) ->
            angular.copy(window.blankjob)
    ]
    ###

    app.service 'JobService', [
        '$http', 'lepApi2', '$q', 'enums','Utils','$rootScope', '$timeout', '$log',
        ($http, lepApi2, $q, enums, utils, $root, $timeout, $log) ->
            apiBase = window.apiBase
            t = enums.KeyVal.JobTypeOptions
            ro = enums.KeyVal.RoundOption
            rdo = enums.KeyVal.RoundDetailOption

            @sortedTempalates = null

            templatesToHide = [3,4,5,6,13,6020, 12, 30, 2 ,20 ,25 ,38 ,19 ,24]
            @getTemplates = (searchParams) ->
                if @sortedTempalates
                    $q.when( @sortedTempalates )
                else
                    lepApi2.get('JobOptions/Templates', null, true).then (ts) ->
                        @sortedTempalates = _.sortBy(ts, (t) -> tOrderId(t.Id))
                        @sortedTempalates = _.reject @sortedTempalates, (x) ->
                            templatesToHide.indexOf(x.Id) > -1
                        return @sortedTempalates




            @getTemplates2 = () ->
                lepApi2.get('JobOptions/Templates', null, true).then (ts) ->
                    return ts

            # a Y-combinator that checks passed in aruments are truish (not 0, null or undefined)
            # returns a rejected promise if so
            maybe = (fn) ->
                () ->
                    if arguments.length == 0 then return
                    i = 0
                    while i < arguments.length
                        if !arguments[i++]
                            return $q.when([])
                    fn.apply this, arguments

            @getPrintTypes = (templateId, paperSizeId, stockId, quantity, job) ->
                if !quantity then quantity = 0
                if !templateId or !paperSizeId or !stockId then return $q.when()
                if job?.FinishedSize?.PaperSize?.Name is 'Custom'
                    # and only if not the following Wide format or envelope types
                    if [10,32,33,34,36,37,38,41,42,43,44,51,52,53,61,62,63].indexOf(templateId) == -1
                        s = job?.FinishedSize
                        if ( Math.max(s.Width, s.Height) > 430 or Math.min(s.Width, s.Height) > 300)
                            return $q.when('O')
                # LORD-1210
                # if templateId in [8,14,22]
                #     t1 = job?.Stock?.Thickness
                #     pp = job?.Pages || 0
                #     t2 = (job?.StockForCover?.Thickness || 0) * 2
                #     if t1 and pp
                #         thickness = (t1 * (pp/2)) + t2
                #         if thickness > 2.85
                #             return $q.when('O')
                lepApi2.get "JobOptions/GetPrintTypes/Template/#{templateId}/SizeOptions/#{paperSizeId}/stock/#{stockId}/quantity/#{quantity}" , null, true

            @getPrintTypes0 = maybe (templateId, paperSizeId, stockId) ->
                quantity = 0
                lepApi2.get "JobOptions/GetPrintTypes/Template/#{templateId}/SizeOptions/#{paperSizeId}/stock/#{stockId}/quantity/#{quantity}", null, true


            @getAllSizes = (searchParams) ->
                lepApi2.get 'JobOptions/Sizes', null, true

            @getAllStocks = (searchParams) ->
                lepApi2.get 'JobOptions/Stocks' , null, true

            @getAllStocksForRuns = (searchParams) ->
                lepApi2.get 'JobOptions/StocksForRuns' , null, true

            @getAllCellosForOverride = () ->
                lepApi2.get 'JobOptions/GetAllCellosForOverride', null, true

            @getSizeOptions = maybe (templateId) ->
                if templateId != 12
                    d = $q.defer()
                    lepApi2.get("JobOptions/GetSizeOptions/Template/#{templateId}" , null, true).then (r) ->
                        if $root.globals.IsSubCustomer
                            WhiteLabelAllowedProducts  = $root.globals?.User?.PrintPortalSettings?.WhiteLabelAllowedProducts
                            if WhiteLabelAllowedProducts
                                sizesAllowed = _(WhiteLabelAllowedProducts).filter({T:templateId}).map('P').uniq().value()
                                if sizesAllowed.length
                                    r = _.filter(r, (x) -> x.Id in sizesAllowed)
                                d.resolve(r)

                        d.resolve(r)
                    return d.promise
                else
                    return @getAllSizes()

            @getSizeOptions2 = maybe (templateIds) ->
                tids = templateIds.join('&')
                return lepApi2.get "JobOptions/GetSizeOptions/Templates?templateId=#{tids}", null, true

            @getStocksForCover = maybe (textStockId) ->
                lepApi2.get "JobOptions/GetStocksForCover/TextStock/#{textStockId}" , null, true

            @getStocksForCoverOver250 = () ->
                lepApi2.get "JobOptions/GetStocksForCover/250" , null, true

            @getStockOptions = maybe (templateId, paperSizeId) ->
                if templateId != 12
                    d = $q.defer()
                    lepApi2.get("JobOptions/GetStockOptions/Template/#{templateId}/SizeOptions/#{paperSizeId}" , null, true).then (r) ->
                        if $root.globals.IsSubCustomer
                            WhiteLabelAllowedProducts  = $root.globals?.User?.PrintPortalSettings?.WhiteLabelAllowedProducts
                            if WhiteLabelAllowedProducts
                                stocksAllowed = _(WhiteLabelAllowedProducts).filter({T:templateId, P: paperSizeId}).map('S').uniq().value()
                                if stocksAllowed.length
                                    r = _.filter(r, (x) -> x.Id in stocksAllowed)
                                d.resolve(r)

                        d.resolve(r)
                    return d.promise
                else
                    return @getAllStocks()


            @getStockOptionsOnTemplate = _.memoize maybe (templateId) ->
                if templateId != 12
                    return lepApi2.get "JobOptions/GetStockOptions/Template/#{templateId}", null, true
                else
                    return @getAllStocks()

            @getStockOption = maybe (templateId, paperSizeId, stockId) ->
                lepApi2.get "JobOptions/GetStockSpec/Template/#{templateId}/SizeOptions/#{paperSizeId}/stock/#{stockId}", null, true

            @getFoldOptions = maybe (specStockId) ->
                lepApi2.get "JobOptions/GetFoldOptions/SpecStock/#{specStockId}", null, true

            @getCelloOptions = maybe (specStockId, isNormal) ->
                lepApi2.get "JobOptions/GetCelloOptions/#{specStockId}/#{isNormal}", null, true

            @getColourOptions = maybe (specStockId) ->
                lepApi2.get "JobOptions/getColourOptions/#{specStockId}"

            @getBindingOptions = maybe (templateId, paperSizeId, stockId, printType, pages) ->
                lepApi2.get "JobOptions/GetBindingOptions/Template/#{templateId}/SizeOptions/#{paperSizeId}/stock/#{stockId}/PrintType/#{printType}/pages/#{pages}", null, true

            @getSuburbs = (postcode) ->
                lepApi2.get "/Postcode/GetSuburbs/#{postcode}"

            @getPrice = (vm) ->
                lepApi2.post("JobPrice/GetPrice", vm)

            @getPrice2 = (vm) ->
                lepApi2.post("JobPrice/GetPrice2", vm)

            @getPrice3 = (vm) ->
                lepApi2.post("JobPrice/GetPrice3", vm)


            @getBrohureMailHouses = (tId) ->
                d = $q.defer()
                lepApi2.get "JobOptions/GetBrohureMailHouses", null, true
                    .then (r) ->
                        if tId in [8,14]
                            _.forEach(r, (v) ->  v.Instructions = v.Instructions.replace(/flyers/g, 'magazines'))
                        r = _.reject(r, (x)->  x.Id in [3,4,5])
                        d.resolve(r)
                return d.promise


            @getFoilColours = () ->
                [
                    'Gloss Silver (AL)'
                    #'Black (812)'
                    'Copper (396)'
                    'Electric Blue (391)'
                    'Magenta (360)'
                    'Red (307)'
                    'Navy Blue ( 302)'
                    'Mat Gold (428)'
                    'Rose Gold'
                    'Gloss Gold (220)'
                    'Mat Silver (ALMT)'
                    #'Reflex Holographic'
                    #'Clear Foil'
                ]

            @getEnvelopes = () -> ['White', 'Green', 'Red']

            # @getMyob = (job) ->
            #     req =
            #         Id                 : job.Id
            #         Template           : {Id: job.Template.Id}
            #         FinishedSize       : {PaperSize: { Id: job.FinishedSize.PaperSize.Id}, Width: job.FinishedSize?.Width, Height: job.FinishedSize?.Height}
            #         FoldedSize         : {PaperSize: { Id: job.FoldedSize?.PaperSize?.Id || 0}, Width: job.FoldedSize?.Width, Height: job.FoldedSize?.Height}
            #         Stock              : {Id:  job.Stock.Id  }
            #         BindingOption      : {Id:  job.BindingOption?.Id || 0 }
            #         Quantity           : job.Quantity
            #         PrintType          : job.PrintType
            #         FrontPrinting      : job.FrontPrinting
            #         BackPrinting       : job.BackPrinting
            #         FrontCelloglaze    : job.FrontCelloglaze
            #         BackCelloglaze     : job.BackCelloglaze
            #         Magnet             : job.Magnet                 || false
            #         Pages              : job.Pages                  || 0
            #     lepApi2.post("JobPrice/GetMyob", req)

            @getPricePoints = (basicJobDeails) ->
                lepApi2.post('JobPrice/GetPricePointsForJobType', basicJobDeails)

            __ = @
            # get a job given its id
            @getJob = (jobId, orderId) ->
                if !jobId
                    u = "Orders/job/blank?"
                    if orderId
                        u+= "orderId=#{orderId}"
                    return lepApi2.get(u,null,false).then (blankjob) ->
                        blankjob.FrontCelloglaze = null
                        blankjob.BackCelloglaze = null
                        blankjob.FrontPrinting = null
                        blankjob.BackPrinting = null
                        blankjob.OrderId = orderId
                        blankjob
                else
                    # optimisation: when getting a job, fire call calls to get details too
                    return lepApi2.get("Orders/job/#{jobId}").then (job) ->
                        tId = job.Template?.Id
                        psId = job.FinishedSize?.PaperSize?.Id
                        sId = job.Stock?.Id
                        qty = job.Quantity

                        $q.all([
                            __.getSizeOptions( tId ),
                            __.getStockOptions(tId, psId),
                            __.getStockOption(tId, psId, sId),
                            __.getPrintTypes(tId, psId, sId, qty)
                        ]).then () ->
                            return job

            @jobsWithoutRotation = [t.BusinessCard, t.DoubleBusinessCard, t.BusinessCardNdd, t.BusinessCardSdd ,t.PresentationFolder, t.PresentationFolderNDD]

            @getVisibilityByTemplate = (templateId, job) ->
                vis = {}
                vis.stock       = true
                vis.printType   = true
                if [t.DuplicateNCRBooks, t.TriplicateNCRBooks, t.QuadruplicateNCRBooks].indexOf(templateId)> -1
                    vis.stock       = false
                    vis.printType   = false
                vis.qtyList = [t.DLSpecial, t.BrochureSpecial].indexOf(templateId) > -1
                vis.qty = !(vis.qtyList)
                vis.round = [t.BusinessCard, t.FridgeMagnet].indexOf(templateId) > -1
                vis.roundDetail = vis.round && job.RoundOption != 0 && job.RoundOption != 3 && job.RoundOption != 5
                vis.magnet = [t.Custom, t.BusinessCard, t.Postcard, t.FridgeMagnet].indexOf(templateId) > -1
                vis.fold = [t.Custom, t.Brochure, t.BrochureNDD, t.BrochureSDD, t.Magazine, t.GolfScoreCards, t.TentCalendars, t.DLCalendars, t.GreetingCards].indexOf(templateId) > -1
                vis.scoreReqd = false
                vis.score = [t.Brochure, t.BrochureSDD, t.GolfScoreCards, t.BusinessCard, t.DoubleBusinessCard, t.BusinessCardNdd, t.BusinessCardSdd ].indexOf(templateId) > -1
                vis.perforate = [t.Brochure].indexOf(templateId) > -1
                vis.coverstock = [t.MagazineSeparate,  t.A4CalendarSeparateCover].indexOf(templateId) > -1
                
                vis.coverstockBack = false # [t.WiroMagazines].indexOf(templateId) > -1
                vis.wiro = [t.WiroMagazines].indexOf(templateId) > -1
                
                vis.page = [t.MagazineSeparate, t.WiroMagazines, t.Magazine, t.MagazineNDD, t.Notepads, t.DuplicateNCRBooks, t.TriplicateNCRBooks, t.QuadruplicateNCRBooks,
                    t.A4CalendarSelfCover, t.A4CalendarSeparateCover].indexOf(templateId) > -1

                vis.diecut = [t.PresentationFolder, t.PresentationFolderNDD].indexOf(templateId) > -1
                vis.diecutTentCalendars = [t.TentCalendars].indexOf(templateId) > -1
                vis.binding = [t.WiroMagazines, t.Magazine, t.MagazineSeparate,  t.WiroMagazines,  t.MagazineNDD, t.A4CalendarSelfCover, t.A4CalendarSeparateCover].indexOf(templateId) > -1

                vis.boundEdge = vis.binding or [t.WiroMagazines, t.DuplicateNCRBooks, t.TriplicateNCRBooks, t.QuadruplicateNCRBooks].indexOf(templateId)> -1
                vis.NCRNumbered = [t.DuplicateNCRBooks, t.TriplicateNCRBooks, t.QuadruplicateNCRBooks].indexOf(templateId)> -1
                vis.hole = [t.BusinessCard, t.Postcard, t.MagazineSeparate, t.Magazine, t.A4CalendarSelfCover, t.A4CalendarSeparateCover].indexOf(templateId) > -1

                vis.cello = [t.Custom, t.PresentationFolder, t.PresentationFolderNDD, t.Poster, t.DL, t.Brochure, t.BusinessCard, t.BusinessCardNdd, t.BusinessCardSdd,
                    t.Postcard, t.FridgeMagnet, t.Magazine, t.MagazineSeparate, t.A4CalendarSeparateCover, t.TentCalendars, t.DLCalendars, t.GreetingCards].indexOf(templateId) > -1

                vis.finishedBy = [t.Notepads].indexOf(templateId) > -1
                vis.envelope = job.FinishedSize?.PaperSize?.Name?.indexOf('Invitation Pack') == 0
                vis.sendSamples = [t.EnvelopeBlack, t.Envelope1Pms, t.Envelope2Pms, t.EnvelopeCmyk, t.BacklitPosters, t.MeshBanner, t.VinylOutdoor, t.PosterSilk, t.PosterCanvas, t.PullUpBannerStandardStand, t.PullUpBannerPremiumStand, t.BumperSticker, t.RepositionalWallCovering, t.VinylStickerOutdoor, t.DuplicateNCRBooks, t.TriplicateNCRBooks, t.QuadruplicateNCRBooks].indexOf(templateId) == -1

                if job.PrintType is 'W' or !job.PrintType
                    vis.sendSamples = false

                vis.envelopeType= [t.EnvelopeBlack, t.Envelope1Pms, t.Envelope2Pms, t.EnvelopeCmyk].indexOf(templateId) > -1
                vis.portrait = true
                vis.landscape = true

                if job && (job.FrontCelloglaze == 5 || job.FrontCelloglaze == 7)
                    vis.foil = true
                else
                    vis.foil = false

                if !job
                    vis.rotation = false
                else
                    if job.Template.Id == 0
                        vis.rotation = false
                    else
                        vis.rotation = true
                        if job.Template.Id && @jobsWithoutRotation.indexOf(job.Template.Id) > -1
                            vis.rotation = false

                if [t.A4CalendarSelfCover, t.A4CalendarSeparateCover].indexOf(templateId) > -1
                    vis.portrait = false

                for r in @rules
                    r(job, vis)
                vis

            @getNumberOfPagesByTemplate = (j) ->
                numberOfPages = {}
                numberOfPages[t.A4CalendarSelfCover] = [28]
                numberOfPages[t.A4CalendarSeparateCover] = [24]

                numberOfPages[t.Magazine] = _.range(8,200+4,4)
                numberOfPages[t.Magazine] = _.range(8,100+4,4)
                numberOfPages[t.MagazineSeparate] = _.range(4,200+4,4)
                
                numberOfPages[t.WiroMagazines] = _.range(4,134+2, 2)

                numberOfPages[t.MagazineNDD] = _.range(8,36,4)
                numberOfPages[t.Notepads] = [25,50,100]
                numberOfPages[t.DuplicateNCRBooks]= [50,100]
                numberOfPages[t.TriplicateNCRBooks]= [50]
                numberOfPages[t.QuadruplicateNCRBooks]= [50]
                if (j?.Template?.Id is t.Notepads)
                    #if ['A4 Notepads', 'A5 Notepads', 'A6 Notepads', 'DL Notepads'].indexOf(j?.FinishedSize?.PaperSize?.Name ) > -1
                    if [24,25,26,50].indexOf(j?.FinishedSize?.PaperSize?.Id ) > -1
                        numberOfPages[t.Notepads] = [50,100]
                numberOfPages


            @getFixedQuantityByTemplate = (templateId ) ->
                qtylistValues = {}
                #qtylistValues[t.DL             ] = [1000,2000,5000,10000] # todo ask
                #qtylistValues[t.Brochure       ] = [2000,5000,10000]
                qtylistValues[t.DLSpecial ] = [2000]
                qtylistValues[t.BrochureSpecial ] = [1000]
                qtylistValues

            @getHolesListByJobTemplate = (templateId) ->
                holesList = angular.copy(enums.ValueDesc.HoleDrilling)
                # magazines do not have 8mm
                #if [t.Magazine, t.MagazineSeparate,t.MagazineNDD].indexOf(templateId) > -1
                #   delete holesList[5]
                holesList

            @getCutOptionsByTemplate = (templateId, job) ->
                cutValues = {}
                cutValues[t.PresentationFolder ] = [1,41,2,42,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3]
                cutValues[t.PresentationFolderNDD ] = [1,41,2,42,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]
                cutValues[t.TentCalendars ] = [31,3]

                if job
                    psName = job.FinishedSize?.PaperSize?.Name
                    if [t.PresentationFolder,t.PresentationFolderNDD].indexOf(job.Template?.Id) > -1
                        if psName is 'A4'
                            cutValues[templateId] = _.reject(cutValues[templateId], (x) -> [4,5].indexOf(x) > -1)

                        if psName is 'A5'
                            cutValues[templateId] = _.reject(cutValues[templateId], (x) -> x>=6 and x <=26 ) # remove all A4
                            if job.PrintType is 'D'
                                cutValues[templateId] = _.reject(cutValues[templateId], (x) ->[4,5].indexOf(x) > -1)

                    if templateId is t.TentCalendars and psName is 'Custom'
                        cutValues[templateId] = _.reject(cutValues[templateId], (x) -> x != 3)

                cutValues


            @getLabelsByJobTemplate = (n, j) ->
                n = n || 7
                labeldata = {}
                labeldata["size" ] = "Trim Size"
                labeldata["cello" ] = "Finish"
                labeldata["frontprint" ] = "Front"
                labeldata["backprint" ] = "Back"
                labeldata["stock" ] = "Stock"
                labeldata["fold" ] = "Fold"
                labeldata["pages" ] = "No. Pages"
                labeldata["quantity" ] = "Quantity"
                labeldata["nofold" ] = "Don't Fold"
                labeldata["portrait" ] = "Portrait"
                labeldata["landscape" ] = "Landscape"

                labeldata["width"] = "W";
                labeldata["height"] = "H";

                labeldata["coverstock"] = "Cover Stock"

                if ([t.Magazine, t.MagazineSeparate, t.MagazineNDD, t.A4CalendarSelfCover, t.A4CalendarSeparateCover ].indexOf(n) > -1)

                    #labeldata["portrait"  ] = "Portrait (Bound on long edge)"
                    #labeldata["landscape" ] = "Landscape (Bound on short edge)"

                    #if j?.FinishedSize?.PaperSize?.Name == 'Custom'
                    #    labeldata["portrait"  ] = "Bound on long edge"
                    #    labeldata["landscape" ] = "Bound on short edge"
                    #    labeldata["width"  ] = "Bound Edge"
                    #    labeldata["height"  ] = "Head/Tail Edge"

                    labeldata["cello" ] = "Outside/Inside cover celloglaze"
                    labeldata["frontprint"] = "Outside cover"
                    labeldata["backprint" ] = "Inside cover"
                    labeldata["stock" ] = "Text Stock"
                    labeldata["fold" ] = "Crash Folding"
                    labeldata["nofold" ] = "Don't Fold"
                    labeldata["pages" ] = "Number of Pages"
                if ([t.MagazineSeparate, t.A4CalendarSeparateCover ].indexOf(n) > -1)
                    labeldata["pages"] = "Number of Pages"
                    labeldata["pages1"] = "(Excluding Covers)"

                if ([t.WiroMagazines].indexOf(n) > -1)
                    labeldata["cello" ] = "Inner Front/Back cover celloglaze"
                    labeldata["frontprint"] = "Inner Front Print "
                    labeldata["backprint" ] = "Inner Back Print"
                    labeldata["stock" ] = "Text Stock"
                    labeldata["fold" ] = "Crash Folding"
                    labeldata["nofold" ] = "Don't Fold"
                    labeldata["pages"] = "Number of Pages"
                    labeldata["pages1"] = "(Excluding Covers)"
                    labeldata["coverstock"] = "Inner Front Cover Stock"
                    labeldata["coverstockBack"] = "Inner Back Cover Stock"

                if ([t.PresentationFolder].indexOf(n) > -1)
                    labeldata["cello"] = "Finish";
                    labeldata["frontprint"] = "Outside";
                    labeldata["backprint"] = "Inside";

                if ([t.BusinessCard, t.BusinessCardNdd, t.BusinessCardSdd, t.GolfScoreCards ].indexOf(n) > -1)
                    labeldata["fold"] = "Score";
                    labeldata["nofold"] = "Don't Score";

                if ([t.Notepads].indexOf(n) > -1)
                    labeldata["quantity"] = "Number of Pads";
                    labeldata["pages"] = "Number of Sheets/pad";

                if ([t.DuplicateNCRBooks, t.TriplicateNCRBooks, t.QuadruplicateNCRBooks].indexOf(n) > -1)
                    labeldata["quantity"] = "Number of Pads";
                    labeldata["pages"] = "Number of Sheets/pad";
                    labeldata["frontprint" ] = "Ink Color"

                #template option text
                labeldata[ t.BusinessCard ] = "";
                labeldata[ t.BusinessCardNdd ] = "Business Card (next day dispatch)";
                labeldata[ t.BusinessCardSdd ] = "Business Card (same day dispatch)";
                labeldata[ t.DL ] = "DL (Full Range)";
                labeldata[ t.DLSpecial ] = "DL Next Day Dispatch";
                labeldata[ t.BrochureSpecial ] = "Brochure Next Day Despatch";
                labeldata[ t.Brochure ] = "Brochure (Full Range)";
                labeldata[ t.PresentationFolder ] = "";
                labeldata[ t.Letterhead ] = "";
                labeldata[ t.Magazine ] = "Self Covered";
                labeldata[ t.MagazineNDD ] = "Magazine/Booklet (next day dispatch)";
                labeldata[ t.MagazineSeparate ] = "Separate Cover";
                labeldata[ t.Poster ] = "";
                labeldata[ t.Postcard ] = "";
                labeldata[ t.Custom ] = "";
                labeldata[ t.Letterhead ] = "Letterhead";
                labeldata[ t.Compliments ] = "With Compliments Slips";
                labeldata[ t.GolfScoreCards ] = "";
                labeldata[ t.Notepads ] = "";
                labeldata[ t.FridgeMagnet ] = "Fridge Magnet";
                labeldata[ t.BrochureNDD ] = "Brochure (next day dispatch)";
                labeldata[ t.BrochureSDD ] = "Brochure (same day dispatch)";
                labeldata[ t.LetterheadNDD ] = "Letterhead (next day dispatch)";
                labeldata[ t.LetterheadSDD ] = "Letterhead (same day dispatch)";
                labeldata[ t.ComplimentsNDD ] = "With Comps Slips (next day dispatch)";
                labeldata[ t.ComplimentsSDD ] = "With Comps Slips (same day dispatch)";
                labeldata[ t.PresentationFolderNDD] = "Presentation Folder (next day dispatch)";
                labeldata["perforate"] = "Perforating"
                labeldata["score"] = "Scoring"

                return labeldata

            @templates = []
            @getTemplates().then (d) =>
                @templates = d

            @getMaximumQuantityFromTemplate = (templateId) ->
                if [t.BusinessCardNdd, t.BusinessCardSdd, t.PresentationFolderNDD, t.Notepads].indexOf(templateId) > -1
                    return 1000
                if [t.BrochureNDD, t.LetterheadNDD, t.ComplimentsNDD, t.LetterheadSDD, t.ComplimentsSDD ].indexOf(templateId) > -1
                    return 10000
                if [t.BrochureSDD, t.MagazineNDD ].indexOf(templateId) > -1
                    return 2000
                if [t.PullUpBanner, t.BacklitPosters, t.MeshBanner, t.VinylOutdoor, t.PosterCanvas ].indexOf(templateId) > -1
                    return 5
                if [t.PosterSilk].indexOf(templateId) > -1
                    return 10
                return 100000

            @getSizeMessageFromTemplateAndSize = (templateId, paperSizeId) ->
                message = ""
                if [t.Magazine, t.MagazineSeparate, t.MagazineNDD, t.A4CalendarSelfCover, t.A4CalendarSeparateCover].indexOf(templateId) > -1
                    #if [22,23].indexOf(paperSizeId) > -1 then message = "25 Sheets per pad"
                    #if [24,25,26].indexOf(paperSizeId) > -1 then message = "50 Sheets per pad"
                    if [4].indexOf(paperSizeId) > -1 then message = "Saddled Stitched on 210mm edge only"
                message

            @pos2label = utils.pos2label


            isOffset    = (job) -> job.PrintType is 'O'
            isDigital   = (job) -> job.PrintType is 'D'
            isBusinessCard  = (job) -> [t.BusinessCard, t.BusinessCardNdd, t.BusinessCardSdd].indexOf(job.Template?.Id) > -1
            isMagazine  = (job) -> [t.Magazine, t.MagazineSeparate, t.WiroMagazines, t.MagazineNDD].indexOf(job.Template?.Id) > -1
            @isBusinessCard  = (job) -> [t.BusinessCard, t.BusinessCardNdd, t.BusinessCardSdd].indexOf(job.Template?.Id) > -1
            isPapersize = (job, sizes) -> sizes.indexOf(job.FinishedSize?.PaperSize?.Name ) > -1

            oneSideBinding = (job,vis, scope) ->
                oldBoundEdge = job.BoundEdge
                if job.Rotation == 0 # portrait
                    vis.boundEdgeTop = false
                    vis.boundEdgeSide = true
                    job.BoundEdge = 1 # SideEdge
                    job.oneSideBindingMsg= "Digital Only Qty, can't be bound on the Top/long edge, system changed to Side/short edge"
                else if job.Rotation == 1 # landscape
                    vis.boundEdgeTop = true
                    vis.boundEdgeSide = false
                    job.BoundEdge = 2 # top
                    job.oneSideBindingMsg= "Digital Only Qty, can't be bound on the Side/short edge, system changed to Top/long edge"

                # if oldBoundEdge != job.BoundEdge
                #     opts =  
                #         containerId: 'boundEdgeDiv'
                #         positionClass: 'toast-bottom-left'
                #         #timeOut: 5000
                #         extendedTimeOut: 0 
                #         closeHtml: null
                #     #document.querySelector('#boundEdgeDiv0').scrollIntoView({block: "center", inline: "end", behavior: 'smooth'})
                #     if oldBoundEdge is 1 # SideEdge
                #         msg = "Warning, Digital Only Qty, can't be bound on the Side/short edge, system changed to Top/long edge"
                #         toastr.clear()
                #         toastr.warning(msg,'', opts)
                #     else if oldBoundEdge is 2 # top
                #         msg =  "Warning, Digital Only Qty, can't be bound on the Top/long edge, system changed to Side/short edge"
                #         toastr.clear()
                #         toastr.warning(msg,'', opts)
                
                return

            bothSideBinding = (job,vis) ->
                delete job.oneSideBindingMsg
                vis.boundEdgeTop = true
                vis.boundEdgeSide = true
                return


            class A5ish
                constructor: (@w,@h) ->
                d1 = 210
                r = [148..150]

                equal    : () ->
                    a = (@w is 210 and @h in r) or
                        (@h is 210 and @w in r)
                    # cd 'equal ', a
                    a

                lessThan  : ->
                    [x,y] = [210,148]
                    [w,h] = [@w,@h]
                    a = ((w * h) < (x * y)) \
                        and (((w * w) + (h * h))   < ((x * x) + (y * y))) \
                        and ((w + h) < (x + y))
                    # cd 'lessThan ', a
                    a

                moreThan  : () ->
                    [x,y] = [210,148]
                    [w,h] = [@w,@h]
                    a = ((w * h) > (x * y)) \
                        and (((w * w) + (h * h))   > ((x * x) + (y * y))) \
                        and ((w + h) > (x + y))
                    # cd 'moreThan ', a
                    a

                upto_420_297: () ->
                    [x,y] = [420,297]
                    [w,h] = [@w,@h]
                    a = ((w * h) <= (x * y)) \
                        and (((w * w) + (h * h))   <=((x * x) + (y * y))) \
                        and ((w + h) <= (x + y))
                    # cd 'upto_420_297 ', a
                    a



            @rules = [

                #-----------------------------------------------------------------------------------
                rule0a = (job, vis)->
                    if [ t.A4CalendarSelfCover, t.A4CalendarSeparateCover].indexOf(job.Template?.Id) > -1
                        job.Rotation = 1
                #-----------------------------------------------------------------------------------
                # Scoring and perforating not available if Template is Brochure and Trim size is 8pp A4
                rule1 = (job, vis)->
                    #if [t.BrochureSDD].indexOf(job.Template?.Id) > -1
                    #        vis.score       = false

                    if [t.Brochure, t.DLCalendars, t.GreetingCards].indexOf(job.Template?.Id) > -1
                        if [27].indexOf(job.FinishedSize?.PaperSize?.Id ) > -1
                            vis.score = false
                            vis.perforate = false
                            job.Perforating = false
                            job.Scoring = false
                        else
                            vis.score = true
                            vis.perforate = true

                #-----------------------------------------------------------------------------------
                # Scoring is required For Brochure and  if a foldsize is selected and stock selection is greater than 150GSM
                rule2 = (job, vis, scope)->
                    
                    if [t.Brochure,t.DLCalendars, t.GreetingCards].indexOf(job.Template?.Id) > -1
                        if job?.FoldedSize?.PaperSize?.Id
                            if job.Stock?.GSM > 150
                                vis.score = true
                                vis.scoreReqd = true
                                job.Scoring = true
                            else if job.Stock?.GSM < 150
                                vis.score = false
                                vis.scoreReqd = false
                                job.Scoring = false
                            else # 150gms
                                vis.score = true
                                vis.scoreReqd = false
                                #job.Scoring   = false
                                if job.Scoring
                                    window.toastr.info 'Scoring can be deselected for this Stock'

                                if vis.scoreReqd and job.Scoring #if scoring was required thus far and scoring was selected then
                                    job.Scoring = false
                                vis.scoreReqd = false

                    if [t.BrochureSDD].indexOf(job.Template?.Id) > -1
                        vis.score = false
                        vis.scoreReqd = false
                        job.Scoring = false


                #-----------------------------------------------------------------------------------
                # for  NDD Brochures, Size  A4/A5, QTY > 10000,  No Folding option can be selected with message.
                rule2a = (job, vis)->
                    if [t.BrochureNDD].indexOf(job.Template?.Id) > -1
                        if ['A4','A5'].indexOf(job.FinishedSize?.PaperSize?.Name ) > -1
                            if parseInt(job?.Quantity) > 10000
                                if job.FoldedSize?.PaperSize
                                    job.FoldedSize = {PaperSize: null}
                                    window.toastr.error( "NDD Brochures over 10,000 can not be folded")

                #-----------------------------------------------------------------------------------
                # For Magazine types if number of pages is greater than 16 then no folding available
                rule3 = (job, vis)->
                    if [t.Magazine, t.A4CalendarSelfCover, t.A4CalendarSeparateCover].indexOf(job.Template?.Id ) > -1
                        if job.Pages > 16
                            vis.fold = false
                            job.FoldedSize = {PaperSize: {Id: 0}}
                        else
                            vis.fold = true
                        #if  job.Pages > 28
                        #    job.PrintType = 'O'


                #------------------------------------------------------------------------------------
                #  blank out binding options if not a magazine
                rule4 = (job, vis) ->
                    if job.RoundOption == 3 then job.RoundDetailOption = 0
                    if [t.Magazine, t.MagazineSeparate, t.MagazineNDD, t.A4CalendarSelfCover, t.A4CalendarSeparateCover].indexOf(job.Template?.Id) == -1
                        if !vis.binding and job.BindingOption
                            job.BindingOption.Id = 0
                            job.BindingOption.Name = ""

                        if !vis.page and job.Pages != 0
                            job.Pages = 0

                #------------------------------------------------------------------------------------
                rule6oa = (job, vis) ->
                    if [t.DuplicateNCRBooks, t.TriplicateNCRBooks, t.QuadruplicateNCRBooks].indexOf(job.Template?.Id) > -1
                        vis.boundEdgeTop = true
                        vis.boundEdgeSide = true

                #------------------------------------------------------------------------------------
                # LORD-1193
                rule60 = (job, vis, scope)->
                    if not isMagazine(job) then return
                    if isPapersize(job, ['A3'])
                        oneSideBinding(job, vis)

                    if isPapersize(job, ['A6','DL'])
                        oneSideBinding(job, vis)

                    if isPapersize(job, ['A4'])
                        if isOffset(job) then bothSideBinding(job, vis)
                        if isDigital(job) then oneSideBinding(job, vis)

                    if isPapersize(job,  ['A5'])
                        bothSideBinding(job,vis)

                    if isPapersize(job,  ['Custom'])
                        [w,h] = [parseInt(job.FinishedSize.Width), parseInt(job.FinishedSize.Height)]
                        if !w or !h then return
                        a5ish = new A5ish(w,h)
                        if isDigital(job)
                            if a5ish.equal()
                                bothSideBinding(job, vis)
                            else
                                oneSideBinding(job, vis)

                        if isOffset(job)
                            if a5ish.equal()
                                bothSideBinding(job, vis)
                            if a5ish.lessThan()
                                oneSideBinding(job,vis)
                            if a5ish.moreThan()
                                if a5ish.upto_420_297()
                                    bothSideBinding(job, vis)
                                else
                                    oneSideBinding(job,vis)

                #------------------------------------------------------------------------------------
                # for A4CalendarSelfCover ,A4CalendarSeparateCover with A4  only Top edge visible
                rule6C = (job, vis) ->
                    if [t.A4CalendarSelfCover, t.A4CalendarSeparateCover].indexOf(job.Template?.Id) > -1
                        if ['A4'].indexOf(job.FinishedSize?.PaperSize?.Name) > -1
                            vis.boundEdgeTop = true
                            vis.boundEdgeSide = false


                #------------------------------------------------------------------------------------
                # for Brochure types, Brochure 6pp A4, 6pp A5, 8pp A4 make orentation Portrait
                rule7 = (job, vis) ->
                    if [t.Brochure, t.BrochureNDD, t.BrochureSDD].indexOf(job.Template?.Id) > -1
                        if ['6pp A4', '6pp A5', '8pp A4'].indexOf(job.FinishedSize?.PaperSize?.Name ) > -1
                            vis.landscape = false
                            job.Rotation = 0
                        else
                            vis.landscape = true

                #------------------------------------------------------------------------------------
                #rule8 = (job, vis) ->
                #    if !vis.sendSamples
                #        job.SendSamples = false


                #------------------------------------------------------------------------------------
                rule8 = (job, vis) ->
                    if([t.PullUpBannerStandardStand, t.PullUpBannerPremiumStand].indexOf(job?.Template?.Id) > -1)
                        vis.landscape = false

                #------------------------------------------------------------------------------------
                # stockId: 30, Plastic card, we dont do rounding or hole drilling anymore
                rule9 = (job, vis) ->
                    if job?.Stock?.Id == 30
                        vis.round = false
                        vis.hole = false
                        job.RoundOption = 0
                        job.HoleDrilling = 0
                        job.TRround = false
                        job.TLround = false
                        job.BRround = false
                        job.BLround = false
                #------------------------------------------------------------------------------------
                # Envelopes dont have front and back printing, these are specified in job template name
                rule10 = (job, vis) ->
                    if [t.EnvelopeBlack, t.Envelope1Pms, t.Envelope2Pms, t.EnvelopeCmyk].indexOf(job.Template?.Id) > -1
                        job.FrontPrinting = 0
                        job.BackPrinting = 0
                        job.BackCelloglaze = 0
                        job.FrontCelloglaze = 0

                #------------------------------------------------------------------------------------
                # Error converting value {null} to type 'lep.job.CutOptions'. Path 'DieCutType', line 59, position 2
                rule11 = (job, vis) ->
                    if job.DieCutType == null
                        job.DieCutType = 0

                #------------------------------------------------------------------------------------
                # For MagazineSeparate if GSM is < 200 then No Cello available
                rule12 = (job,vis, scope)->
                    #return unless n
                    if [t.MagazineSeparate].indexOf(job.Template?.Id) > -1
                        return unless scope
                        return unless job.StockForCover
                        if job.StockForCover.GSM < 200
                            scope.lookups.celloOptions = [{Key: "00", Value: "None"}]
                            scope.selectedCello = '00'
                        else
                            scope.getStock()

                #------------------------------------------------------------------------------------
                # For MagazineSeparate....  if there is a binding option mark as involves outwork
                rule12a = (job,vis, scope)->
                    if [t.MagazineSeparate].indexOf(job.Template?.Id) > -1
                        if [3,4,5].indexOf(job.BindingOption?.Id) > -1
                            job.InvolvesOutwork = true

                rule13 = (job,vis, scope)->
                    #return unless n
                    if !vis.sendSamples
                        job.SendSamples = false

                rule14 = (job,vis, scope)->
                    if [t.GolfScoreCards].indexOf(job.Template?.Id) > -1
                        job.Scoring = true

                rule15 = (job, vis, scope) ->
                    if job.Template?.Id in [t.DuplicateNCRBooks, t.TriplicateNCRBooks, t.QuadruplicateNCRBooks]
                        job.BackPrinting = 0

                # LORD-1259 When custom size BC’s W/H is less than 40 remove RC standard from Diecut list and hide hole drilling dropdown
                rule16 = (job, vis, scope) ->
                    if scope?.roundListValues && job.Template?.Id in [t.BusinessCard, t.BusinessCardNdd, t.BusinessCardSdd]
                        if job.FinishedSize?.PaperSize?.Id is 12 # custom
                            if job.FinishedSize.Width < 40 or job.FinishedSize.Height < 40
                                scope.roundListValues  = _.without(scope.roundListValues,1) # take out RCStandard
                                vis.hole = false
                            else
                                scope.updateRoundDetails(job.Template?.Id, job.FinishedSize?.PaperSize?.Id)
                                vis.hole = true

                #------------------------------------------------------------------------------------
                # for Brochure types,
                # When Trim is     8pp DL
                # And selected fold is  ----------------------------> show this
                # "DL (Zig zag)"    value="19" > DL (Zig zag)         396 x 210
                # "Double Parallel" value="43" > Double Parallel     396 X 210
                # "DL Roll Fold"    value="49" > DL Roll Fold        396 X 210
                # "DL Double-Gate"  value="83" > DL Double-Gate      390 x 210
                # "Double DL Single-Gate"  value="85" > Double DL Gate      394 x 210

                # When Trim is     8pp A4
                # And selected fold is  ----------------------------> show this
                # "A4 (Roll Fold)"  value="30">   A4 (Roll Fold)     834 X 297
                # "Double Parallel" value="43">   Double Parallel    839 X 297
                # "A4 (Zig Zag)"    value="46">   A4 (Zig Zag)       840 X 297
                # "A4 Double-Gate"  value="84">   A4 Double-Gate     834 X 297
                # "A3 Single-Gate"         value="86">   A3 Single-Gate            838 X 297
                rule75 = (job, vis, scope) ->
                    if !job?.FinishedSize?.PaperSize?.Name or !job?.FoldedSize?.PaperSize?.Name or !scope?.sizeOptions then return
                    if not (job.Template?.Id in [t.Brochure, t.BrochureNDD, t.BrochureSDD]) then return
                    p = job.FinishedSize.PaperSize
                    f = job.FoldedSize.PaperSize
                    if !p then return

                    if p.Name is '8pp DL'
                        [p.Width,p.Height] = switch f.Name
                            when 'DL (Zig zag)'     then [210,396]
                            when 'Double Parallel'  then [210,395]
                            when 'DL Roll Fold'     then [210,396]
                            when 'DL Double-Gate'   then [210,392]
                            when 'Double DL Single-Gate'   then [210,392]
                            else [210,396]

                        [f.Width,f.Height] = switch f.Name
                            when 'DL (Zig zag)'     then [99,210]
                            when 'DL Roll Fold'     then [101,210]
                            when 'Double Parallel'  then [99,210]
                            when 'DL Double-Gate'   then [99,210]
                            when 'Double DL Single-Gate'   then [198,210]
                            else [100,210]

                    if p.Name is '8pp A4'
                        [p.Width,p.Height] = switch f.Name
                            when 'A4 (Roll Fold)'   then [297,834]
                            when 'Double Parallel'  then [297,839]
                            when 'A4 (Zig Zag)'     then [297,840]
                            when 'A4 Double-Gate'   then [297,834]
                            when 'A3 Single-Gate'          then [297,838]
                            else [297,840]

                        [f.Width,f.Height] = switch f.Name
                            when 'A4 (Roll Fold)'   then [210, 297]
                            when 'Double Parallel'  then [210, 297]
                            when 'A4 (Zig Zag)'     then [210, 297]
                            when 'A4 Double-Gate'   then [210, 297]
                            when 'A3 Single-Gate'          then [420, 297]
                            else [210, 297]
            ]

            @applyRules = (job, scope) ->
                #if !job.Template?.Id then return
                #if !job.Stock?.Id then return
                # job.FoldedSize?.PaperSize?.Id   or job.Stock?.Id then return
                for r in @rules
                    r(job, scope.vis, scope)





            # return this object
            @


    ]
