do (window, angular)  ->
    app = angular.module('app')

    rewardLevels = [
        { level: 25, from: 10000, to: 999999999 , t: 'more than $10,000 ' , isCurrent: false }
        { level: 20, from: 7500,  to: 9999      , t: '$7,500  to $9,999 ' , isCurrent: false }
        { level: 15, from: 5000,  to: 7499      , t: '$5,000  to $7,499 ' , isCurrent: false }
        { level: 10, from: 2000,  to: 4999      , t: '$2,000  to $4,999 ' , isCurrent: false }
        { level: 5 , from: 500,   to: 1999      , t: '$500  to $1,999 ' , isCurrent: false }
    ]

    app.directive 'lepRewards', () ->
        restrict	: 'EA'
        scope		: { cid: '=' , vertical: '=' }
        templateUrl : 'common/directives/rewards.html'
        controller	: 'RewardsController'

    app.controller 'RewardsController', [
        '$scope', '$rootScope',  'lepApi2',       
        ($scope ,  $rootScope,    lepApi2) ->
            id = $scope.cid
            $scope.p = null
            $scope.rewardLevels = angular.copy(rewardLevels)
            $scope.$watch 'cid', (n,o) ->
                if !n then return
                lepApi2.get("cust/#{n}/si").then (r) ->
                    $scope.r = r 
                    $scope.p = p = r.offer?.Promotion
                    
                    if p and p.Discount
                        _.find($scope.rewardLevels, {level: p.Discount}).isCurrent = true


        ]

