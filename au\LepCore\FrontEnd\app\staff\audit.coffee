
appStaff = angular.module('app.staff')

aborter = {promise : {}}

appStaff.factory 'Audit', ['$resource', ($resource) ->
    url = 'api/Staff/Audit/:Id'
    $resource url, { Id: '@Id' },
        #'update': method: 'PUT'
        'query':
            method: 'GET'
            isArray: false
            cancellable: true
        # 'patch':
        #     method: 'PATCH'
        #     headers: {'Content-Type': 'application/json-patch+json'}
]

appStaff.controller 'StaffAuditListController', [
    '$scope','lepApi2',	 'Audit', '$state', 'Utils', '$http', '$sessionStorage', '$q', '$timeout'
    ($scope, lepApi2,    Audit,   $state,    Utils,   $http,   $sessionStorage, $q, $timeout ) ->

        #cd "here"
        cs = "staffAuditList"
        $sessionStorage.search        = $sessionStorage.search        || {SystemAccess: 'true'}
        $sessionStorage.searchresults = $sessionStorage.searchresults || {}

        $scope.vm = angular.copy($sessionStorage.search[cs]) || {Page: 1}
        $scope.r       = $sessionStorage.searchresults[cs] || {}


        $scope.clear = () -> $scope.vm = {Page:1, SystemAccess: 'true'}
        cc = null

        $scope.search = () ->
            if cc then cc.$cancelRequest()
            $sessionStorage.search[cs] = angular.copy  $scope.vm
            cc = Audit.query $scope.vm, (r) ->
                $scope.r = r
                #$sessionStorage.searchresults[cs] = r

        $scope.toggleSort = (sort) ->
            $scope.vm.SortDir =  sort == $scope.vm.SortField and !$scope.vm.SortDir
            $scope.vm.SortField = sort

        $scope.goPage = (p) ->
            $scope.vm.Page = p

        $scope.$watch 'vm', (x,y) ->
            if angular.equals(x,y)
                return
            $scope.search()
        , true

        $scope.search()
    ]
