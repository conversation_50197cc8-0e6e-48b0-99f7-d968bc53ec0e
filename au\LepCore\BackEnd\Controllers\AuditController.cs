using AutoMapper;
using CsvHelper;
using lep;
using lep.audit;
using lep.email;
using lep.order;
using lep.promotion;
using lep.user;
using lep.user.impl;
using LepCore.Dto;
using lumen.csv;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using NHibernate.Criterion;
using NHibernate.Transform;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;

namespace LepCore.Controllers
{
	/// <summary>
	/// </summary>
	[Produces("application/json")]
	[Route("api/Staff/[controller]")]
	[Authorize(Roles = LepRoles.Staff)]
	[ApiExplorerSettings(IgnoreApi = true)]
	public class AuditController : Controller
	{
		private readonly IMapper _mapper;
		private readonly IUserApplication _userApplication;
		private readonly IEmailApplication _emailApplication;
		private readonly IOrderApplication _orderApp;
		private NHibernate.ISession _session;
		private readonly IHttpContextAccessor _contextAccessor;
		private CachedAccessTo _cachedAccessTo;
		public AuditController(
			IHttpContextAccessor contextAccessor,
			IUserApplication userApplication,
			IEmailApplication emailApplication,
			IOrderApplication oa,
			IMapper mapper,
			NHibernate.ISession session,
			CachedAccessTo cachedAccessTo
		)
		{
			_contextAccessor = contextAccessor;
			_userApplication = userApplication;
			_emailApplication = emailApplication;
			_mapper = mapper;
			_session = session;
			_cachedAccessTo = cachedAccessTo;
			_orderApp = oa;
		}

		// called from staff pages to get a list of orders
		[HttpGet("")]
		//[ValidateActionParameters]
		[Produces(typeof(PagedResult<Audit>))]
		public IActionResult GetAll([FromQuery] [Required] AuditSearchCriteria c)
		{

			//var customerid = 0;
			//if (!string.IsNullOrEmpty(sp.Customer))
			//{
			//	int.TryParse(sp.Customer.Trim(), out customerid);
			//}


			Order order = null;
			if (!string.IsNullOrEmpty(c.SortField))
			{
				order = new Order(c.SortField, c.SortDir == "true");
			}
			else
			{
				order = new Order("Id", true);
			}


			var criteria = _session.CreateCriteria<Audit>();

			if (!string.IsNullOrEmpty(c.UserName))
			{
				criteria.Add(Restrictions.Like("UserName",$"%{c.UserName.Trim()}%"));
			}

			if (!string.IsNullOrEmpty(c.Entity))
			{
				criteria.Add(Restrictions.Like("Entity", $"%{c.Entity.Trim()}%"));
			}

			if (c.EntityId != null  && c.EntityId != 0 )
			{
				criteria.Add(Restrictions.Eq("EntityId", c.EntityId));
			}

			if (c.FromDate != null)
			{
				criteria.Add(Restrictions.Ge("EventDate", c.FromDate));
			}

			if (c.ToDate != null)
			{
				criteria.Add(Restrictions.Le("EventDate", c.ToDate));
			}

			var sortOrder = new[] { new Order("EventDate", false) };
	 

			var list = Utils.GetPagedResult2<Audit>(_session, criteria, c.Page, 20, x => x, sortOrder);

			return new OkObjectResult(list);
		}

 
		[HttpGet("CustomerLookups")]
		public IActionResult GetCustomerLookups()
		{
			IList result = _userApplication.GetCustomerProductPricing();
			var CustomerProductPricing = new Dictionary<string, string>();
			foreach (object[] r in result)
			{
				CustomerProductPricing.Add((string)r[0], (string)r[1]);
			}


			result = _userApplication.GetCustomerFreightPricing();
			var CustomerFreightPricing = new Dictionary<string, string>();

			foreach (object[] r in result)
			{
				CustomerFreightPricing.Add((string)r[0], (string)r[1]);
			}

			var CustomerStatus = _session.CreateSQLQuery("Select CustomerStatus From CustomerStatus2 order by 1").List<String>();
			var Franchise  = _session.CreateSQLQuery("Select Code, Name From Franchise order by 1")
					.List<object[]>()
					.Select(x => new { code = (string)x[0], name = (string)x[1] });

			var SalesConsultant =  _session.CreateSQLQuery("Select Name,Name From SalesConsultant order by 1").List<object[]>()
				.Select(x => new { Id = x[0].ToString(), Name = (string)x[0] }); ;



			var BusinessTypes = _session.CreateSQLQuery("Select Title, Title From CustomerBusinessTypes order by 1").List<object[]>()
				.Select(x => new { Id = (string)x[0], Name = (string)x[0] }); ;

			var RegionLep = _session.CreateSQLQuery("SELECT distinct [LEP_Region] FROM  [SalesRegion] order by 1").List<string>();
			var RegionAP = _session.CreateSQLQuery("SELECT distinct   [Aust_Post_Region]  FROM  [SalesRegion] order by 1").List<string>();

			return new OkObjectResult( new { CustomerProductPricing , CustomerFreightPricing, CustomerStatus , Franchise , SalesConsultant , BusinessTypes, RegionLep, RegionAP });
		}

		[HttpGet("CustomerStatus")]
		public IActionResult GetCustomerStatus()
		{
			var q = _session.CreateSQLQuery("Select CustomerStatus From CustomerStatus2 order by 1").List<String>();
			return new OkObjectResult(q);
		}

		[HttpGet("Franchise")]
		public IActionResult GetFranchiseCode()
		{
			var q = _session.CreateSQLQuery("Select Code, Name From Franchise order by 1")
					.List<object[]>()
					.Select(x => new { code = (string)x[0], name = (string)x[1] });
			return new OkObjectResult(q);
		}

		[HttpGet("SalesConsultant")]
		public IActionResult GetSalesConsultant()
		{
			var q = _session.CreateSQLQuery("Select Name From SalesConsultant order by 1").List<String>();
			return new OkObjectResult(q);
		}

		[HttpGet("BusinessTypes")]
		public IActionResult GetCustomerBusinessTypes()
		{
			var q = _session.CreateSQLQuery("Select Title From CustomerBusinessTypes order by 1").List<String>();
			return new OkObjectResult(q);
		}

		protected IStaff GetCurrentUser()
		{
			var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
			return (IStaff)_userApplication.GetUser(userId);
		}

	}
}
