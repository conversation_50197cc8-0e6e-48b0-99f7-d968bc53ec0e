/// <binding />
// Gulpfile v0.2.0
var childProcess = require("child_process");
var gulp = require('gulp');
var $ = require('gulp-load-plugins')();
var del = require('del');
var es = require('event-stream');
var bowerFiles = require('main-bower-files');
var print = require('gulp-print');
var Q = require('q');
var gutil = require('gulp-util');
var order = require('gulp-order')
// == PATH STRINGS ========


function preseveLicence(node, comment) {
	var text = comment.value;
	var type = comment.type;
	if (type == "comment2") {
		// multiline comment
		return /@preserve|@license|@cc_on/i.test(test);
	}
}

function swallowError(error) {
	// If you want details of the error in the console
	console.log(error.toString());
	this.emit('end');
}

var paths = {
	coffee_scripts: 'app/**/*.coffee',
	coffee_scripts_e2e: 'e2e/**/*.coffee',
	scripts: 'app/**/*.js',
	styles: ['./app/css/*.css', './app/css/*.scss'],
	images: './images/**/*',
	index: ['./app/index.html', './app/index_.html', './app/index2.html', './app/index_r1.html', './app/staff.html', './app/index_wl.html'],
	partials: ['app/**/*.html', '!app/index*.html', '!./app/staff.html', '!./app/index_wl.html'],
	appfonts: './fonts/*',   // LEP's fonts, bar code fonts etc here
	distDev: './../wwwroot',
	distProd: './../wwwroot',
	distScriptsProd: './../wwwroot/scripts',
	scriptsDevServer: 'devServer/**/*.js'
};

var p = {
	core: {
		coffee: ['app/**/*.coffee', '!app/staff/**'],
		js: ['app/**/*.js', '!app/staff/**'],
		html: ['app/**/*.html', '!app/staff/**'],
	},
	staff: {
		coffee: ['app/staff/**/*.coffee'],
		js: ['app/staff/**/*.js'],
		html: ['app/staff/**/*.html'],
	},
	coffee_scripts: 'app/**/*.coffee',
	coffee_scripts_e2e: 'e2e/**/*.coffee',
	scripts: 'app/**/*.js',
	styles: ['./app/css/*.css', './app/css/*.scss'],
	images: './images/**/*',
	index: ['./app/index.html', './app/index2.html', './app/index_r1.html', './app/staff.html', './app/index_wl.html'],
	partials: ['app/**/*.html', '!app/index*.html', '!./app/staff.html', '!./app/index_wl.html'],
	appfonts: './fonts/*',   // LEP's fonts, bar code fonts etc here
	distDev: './../wwwroot',
	distProd: './../wwwroot',
	distScriptsProd: './wwwroot/scripts',
	scriptsDevServer: 'devServer/**/*.js'
};



var jsScriptsRegex = /.js$/i;
var cssStylesRegex = /.css$/i;
var lessStylesRegex = /.less$/i;
var fontsStylesRegex = /.(ttf)$/i; //|woff|eot|svg|woff2

// == PIPE SEGMENTS ========

var pipes = {};


process.on('unhandledRejection', error => {
	// Will print "unhandledRejection err is not defined"
	console.log('PUNTUI unhandledRejection', error.message);
});



var defaultOrder = [
	'browser-polyfill.js', 'es5-shim.js',
	'jquery.js', 'moment.js', 'jquery.signalR.js', 'toastr.js',
	'jquery.dataTables.js', 'bootstrap.js', 'tv4.js', 'bower.json',
	'angular.js', 'ocLazyLoad.js', 'angular-loader', 'angular-sanitize', 'angular-resource', 'angular-animate', 'angular-ui-router', 'ng-animate-model-change.js'

];
var defaultOrderStyles = [
	'toastr.min.css',
	'bootstrap.css',
	'overrides.css',
	'angucomplete-alt.css',
	'balloon.css',
	'bzm-range-slider.css',
	'ngDialog-theme-default.css',
	'ngDialog.css',
	'roundingBox.css',
	'rs.css',
	'speech-bubble.css',
	'template-cards.css',
	'jobboard.css',
	'tooltip-buttons.css',
	'site.css',

];


pipes.builtStylesDev = function () {
	return gulp.src(paths.styles)
		.pipe($.sass().on('error', $.sass.logError))
		.pipe(order(defaultOrderStyles))
		.pipe(print())
		.pipe($.sourcemaps.init({ loadMaps: true }))
		.pipe($.sass({ indentedSyntax: false, outputStyle: 'expanded' }))
		.pipe($.sourcemaps.write('./maps'))
		.pipe(gulp.dest(paths.distDev + '/css'));
};


pipes.orderedVendorScripts = function () {
	return $.order(defaultOrder).pipe(print());
};

pipes.orderedAppScripts = function () {
	return $.angularFilesort();
};

pipes.minifiedFileName = function () {
	return $.rename(function (path) {
		path.extname = '.min' + path.extname;
	});
};

pipes.validatedAppScripts = function () {
	return gulp.src(paths.scripts)
		.pipe($.jshint())
		.pipe($.jshint.reporter('jshint-stylish'));
	// .pipe(gulp.dest(paths.distDev));
};

pipes.compiledAppCS0 = function () {
	var coreCS = gulp.src(p.core.coffee)
		.pipe($.sourcemaps.init())
		.pipe($.plumber())
		.pipe($.coffee({ bare: true }).on('error', function (error) {
			console.error("Compilation error: " + error.stack);
			childProcess.exec("powershell.exe [console]::beep(2500,1000)");
		}))
		.pipe($.sourcemaps.write());
	var coreJS = gulp.src(p.core.js);
	//.pipe($.jshint())
	//.pipe($.jshint.reporter('jshint-stylish', { beep:true, verbose: false }));
	// unicode for beep
	childProcess.exec("powershell.exe [console]::beep(500,100)");
	process.stdout.write('\x07');
	$.util.log(new Date().toLocaleTimeString() + '\x07');

	return es.merge(coreCS, coreJS);
	//		.pipe($.concat('app.core.min.js'))
	//		//.pipe($.uglify({ compress: { hoist_funs: false, hoist_vars: false, drop_console: true } }))
	//		.pipe($.rev())
	//		.pipe(gulp.dest(paths.distDev));
};

pipes.compiledAppCS1 = function () {
	var staffCS = gulp.src(p.staff.coffee)
		.pipe($.sourcemaps.init())
		.pipe($.coffee({ bare: true })
			.on('error', $.util.log))
		//.pipe($.print())
		.pipe($.sourcemaps.write());

	var staffJS = gulp.src(p.staff.js);
	return es.merge(staffCS, staffJS);
};

/*
pipes.builtAppScriptsProd = function () {
	var compiledAppCS0 = pipes.compiledAppCS0()
			.pipe($.concat('app.core.min.js'))
			.pipe($.uglify({ compress: { hoist_funs: false, hoist_vars: false, drop_console: true } }))
			.pipe($.rev());

	var compiledAppCS1 = pipes.compiledAppCS1()
			.pipe($.concat('app.staff.min.js'))
			.pipe($.uglify({ compress: { hoist_funs: false, hoist_vars: false, drop_console: true } }))
			.pipe($.rev());

	return es.merge(compiledAppCS0, compiledAppCS1)
			.pipe($.print())
			.pipe(gulp.dest(p.distDev));
};
*/

pipes.builtAppScriptsProd0 = function () {
	var compiledAppCS0 = pipes.compiledAppCS0()
		.pipe($.concat('app.core.min.js'))
		.pipe($.uglify({ compress: { hoist_funs: false, hoist_vars: false, drop_console: true } }).on('error', function (e) { console.log(e); }))
		.pipe($.rev())
		.pipe(gulp.dest(p.distDev));
	return compiledAppCS0;
};

pipes.builtAppScriptsProd1 = function () {
	var compiledAppCS1 = pipes.compiledAppCS1()
		.pipe($.concat('app.staff.min.js'))
		.pipe($.uglify({ compress: { hoist_funs: false, hoist_vars: false, drop_console: true } }).on('error', function (e) { console.log(e); }))
		.pipe($.rev())
		.pipe(gulp.dest(p.distDev));
	return compiledAppCS1;
};

pipes.builtAppScriptsDev = function () {
	var compiledAppCS0 = pipes.compiledAppCS0();
	var compiledAppCS1 = pipes.compiledAppCS1();
	return es.merge(compiledAppCS0, compiledAppCS1)
		.pipe(gulp.dest(p.distDev));;
};

pipes.builtVendorScriptsProd = function () {
	return gulp.src(bowerFiles({
		filter: function (e) {
			return jsScriptsRegex.test(e);
		}
	}))
		.pipe(pipes.orderedVendorScripts())
		//.pipe(print())
		.pipe($.concat('vendor.min.js'))
		.pipe($.uglify({
			//preserveComments: /^!/i,
			compress: { hoist_funs: false, hoist_vars: false, drop_debugger: true, drop_console: false }
		}).on('error', function (e) { console.log(e); }))
		.pipe(gulp.dest(paths.distProd));
};




pipes.builtVendorScriptsDev = function () {
	return gulp.src(bowerFiles({
				filter: function (e) {
					return jsScriptsRegex.test(e);
				}
			})
			)
		.pipe(pipes.orderedVendorScripts())
		.pipe(print())
		//.pipe($.uglify( {  preserveComments: /^!|@preserve|@license|@cc_on/i,       compress: { hoist_funs: false, hoist_vars: false, drop_debugger: true, drop_console: false  }0        }))
		//.pipe($.concat('vendor.min.js'))
		.pipe(gulp.dest(paths.distDev + '/libs'));


	//return gulp.src(bowerFiles({
	//	filter: function (e) {
	//		return jsScriptsRegex.test(e);
	//	}
	//}))
	//	.pipe(pipes.orderedVendorScripts())
	//	.pipe(print())
	//	.pipe($.concat('vendor.min.js'))
	//	.pipe($.uglify({
	//		preserveComments: /^!|@preserve|@license|@cc_on/i,
	//		compress: { hoist_funs: false, hoist_vars: false, drop_debugger: true, drop_console: false }
	//	}))
	//	.pipe(gulp.dest(paths.distDev + '/libs'));
};


pipes.validatedDevServerScripts = function () {
	return gulp.src(paths.scriptsDevServer)
		.pipe($.jshint())
		.pipe($.jshint.reporter('jshint-stylish'));
};

pipes.validatedPartials = function () {
	return gulp.src(paths.partials, { base: 'app' })
		.pipe($.htmlhint({ 'doctype-first': false }))
		.pipe($.htmlhint.reporter());
};

pipes.builtPartialsDev = function () {
	return pipes.validatedPartials()
		.pipe(gulp.dest(paths.distDev));
};

pipes.scriptedPartialsDev = function () {
	return pipes.validatedPartials()
		.pipe($.htmlhint.failReporter())
		.on('error', swallowError)
		.pipe($.htmlmin({ collapseWhitespace: true, removeComments: true }))
		//.pipe($.ngHtml2js({
		//    moduleName: "app"
		//}))
		.pipe(gulp.dest(paths.distDev));
};

/*
pipes.scriptedPartialsProd = function () {
	return pipes.validatedPartials()
		.pipe($.htmlhint.failReporter())
		.pipe($.htmlmin({ collapseWhitespace: true, removeComments: true }))
		.pipe($.ngHtml2js({
			moduleName: "app", declareModule: false
		}))
		.pipe($.concat('templates.min.js'))
		.pipe($.rev())
		.pipe($.uglify())
		.pipe(gulp.dest(paths.distProd));
};
*/

pipes.scriptedPartialsProd0 = function () {
	return gulp.src(p.core.html, { base: 'app' })
		.pipe($.htmlhint({ 'doctype-first': false }))
		.pipe($.htmlhint.reporter())
		.pipe($.htmlhint.failReporter())
		.pipe($.htmlmin({ collapseWhitespace: true, removeComments: true }))
		.pipe($.ngHtml2js({
			moduleName: "app", declareModule: false
		}))
		.pipe($.concat('templates.core.min.js'))
		.pipe($.rev())
		.pipe($.uglify())
		.pipe(gulp.dest(paths.distProd));

};

pipes.scriptedPartialsProd1 = function () {
	return gulp.src(p.staff.html, { base: 'app' })
		.pipe($.htmlhint({ 'doctype-first': false }))
		.pipe($.htmlhint.reporter())
		.pipe($.htmlhint.failReporter())
		.pipe($.htmlmin({ collapseWhitespace: true, removeComments: true }))
		.pipe($.ngHtml2js({
			moduleName: "app", declareModule: false
		}))
		.pipe($.concat('templates.staff.min.js'))
		.pipe($.rev())
		.pipe($.uglify())
		.pipe(gulp.dest(paths.distProd));
};

pipes.buildVendorStylesLess = function () {
	return gulp.src(bowerFiles({
		filter: function (e) {
			return lessStylesRegex.test(e);
		}
	})
	).pipe($.less({}))
}

pipes.builtVendorCssStyles = function () {
	return gulp.src(bowerFiles({
		filter: function (e) {
			return cssStylesRegex.test(e);
		}
	})
	)
}

pipes.builtVendorStylesDev = function () {
	return es.merge(pipes.buildVendorStylesLess(), pipes.builtVendorCssStyles())
		.pipe($.htmlmin({ removeComments: true }))
		.pipe(gulp.dest(paths.distDev + "/styles"));
}

pipes.builtVendorStylesProd = function () {
	return es.merge(pipes.buildVendorStylesLess(), pipes.builtVendorCssStyles())
		.pipe($.concat('vendor.min.css'))
		.pipe($.minifyCss())
		.pipe(gulp.dest(paths.distProd));
}

pipes.builtStylesProd = function () {
	return gulp.src(paths.styles)
		// .pipe($.sourcemaps.init())
		.pipe($.sass())
		.pipe($.minifyCss())
		//.pipe($.sourcemaps.write())
		.pipe($.concat('app.min.css'))
		.pipe($.rev())
		.pipe(gulp.dest(paths.distProd));
};

pipes.processedFonts = function (base_path) {
	return gulp.src(bowerFiles({
		filter: function (e) {
			return fontsStylesRegex.test(e);
		}
	}),
		{ base: 'bower_components' }
	)//.pipe(print())
		.pipe($.rename(function (path) {
			var arrayPath = path.dirname.split("/");
			if (arrayPath.length > 1) {
				arrayPath.splice(0, 1);
				new_path = "../" + arrayPath.join('/');
			} else {
				new_path = "./"
			}
			path.dirname = new_path;
		}))
		.pipe(gulp.dest(base_path + "/fonts"));
};


pipes.processedAppFonts = function (base_path) {
	return gulp.src(paths.appfonts)
		//.pipe(print())
		.pipe(gulp.dest(base_path + "/fonts"));
};


pipes.processedImagesDev = function () {
	return gulp.src(paths.images)
		.pipe(gulp.dest(paths.distDev + '/images/'));
};

pipes.processedImagesProd = function () {
	return gulp.src(paths.images)
		.pipe(gulp.dest(paths.distProd + '/images/'));
};

pipes.validatedIndex = function () {
	return gulp.src(paths.index)
		.pipe($.htmlhint())
		.pipe($.htmlhint.reporter());
};

pipes.builtIndexDev = function () {
	var orderedVendorScripts = pipes.builtVendorScriptsDev()
		.pipe(pipes.orderedVendorScripts());

	var orderedAppScripts = pipes.builtAppScriptsDev()
		.pipe(pipes.orderedAppScripts());

	var scriptedPartialsDev = pipes.scriptedPartialsDev();

	var appStyles = pipes.builtStylesDev();
	var vendorStyles = pipes.builtVendorStylesDev();

	var vendorScripts = pipes.builtVendorScriptsProd();

	return pipes.validatedIndex()
		.pipe(gulp.dest(paths.distDev)) // write first to get relative path for inject
		.pipe($.inject(orderedVendorScripts, { relative: true, name: 'bower' }))
		//.pipe($.inject(vendorScripts, { relative: true, name: 'bower' }))
		.pipe($.inject(scriptedPartialsDev, { relative: true, name: 'templates' }))
		.pipe($.inject(orderedAppScripts, { relative: true }))
		.pipe($.inject(appStyles, { relative: true }))
		.pipe($.inject(vendorStyles, { relative: true, name: 'bower' }))
		.pipe(gulp.dest(paths.distDev));
};

pipes.builtIndexProd = function () {
	var vendorScripts = pipes.builtVendorScriptsProd();
	//var appScripts = pipes.builtAppScriptsProd();
	var tpl0 = pipes.scriptedPartialsProd0();
	var tpl1 = pipes.scriptedPartialsProd1();

	var app0 = pipes.builtAppScriptsProd0();
	var app1 = pipes.builtAppScriptsProd1();

	var appStyles = pipes.builtStylesProd();
	var vendorStyles = pipes.builtVendorStylesProd();

	var r = pipes.validatedIndex()
		//.pipe(print())
		.pipe(gulp.dest(paths.distProd)) // write first to get relative path for inject
		.pipe($.inject(vendorScripts, { relative: true, name: 'bower' }))
		//.pipe($.inject(scriptedPartialsProd, { relative: true, name: 'templates' }))

		.pipe($.inject(app0, { name: 'appcore', relative: true }))
		.pipe($.inject(app1, { name: 'appstaff', relative: true }))

		.pipe($.inject(tpl0, { name: 'tplcore', relative: true }))
		.pipe($.inject(tpl1, { name: 'tplstaff', relative: true }))

		.pipe($.inject(appStyles, { relative: true }))
		.pipe($.inject(vendorStyles, { relative: true, name: 'bower' }))
		//.pipe($.htmlmin({ collapseWhitespace: true, removeComments: true }))
		.pipe(gulp.dest(paths.distProd));
	return r;
};

pipes.builtAppDev = function () {
	return es.merge(
		pipes.builtIndexDev(),
		pipes.processedFonts(paths.distDev),
		pipes.processedAppFonts(paths.distDev),
		pipes.processedImagesDev());
};

pipes.builtAppProd = function () {
	return es.merge(
		pipes.builtIndexProd(),
		pipes.processedFonts(paths.distProd),
		pipes.processedAppFonts(paths.distProd),
		pipes.processedImagesProd()
	);
};

// == TASKS ========
// removes all compiled dev files
gulp.task('copy-app-fonts', pipes.processedAppFonts);


// removes all compiled dev files
gulp.task('clean-dev', function () {
	var deferred = Q.defer();
	del(paths.distDev, { force: true }, function () {
		deferred.resolve();
	});
	return deferred.promise;
});

// removes all compiled production files
gulp.task('clean-prod', function () {
	var deferred = Q.defer();
	del(paths.distProd, { force: true }, function () {
		deferred.resolve();
	});
	return deferred.promise;
});

// checks html source files for syntax errors
gulp.task('validate-partials', pipes.validatedPartials);

// checks index.html for syntax errors
gulp.task('validate-index', pipes.validatedIndex);

// moves html source files into the dev environment
gulp.task('build-partials-dev', pipes.builtPartialsDev);

// converts partials to javascript using html2js
gulp.task('convert-partials-to-js', pipes.scriptedPartialsDev);

// runs jshint on the dev server scripts
gulp.task('validate-devserver-scripts', pipes.validatedDevServerScripts);

// runs jshint on the app scripts
gulp.task('validate-app-scripts', pipes.validatedAppScripts);

// moves app scripts into the dev environment
gulp.task('build-app-scripts-dev', pipes.builtAppScriptsDev);

// concatenates, uglifies, and moves app scripts and partials into the prod environment
gulp.task('build-app-scripts-prod', pipes.builtAppScriptsProd);

// compiles app sass and moves to the dev environment
gulp.task('build-styles-dev', pipes.builtStylesDev);

// compiles and minifies app sass to css and moves to the prod environment
gulp.task('build-styles-prod', pipes.builtStylesProd);

// moves vendor scripts into the dev environment
gulp.task('build-vendor-scripts-dev', pipes.builtVendorScriptsDev);

// concatenates, uglifies, and moves vendor scripts into the prod environment
gulp.task('build-vendor-scripts-prod', pipes.builtVendorScriptsProd);

// validates and injects sources into index.html and moves it to the dev environment
gulp.task('build-index-dev', pipes.builtIndexDev);
gulp.task('c', pipes.builtIndexDev);

// validates and injects sources into index.html, minifies and moves it to the dev environment
gulp.task('build-index-prod', pipes.builtIndexProd);

// builds a complete dev environment
gulp.task('build', pipes.builtAppDev);

// builds a complete prod environment
gulp.task('build:rel', pipes.builtAppProd);

// cleans and builds a complete dev environment
gulp.task('clean-build-app-dev', ['clean-dev'], pipes.builtAppDev);

// cleans and builds a complete prod environment
gulp.task('clean-build-app-prod', ['clean-prod'], pipes.builtAppProd);
gulp.task('release', ['clean-prod'], pipes.builtAppProd);

// clean, build, and watch live changes to the dev environment
gulp.task('watch-dev', ['clean-build-app-dev', 'validate-devserver-scripts'], function () {

	// start nodemon to auto-reload the dev server
	$.nodemon({ script: 'server.js', ext: 'js', watch: ['devServer/'], env: { NODE_ENV: 'development' } })
		.on('change', ['validate-devserver-scripts'])
		.on('restart', function () {
			console.log('[nodemon] restarted dev server');
		});

	// start live-reload server
	$.livereload.listen({ start: true, quiet: true });

	// watch index
	gulp.watch(paths.index, function () {
		return pipes.builtIndexDev()
			.pipe($.livereload());
	});

	// watch app coffee scripts
	gulp.watch(paths.coffee_scripts, function () {
		return pipes.builtAppScriptsDev()
			.pipe($.livereload());
	});

	// watch app scripts
	gulp.watch(paths.scripts, function () {
		return pipes.builtAppScriptsDev()
			.pipe($.livereload());
	});

	// watch html partials
	gulp.watch(paths.partials, function () {
		return pipes.scriptedPartialsDev().pipe($.livereload());
	});

	// watch styles
	// watch styles


	gulp.watch(paths.styles, function () {
		return pipes.builtStylesDev()
			.pipe($.livereload());
	});

	gulp.watch("./app/css/**/*.*", function () {
		return pipes.builtStylesDev()
			.pipe($.livereload());
	});

	gulp.watch(paths.images, function () {
		return pipes.processedImagesDev()
			.pipe($.livereload());
	});

	//gulp.watch(['./app/css/bootstrap/*.less'], function () {
	//   return pipes.builtStylesDev()
	//       .pipe($.livereload());
	//});

	// watch e2e test suites
	//gulp.watch(paths.coffee_scripts_e2e, function () {
	//    //return
	//});


});

// clean, build, and watch live changes to the prod environment
gulp.task('watch-prod', ['clean-build-app-prod', 'validate-devserver-scripts'], function () {

	// start nodemon to auto-reload the dev server
	$.nodemon({ script: 'server.js', ext: 'js', watch: ['devServer/'], env: { NODE_ENV: 'production' } })
		.on('change', ['validate-devserver-scripts'])
		.on('restart', function () {
			console.log('[nodemon] restarted dev server');
		});

	// start live-reload server
	$.livereload.listen({ start: true });

	// watch index
	gulp.watch(paths.index, function () {
		return pipes.builtIndexProd()
			.pipe($.livereload());
	});

	// watch app coffee scripts
	gulp.watch(paths.coffee_scripts, function () {
		return pipes.builtAppScriptsProd()
			.pipe($.livereload());
	});

	// watch app scripts
	gulp.watch(paths.scripts, function () {
		return pipes.builtAppScriptsProd()
			.pipe($.livereload());
	});

	// watch hhtml partials
	gulp.watch(paths.partials, function () {
		return pipes.scriptedPartialsProd()
			.pipe($.livereload());
	});


	gulp.watch(paths.styles, function () {
		return pipes.builtStylesProd()
			.pipe($.livereload());
	});


	gulp.watch("./app/css/**/*.*", function () {
		return pipes.builtStylesProd()
			.pipe($.livereload());
	});

});

// default task builds for prod
gulp.task('default', ['watch-dev']);
