(function (angular, window) {
  'use strict';

  var blankJobResolver = [
    'JobService', function (JobService) {
      return JobService.getJob(0);
    }
  ];
  var deniedCategoriesResolver = [
    'lepApi2', '$stateParams', function (lepApi2, p) {
      var customerId = parseInt(p.customerId);
      if (customerId === 0) return [];
      return lepApi2.get('JobOptions/DCats/' + customerId + '/WL');
    }
  ];

  var customerResolver = [
    'lepApi2', '$stateParams', function (lepApi2, p) {
      var customerId = parseInt(p.customerId);
      if (customerId === 0) return '';
      return lepApi2.get('Cust/settings/');
    }
  ];

  var ppcResolver = [
    'lepApi2', '$stateParams', function (lepApi2, p) {
      var customerId = parseInt(p.customerId);
      if (customerId === 0) return '';
      return lepApi2.get('account/ppc');
    }
  ];

  var dcatsCustResolver = [
      '$rootScope', function ($rootScope) {
        var x = $rootScope.globals.DeniedTemplates || [];
        return x;
    }
  ];


  var orderResolver = [
    'OrderService', '$stateParams', function (OrderService, p) {
      var orderId = parseInt(p.orderId);
      if (orderId === 0) return {};
      return OrderService.getOrder(orderId);
    }
  ];

  var orderLargeResolver = [
    'OrderService', '$stateParams', function (OrderService, p) {
      var orderId = parseInt(p.orderId);
      if (orderId === 0) return {};
      return OrderService.getOrderLarge(orderId);
    }
  ];

  var jobResolver = [
    'JobService', '$stateParams', function (JobService, p) {
          var jobId = parseInt(p.jobId, 10);
          return JobService.getJob(jobId, p.orderId);
    }
  ];


  var cust = angular.module('app.cust');
  cust.config([  '$stateProvider',
      function ($stateProvider) {

      var custStates = [
        {
          name: 'cust',
          url: '/cust',
          templateUrl: 'cust/base.html'
        }, {
          name: 'cust.job-pricer', url: '/job-pricer',
          templateUrl: 'common/directives/price-calculator.html',
          controller: 'PriceCalculatorController',
          resolve: { blankJob: blankJobResolver, dcatsCust: dcatsCustResolver }
        }, {
          name: 'cust.orders',
          url: '/your-orders',
          templateUrl: 'cust/your-orders.html',
          controller: 'CustOrdersListController',
          title: 'My orders',
          params: { justSubmitted: null }
        }, {
          name: 'cust.settings',
          url: '/your-settings',
          templateUrl: 'cust/cust-your-settings.html',
          controller: 'YourSettingsController',
          title: 'My settings'
        }, {
          name: 'cust.print-portal',
          url: '/your-print-portal',
          templateUrl: 'cust/cust-your-settings-print-portal.html',
          controller: 'YourSettingsPPController',
          title: 'My Print Portal'
        }, {
          name: 'cust.order',
          url: '/your-order/{orderId:int}',
          templateUrl: function (stateParam)  {
           
            if (stateParam.orderId === 0 || stateParam.base ) {
              return 'cust/base.html';
            } else {
              return 'cust/order.html';
            }
          },
          controller: 'OrderController',
          resolve: { order: orderLargeResolver }
        }, {
          name: 'cust.order.view',
          url: '/open',
          templateUrl: 'cust/order-view.html',
          controller: 'OrderViewController',
        }, {
          // add job under existing order
          class: 'container-fluid',
          name: 'cust.order.addnewjob',
          url: '/new-order',
          templateUrl: 'cust/cust-templates.html',
          controller: 'TemplatesController',
          resolve: { dcatsCust: dcatsCustResolver },
        }, {
          // open job under existing order
          name: 'cust.order.job',
          url: '/job/{jobId:int}',
          templateUrl: 'cust/job.html',
          controller: 'OrderJobController',
          resolve: { job: jobResolver, dcatsCust: dcatsCustResolver },
          params: { templateId: null, category: null },
          onEnter: ['$stateParams', 'job', function ($stateParams, job) {
            //cd ('cust.order.job', job)
            }],
        },

        {
          // split job delivery address
          name: 'cust.order.jobsplit',
          url: '/job/{jobId:int}/splits',
          //templateUrl: 'cust/job.splits.html',
          template: '<div lep-job-split-delivery job="job" />',
          onEnter: ['$stateParams', 'job', function ($stateParams, job) {
            //  cd ('cust.order.jobsplit', job)
          }],
          controller: 'OrderJobController1',
          resolve: { job: jobResolver, dcatsCust: dcatsCustResolver },
          params: { base: true },
        },
        
        //  For REMOTE PLUGIN - EG: BARNEYS 
        {
          name: 'remote',
          url: '/remote/{orderId:int}',
          templateUrl: 'cust/remote/base.html'
        }, {
          // open job under existing order
          name: 'remote.job',
          url: '/job/{jobId:int}',
          templateUrl: 'cust/remote/job.html',
          controller: 'OrderJobController',
          resolve: { job: jobResolver, dcatsCust: dcatsCustResolver },
          params: { templateId: null, category: null },
          onEnter: ['$stateParams', 'job', function ($stateParams, job) {
            var m = { command: 'openjobresult', data: job };
            if (window.parent) window.parent.postMessage(m, '*');
          }]
        },
        {
          name: 'cust.sub-customers',
          url: '/your-customers/',
          templateUrl: 'cust/sub-customer-list.html',
          controller: 'CustomersSubCustomerListController'
        },
        {
          name: 'cust.sub-customers-view',
          url: '/my-customer/{id:int}',
          templateUrl: 'cust/sub-customer-edit.html',
          controller: 'CustomersSubCustomerViewController'
        },
        // *************************
        //       WhiteLabel 
        // *************************
        {
          name: 'whitelabel',
          url: '/wl/{customerId:int}',
          templateUrl: 'cust/wl/base.html',
          controller: 'WhitelabelBaseController',
          resolve: {
            ppc: ppcResolver,
          }
        },
        {
          name: 'whitelabel.registerAccount',
          url: '/register-account',
          templateUrl: 'cust/wl/account-register.html',
          controller: 'WLRegisterAccountController',
        },
        {
          name: 'whitelabel.editAccount',
          url: '/edit-account',
          templateUrl: 'cust/wl/account-edit.html',
          controller: 'WLEditAccountController',
        },
        {
          name: 'whitelabel.order',
          url: '/o/{orderId:int}?t',
          templateUrl: 'cust/wl/order-view.html',
          controller: 'OrderViewControllerWL',
          params: { customerId: 0, orderId: 0 },
          resolve: {
            order: orderLargeResolver,
          }
        },
        {
          name: 'whitelabel.job',
          url: '/o/{orderId:int}/j/{jobId:int}?t',
          templateUrl: 'cust/wl/job.html',
          controller: 'OrderJobControllerWL',
          params: { customerId: 0, orderId: 0, jobId: 0, templateId: null, category: null },
          resolve: {
            job: jobResolver,
            order: orderLargeResolver,
          }
        },
        {
          name: 'whitelabel.orderslist',
          url: '/wlc/{wlCustomerId}',
          templateUrl: 'cust/wl/your-orders.html',
          controller: 'SubCustOrdersListController',
        },
      ];


      angular.forEach(custStates, function (state) {
        $stateProvider.state(state);
      });
    }]);

})(window.angular, window);
