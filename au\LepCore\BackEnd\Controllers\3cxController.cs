﻿using AutoMapper;
using lep;
using lep.contact;
using lep.user;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

using System;
using System.ComponentModel.DataAnnotations;
using System.Dynamic;
using System.Linq;

namespace LepCore.src.Controllers
{
	[ApiController]
	[AllowAnonymous]
	[Route("api/[controller]")]
	[ApiExplorerSettings(IgnoreApi = true)]
	public class ContactsController : ControllerBase
	{
		private readonly IMapper _mapper;
		private readonly IUserApplication _userApplication;
		private NHibernate.ISession _session;
		private readonly IHttpContextAccessor _contextAccessor;


		public ContactsController( IHttpContextAccessor contextAccessor, IUserApplication userApplication, IMapper mapper, NHibernate.ISession session)
		{
			_contextAccessor = contextAccessor;
			_userApplication = userApplication;
			_mapper = mapper;
			_session = session;
		}


		[HttpGet("Search")]
		public IActionResult Search([FromQuery][Required] string number)
		{
			ICustomerUser customer = null;
			IContact contact = null;
			number = number.Replace(" ", "");
			string searchSQLFromCache = "SELECT top 1 CustomerId from CallerToCustCache WHERE CLookupBy like '%" + number + "' order by lastOrderDate desc";
			int customerId = _session.CreateSQLQuery(searchSQLFromCache).List<int>().FirstOrDefault();
			if (customerId == 0)
			{
				return new OkObjectResult(new{});
			}
			customer = _userApplication.GetCustomerUser(customerId);
			if (customer == null)
			{
				return new OkObjectResult(new{});
			}
			if (customer != null)
			{
				contact = customer.Contacts.Where(_ => _.Phone.Contains(number) || _.Mobile.Contains(number)).FirstOrDefault();
			}
			string urlBase = LepGlobal.Instance.AbsolutePathURL;
			string url = $"{urlBase}/#!/staff/customers/{customer.Id}";
			dynamic r = new ExpandoObject();
			if (contact != null)
			{
				string fn = "";
				string ln = "";
				try
				{
					fn = contact.Name.Split(' ')[0] ?? "";
					ln = contact.Name.Split(' ')[1] ?? "";
				}
				catch (Exception)
				{
				}
				r = new
				{
					contact = new
					{
						id = customer.Id,
						firstname = fn,
						lastname = ln,
						company = customer.Name,
						email = contact.Email,
						businessphone = contact.Phone,
						mobilephone = contact.Mobile,
						url = url
					}
				};
			}
			else
			{
				r = new
				{
					contact = new
					{
						id = customer.Id,
						firstname = customer.Name,
						lastname = "",
						company = customer.Name,
						email = customer.Email,
						businessphone = customer.Phone,
						mobilephone = customer.Mobile,
						url = url
					}
				};
			}
			return new OkObjectResult(r);
		}




		[HttpGet("Search2")]
		public IActionResult Search2([FromQuery][Required] string number)
		{
			ICustomerUser customer = null;
			//IContact contact = null;
			number = number.Replace(" ", "");
			string searchSQLFromCache = "SELECT top 1 CustomerId from CallerToCustCache WHERE CLookupBy like '%" + number + "' order by lastOrderDate desc";
			int customerId = _session.CreateSQLQuery(searchSQLFromCache).List<int>().FirstOrDefault();
			if (customerId == 0 && (number.StartsWith("02") || number.StartsWith("03") || number.StartsWith("07") || number.StartsWith("08")))
			{
				string numberLastpart = number.Substring(2);
				searchSQLFromCache = "SELECT top 1 CustomerId from CallerToCustCache WHERE CLookupBy like '%" + numberLastpart + "' order by lastOrderDate desc";
				customerId = (_session.CreateSQLQuery(searchSQLFromCache)).List<int>().FirstOrDefault();
			}
			customer = _userApplication.GetCustomerUser(customerId);
			if (customerId == 0 || customer == null)
			{
				string notFound = number + " not found in customer records";
				ContentResult val = new ContentResult() { Content = notFound} ;
				
				return val;
			}
			string urlBase = LepGlobal.Instance.AbsolutePathURL;
			string v = $"{urlBase}/#!/staff/customers/{customer.Id}?notes=true";
			return Redirect(v);

		}

		public partial class Eng
		{
			public int CustomerId { get; set; }
			public string Agent { get; set; }
			public string State { get; set; }
			public string RedirectUri { get; set; }
			public string Number { get; set; }
			public string Query { get; set; }
			public string FoundRecordCount { get; set; }
			public string EntityId { get; set; }
			public DateTimeOffset DurationTimespan { get; set; }
			public string Duration { get; set; }
			public string DestinationEmail { get; set; }
			public string DateTime { get; set; }
			public string CallType { get; set; }
			public string CallStartTimeUtc { get; set; }
			public string CallStartTimeLocal { get; set; }
			public string CallEndTimeUtc { get; set; }
			public string CallEndTimeLocal { get; set; }
			public string CallDirection { get; set; }
		}

		[HttpPost("ReportCall")]
		public IActionResult ReportCall([FromBody]Eng eng)
		{


		 

			return Ok();
		}

	}
}
