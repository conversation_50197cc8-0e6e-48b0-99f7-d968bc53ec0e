app = angular.module('app')

app.factory 'SubCustomers', ['$resource', ($resource) ->
    url = 'api/Cust/SubCustomer/:Id'
    $resource url, { Id: '@Id' },
        'update': method: 'PUT'
        'query':
            method: 'GET'
            isArray: false
        'patch':
            method: 'PATCH'
            headers: {'Content-Type': 'application/json-patch+json'}
        'password':
            method: 'POST'
            url: "#{url}/password"
        'archive':
            method: 'POST'
            url: "#{url}/archive"
        'unarchive':
            method: 'POST'
            url: "#{url}/unarchive"
        'newOrder':
            method: 'GET'
            url: "#{url}/newOrder"
        'delete':
            method: 'Delete'
            url: "#{url}"
]         

app.controller 'CustomersSubCustomerListController', [
    '$scope', 'SubCustomers', '$state',
    ($scope, SubCustomers, $state) ->

        $scope.vm = {Page:1}
        $scope.r = {}

        vm = $scope.vm 
        $scope.clear = () ->  vm = {Page:1}
        $scope.search = () ->
            SubCustomers.query  vm, (r) ->
                $scope.r = r

        $scope.toggleSort = (sort) ->
            vm.SortDir   = sort == vm.SortField and !vm.SortDir
            vm.SortField = sort

        $scope.goPage = (p) ->
            vm.Page = p

        $scope.$watch 'vm', () ->
            $scope.search()
        , true

        $scope.createOrderAs = (scId) ->
            SubCustomers.newOrder({Id: scId}, {}).$promise.then (r) ->
                $state.go('cust.order.job', {orderId: r.orderId, jobId: 0})
            
            

        return @
    ]

app.factory 'guProvider', ['$http', '$rootScope', '$q',  ($http, $rootScope, $q) ->
    r =  $q.defer()
    $http.get('/api/Account/LoginDetails').then (response) ->
        $rootScope.globals = response.data
        if !response.data.error
            r.resolve(response.data)
        else
            r.reject(null)
    r.promise
]




app.controller 'CustomersSubCustomerViewController', [
    '$scope', '$stateParams', 'SubCustomers', '$state'  , 'templateTree', 'JobService', 'lepApi2',
    ($scope,   $stateParams, SubCustomers,     $state ,    templateTree, JobService, lepApi2) ->
        $scope.vis = {}
        $scope.vm = {} #important
        $scope.logo = ""
        id =  $stateParams.id

        # shows up in the DeniedTemplates box so user can select things he does not want
        $scope.allTemplatesName = []

        $scope.ppv = $scope.$root.globals.User.PrintPortalSettings.Version 
        $scope.availableCategoies = angular.copy(Object.keys( templateTree.topdown.Category))


        $scope.filterOutDeniedTemplates = (t) -> 
            r = !(t.Id in $scope.vm.PrintPortalSettings.DeniedTemplates) and !(t.Id in $scope.$root.globals.DeniedTemplates)
            r

        $scope.load = (id) ->
            SubCustomers.get({Id:id}).$promise.then (r) ->
                $scope.vm  = r

                JobService.getTemplates().then (d) ->
                    omit = $scope.$root.globals.DeniedTemplates
                    d2 = _.reject d, (x) ->
                         omit.indexOf(x.Id) > -1
                    $scope.allTemplatesName = d2

                lepApi2.get('Cust/Settings/ppc/matrix3').then (d) ->
                    $scope.d = d
                    $scope.d.matrix2 = {}
                    pps = $scope.vm.PrintPortalSettings.WhiteLabelAllowedProducts
                    r = {}
                    for t in d.templatesL
                        if !(r[t.Id]) then  r[t.Id] = { Sizes: [], Stocks: [] }

                    _.each pps, (x) ->
                        if r[x.T].Sizes.indexOf(x.P) == -1 then r[x.T].Sizes.push(x.P)
                        if r[x.T].Stocks.indexOf(x.S) == -1 then r[x.T].Stocks.push(x.S)
                    $scope.d.matrix2 = r


        $scope.load(id)

        $scope.save = () ->
            SubCustomers.update {Id: id}, $scope.vm, (value, responseHeadersFn) ->
                id = parseInt(responseHeadersFn('Id'), 10)
                toastr.info( "Success")
                $state.go('cust.sub-customers-view', {id: id},   {reload: true})
                $scope.$emit("update-login-details");
            

        $scope.updatePassword = (pwd) ->
            SubCustomers.password {Id:id}, JSON.stringify(pwd)
            .$promise.then (r) -> toastr.info "Password updated"

        $scope.makePostalSameAsBilling = (b) ->
            if b
                $scope.vm.PostalAddress = angular.copy( $scope.vm.BillingAddress )

        $scope.delete = (id) ->
            SubCustomers.delete({Id:id}).$promise.then (r) ->
                if r.error 
                    toastr.error "Customer already has order in the system. So can not be deleted"

        $scope.archive = (id) ->
            SubCustomers.archive({Id:id}).$promise.then (r) ->
                if !r.error 
                    toastr.success "Customer archived"
                    $state.reload()

        $scope.unarchive = (id) ->
            SubCustomers.unarchive({Id:id}).$promise.then (r) ->
                if !r.error 
                    toastr.success "Customer un archived"
                    $state.reload()

        $scope.updateVm = () ->
            a = []
            for t in  $scope.d.templatesL
                if t.Id in $scope.vm.PrintPortalSettings.DeniedTemplates 
                    continue
                x = $scope.d.matrix2[t.Id]
                if !x then continue
                for stk in x.Stocks
                    for ps in x.Sizes
                        a.push({T:t.Id, S:stk, P: ps})
            $scope.vm.PrintPortalSettings.WhiteLabelAllowedProducts = a

        $scope.toggleSizesUnderTemplate = (e,templateId) ->
            checked = e.target.checked
            if !$scope.d.matrix2[templateId] then  $scope.d.matrix2[templateId] = { Sizes: [], Stocks: [] }
            if checked
                $scope.d.matrix2[templateId].Sizes = angular.copy($scope.d.matrix[templateId].Sizes)
            else
                $scope.d.matrix2[templateId].Sizes = []

        $scope.toggleStocksUnderTemplate =  (e,templateId) ->
            checked = e.target.checked
            if !$scope.d.matrix2[templateId] then  $scope.d.matrix2[templateId] = { Sizes: [], Stocks: [] }
            if checked
                $scope.d.matrix2[templateId].Stocks = angular.copy($scope.d.matrix[templateId].Stocks)
            else
                $scope.d.matrix2[templateId].Stocks = []

        $scope.toggleAllUnderTemplate = (e,templateId) ->
            $scope.toggleSizesUnderTemplate(e,templateId)
            $scope.toggleStocksUnderTemplate(e,templateId)

        $scope.toggleAllTemplates = (e) ->
            checked = e.target.checked
            if checked
                $scope.d.matrix2 = angular.copy($scope.d.matrix)
            else
                $scope.d.matrix2 = {}




























        return
    ]


