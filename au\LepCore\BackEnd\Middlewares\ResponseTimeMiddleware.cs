using Microsoft.AspNetCore.Http;
using System.Diagnostics;
using System.Threading.Tasks;

namespace LepCore
{
	public class ResponseTimeMiddleware
	{
		private readonly RequestDelegate _next;
		public ResponseTimeMiddleware(RequestDelegate next)
		{
			_next = next;
		}

		public async Task Invoke(HttpContext context)
		{
			var sw = new Stopwatch();
			sw.Start();

			context.Response.OnStarting((state) =>
			{
				sw.Stop();
				context.Response.Headers.Add("x-elapsed-time", sw.ElapsedMilliseconds.ToString());
				return Task.FromResult(0);
			}, null);

			await _next.Invoke(context);
		}
	}
}
