app = angular.module('app')
app.controller 'PriceCalculatorController', [
    '$scope', 'OrderService', 'blankJob', 'lepApi2', '$state',  'Utils',  '$http', '$timeout', 'dcatsCust',
    ($scope, OrderService, blankJob, lepApi2, $state,  utils,    $http, $timeout , dcatsCust) ->

        $scope.job = blankJob
        $scope.job.Country = 'AU'

        $scope.dcats = dcatsCust

        $scope.address = {Postcode : ''}
        $scope.courierRates = []

        $scope.$on 'priceResult', (e,a) ->
            $scope.courierRates = a.courierRates
            $scope.FGPackageStr = a.FGPackageStr
            $scope.PMPackageStr = a.PMPackageStr
            $scope.EDRs = a.EDRs
            $scope.p = a.Price

        $scope.$watch 'address.Postcode', (n, o) ->

            if !n or !n.originalObject
                $scope.job.DeliveryAddress.City = ''
                return
            $scope.job.DeliveryAddress.State = n.originalObject.State
            $scope.job.DeliveryAddress.Postcode = n.originalObject.PostCode
            $scope.job.DeliveryAddress.City     = n.originalObject.Suburb
#
#
#
#		$scope.convertToJob0 = () ->
#			url = "Orders/Order/0/job/0"
#			jobState = "cust.order.job"
#			$scope.job.Name = "New job "
#			$scope.job.Name = ""
#
#			lepApi2.post(url, $scope.job).then (r) ->
#				toastr.success("A new order has just been created from the job pricer!\n Please enter the job name and other infromation to proceed with the order")
#				$state.go(jobState, {orderId: r.Id, jobId: r.Jobs[0].Id}, {reload:true})
#

        $scope.convertToJob = () ->
            url = "/api/Orders/Order/0/job/0"
            job = angular.copy($scope.job)
            delete job.Visibility
            job.Name = "New job "
            formData = new FormData
            formData.append 'request',  angular.toJson(job, true)
            $http(
                method          : 'POST'
                url             : url
                headers         : {'Content-Type': undefined}
                transformRequest: angular.identity
                data: formData
            ).then((result, status, headers, config) ->
                    r = result.data
                    $state.go("cust.order.view", {orderId: r.Id}, {reload:true})
                    return
                ,(data, status, headers, config) ->
                    alert 'failed!'
            )
            return


        $scope.convertToQuote = () ->
            orderState = "cust.order.view"
            url = "/api/Orders/Order/0/job/0"
            job = angular.copy($scope.job)
            delete job.Visibility
            job.Name = "NamedPrice request "
            formData = new FormData
            formData.append 'request',  angular.toJson(job, true)
            $http(
                method          : 'POST'
                url             : url
                headers         : {'Content-Type': undefined}
                transformRequest: angular.identity
                data: formData
            ).then((result, status, headers, config) ->
                    r = result.data

                    $http.post("/api/Orders/order/#{r.Id}/submit").then (r)->
                        $state.go("cust.order.view", {orderId: r.Id}, {reload:true})
                        ##$state.go(orderState, {orderId: r.Id, jobId: r.Jobs[0].Id}, {reload:true})
                        window.toastr.success("Quote request submitted!\n")
                    return
                ,(data, status, headers, config) ->
                    alert 'failed!'
            )
            return

        # crp means customer requested price
        # crp is visible when the job is a quote
        # crp is required only when the job price is 0
        job = $scope.job
        crp = {visible: false, required : false, valid: false}
        crp.btnIcon = ''
        crp.btnText = 'Continue'
        $scope.crp = crp
        
        $scope.$watchGroup ['job.crpDate','job.CustomerRequestedPrice'], (n,o) ->
            $scope.updateCrp()

        $scope.$on 'priceResult', (x,y) ->
            $scope.updateCrp()

        $scope.updateCrp = () ->
            #cd +new Date
            f = $scope.totalJobForm.jobDetailsForm
            if !f
                return
            e = f.$error

            if !($scope.$root.globals.IsCustomer)
                crp.visible = false
                return

            # # if job is while label crp not visible
            if job.IsWhiteLabel
                crp.visible = false
                return
            if job.Price > 0
                crp.visible = false
                return
            # crp is visible when the job is a quote
            crp.visible = (f.$valid && !job.Price)

        @
    ]
