app = angular.module('app')


app.service 'OrderService', [
    'lepApi2', '$q', '$timeout', '$state',
    (lepApi2, $q, $timeout, $state) ->


        formatRates = (rates, facility) ->
            l1 = _(rates).map('CarrierName').map((x) -> x.length).max() + 2
            l2 = _(rates).map('ServiceName').map((x) -> x.length).max() + 2
            rates2 = _(rates).map((x) ->
                y = {}
                name1 = x.CarrierName
                name2 = x.ServiceName
                name3 = ""
                if name1 is "Customer"
                    xxx = 0
                #else if name1 is "STARTRACK EXP"
                #    if name2  is "PREMIUM" or name2 is "FP PREMIUM"
                #        name3 = " (1-2 days)"
                #    else
                #        name3 = " (1-3 days)"
                else if name1 is "FASTWAY"
                    if name2  is "LIME" or name2 is "BLACK" or name2 is "BLUE"
                        name3 = " (next day)"
                    else
                        name3 = " (1-3 days)"
                else
                    name3 = " (1-3 days)"
                
                if  facility is "PM" and name1 is "FASTWAY" and name2 is "BROWN"
                    name3 = " (1-3 days)"

                if name1 is "TNT" and name2 is "TECH EXP B"
                       name3 += " (No Forklift Required)"

                if name1 is "TOLL NQX"
                       name3 += " (Forklift Required at Delivery Address)"

                name1 = _.padEnd(name1, l1)
                name2 = _.padEnd(name2, l2)

                charge = _.padStart( "$" + parseFloat(x.CustomerCharge).toFixed(2), 8)

                estDelivery = x.EstDelivery

                y.CustomerCharge = parseFloat(x.CustomerCharge)

                value =  "#{x.CarrierName} ~ #{x.ServiceName} ~ #{x.CarrierAccount}"
                y.Value =  value
                name =  "#{name1} #{name2} #{charge}  #{name3}"
                y.Name = name.replace(/\s/g, '\u00A0')

                y.name1 = name1.trim()
                y.name2 = name2.trim()
                y.name3 = name3.trim()
                y
            ).value()
            rates2
        @formatRates = formatRates
        @getFreights = _.memoize((orderId, postcode, jobcount) ->
                lepApi2.get("Orders/order/#{orderId}/GetAvailableRates")
                .then (rates) ->
                    formatRates(rates)
            , (a,b,c) -> a + b + c
        )

        @getFreights2 = (orderId) ->
            lepApi2.get("Orders/order/#{orderId}/GetAvailableRates")
            .then (rates) ->
                formatRates(rates)

        @getFreightsFromFacility = (orderId, facility) ->
            lepApi2.get("Orders/order/#{orderId}/GetAvailableRatesFromFacility/#{facility}")
            .then (rates) ->
                formatRates(rates, facility)


        #  get orders list function for staff side
        @getOrdersStaff = (searchParams, canceller) ->
            lepApi2.get('Orders/CustOrders',searchParams, false, canceller)

        #  get orders list function for customer side
        @getOrders = (searchParams)->
            lepApi2.get('Orders/MyOrders2', searchParams)

        # get order by Id,  just basic fields
        @getOrder = (orderId) ->
            lepApi2.post('Orders/order/' + orderId + '/small')

        # get order by Id, full object
        @getOrderLarge = (orderId) ->
            lepApi2.post('Orders/order/' + orderId + '/large')

        # get jobs comments
        @getJobComments = (jobId) ->
            lepApi2.get("Orders/job/#{jobId}/comments")

        @getJobArtworks = (jobId) ->
            lepApi2.get("Orders/job/#{jobId}/artworks")

        @downloadArtwork = (jobId,artworkId, issupply) ->
            lepApi2.download("/Api/Orders/Job/#{jobId}/#{artworkId}/#{issupply}/download")
        @
]
