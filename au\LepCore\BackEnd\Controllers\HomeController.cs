using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Infrastructure;

using System.Linq;
using System.Threading.Tasks;

namespace LepCore.Controllers
{
	[AllowAnonymous]
	public class HomeController : Controller
	{
		private readonly Microsoft.AspNetCore.Hosting.IHostingEnvironment _hostingEnvironment;

		// do not remove this

		public HomeController(Microsoft.AspNetCore.Hosting.IHostingEnvironment hostingEnvironment)
		{
			_hostingEnvironment = hostingEnvironment;
		}
		
		
		[ResponseCache(NoStore = true, Location = ResponseCacheLocation.None)]
		public async Task<ActionResult> Index()
		{
			await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
			return Redirect("~/" + "?r=" + System.DateTime.Now.ToString("yyyymmddhh"));//index.html/#!/login
		}
	}

	[AllowAnonymous]
	[Route("monitor")]
	[ApiExplorerSettings(IgnoreApi = true)]
	public class MonitorController : Controller
	{
		private readonly IActionDescriptorCollectionProvider _provider;

		public MonitorController(IActionDescriptorCollectionProvider provider)
		{
			_provider = provider;
		}

		[HttpGet("routes")]
		public IActionResult GetRoutes()
		{
			var routes = _provider.ActionDescriptors.Items.Select(x => new
			{
				Action = x.RouteValues["Action"],
				Controller = x.RouteValues["Controller"],
				Name = x.AttributeRouteInfo?.Name ?? "x.AttributeRouteInfo?.Name",
				Template = x.AttributeRouteInfo?.Template ?? "x.AttributeRouteInfo?.Template"
			}).ToList();
			return Ok(routes);
		}
	}
}
