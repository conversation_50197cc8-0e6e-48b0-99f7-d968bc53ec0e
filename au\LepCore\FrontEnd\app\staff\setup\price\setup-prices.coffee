appStaff = angular.module('app.staff')

appStaff.controller 'StaffSetupPricesController', [
    '$scope','lepApi2','JobService','Utils','$http','blankJob', '$stateParams'
    ($scope , lepApi2 , JobService , Utils  , $http, job      , $stateParams) ->
        # initialize
        # set the available number of pages
        $scope.numberOfPages = JobService.getNumberOfPagesByTemplate(job)
        job.FrontPrinting = 1
        job.BackPrinting = 1
        $scope.job = job
        if($stateParams.tId)
            job.Template.Id = $stateParams.tId
        if($stateParams.sId)
            job.Stock.Id = $stateParams.sId
        if($stateParams.psId)
            job.FinishedSize.PaperSize.Id = $stateParams.psId
        $scope.gridOptions = { }
        $scope.gridOptions.enableCellEditOnFocus = true
        # set the column definitions for the grid
        $scope.gridOptions.columnDefs = [
            { name: 'Qty',   enableCellEditOnFocus: true,  width: 80 },
            { name: 'Price', enableCellEditOnFocus: true,  width: 80 },
            # { name: 'MYOB',  enableCellEditOnFocus:true,  width: 150} ,
            {
                name: 'Remove'
                cellTemplate:
                    '''<button class="btn"
                        ng-click="grid.appScope.deleteRow(row)">
                          <i class="glyphicon glyphicon-trash"></i>
                    </button>'''
                displayName: ''
                enableSorting: false
            }
        ]
        $scope.gridOptions.data = []
        # when quantity changes get print types
        $scope.printTypesAvaiable = null
        JobService.getTemplates().then (d) ->
            d.push({Id: 30, Name: "Magazine Cover"})
            $scope.templates = d

        # when template changes get prices and populate the grid
        $scope.showOldPrices = () ->
            $scope.result = ""
            ###
            if !job.ColorSides
                job.ColorSides  = 2
                if !stock.FrontPrintOptions.length
                    job.ColorSides  = 0
            ###
            if !job.BackCelloglaze  then job.BackCelloglaze  = 0
            if !job.FrontCelloglaze then job.FrontCelloglaze = 0


            JobService.getPricePoints($scope.job).then (d) ->
                $scope.result1 = d
                $scope.gridOptions.data = $scope.result1.pricePoints
                currQtys = _.map($scope.gridOptions.data, 'Qty')
                $scope.missing = _.difference($scope.result1.ReqdQtys,
                                                    currQtys)


        $scope.validate = () ->
            qo = stock.QuantityOption
            qty_step1   = qo.Step1
            qty_change  = qo.Change
            qty_step2   = qo.Step2
            qty_change2 = qo.Change2


        $scope.addBlankRow = () ->
            $scope.result1.pricePoints.push( {Qty: 0, Price: 0})


        $scope.deleteRow =  (row) ->
            index = $scope.gridOptions.data.indexOf(row.entity)
            $scope.gridOptions.data.splice(index, 1)


        $scope.addMissingQty = () ->
            currQtys = _.map($scope.result1.pricePoints, 'Qty')
            $scope.missing = _.difference($scope.result1.ReqdQtys, currQtys)
            if !$scope.missing.length  then return
            c = angular.copy($scope.result1.pricePoints)
            _.each($scope.missing, (x) ->
                c.push( {Qty: x, Price: 0 })
            )
            $scope.result1.pricePoints = _.sortBy(c, (x)->x.Qty)
            $scope.gridOptions.data = $scope.result1.pricePoints
            currQtys = _.map($scope.gridOptions.data, 'Qty')
            $scope.missing = _.difference($scope.result1.ReqdQtys, currQtys)
            # c.push( {Qty: })
            # currQtys = _.pluck($scope.gridOptions.data, 'Qty')
            # $scope.missing = _.diffrence(currQtys,$scope.result1)


        $scope.savePrices = () ->
            pps = $scope.result1.pricePoints
            j = angular.copy($scope.job)
            j.NewPrices = {}

            _.map(pps, (x) ->
                j.NewPrices[x.Qty] = x.Price
            )

            lepApi2.post('JobPrice/SavePricePointsForJobType', j).then (r) ->
                toastr.info('Success')


        $scope.$watch 'job.Template.Id', (n, o) ->
            if !n? or n==0 then return
            $scope.result1 = null
            # fetch Size options
            $scope.vis = JobService.getVisibilityByTemplate(n, job)
            $scope.labeldata = JobService.getLabelsByJobTemplate(n)
            if !$scope.vis.page then $scope.job.Pages = 1
            JobService.getSizeOptions(n).then (d) ->
                $scope.jobTemplateSizeOptions = d


        # when the size chanegs get the stocks
        $scope.$watch 'job.FinishedSize.PaperSize.Id', (n, o) ->
            if !n? or n==0 then return
            tId  = $scope.job?.Template?.Id
            psId = $scope.job?.FinishedSize?.PaperSize?.Id
            if !tId? or  !psId? then return
            JobService.getStockOptions(tId, psId).then (d) ->
                $scope.stockOptions = d
            $scope.numberOfPages = JobService.getNumberOfPagesByTemplate($scope.job)
            return


        $scope.getStock = () ->
            tId  = $scope.job?.Template.Id
            psId = $scope.job?.FinishedSize?.PaperSize?.Id
            sId  = $scope.job?.Stock?.Id
            if  !tId? or  !psId? or !sId? then return
            JobService.getStockOption(tId, psId, sId).then (d) ->
                r = d
                stock = _.find(r, PrintType: $scope.job.PrintType)
                if stock == undefined
                    if r.length == 0 then return
                    stock = r[0]
                foldOptions = _.map(stock.FoldOptions, (fo) ->  {Id: fo.Id, Name: fo.Name })
                minValue = _.min( stock.QuantityOptions)
                maxValue = _.max( stock.QuantityOptions)
                $scope.maxValue  = maxValue
                stock.QuantityOption.QuantityOptions2 = _.map(stock.QuantityOptions, (x) -> {id: x, value: x})
                JobService.getColourOptions(stock.Id).then (d) ->
                    stock.colourOptions = d
                $scope.stock = stock
                $scope.job.SpecStockId = stock.Id
                $scope.foldOptions = foldOptions


        $scope.$watch 'job.Stock.Id + job.PrintType', (n, o) ->
            $scope.getStock()


        $scope.$watch 'job.Template.Id + job.Stock.Id + job.FinishedSize.PaperSize.Id', () ->
            $scope.result1 = null
            tId            = $scope.job.Template?.Id
            psId           = $scope.job.FinishedSize?.PaperSize?.Id
            sId            = $scope.job.Stock?.Id
            #if  !tId or  !psId or !sId then return
            JobService.getPrintTypes0(tId, psId, sId).then (d) ->
                #if d is 'O' or d is 'D' then $scope.job.PrintType = d
                $scope.printTypesAvaiable = d


        $scope.changeCello = (selectedCello) ->
            if !selectedCello then selectedCello = "00"
            $scope.job.FrontCelloglaze = parseInt(selectedCello[0])
            $scope.job.BackCelloglaze = parseInt(selectedCello[1])


        $scope.uploadPriceCsv = () ->
            $scope.result = ""
            Utils.browseForFile().then (file) ->
                if !file then return
                formData = new FormData
                formData.append 'csv', file.file
                $http(
                    method: 'POST'
                    url: "/api/JobPrice/JobPriceCsvImport"
                    headers: {'Content-Type': undefined}
                    transformRequest: angular.identity
                    data: formData
                ).then((data, status, headers, config) ->
                        $scope.result = (data.data)
                    ,(data, status, headers, config) ->
                        alert 'failed!'
                )


        $scope.downloadPriceCsv =  () ->
            $scope.result = ""
            desiredTemplate = $scope.job?.Template?.Id || 0
            lepApi2.download("/api/JobPrice/JobPriceCsvExport", { desiredTemplate: desiredTemplate})

        $scope.importJobOption = () ->
            $scope.result = ""
            lepApi2.post("JobPrice/ImportJobOptionCSVs").then( (r)->
                $scope.result = r
            )


        return @
    ]
