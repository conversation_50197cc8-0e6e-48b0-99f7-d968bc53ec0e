﻿<div id="jobBoardPage">
    <script type="text/javascript">
        function openJob(jid) {
            var url = window.location.origin + '/#!/staff/jobs/' + jid;
        }
        var facility = 'FG';
        var jobBoardHub;
        var jobboardfacility = 'FG';


             if (window.location.href.endsWith('/FG')) {
                facility = 'FG';
                $('#facilityx').html('Forest Glen');
            }

            if (window.location.href.endsWith('/PM')) {
                facility = 'PM';
                $('#facilityx').html('Port Melbourne');
            }



        var delay = 30000;
        var timer = null;
        var oT = null;
        var gjson = null;
        var gfilter = 'Exceptions';
        var gRunGrouping = true;
        var giLastVersionRendered = 0;
        var giNoOfJobs = 50;
        var t = new Date();
        var gSort = [[9, 'asc'], [0, 'asc'], [24, 'asc'], [21, 'desc']];//[4,'desc'],
        var gBusy = false;

        function renderJob<PERSON>ell(oObj, filter, row, cell) {
            if (oObj == null) return;
            var id = oObj;

            if (id != 0) {
                var url = window.location.origin + '/#!/staff/order/' + row[3] + '/job/' + row[2];
                return '<a href="' + url + '" target="_blank" >' + id + '</a>';
            }

            return '';
        }

        function renderOrderCell(oObj) {
            if (oObj == null) return;
            var id = oObj;
            if (id != 0) {
                var url = window.location.origin + '/#!/staff/order/' + id + '/open';
                return '<a href="' + url + '"      target="_blank"  >' + id + '</a>';
            }


            return '';
        }

        function renderRunCell(oObj) {
            if (oObj == null) return;
            var id = oObj;
            if (id != -1) {
                var url = window.location.origin + '/#!/staff/run/' + id + '';
                return '<a href="' + url + '"      target="_blank"  >' + id + '</a>';
            }
            return '';
        }

        function secondsToString(seconds) {
            var value = seconds;

            var units = {
                "d": 24 * 60 * 60,
                "h": 60 * 60,
                "m": 60,
                //"second": 1
            }

            var result = [];

            for (var name in units) {
                var p = Math.floor(value / units[name]);
                if (p)
                    result.push("" + p + "" + name);
                value %= units[name];
            }
            result = result.join(" ");

            return result;

        }
        // render the cell Hours to dispatch
        // if dispatchEst column [28] is year 0001 then show ?
        // else convert the total hours to dispatch tht comes in seconds as hh:mm and show that
        function renderHDCell(oObj, type, row) {

            var hd = row[9];

            if (type === "sort" || type === 'type') {
                return hd;
            }
            else {
                var result = "";
                if (!hd) return result;

                result = secondsToString(Math.abs(hd));
                if (hd && hd < 0) result = '-' + result;
                return result;
            }
        }

        function renderDateTimeCell(oObj, type, row) {
            if (!oObj) return '';
            return moment(oObj).format('DD/MM/YY HH:mm');
            //var d = new Date(oObj);
            //return d.toLocaleDateString() + ' ' + d.toLocaleTimeString();


        }



        // apply filter
        function applyJobBoardFilter() {
            if (oT == null) return;
            //console.log('applyJobBoardFilter start');
            var lFilter = gfilter;
            if (lFilter != 'undefined') {
                fnResetAllFilters(oT);

                if (lFilter == 'All' || lFilter == '' || !lFilter) {
                } else if (lFilter == 'Exceptions') {
                    oT.fnFilter("E", 23, false);
                } else if (lFilter == 'HD') {
                    oT.fnFilter('^.+$', 9 /* HD */, true, false);
                    oT.fnSetColumnVis(5 /* Customer */, false);
                }
                else {
                    var x = oT.fnSetColumnVis(5 /* Customer */, true);
                    oT.fnFilter(lFilter, 1, false);

                    if (!gRunGrouping) {
                        oT.fnFilter(lFilter, 1, false);
                    }
                }
            }
            updateColouredJobCounts();
        };



        function constructJobBoard(aa) {

            var jsonL = aa;

            if (aa.version == 0) {
                toastr.info("Job board is being created in the server, please wait a few seconds...\n");
                setTimeout(function () { window.location.reload(); }, 10000);
                return;
            }
            gBusy = true;

            //console.log('busy building board', aa);

            var paginate = false;

            giLastVersionRendered = jsonL.version;
            jsonL.bAutoWidth = false;
            //jsonL.bJQueryUI = true;              // jquery ui themeroller
            jsonL.bDestroy = true;               // cos we reload the table
            $('#lastthingIwasdoing').html(t.toLocaleTimeString() + " Updating Job board...");
            $('#busy').toggle();

            jsonL.order = gSort;
            if (paginate) {
                jsonL.paging = true;             // cos we want all rows
                jsonL.lengthChange = true;
                jsonL.pagingType = "full_numbers";
                jsonL.pageLength = giNoOfJobs;
            } else {
                jsonL.paging = false;             // cos we want all rows
                jsonL.lengthChange = false;

            }

            // color the row as per the jobs color
            jsonL.rowCallback = rowRenderCallback;

            // cos we wanna hightlight the row as per job health
            jsonL.processing = true;
            jsonL.language = {
                'processing': 'Processing Job Board...',
                "lengthMenu": 'Show <select id="noOfJobs" name="noOfJobs"><option value="10">10</option><option value="25">25</option><option value="50">50</option><option value="100">100</option><option value="-1">All</option></select> Jobs ',
                'eroRecords': 'No Jobs in Board',
                'info': 'Showing _START_ to _END_ of _TOTAL_ Jobs',
                'infoEmpty': 'Showing 0 to 0 of 0 Jobs',
                'infoFiltered': '(filtered from _MAX_ total Jobs)',
                'infoPostFix': '',
                'search': 'Search:',
                'sUrl': '',
                'paginate': {
                    'first': 'First',
                    'previous': 'Previous',
                    'next': 'Next',
                    'last': 'Last'
                }
            };

            // set  column properties here

            jsonL.columnDefs = [
                { 'visible': false, 'targets': [0, 1, 10, 22, 23, 24, 25, 26, 27, 28] },
                { 'type': 'numeric', 'targets': [18, 19, 20] },
                { 'type': 'html', 'targets': [2, 3, 4] },
                { "render": renderJobCell, "targets": [2] },
                { "render": renderOrderCell, "targets": [3] },
                { "render": renderRunCell, "targets": [4] },
                { "render": renderDateTimeCell, "targets": [11,7] },
                { "render": renderHDCell, "targets": [9], orderData: [9] }
            ];

            jsonL.aoColumns = [
                                    /* 0  */   { 'title': 'health' },
                                    /* 1  */   { 'title': 'Board to appear ' },
                                    /* 2  */   { 'title': 'Job#', 'sClass': 'JID' },
                                    /* 3  */   { 'title': 'Order#', 'sClass': 'OID' },
                                    /* 4  */   { 'title': 'Run#', 'sClass': 'RID' },
                                    /* 5  */   { 'title': 'Customer', sWidth: '180px' },
                                    /* 6  */   { 'title': 'Job Name', sWidth: '180px' },
                /* 7  */   { 'title': 'Print by',  sWidth: '150px' },
                /* 8  */   { 'title': 'Despatch by', sWidth: '10px' },
                /* 9  */   { 'title': 'Hours to Despatch', sWidth: '100px' },
                /* 10 */   { 'title': 'Status' },
                /* 11 */   { 'title': 'Status Time', sWidth: '180px' },
                /* 12 */   { 'title': 'NextStatus', sWidth: '180px' },
                /* 13 */   { 'title': 'Job Type' },
                /* 14 */   { 'title': 'Stock' },
                /* 15 */   { 'title': 'Cello' },
                /* 16 */   { 'title': 'TrimSize' },
                /* 17 */   { 'title': 'Folding' },
                /* 18 */   { 'title': 'Qty' },
                /* 19 */   { 'title': 'PS' },
                /* 20 */   { 'title': 'Sheets' },
                /* 21 */   { 'title': 'Age' },
                /* 22 */   { 'title': 'RunG', sWidth: '10px' },
                /* 23 */   { 'title': 'H', sWidth: '10px' },
                /* 24 */   { 'title': 'RH', sWidth: '10px' },
                /* 25 */   { 'title': 'RJ', sWidth: '10px' },
                /* 26 */   { 'title': 'RID' },
                /* 27 */   { 'title': 'ED' },
                /* 28 */   { 'title': 'OrderDispatchEst' },
            ];


            //jsonL.scrollY = 300;
            //jsonL.scrollX = 600;


            jsonL.paging = false;
            jsonL.fnInitComplete = function () {
                this.fnAdjustColumnSizing();
                //this.fnDraw();
                applyJobBoardFilter();


                $("select#noOfJobs").change(function () {
                    giNoOfJobs = parseInt($(this).val());
                });

                $('tr.r').click(function () {
                    var next = $(this).next('tr');
                    while ($(next).is('tr.jbc')) {
                        $(next).toggle();
                        if (!$(next).hasClass('rexpanded-group-row'))
                            $(next).addClass('rexpanded-group-row');
                        else
                            $(next).removeClass('rexpanded-group-row');
                        next = $(next).next('tr');
                    }

                    if ($(this).hasClass('rcollapsed-group'))
                        $(this).removeClass('rcollapsed-group').addClass('rexpanded-group');
                    else if ($(this).hasClass('rexpanded-group'))
                        $(this).removeClass('rexpanded-group').addClass('rcollapsed-group');

                    return null;
                });
                $('#busy').toggle();
                updateColouredJobCounts();
                gBusy = false;
                $('#lastthingIwasdoing').html("");

            }
            // jsonL.fixedColumns = { leftColumns: 6 }
            jsonL.autoWidth = true;
            gjson = null;
            gjson = jsonL; //save a copy for counting

            jsonL.fixedHeader = true;

            oT = $('#jobBoard').dataTable(jsonL);
            applyJobBoardFilter();

            gBusy = false;
            return null;
        }

        function rowRenderCallback(nRow, aData, iDisplayIndex, iDisplayIndexFull) {
            var jobHealth = aData[0];
            var styleClass = ['', 'redAlert', 'amberAlert', 'noAlert'][jobHealth];
            $(nRow).removeClass('redAlert amberAlert noAlert').addClass(styleClass);

            if (aData[24] == 'jbc') {
                $(nRow).addClass('jbc').css('display', 'none');
            }

            if (aData[24] == 'r') {
                $(nRow).addClass('r').addClass('rcollapsed-group');
            }

            return nRow;
        };

        // use json to populate created table _.debounce(
        function buildJobBoard() {
            //if (!$('#jobBoard').dataTable) return;
            if (gBusy) {
                //console.log("busy  returning from build Job Board");
                return null;
            }
            gBusy = true;
            facility = 'FG';
            if (window.location.href.endsWith('/FG')) {
                facility = 'FG';
                $('#facilityx').html('Forest Glen');
            }

            if (window.location.href.endsWith('/PM')) {
                facility = 'PM';
                $('#facilityx').html('Port Melbourne');
            }


            var auth = 'Bearer ' + window.localStorage['ngStorage-lepToken'].replace(/\"/g, '')

            $.ajaxSetup({
                headers: {
                    'Authorization': auth

                }
            });



            // check version if not changed exit
            t = new Date();
            var sUrlVersion = '/api/jobboard/version';
            $.getJSON(sUrlVersion, null, function (version) {

                $('#lastthingIwasdoing').html(t.toLocaleTimeString() + " Detecting changes if any...");

                //if the version number of job board last rendered is the same as that was just fetched from server skip the board rebuilding...
                // console.debug(giLastVersionRendered, version);
                if (giLastVersionRendered == version) {
                    $('#lastthingIwasdoing').html(t.toLocaleTimeString() + " No changes...");
                    gBusy = false;
                    //console.log("nochange in board,  not busy... calling buildJobBoard in " + (delay/1000) + " seconds");

                    return;
                }

                // rebuild the boards with new data...
                //console.debug("constructing board");
                var sUrlBoard = '/api/jobboard/get?facilityStr=' + facility + '&t=' + (+new Date());
                $.getJSON(sUrlBoard, null, constructJobBoard);

            });


        }
        //}, 5 * 1000, true);

        // clears the filters that are applied now
        function fnResetAllFilters() {
            if (oT == null) return;
            var oSettings = oT.fnSettings();
            for (var iCol = 0, len = oSettings.aoPreSearchCols.length; iCol < len; iCol++) {
                oSettings.aoPreSearchCols[iCol].sSearch = '';
            }
            oSettings.oPreviousSearch.sSearch = '';
            return null;
        };

        function updateColouredJobCounts() {
            try {
                if (oT == null) return;
                var redJobsCount = 0;
                var amberJobsCount = 0;
                if (gjson === null) return;
                var d = gjson.aaData;
                if (d === undefined) {
                    return;
                }

                var lFilter = gfilter;
                var i = 0, len = 0;
                var row;

                if (lFilter != 'All' && lFilter != 'Exceptions' && lFilter != '') {
                    for (i = 0, len = d.length; i < len; i++) {
                        row = d[i];
                        if (row[1].indexOf(lFilter) > -1) {           // 2nd column in aaData is for Boards to appear in
                            if (row[0] == 1) { redJobsCount++; }
                            else if (row[0] == 2) { amberJobsCount++; }
                        }
                    }
                } else {
                    for (i = 0, len = d.length; i < len; i++) {
                        row = d[i];
                        if (row[0] == 1) { redJobsCount++; }
                        else if (row[0] == 2) { amberJobsCount++; }
                    }
                }
                $('#redJobsCount').html(redJobsCount);
                $('#amberJobsCount').html(amberJobsCount);
                return null;
            } catch (ex) { }
        };



        $(document).ready(function () {
            gfilter = '';
            // $("#radio").buttonset();


            $("body").on("buildJobBoard", function (event) {
                //console.log('on trigger buildJobBoard ');
                //buildJobBoard();
            });

            buildJobBoard();


            $('#radio').find('label').click(function () {

                gfilter = $(this).clone()    //clone the element
                    .children() //select all the children
                    .remove()   //remove all the children
                    .end()  //again go back to selected element
                    .text().trim();

                if ($(this).hasClass('norungroup')) {
                    gRunGrouping = false;
                } else {
                    gRunGrouping = true;
                }

                gLastClickTab = $(this);

                //console.log("    start  Clicked " + gfilter);
                if (oT == null) return;
                applyJobBoardFilter();
                oT.fnSort([[9, 'asc'], [0, 'asc'], [23, 'asc'], [20, 'desc']]);
                //console.log("    end  Clicked " + gfilter);
                return null;
            });

            setInterval(buildJobBoard, delay);


            return null;
        });
    </script>

    <table style="width: 100%; border-collapse: collapse; border: 0;">
        <tr>
            <td>
                <h3 id="facilityx" style="margin:0 20px; float:left"></h3>

                <div id="radio" class="select-board">
                    <label class="control-label"> <input type="radio" name="radio" /> All</label>
                    <label class="control-label"> <input type="radio" name="radio" /> PreFlight</label>
                    <label class="control-label"> <input type="radio" name="radio" /> PrePress</label>
                    <label class="control-label"> <input type="radio" name="radio" /> DPCProduction </label>
                    <label class="control-label"> <input type="radio" name="radio" /> WideFormatProduction </label>
                    <label class="control-label"> <input type="radio" name="radio" /> Plateroom</label>
                    <label class="control-label"> <input type="radio" name="radio" /> PressRoom</label>
                    <label class="control-label"> <input type="radio" name="radio" /> Celloglaze</label>
                    <label class="control-label"> <input type="radio" name="radio" /> Guillotine</label>
                    <label class="control-label"> <input type="radio" name="radio" /> Folding</label>
                    <label class="control-label"> <input type="radio" name="radio" /> Stitching</label>
                    <label class="control-label"> <input type="radio" name="radio" /> LetterPress</label>
                    <label class="control-label"> <input type="radio" name="radio" /> Finishing</label>
                    <label class="control-label norungroup"> <input type="radio" name="radio" /> Despatch</label>
                    <label class="control-label norungroup"> <input type="radio" name="radio" /> Outwork</label>
                    <label class="control-label norungroup"> <input type="radio" name="radio" /> Pay Me</label>
                    <label class="control-label norungroup"> <input type="radio" name="radio" /> OnHold</label>
                    <label class="control-label"> <input type="radio" name="radio" />HD</label>
                    <!-- <label class="control-label"> <input type="radio" name="radio" /> Exceptions</label> -->

                </div>
            </td>

            <td align="right">
                <span style="white-space: nowrap" id="counts">
                    <span class="alertbox redAlertB">
                        Red <span id="redJobsCount"></span>
                    </span>

                    <span class="alertbox amberAlertB">
                        Amber <span id="amberJobsCount"></span>
                    </span>
                </span>
            </td>

        </tr>
    </table>

    <table class="projector" id="jobBoard"></table>
    <span id="lastthingIwasdoing">loading data for the first time</span>


    <span id="busy" style="font-size: 50px; position: absolute; top: 50%; left: 50%; display: none">busy! </span>


</div>
