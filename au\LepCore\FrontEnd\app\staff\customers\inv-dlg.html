<div>
    <div class="modal-header">
        <h3 class="modal-title">Add Invoice</h3>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-sm-12">
                <div class="form-horizontal">

                    <div class="form-group ng-class:{'has-error' : (!cn.Type)}">
                        <label class="col-xs-3 control-label">Type</label>
                        <div class="col-xs-7 ">

                            <span class="radio-inline" style="margin-right:10px;">
                                <label>
                                    <input type="radio" name="Type" ng-model="cn.Type" ng-value="'S'" ng-required="true">Customer Invoice
                                </label>
                            </span>


                        </div>
                    </div>

                    <div class="form-group  ng-class:{'has-error' : (!cn.Description)}">
                        <label class="col-xs-3 control-label" for="Description">Description</label>
                        <div class="col-xs-7">
                            <input id="Description" name="Description" type="text" class="form-control input" ng-model="cn.Description" />
                        </div>
                    </div>

                    <div class="form-group  ng-class:{'has-error' : (!cn.Amount)}">
                        <label class="col-xs-3 control-label" for="Amount">Amount</label>
                        <div class="col-xs-7">
                            <input id="Amount" name="Amount" type="number" class="form-control input" ng-model="cn.Amount" />
                        </div>
                    </div>

                    <div class="form-group  ng-class:{'has-error' : (!cn.Account)}">
                        <label class="col-xs-3 control-label" for="Account">Account</label>
                        <div class="col-xs-7">
                            <select id="Account" name="Account" class="form-control input"
                                    ng-options="s.Id as  (s.Id + '  :  '+ s.Name) for s in myobAccounts"
                                    ng-model="cn.Account">
                                <option value="">select...</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-xs-3 control-label" for="DateCreated">Date</label>
                        <div class="col-xs-7">
                            <input id="DateCreated" name="DateCreated" type="date" class="form-control input" ng-model="cn.DateCreated" />
                        </div>
                    </div>



                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <div>
            <button class="btn" ng-click="closeThisDialog(null)">Cancel</button>
            <button class="btn" ng-click="returnOrderCredit()"><i class="glyphicon glyphicon-floppy-save"></i> Add </button>
        </div>
    </div>
</div>
