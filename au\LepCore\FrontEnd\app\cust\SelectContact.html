<div class="row" ng-init="idx=null">
	<div class="col-md-12">
		<div class="modal-header">
			<h3 class="modal-title">Contacts list</h3>
		</div>
		<div class="modal-body">
			<div class="row">
				<div >
					<input type="text" ng-model="addrSeach" class="form-control  input-sm" placeholder="seach..."/>
					<table class="table table-condensed table-bordered table-striped" id="addressList">
						<thead>
							<tr>
								<th></th>
								<th>Ordered by Contact Name</th>
								<th>Email</th>
								<th>Phone</th>
								<th>Mobile</th>
							</tr>
						</thead>
						<tbody class="table-wrapper-scroll-y">
							<tr ng-repeat="fa in ContactsList | filter: addrSeach">
								<td>
									<a name="selectedAdress" ng-click="$parent.idx=fa; closeThisDialog(idx)" class="scale2" title="Use this Contact for Ordered by">
										<i class="glyphicon glyphicon-hand-right " style="font-size: 16pt"> </i>
									</a>
								</td>
								<td>{{fa.Name}}</td>
								<td>{{fa.Email}}</td>
								<td>{{fa.AreaCode}} {{fa.Phone}}</td>
								<td>{{fa.Mobile}}</td>
							</tr>
						</tbody>
					</table>
				</div>

			</div>
		</div>
		<div class="modal-footer">
			<div>
				<button class="btn" ng-click="closeThisDialog()">Cancel</button>
				<!--<button class="btn" ng-click="closeThisDialog(idx)"><i class="glyphicon glyphicon-floppy-save" ng-show="idx"></i> Use selected address</button>-->
			</div>
		</div>

	</div>

</div>
