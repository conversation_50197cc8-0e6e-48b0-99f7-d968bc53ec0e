using lep;
using lep.extensionmethods;
using lep.job;
using lep.user;
using System;
using System.Dynamic;
using System.Linq;
using static lep.job.ArtworkStatusOption;
using static lep.job.JobApprovalOptions;
using static lep.job.JobStatusOptions;
using static lep.job.JobTypeOptions;
using static lep.OrderStatusOptions;

namespace LepCore.Controllers
{
	public partial class OrdersController
	{
		protected dynamic GetFlagsRelatedToJob(IJob job)
		{
			if (job == null)
				return null;

			var order = job.Order;

			var adminAndPrepress = new[] { Role.Administrator, Role.Prepress , Role.SuperAdministrator};
			Func<Role[], bool> AllowAccess = roles =>
			{
				if (_currentUser != null && _currentUser is IStaff)
					for (var i = 0; i < roles.Length; i++) if (((IStaff)_currentUser).Role == roles[i]) return true;
				return false;
			};

			var artworkValidForSubmit = job.IsArtworkValidForSubmit();
			var canEdit = job.Order.CanUpdate(_currentUser);
			// var vis = new Dictionary<string, bool>();

			dynamic vis = new ExpandoObject();

			var isAdminAndPrepress = AllowAccess(adminAndPrepress);

			vis.saveButton =/* job.Enable && */job.IsOpenish() && job.Order.Status == OrderStatusOptions.Open;

			var isOptionEditable = job.IsOptionEditable(_currentUser);
			var isArtworkEditable = job.IsArtworkEditable();

			vis.saveButton = isOptionEditable;
			vis.clearButton = isOptionEditable;
			vis.deleteButton = job.Id > 0 && job.IsOpenish() && job.Order.Status == OrderStatusOptions.Open
				&& job.SupplyArtworkApproval != Rejected;

			if (job.Order.Customer != null && (job.Order.Customer as ICustomerUser).PaymentTerms == PaymentTermsOptions.COD &&
				/*job.Enable &&*/ job.HasReject) vis.deleteButton = false;

			if (job.Order.Customer != null && (job.Order.Customer as ICustomerUser).PaymentTerms == PaymentTermsOptions.Account &&
				/*job.Enable &&*/ job.HasReject) vis.deleteButton = true;

			vis.approvalButton = /*job.Enable &&*/ job.QuoteNeedApprove && job.Order.Status == OrderStatusOptions.Open;

			// accept supplied art work by customer
			vis.acceptButton = _currentUser is ICustomerUser && /*job.Enable &&*/
							   job.SupplyArtworkApproval == Rejected &&
							   job.IsOpenish() && artworkValidForSubmit
							   
							   ;
			vis.hasReject = vis.acceptButton;
			vis.acceptButtonWithSubmit = vis.acceptButton && job.Order.Jobs.Count == 1;
			if (vis.acceptButtonWithSubmit) vis.acceptButton = false;

			vis.optionPnlEnabled = (_currentUser is IStaff && isAdminAndPrepress) ||
								   (_currentUser is ICustomerUser && (job == null || isOptionEditable));

			if (_currentUserIsAnonymousWLCustomer && job.Order.Status != OrderStatusOptions.Open)
			{
				vis.optionPnlEnabled = false;
			}

			vis.priceButton = job.Id == 0 || !job.QuoteNeedApprove || string.IsNullOrEmpty(job.Price) ||
							  job.Price == "0";

			if (_currentUser is ICustomerUser) vis.withdraw = job.CanWithdraw(_currentUser);
			else
				vis.withdraw = job.CanWithdraw(_currentUser) && isAdminAndPrepress && canEdit &&
							   job.Status < Packed;

			if (job.Order.Customer != null &&
				(job.Order.Customer as ICustomerUser).PaymentTerms == PaymentTermsOptions.COD /*&&job.Enable*/ && job.HasReject)
				vis.withdraw = false;

			//vis.reactivate = job.Order.Enable && job.CanReactivate(_currentUser) && isAdminAndPrepress && canEdit;

			/*
			 * from order edit .cs
			 */

			//vis.delete = job.IsOpenish() && isAdminAndPrepress && canEdit;

			vis.restart =/* order.Enable && */ /* job.Enable && */
						  job.Status > PreflightDone &&
						  job.Status < JobStatusOptions.Dispatched &&

						  ((job.PrintType == PrintType.O && job.Runs.Count > 0) ||
						   (job.PrintType == PrintType.D))
						  &&
						  isAdminAndPrepress && _currentUser is IStaff;

			//vis.copy = order.Enable && order.Status == OrderStatusOptions.Open && isAdminAndPrepress && canEdit &&
			//           order.PaymentStatus != OrderPaymentStatusOptions.Paid;
			//if (_currentUser is ICustomerUser)
			//	vis.copy = order.Enable && order.Status == OrderStatusOptions.Open &&
			//	           order.PaymentStatus != OrderPaymentStatusOptions.Paid;

			vis.copy = true;

			vis.reorder = order.Status.Is(OrderStatusOptions.Archived, OrderStatusOptions.Dispatched,  OrderStatusOptions.Finished) &&
						  _jobApp.ReadyArtExists(job)  
						  && !job.IsWideFormat()
						  ;


			// approvalButton already specified in line 2176
			vis.previewButton = job.Artworks.Any(a => !string.IsNullOrEmpty(a.Preview)) && isAdminAndPrepress;

			if (vis.previewButton)
				vis.previewButtonText = job.ReadyArtworkApproval == NeedsApproval
					? "Preview artwork (needs your approval)"
					: "Preview artwork";

			// Artwork related
			vis.awaitingArtwork = job.Id > 0 && job.ArtworkStatus == LATER;
			vis.artworkRequiredMsg = job.Id > 0 && !vis.awaitingArtwork && !job.IsArtworkValidForSubmit();

			vis.addOrderHint = job.Id == 0 && _currentUser is ICustomerUser;

			vis.inPrintInst = _currentUser is IStaff && job.Runs.Count > 0 &&
							  job.Runs[0].Status != RunStatusOptions.Filling &&
							  job.Runs[0].Status != RunStatusOptions.LayoutRequired;

			vis.quoteExpireInst = job.Order != null && job.Order.Status == OrderStatusOptions.Open &&
								  job.IsQuoteExpired;

			//    vis.freightGSTText = SiteLocation == lep.SiteLocation.AU;

			vis.hideFileUpload = false;

			if (job.ReOrderSourceJobId != 0) vis.hideFileUpload = true;

			//if (!job.Enable) {
			//	if ((_currentUser is ICustomerUser) && ((ICustomerUser)_currentUser).PaymentTerms == PaymentTermsOptions.COD) {
			//		vis.deleteButton = false;
			//		isOptionEditable = false;
			//		vis.clearButton = false;
			//		vis.saveButton = false;
			//		vis.optionPnlEnabled = false;
			//	}
			//}

			if (_currentUser is IStaff)
			{
				vis.deleteButton = job.IsOpenish() && isAdminAndPrepress;
				vis.saveButton = AllowAccess(new Role[]
					{Role.SuperAdministrator, Role.Administrator, Role.Prepress, Role.MarketingSpecialist, Role.CustomerService, Role.Accounts});
				vis.rejectButton =

					job.Order.Status == OrderStatusOptions.Submitted &&
					(job.Status == JobStatusOptions.Submitted || job.Status == JobStatusOptions.UnableToMeetPrice || job.Status == PreflightDone)
					//&& job.Enable
					&& job.SupplyArtworkApproval != Rejected && isAdminAndPrepress;

				vis.clearButton = job.IsOptionEditable(_currentUser) && isAdminAndPrepress;
				vis.reprintButton = job != null &&
									//job.Status != JobStatusOptions.Open &&
									//job.Status != JobStatusOptions.Submitted && job.Status != PreflightDone &&
									 job.Status >= JobStatusOptions.Dispatched

									&& isAdminAndPrepress;

				vis.acceptButton = _currentUser is IStaff &&/* job.Enable &&*/
								   job.SupplyArtworkApproval == Rejected &&
								   (job.IsOpenish() || job.Status == JobStatusOptions.Submitted);

				var RdyArtworkInst = false;
				if (job != null)
					foreach (var art in job.Artworks)
						if (!string.IsNullOrEmpty(art.Supplied) && string.IsNullOrEmpty(art.Ready) &&
							job.SupplyArtworkApproval != Rejected)
						{
							RdyArtworkInst = true;
							break;
						}
				vis.RdyArtworkInst = RdyArtworkInst;

				vis.QuoteInst = job.Id > 0 && string.IsNullOrEmpty(job.Price);
				vis.NoActionInst = job.Id > 0 && RdyArtworkInst == false && vis.QuoteInst == false;

				var containsupplyart = false;
				foreach (var art in job.Artworks) if (!string.IsNullOrEmpty(art.Supplied)) containsupplyart = true;

				vis.moveArtButtonVisible = job != null && containsupplyart &&
										   (job.Template.Id != (int)BusinessCard) &
										   (job.Template.Id != (int)BusinessCardNdd) &
										   (job.Template.Id != (int)BusinessCardSdd) &
										   (job.Template.Id != (int)Postcard)
										   && isArtworkEditable /*&& job.Enable*/ && isAdminAndPrepress;

				string userAgent = HttpContext.Request.Headers["User-Agent"];
				if (job.Id > 0)
				{
					var jobFolderName = job.JobFolderName;
					if (userAgent.ToLower().IndexOf("mac") != -1)
					{
						if (order.Status == Archived)
							vis.artworkFolderNavigateUrl =
								$"lep:mount:{LepGlobal.Instance.ArtworkArchiveDirectoryMac}/{order.DateCreated:yyyyMMdd}/{order.OrderNr}/{jobFolderName}"
									.Replace(" ", "%20");
						else
							vis.artworkFolderNavigateUrl =
								$"lep:mount:{LepGlobal.Instance.ArtworkDirectoryMac}/{order.DateCreated:yyyyMMdd}/{order.OrderNr}/{jobFolderName}"
									.Replace(" ", "%20");
					}
					else
					{
						// IE/win32 gets a file explorer link
						if (order.Status == Archived)
							vis.artworkFolderNavigateUrl =
								$"{LepGlobal.Instance.ArtworkArchiveDirectoryPC}/{order.DateCreated:yyyyMMdd}/{order.OrderNr}/{jobFolderName}";
						else
							vis.artworkFolderNavigateUrl =
								$"{LepGlobal.Instance.ArtworkDirectoryPC}/{order.DateCreated:yyyyMMdd}/{order.OrderNr}/{jobFolderName}";
					}
				}
			}

			vis.sendSamplesCheckVisible = false;
			vis.sendSamplesCheckEnabled = false;

			if (_currentUser is ICustomerUser)
			{
				var customer = _currentUser as ICustomerUser;
				if (customer.AllowedSamples == true)
				{
					vis.sendSamplesCheckVisible = true;
					if (order == null)
					{
						vis.sendSamplesCheckEnabled = true;
					}
					else if ((int)order.Status < (int)OrderStatusOptions.Submitted)
					{
						vis.sendSamplesCheckEnabled = true;
					}
				}
			}
			else
			{
				var customer = order.Customer;
				if (customer != null && customer.AllowedSamples)
				{
					vis.sendSamplesCheckVisible = true;
					vis.sendSamplesCheckEnabled = true;
				}
			}


					// adorn with progress infomation for client side ui
			if (job.Status >= JobStatusOptions.Submitted && job.Order.Status >= OrderStatusOptions.Submitted &&
				job.Order.Status < OrderStatusOptions.Dispatched)
			{
			}
			vis.Progress = _cachedAccessTo.JobProgress(job);
			vis.removeArtButtonVisible =
				job != null && job.Artworks.Count > 0 && (isArtworkEditable || isAdminAndPrepress);

			vis.IsArtworkEditable = isArtworkEditable;
			vis.healthCss = _jobBoardDTOHelper.GetJobHealthCSS(job.Id);

			if (job.ReOrderSourceJobId != 0 /* that is this is  a re-order job*/)
			{
				vis.IsArtworkEditable = false;
				vis.removeArtButtonVisible = false;
				vis.optionPnlEnabled = false;
			}

			if (job.ReOrderSourceJobId != 0 && string.IsNullOrEmpty(job.Price))
			{
				//	vis.optionPnlEnabled = true;
			}
			//vis.Thumbs = _configApp.GetThumbs(job).Take(2).Select(f => f.Name).ToList();
			vis.Thumbs = _cachedAccessTo.Thumbnails(job).Take(2).Select(f => f.Name).ToList();

			if (_currentUserIsAnonymousWLCustomer && job.Order.IsWLOrder && job.Order.IsWLOrderPaidFor)
			{
				vis.optionPnlEnabled = false;
				vis.IsArtworkEditable = false;
				vis.deleteButton = false;
			}

			if (_currentUserIsLoggedInWLCustomer && job.Order.IsWLOrder && job.Order.IsWLOrderPaidFor)
			{
				vis.optionPnlEnabled = false;
				vis.IsArtworkEditable = false;
				vis.deleteButton = false;
			}

			if (_currentUser is IStaff)
			{
				if (job.ReOrderSourceJobId != 0)
				{
					vis.optionPnlEnabled = true;
				}
			}

			//bool inHolidays = bool.Parse(_config["HolidayMode"]);
			//if (inHolidays && (job.IsNDD() || job.IsSDD()))
			//{
			//	vis.reorder = false;
			//	vis.copy = false;
			//}

			//if (job.IsWideFormat())
			//{
			//	vis.reorder = false;
			//	vis.copy = false;
			//}
			

			if (job.Status == JobStatusOptions.RejectedVariation)
				vis.copy = false;

			if (job.Status >= JobStatusOptions.Submitted && job.Status <= JobStatusOptions.Dispatched && _currentUser is ICustomerUser)
			{
				vis.optionPnlEnabled = false;
			}


			vis.splitDeliveryPossible = job.Status == JobStatusOptions.Open && job.Order.Jobs.Count == 1 &&
						(job.IsBrochure() || job.IsMagazine() || job.IsMagazineSeparate() 
						|| job.Template.Is(Stationery, StationerySDD) 
						);
			vis.splitCount = job.Splits?.Count ?? 0;



			return vis;
		}
	}
}
