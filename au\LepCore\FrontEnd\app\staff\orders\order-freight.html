<script type="text/ng-template" id="packageTree">
    <div>
        <span ng-hide="(package.JobId==0 || package.JobId=='0') ">
            <input style="width:80px; text-align: right; padding:2px; height:24px;  border: 1px solid lightgray;" ng-model="package.JobId" title="Job Id">
        </span>

        <select style=" padding:2px; height:24px;  border: 1px solid lightgray;" ng-options="c.Code as c.Code for c in packages" ng-model="package.CartonCode" title="Carton"> </select>

        <input style="width:60px; text-align: right; padding:2px; height:24px;  border: 1px solid lightgray;" ng-model="package.Weight" title="Conent + package Weight in Kg"> Kg

        <span ng-hide="package.JobQty && package.JobQty==0">
            @<input style="width:80px; text-align: right; padding:2px; height:24px;  border: 1px solid lightgray;" ng-model="package.JobQty" placeholder="Qty" title="Quantity in each box">
        </span>

        <!--<span style="font-size:x-small" ng-if="package.CartonLevel"> (Level: {{ package.CartonLevel}}) </span>-->

        <span class="glyphicon glyphicon-plus" style="color:lightgray; cursor: pointer; " ng-click="addNodeUnder(package)" title="Add Box Inside"></span>
        <span class="glyphicon glyphicon-sort-by-attributes" style="color:lightgray; cursor: pointer; " ng-click="replicateNode(this)" title="Replicate package n times"></span>
        <span class="glyphicon glyphicon-remove" style="color:lightgray; cursor: pointer;" ng-click="deleteNode(parent,$index)" title="Remove this box preserving Boxes inside"></span>
        <span class="glyphicon glyphicon-remove-sign" style="color:lightgray; cursor: pointer;" ng-click="deleteNode2(parent,$index)" title="Remove this box including Boxes inside"></span>

        <!--parent.splice($index,1)-->
    </div>

    <ol ng-if="package.Items">
        <li ng-repeat="package in package.Items track by $index" ng-include="'packageTree'" ng-init="parent = $parent.$parent.package"></li>
    </ol>
</script>

<div leppane="Packing details" visible="true">
    <table class="table table-bordered">
        <tr>
            <td width="50%" ng-show0="vm.PackDetail.FGPackageCount" style="vertical-align:top; padding:10px;">
                <h5>Packaging - Forest Glen</h5>
                <ol>
                    <li ng-repeat="package in vm.PackDetail.FGPackageJson  track by $index" ng-include="'packageTree'" ng-init="parent = vm.PackDetail.FGPackageJson"></li>
                    <span ng-click="vm.PackDetail.FGPackageJson.push({})"><i class="glyphicon glyphicon-plus"></i>  Add a package</span>
                    <span ng-click="vm.PackDetail.FGPackageJson = wrapIn(vm.PackDetail.FGPackageJson)"
                          ng-show="vm.PackDetail.FGPackageJson.length>1"><i class="glyphicon glyphicon-plus"></i>Wrap in a package</span>
                </ol>


            </td>
            <td width="50%" ng-show0="vm.PackDetail.PMPackageCount" style="vertical-align:top; padding:10px;">
                <h5>Packaging - Port Melbourne</h5>
                <ol>
                    <li ng-repeat="package in vm.PackDetail.PMPackageJson  track by $index" ng-include="'packageTree'" ng-init="parent = vm.PackDetail.PMPackageJson"></li>
                    <span ng-click="vm.PackDetail.PMPackageJson.push({})"><i class="glyphicon glyphicon-plus"></i>  Add a package</span>
                    <span ng-click="vm.PackDetail.PMPackageJson = wrapIn(vm.PackDetail.PMPackageJson)"
                          ng-show="vm.PackDetail.PMPackageJson.length>1"><i class="glyphicon glyphicon-plus"></i>Wrap in a package</span>
                </ol>



            </td>
        </tr>


    </table>
    <label>
        <input type="checkbox" ng-model="vm.PackDetail.IsCustomPackages" /> Is this custom packaging? ( so that it does not get overwritten by system)
    </label>
</div>
<div class="row">
    <div class="col-sm-12">
        <div class="form-actions pull-right">

            <a class="btn " ng-click="backToOrder()"><i class="glyphicon glyphicon-chevron-left "></i> Back to order</a>

            <button type="button" class="btn btn-default " ng-click="setFreightPackDetails()"><span class="glyphicon glyphicon-floppy-save"></span> Save order</button>


            <a class="btn btn-default "  ng-click="closeWindow()">Close Window</a>

        </div>
    </div>
</div>



<div leppane="Courier details" visible="true">
    <div class="row">

        <div class="col-sm-6">

            <h5 class="bar">Customer selected courier and service</h5>


            <div class="form-group">
                <label class="control-label">Current</label>

                <div>{{vm.Courier}} - {{vm.PackDetail.Price|currency}}</div>



                <select id="freights" class="form-control input monospace"
                        ng-model="vm.Courier"
                        ng-options="f.Value as f.Name for f in freights"></select>

            </div>

            <div class="form-group">
                <label class="control-label ">Override Price ($ ex GST)</label>
                <input class="form-control" ng-model="vm.PackDetail.Price2" />

            </div>


        </div>



        <div class="col-sm-6">

        </div>
    </div>

    <hr />

    <div class="row">


        <table class=" ">
            <tr>
                <td class="col-sm-6">
                    <div ng-show="vm.PackDetail.FGPackageCount">

                        <h5 class="bar">Forest Glen</h5>

                        <div class="form-group">
                            <label class="control-label">Current Courier</label>
                            <div>{{vm.PackDetail.FGCourier}} </div>
                        </div>


                        <div class="form-group">
                                <select id="freightsFG" class="form-control input monospace"
                                        ng-model="vm.PackDetail.FGCourier"
                                        ng-options="f.Value as f.Name for f in freightsFG"></select>
                        </div>


                        <div class="form-group">
                            <label class="control-label">Total weight (kg): {{::vm.PackDetail.FGWeight}} </label>
                        </div>


                        <div class="form-group">
                            <label class="control-label">Packing specification</label>

                            <textarea class="form-control input" rows="6"
                                      ng-model="vm.PackDetail.FGPackingSpecification"></textarea>


                        </div>
                    </div>


                </td>


                <td class="col-sm-6">

                    <div ng-show="vm.PackDetail.PMPackageCount">
                        <h5 class="bar">Port Melbourne</h5>
                        <div class="form-group">
                            <label class="control-label">Current Courier</label>
                            <div>{{vm.PackDetail.PMCourier}} </div>
                        </div>


                        <div class="form-group">
                                <select id="freightsPM" class="form-control input monospace"
                                        ng-model="vm.PackDetail.PMCourier"
                                        ng-options="f.Value as f.Name for f in freightsPM"></select>

                            </div>


                                <div class="form-group">
                                    <label class="control-label">Total weight (kg): {{::vm.PackDetail.PMWeight}} </label>
                                </div>

                                <div class="form-group">
                                    <label class="control-label">Packing specification</label>
                                    <textarea class="form-control input" rows="6"
                                              ng-model="vm.PackDetail.PMPackingSpecification"></textarea>


                                </div>
                            </div>


                </td>
            </tr>

        </table>






    </div>


    <div class="row">
        <div class="col-sm-12">
            <div connotes order="vm">

            </div>
            <div ng-click="syncConnotes()" class="btn btn-sm"> <i class="glyphicon glyphicon-refresh"></i>  Pull Connotes from SF</div>
        </div>
    </div>

    <!--
    <div leppane="debug" visible="true">
            <json-explorer data="order"></json-explorer>
        </div>

        -->


</div>
<div class="row">
    <div class="col-sm-12">
        <div class="form-actions pull-right">

            <a class="btn " ng-click="backToOrder()"><i class="glyphicon glyphicon-chevron-left "></i> Back to order</a>
    
            <button type="button" class="btn btn-default " ng-click="setFreightPackDetails()"><span class="glyphicon glyphicon-floppy-save"></span> Save order</button>

            <a class="btn btn-default "  ng-click="closeWindow()">Close Window</a>



        </div>
    </div>
</div>



<!--
<pre>
{{	$scope.packages | json}}
{{order.PackDetail.FGPackageStr}}
{{order.PackDetail.PMPackageStr}}
</pre>
<pre>{{vm | json}}</pre>
-->
