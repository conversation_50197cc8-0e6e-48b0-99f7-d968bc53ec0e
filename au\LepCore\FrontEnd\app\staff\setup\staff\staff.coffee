appStaff = angular.module('app.staff')

Role1 = {
    "0": "Prepress",
    "1": "Press and Finshing",
    "2": "Accounts",
    "3": "Customer Service",
    "4": "Administrator",
    "5": "Marketing Specialist",
    #"6": "System",
    "7": "Packing",
    "8": "Dispatch",
    "9": "Invoicing",
	"10": "Scanner",
   }

appStaff.factory 'Staff', ['$resource', ($resource) ->
	$resource 'api/Staff/Staffs/:Id', { Id: '@Id' },
		'update': method: 'PUT'
		'query':
			method: 'POST'
			isArray: false
		'patch':
			method: 'PATCH'
]

appStaff.controller 'StaffStaffListController', [
	'$scope','enums', 'Staff',
	($scope,  enums, Staff) ->

#		lepApi.post('Orders/CustOrders', $scope.vm).then (r) ->
#			$scope.orders = r
#
		$scope.enums = enums;
		$scope.vm = {Page:1, Roles:[]}
		if $scope.$root.globals.IsSA
			Role1["11"] = "Super Administrator"
			
		$scope.Role1 = Role1

		$scope.search = () ->
			Staff.query($scope.vm, (r) -> $scope.r = r)

		$scope.toggleSort = (sort) ->
			$scope.vm.SortDir =  sort == $scope.vm.SortField and !$scope.vm.SortDir
			$scope.vm.SortField = sort

			$scope.search();

		$scope.goPage= (p) ->
			$scope.vm.Page = p
			$scope.search()

		$scope.search()

		return @
	]



app.controller 'StaffStaffViewController', [
	'$scope','lepApi2',	'$location','enums', '$stateParams', 'Staff', '$state',
	($scope, lepApi2, $location, enums, $stateParams, Staff, $state) ->

		$scope.vis = {}
		$scope.enums = enums;
		$scope.Role1 = Role1

		vmr = null
		vmo = null
		$scope.load = (id) -> Staff.get({Id:id},  (r) ->
			vmr = r

			vmo = angular.copy(vmr)
			delete vmo.$resource
			delete vmo.$promise

			$scope.vm = angular.copy(vmo)
		)

		$scope.load($stateParams.id)

		$scope.save = () ->
			Staff.update({Id: $stateParams.id}, $scope.vm, (value, responseHeadersFn, status, statusText) ->
				id = parseInt(responseHeadersFn('Id'), 10)
				toastr.info( "Success")
				$state.go('staff.setup.staff-view', {id: id},   {reload: true})
			)

		$scope.delete = () ->
			Staff.delete({Id: $stateParams.id}, (value, responseHeadersFn, status, statusText) ->
				toastr.info( "Success")
				$state.go('staff.setup.staff-list', {reload: true})
			)


		$scope.resetPassword = () ->
			url = "Users/#{$stateParams.id}/password/reset"
			newpassword =  $scope.vm.Password1
			lepApi2.post(url, JSON.stringify(newpassword)).then ()->
				toastr.info("Password updated")

	]



