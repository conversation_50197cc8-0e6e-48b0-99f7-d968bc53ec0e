﻿#roundingbox {
    margin: 10px 10px;

    .box {
        border: none;
        transition: all ease-in .3s;
        font-size: 14px;
        font-weight: bold;
        text-align: center;
        color: #ececec;
        background: #aeaeae;
        display: block;
        position: relative;
        user-select: none;
		border: 1px solid #ececec!important;
    }

    .bc {
        width: 54mm;
        height: 54mm;
    }

    .dbc {
        width: 110mm;
        height: 90mm;
    }

    .portrait {
        -webkit-transform: rotate(90deg);
        -moz-transform: rotate(90deg);
        -o-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
        transform: rotate(90deg);
    }

    .r3.tl {
        border-top-left-radius: 3mm;
    }

    .r3.tr {
        border-top-right-radius: 3mm;
    }

    .r3.bl {
        border-bottom-left-radius: 3mm;
    }

    .r3.br {
        border-bottom-right-radius: 3mm;
    }

    .r35.tl {
        border-top-left-radius: 3.5mm;
    }

    .r35.tr {
        border-top-right-radius: 3.5mm;
    }

    .r35.bl {
        border-bottom-left-radius: 3.5mm;
    }

    .r35.br {
        border-bottom-right-radius: 3.5mm;
    }


    .r6.tl {
        border-top-left-radius: 6mm;
    }

    .r6.tr {
        border-top-right-radius: 6mm;
    }

    .r6.bl {
        border-bottom-left-radius: 6mm;
    }

    .r6.br {
        border-bottom-right-radius: 6mm;
    }

    .r10.tl {
        border-top-left-radius: 10mm;
    }

    .r10.tr {
        border-top-right-radius: 10mm;
    }

    .r10.bl {
        border-bottom-left-radius: 10mm;
    }

    .r10.br {
        border-bottom-right-radius: 10mm;
    }


    .r24.tl {
        border-top-left-radius: 24mm;
    }

    .r24.tr {
        border-top-right-radius: 24mm;
    }

    .r24.bl {
        border-bottom-left-radius: 24mm;
    }

    .r24.br {
        border-bottom-right-radius: 24mm;
    }

    .cnr {
        position: absolute;
        width: 20px;
        height: 20px;
        cursor: pointer;
        z-index: 100;
	    border: 1px dashed white;
		border-radius:15px;
    }
}

#roundingbox.ng-invalid .box {
  border: 1px solid red!important;
}




.appear-enter,
.appear-leave {
    -webkit-transition: 300ms cubic-bezier(0.250, 0.250, 0.750, 0.750) all;
    -moz-transition: 300ms cubic-bezier(0.250, 0.250, 0.750, 0.750) all;
    -ms-transition: 300ms cubic-bezier(0.250, 0.250, 0.750, 0.750) all;
    -o-transition: 300ms cubic-bezier(0.250, 0.250, 0.750, 0.750) all;
    transition: 300ms cubic-bezier(0.250, 0.250, 0.750, 0.750) all;
    position: relative;
    display: block;
    overflow: hidden;
    text-overflow: clip;
    white-space: nowrap;
}

.appear-leave.appear-leave-active,
.appear-enter {
    opacity: 0;
    width: 0px;
    height: 0px;
}

.appear-enter.appear-enter-active,
.appear-leave {
    opacity: 1;
    width: 150px;
    height: 30px;
}















/*----------------------------------------------------------------------------*/



.animate-enter,
.animate-leave {
    -webkit-transition: 400ms cubic-bezier(0.250, 0.250, 0.750, 0.750) all;
    -moz-transition: 400ms cubic-bezier(0.250, 0.250, 0.750, 0.750) all;
    -ms-transition: 400ms cubic-bezier(0.250, 0.250, 0.750, 0.750) all;
    -o-transition: 400ms cubic-bezier(0.250, 0.250, 0.750, 0.750) all;
    transition: 400ms cubic-bezier(0.250, 0.250, 0.750, 0.750) all;
    position: relative;
    display: block;
}

.animate-enter.animate-enter-active,
.animate-leave {
    opacity: 1;
    top: 0;
    height: 30px;
}

.animate-leave.animate-leave-active,
.animate-enter {
    opacity: 0;
    top: -50px;
    height: 0px;
}



.fade {
    transition: 1s linear all;
    -webkit-transition: 1s linear all;
}

.fade.ng-enter {
    opacity: 0;
}
