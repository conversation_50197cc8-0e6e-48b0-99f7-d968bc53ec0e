﻿<div ng-form="jobForm">
    <div class="form-horizontal">
        <h4 class="jobH4">Job</h4>
        <!-- common order details -->
        <div class="row">
            <div class="col-md-9">

                <div class="form-horizontal">
                    <div class="form-group" ng-hide="job.Id == 0">
                        <label class="col-xs-3 control-label">Job #</label>
                        <div class="col-xs-7 form-control-static price">{{::(job.Id || 'New Job')}}</div>
                    </div>

                    <div class="form-group ng-class:{'has-error': jobForm.JobName.$invalid}">
                        <label class="col-xs-3 control-label" for="JobName">Job Name</label>
                        <div class="col-xs-7">
                            <input id="JobName" name="JobName" type="text" class="form-control input"
                                   ng-model="job.Name" ng-required="true" maxlength="80"
                                   ng-disabled="!(job.Visibility.optionPnlEnabled)" />
                        </div>
                    </div>
                </div>

                <div lep-job-details job="job" price-calc="false" dcats="dcats"
                     vnp="0" vpp="1"></div>

                <div class="form-horizontal">
                    <!--Special instructions-->
                    <div class="form-group ng-class:{'has-error' : (!job.Copies || job.Copies<=0 ||job.Copies>99)}"
                         ng-if="!priceCalc && job.Id == 0" ng-init="job.Copies = 1">

                        <label class="col-xs-3  control-label" title="Enter a value from 1 to 99">Number of Kinds</label>
                        <div class="col-xs-2 form-control-static">
                            <input type="number" class="form-control input"
                                   ng-model="job.Copies" ng-min="1" ng-max="99" maxlength="2" ng-required="true" title="Enter a value from 1 to 99" />
                        </div>
                        <div class="col-xs-6 ">
                            <span class="help-block">
                                Enter the number of Jobs you want to create from the above template.
                                This can be 1 upto 99.
                            </span>
                        </div>
                    </div>
                </div>

                <div ng-hide="job.Copies>1" lep-job-artwork-uploader-async job="job"></div>
            </div>
        </div>

        <div class="row" ng-if="job.Visibility.optionPnlEnabled">
            <div class="col-md-10 col-sm-10">
                <div class="form-actions">
                    <a class="btn  btn-default" ng-show="job.Visibility.clearButton" ng-click="reload()"><i class="glyphicon glyphicon-refresh"></i> Clear </a> &nbsp;&nbsp;

                    <!--<a class="btn" ng-show="job.Visibility.withdraw" ng-click="jobCommand('Withdraw')">Withdraw</a>-->

                    <button type="submit" class="btn btn-success " ng-show="job.Visibility.saveButton && job.PriceWL" click-and-wait="saveJob2()"
                            ng-disabled="jobForm.$invalid">
                        <i class="glyphicon ng-class:{ 'glyphicon-floppy-save':  job.Id != 0, 'glyphicon-plus':  job.Id == 0 }  "></i>
                        {{ job.Id == 0? "Continue" : "Save Job" }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
