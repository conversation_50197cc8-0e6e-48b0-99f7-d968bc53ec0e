// @ts-check

(function (angular, window, document) {
    "use strict";

    window.cd = ((window.console && window.console.debug) ? window.console.debug : function () { });
    window.cdn = function () { console.debug("line:", /\(htto:[\w\d/.-]+:([\d]+)/.exec(new Error().stack)[1]) };
    var toastr = window.toastr;

    toastr.options = {
        "preventDuplicates": true,
        "preventOpenDuplicates": true,
        "closeButton": true,
        "debug": false,
        "newestOnTop": false,
        "progressBar": false,
        "maxOpened": 1,
        "showDuration": "100",
        "hideDuration": "0",
        "timeOut": "7000",
        "extendedTimeOut": "0",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut",
        "positionClass": "toast-bottom-center"
    };

    var appCore = angular.module('app.core', []);
    var appStaff = angular.module('app.staff', ['app.core']);
    var appCust = angular.module('app.cust', ['app.core']);

    var app = angular.module('app', [
        'oc.lazyLoad',
        'ui.router',
        'ngResource',
        'ngDialog',
        'ui.select',
        'ui.sortable',
        //'ngMessages',
        'ngCookies',
        'ngStorage',
        'angular-loading-bar',
        'rzModule',
        'angucomplete-alt',
        'rzModule',
        'bw.paging',
        'ui.grid', 'ui.grid.edit', 'ui.grid.cellNav', // only in staff price setup page
        'angularMoment',
        'dm.animateModelChange',
        'angularjs-datetime-picker',
        //'angular.filter',
        //'FormErrors',
        'mgcrea.ngStrap',
        'mgcrea.ngStrap.tab',
        'app.core',
        'app.cust',
        'app.staff',
        'ngDroplet',
        'signature',
        //"angulartics",
        //"angulartics.google.tagmanager"
    ]);

    app.config(['$sceDelegateProvider', function ($sceDelegateProvider) {
        $sceDelegateProvider.resourceUrlWhitelist([
            'self', // Allows same-origin URLs
            'localexplorer:**' // Allows URLs with the localexplorer: protocol
        ]);
    }]);

    app.factory('Auth', ['$http', '$rootScope', '$timeout', '$localStorage', '$sessionStorage',
        function ($http, $rootScope, $timeout, $localStorage, $sessionStorage) {
            var service = {};

            service.Login = function (username, password, callback) {
                $http.post('/api/Account/Authenticate', { Username: username, Password: password })
                    .then(function (r) {
                        if (r.data.token) {
                            var lepToken = r.data.token;
                            //$localStorage.lepToken = lepToken;
                            if (r.data.IsStaff)
                                $localStorage.lepToken = lepToken;
                            else
                                $sessionStorage.lepToken = lepToken;

                            $http.get('/api/Account/LoginDetails').then(function (response) {

                                callback(response);
                                $rootScope.$broadcast('loggedin', response);
                            });
                        }
                    }, function () {
                        toastr.error("Login failed, please try again.");
                    });
            };

            service.ClearCredentials = function () {

                $rootScope.globals = {};
                var favSearches = angular.copy($localStorage.favSearches);
                var favRunSearches = angular.copy($localStorage.favRunSearches);
                $localStorage.$reset();
                $localStorage.favSearches = favSearches;
                $localStorage.favRunSearches = favRunSearches;

                $sessionStorage.$reset();
            };

            return service;
        }
    ]);

    app.factory('lepInterceptor1', ['$q', '$location', '$rootScope',
        function ($q, $location, $rootScope) {
            var wlCustRegex = /.*\/wl\/(\d+)\/.*/gm;
            var em = "<div style=\"float:left\">\n<b>Whoops. That was unexpected. There was an error. \nWe have been notified and working on it now.<br/>\nPlease try refreshing the pages soon, or contact customer support for assistance.</div>";
            return {
                'requestError': function (rejection) {
                },
                'responseError': function (rejection) {
                    if (rejection.status == 502) {
                        toastr.error(em);
                    } else if (rejection.status == 400) {
                        toastr.error(rejection.data);
                    } else if (rejection.status == 500) {
                        toastr.error(em);
                    } else if (rejection.status == 401) {
                        if ($location.path() !== '/login') {
                            $rootScope.postLoginRoute = $location.path();
                        }

                        if (rejection.config.url.indexOf("LoginDetails") == -1) {
                            toastr.error("401 Unauthorized");
                        }

                        //see if this is being loaded from Vendors website iframes(those will request index_r1.html)
                        var isForFrameHostedClients = (document.URL.indexOf("index_r1") > -1) || document.URL.indexOf("index_wl") > -1;
                        if (!isForFrameHostedClients) {
                            // redirect to login page if not logged in
                            var ss;
                            delete $rootScope.globals;
                            if ((ss = window.localStorage) != null) {
                                ss.clear();
                            }
                            setTimeout(function () {
                                $location.path('/login');
                            });
                        }

                    } else if (rejection.status == 402) {
                        // Payment required, server returns  payment url in the headers as location
                        window.location.href = rejection.headers().location;
                    } else if (rejection.status == 403) {
                        toastr.error("403 Forbidden");
                    }
                    return $q.reject(rejection);
                }
            };
        }]);

    app.factory('lepInterceptorJwt', ['$localStorage', '$sessionStorage',
        function ($localStorage, $sessionStorage) {
            return {
                'request': function (config) {
                    if ($sessionStorage.lepToken) {
                        config.headers.Authorization = 'Bearer ' + $sessionStorage.lepToken;
                    } else if ($localStorage.lepToken) {
                        config.headers.Authorization = 'Bearer ' + $localStorage.lepToken;
                    }

                    if ($sessionStorage.SessionId) {
                        config.headers.SessionId = $sessionStorage.SessionId;
                    } else if ($localStorage.SessionId) {
                        config.headers.SessionId = $localStorage.SessionId;
                    }

                    return config;
                },
            };
        }]);

    var states = [
        { name: 'login', url: '/login', templateUrl: 'login.html', controller: 'LoginController', class: 'login' },
        { name: 'reset-password', url: '/reset-password', templateUrl: 'reset-password.html', class: 'login' },
        { name: 'help', url: '/help', templateUrl: 'help.html' },
        { name: 'open', url: '/open', controller: function () { } },

        { //todo: printing order
            name: 'orderprint',
            url: '/print/{orderId:int}',
            templateUrl: 'order-print.html',
            controller: 'OrderPrintController',
            onEnter: function () {
            },
            resolve: {
                order: ['lepApi2', '$stateParams', function (lepApi2, $stateParams) {
                    return lepApi2.post('Orders/order/' + $stateParams.orderId + '/large2');
                }]
            }
        }

    ];

    app.config([
        '$stateProvider', '$httpProvider', '$sceProvider', '$locationProvider', '$parseProvider', '$provide', '$rootScopeProvider', 'cfpLoadingBarProvider', '$compileProvider',
        function ($stateProvider, $httpProvider, $sceProvider, $locationProvider, $parseProvider, $provide, $rootScopeProvider, cfpLoadingBarProvider, $compileProvider) {
            $compileProvider.aHrefSanitizationWhitelist(/^\s*(https?|ftp|mailto|chrome-extension|lep|file):/);

            // set up credential & cors in angular
            $httpProvider.defaults.headers.get = { 'If-Modified-Since': 'Mon, 26 Jul 1997 05:00:00 GMT', 'Cache-Control': 'no-cache', 'Pragma': 'no-cache' };
            $httpProvider.defaults.useXDomain = true;
            $httpProvider.defaults.withCredentials = true;
            delete $httpProvider.defaults.headers.common['X-Requested-With'];

            $sceProvider.enabled(false);
            $locationProvider.html5Mode({ html5Mode: true, requireBase: false });

            angular.forEach(states, function (state) {
                $stateProvider.state(state);
            });

            states = null;

            cfpLoadingBarProvider.includeBar = true;
            cfpLoadingBarProvider.includeSpinner = true;
            $httpProvider.interceptors.push('lepInterceptor1');
            $httpProvider.interceptors.push('lepInterceptorJwt');
        }
    ]);

    app.config(['ngDialogProvider', function (ngDialogProvider) {
        ngDialogProvider.setDefaults({
            className: 'ngdialog-theme-default',
            //plain: true,
            showClose: true,
            closeByEscape: true,
            closeByDocument: false,
            backdrop: 'static',
            disableAnimation: true,
            width: '600px'
        });
    }]);

    app.run(['$rootScope', '$location', '$http', '$localStorage', '$sessionStorage', 'enums', '$state', '$window', 'Auth', 'lepApi2', 'cfpLoadingBar', 'lepRobot',
        function ($rootScope, $location, $http, $localStorage, $sessionStorage, enums, $state, $window, Auth, lepApi2, cfpLoadingBar, lepRobot) {
            if (!$sessionStorage.SessionId) {
                $sessionStorage.SessionId = + new Date();
            }


            // the url parameter t is lepToken, only present in whitelabel iframe, save that in session storage if present
            try {
                var t = document.location.hash.split("t=")[1];
                if (t) {
                    $sessionStorage.lepToken = t;
                    delete $sessionStorage.WLCustomerId;
                }
            } catch (e) { }

            $rootScope.$http = $http;

            $rootScope.$watch('$http.pendingRequests.length', function (n, o) {
                $rootScope.pending = n > 0;
            });

            $rootScope.enums = enums; // make enums available everywhere.
            $rootScope.$state = $state;
            $localStorage.search = $localStorage.search || {};
            $localStorage.searchresults = $localStorage.searchsearchresults || {};

            $rootScope.globals = {};

            //$http.get('/api/Account/LoginDetails').then(function (response) {
            //    if (!response.data.error) {
            //        //$localStorage.globals = response.data;
            //        $rootScope.globals = response.data;
            //        if (response.data.User.PrintPortalSettings) {
            //            $rootScope.ppv = response.data.User.PrintPortalSettings.Version;
            //        }
            //    }
            //});

            $rootScope.$on('update-login-details', function () {
                $http.get('/api/Account/LoginDetails').then(function (response) {
                    if (!response.data.error) {

                        //$localStorage.globals = response.data;
                        $rootScope.globals = response.data;
                        if (response.data.User.PrintPortalSettings) {
                            $rootScope.ppv = response.data.User.PrintPortalSettings.Version;
                        }
                    }
                });
            });


            window.document.addEventListener("visibilitychange", function () {
                $rootScope.$broadcast('visibilitychange', window.document.visibilityState);
            });


            $rootScope.$on('$stateChangeSuccess', function (event, toState, toParams, fromState, fromParams) {
                if (fromState.name == toState.name) {
                    return;
                }

                if ($state.current.title) {
                    $rootScope.title = 'My LEP - ' + $state.current.title;
                } else {
                    $rootScope.title = 'My LEP';
                }

                // hide chat window
                if (toState.name == 'login') {
                    try {
                        if (window.zE)
                            window.zE(function () {
                                window.zE.hide();
                            });
                    }
                    catch (err) {
                    }
                }
            });

            /*    
            try {
                setTimeout(function () {
                    if (window.zE)
                        window.zE(function () {
                            window.$zopim(function () {
                                window.$zopim.livechat.hideAll();
                                if ($rootScope.globals.User && !$rootScope.globals.User.IsStaff) {
                                    $zopim.livechat.window.hide();
                                }
                            });
                        });
                }, 1000);
            }
            catch (err) {
            }
            */

            $rootScope.$broadcast('update-login-details');
        }
    ]);
})(window.angular, window, window.document);



if (!String.prototype.endsWith) {
    String.prototype.endsWith = function (searchString, position) {
        var subjectString = this.toString();
        if (typeof position !== 'number' || !isFinite(position) || Math.floor(position) !== position || position > subjectString.length) {
            position = subjectString.length;
        }
        position -= searchString.length;
        var lastIndex = subjectString.indexOf(searchString, position);
        return lastIndex !== -1 && lastIndex === position;
    };
}