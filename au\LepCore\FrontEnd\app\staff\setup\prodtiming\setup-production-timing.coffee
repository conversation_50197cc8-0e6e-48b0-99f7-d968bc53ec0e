appStaff = angular.module('app.staff')


 
appStaff.controller 'StaffSetupProductionTimingController', [
	'$scope'
	'lepApi2'
	'enums'
	'JobService'
	'OrderService',  'Utils', '$http',   '$stateParams'
	($scope, lepApi2,  enums, JobService, OrderService, Utils, $http,   $stateParams) ->

		s = enums.ValueKey.JobStatusOptions
		r = []
		for k,v of s 
			r.push( { id: parseInt(k,10), value: v} )


		$scope.filters = { TemplateId :1, Facility: "FG", Folding: "false"}
		$scope.result = []

		JobService.getTemplates().then (d) ->
			$scope.templates = d
		
		$scope.gridOptions = { };
		$scope.gridOptions.enableCellEditOnFocus = true;

		$scope.gridOptions.columnDefs = [
			{ 
				name: 'Status', displayName: 'Status', editableCellTemplate: 'ui-grid/dropdownEditor', 
				width: '50%',   
				editDropdownOptionsArray: enums.ValueKey.JobStatusOptions  
			}
		
			{ name: '<PERSON>', enableCellEditOnFocus:true,  width: 80 },
			{ name: 'Red',  enableCellEditOnFocus:true,  width: 80} ,
			{ name: 'Remove', cellTemplate: '<button class="btn" ng-click="grid.appScope.deleteRow(row)"><i class="glyphicon glyphicon-trash"></i></button>', displayName: '', enableSorting: false }
		];
		$scope.gridOptions.data = []


		$scope.$watch "filters", (n,o) ->
			url = "staff/Schedules/get/#{n.Facility}/#{n.TemplateId}/#{n.Folding}"
			lepApi2.get(url).then (list)->
					$scope.result =list
					$scope.gridOptions.data =list

		, true


 

		#$scope.showOldPrices()
		return @
	]
