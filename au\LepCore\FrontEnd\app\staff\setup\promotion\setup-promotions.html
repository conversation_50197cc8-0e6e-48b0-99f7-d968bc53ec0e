﻿<div leppane="Promotions">
    <form>
        <div class="row ">
            <div class="col-sm-6 form-horizontal">
                <div class="form-group form-group-sm">
                    <label class="col-sm-4 control-label" for="Staff">Promotion Code or description </label>
                    <div class="col-sm-8">
                        <input id="Staff" type="text" class="form-control input" ng-model="vm.FreeText" placeholder="" />
                    </div>
                </div>
            </div>
            <div class="col-sm-6">
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                     <div class="pull-left">
          <a ui-sref="staff.setup.setup-promotions-edit({id:0})" class="btn btn-default"> <i class="glyphicon glyphicon-plus" ></i> Add promotion</a>
                </div>
                <div class="pull-right">
                    <button type="reset" class="btn btn-default" ng-click="clear()"> <i class="glyphicon glyphicon-erase"></i>  Clear</button>
                    <button type="submit" class="btn btn-default" ng-click="search()"> <i class="glyphicon glyphicon-search" ></i>  Search</button>
                </div>
            </div>
        </div>


    </form>
</div>

<div class="row">
    <div class="col-sm-12">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th><a ng-click="toggleSort('PromotionCode')">Promotion Code</a>

                    </th>
                    <th>
                        Sales Categories
                    </th>
                    <th>
                        Short Description
                    </th>
                    <th>
                        Sales Description
                    </th>
                    <th>
                        Valid
                    </th>

                </tr>
            </thead>
            <tbody>

                <tr ng-animate="'animate'" ng-repeat="o in r.List" class="animate">
                    <td><a ui-sref="staff.setup.setup-promotions-edit({id: o.Id})">{{::o.PromotionCode}}</a> </td>
                    <td>{{::o.SalesCategories }}</td>
                    <td>{{::o.ShortDescription }}</td>
                    <td>{{::o.SalesDescription }}</td>
                    <td>{{::o.Vaild}}</td>

                </tr>
            </tbody>
        </table>
		{{r.Summary}}
		<div paging
			 page="r.Page"
			 page-size="r.PageLength"
			 total="r.Total"
			 paging-action="goPage(page)"
			 scroll-top="false"
			 hide-if-empty="true"
			 show-prev-next="true"
			 show-first-last="true"
			 text-next-class="glyphicon glyphicon-chevron-right"
			 text-prev-class="glyphicon glyphicon-chevron-left"
			 text-first-class="glyphicon glyphicon-backward"
			 text-last-class="glyphicon glyphicon-forward">
		</div>
    </div>


</div>




