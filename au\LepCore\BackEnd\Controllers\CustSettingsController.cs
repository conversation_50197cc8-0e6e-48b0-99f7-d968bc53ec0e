using AutoMapper;
using lep;
using lep.address.impl;
using lep.configuration;
using lep.email;
using lep.printPortal;
using lep.user;
using lep.user.impl;
using LepCore.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Security.Claims;

namespace LepCore.Controllers
{
	[Produces("application/json")]
	[Route("api/Cust")]
	[Authorize(Roles = "LoggedInWLCustomer,Customer,Staff")]
	public class CustSettingsController : Controller
	{
		private IMapper _mapper;
		private IUserApplication _userApp;
		private IConfigurationApplication _configApp;
		private ICustomerUser _currentUser;
		private IEmailApplication _emailApp;
		private IHttpContextAccessor _httpContextAccessor;
		private NHibernate.ISession _session;
		private BaseApplication _baseApp;
		public CustSettingsController(IUserApplication userApp, IMapper mapper, IConfigurationApplication configApp, IEmailApplication emailApp,
			IHttpContextAccessor httpContextAccessor, NHibernate.ISession  session, BaseApplication baseApplication)
		{
			_userApp = userApp;
			_mapper = mapper;
			_configApp = configApp;
			_emailApp = emailApp;
			_httpContextAccessor = httpContextAccessor;
			_session = session;
			_baseApp = baseApplication;
		}

		[ApiExplorerSettings(IgnoreApi = true)]
		public override void OnActionExecuting(ActionExecutingContext context)
		{

			if ((context.ActionDescriptor as Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor).ActionName == "SalesInfo") return;
			try
			{
				var host = _httpContextAccessor.HttpContext.Request.Host;
				var roles = User.Claims.Where(c => c.Type == ClaimTypes.Role).Select(c => c.Value).ToList();
				var _currentUserIsCust = roles.Contains(LepRoles.Customer);
				var _currentUserIsAnonymousWLCustomer = roles.Contains(LepRoles.AnonymousWLCustomer);
				var _currentUserIsLoggedInWLCustomer = roles.Contains(LepRoles.LoggedInWLCustomer);

				var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
				_currentUser = (ICustomerUser)_userApp.GetUser(Convert.ToInt32(userId));

				if (_currentUserIsLoggedInWLCustomer)
				{
					userId = User.Claims.Where(c => c.Type == "SubCustId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
					_currentUser = (ICustomerUser)_userApp.GetUser(Convert.ToInt32(userId));
				}
			}catch (Exception ex)
			{

			}


		}

		/*
		[ApiExplorerSettings(IgnoreApi = true)]
        private ICustomerUser GetCurrentUser ()
        {
            var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => c.Value).FirstOrDefault();
            _currentUser = (ICustomerUser)_userApp.GetUser(Convert.ToInt32(userId));
            return _currentUser;
        }
		*/

		[HttpGet("Settings")]
		[Produces(typeof(ICustomerUser))]
		public IActionResult Get()
		{
			return new OkObjectResult(_currentUser);
		}

		[HttpPost("Settings")]
		public IActionResult PutCustomer([FromBody] [Required] CustomerUserDto update) //int? Id = null
		{
			var detailChange = !HasSameDetail(_currentUser, update);
			_currentUser = _mapper.Map(update, (CustomerUser)_currentUser);
			_userApp.Save(_currentUser);

			if (detailChange)
			{
				// turn off Customer Detail Change Email Notifications in respect of Web Users.
				if (_currentUser.ParentCustomer == null)
				{
					_emailApp.SendCustomerDetailChangeNotification(_currentUser);
				}
			}
			return Ok();
		}

		[HttpGet("Settings/pp")]
		[Produces(typeof(PrintPortalSettingsDto))]
		public IActionResult GetPP()
		{
			var r = new PrintPortalSettingsDto();
			_mapper.Map(_currentUser, r);
			return new OkObjectResult(r);
		}

		[HttpPost("Settings/pp")]
		public IActionResult PutCustomerPP([FromBody] [Required] PrintPortalSettingsDto update) //int? Id = null
		{
			_currentUser = _mapper.Map(update, (CustomerUser)_currentUser);
			_userApp.Save(_currentUser);
			return Ok();
		}

		[HttpGet("Settings/pp/favT")]
		[Produces(typeof(PrintPortalSettingsFavTemplateDto))]
		public IActionResult GetPPfavT()
		{
			var r = new PrintPortalSettingsFavTemplateDto();
			_mapper.Map(_currentUser.PrintPortalSettings, r);
			return new OkObjectResult(r);
		}

		[HttpGet("Settings/pp/favT/Cm")]
		public IActionResult GetCustomerPPfavTemplateCategoryMarkyup()
		{
			return new OkObjectResult(_currentUser.PrintPortalSettings
				.FavouriteCategoryMarkups ?? new FavouriteCategoryMarkups());
		}

		[HttpGet("Settings/pp/favT/Pr")]
		public IActionResult GetCustomerPPfavTemplatePriceRangeMarkyup()
		{
			return new OkObjectResult(_currentUser.PrintPortalSettings
				.FavouritePriceRangeMarkups ?? new FavouritePriceRangeMarkups());
		}

		[HttpPost("Settings/pp/favT/Cm")]
		public IActionResult PutCustomerPPfavTemplateCategoryMarkyup([FromBody] [Required] FavouriteCategoryMarkups update) //int? Id = null
		{
			_currentUser.PrintPortalSettings.FavouriteCategoryMarkups = update;
			_userApp.Save(_currentUser);
			return Ok();
		}

		[HttpPost("Settings/pp/favT/Pr")]
		public IActionResult PutCustomerPPfavTemplatePriceRangeMarkyup([FromBody] [Required] FavouritePriceRangeMarkups update) //int? Id = null
		{
			_currentUser.PrintPortalSettings.FavouritePriceRangeMarkups = update;
			_userApp.Save(_currentUser);
			return Ok();
		}

		/// <summary>
		/// saves a customer logo file to the CustomerLogoDirectory
		/// used only from  my settings page as in the current system only customers upload their logo
		/// </summary>
		/// <returns></returns>
		[HttpPost("logo")]
		public IActionResult SaveCustLogo()
		{
			var files = Request.Form.Files;
			if (!files.Any())
			{
				return BadRequest();
			}

			try
			{
				var filename = ContentDispositionHeaderValue.Parse(files[0].ContentDisposition).FileName.Trim().ToString();
				var extension = Path.GetExtension(filename);
				IFormFile f = files[0];

				_userApp.SaveCustomerLogo(_currentUser.Username, f.OpenReadStream(), extension);
				//_emailApp.SendCustomerDetailChangeNotification(currentUser);
				return Ok();
			}
			catch (Exception)
			{
				return BadRequest();
			}
		}

		/// <summary>
		/// /api/cust/logo
		/// Serves logged in cusomers logo matched by his username if the logo exists
		///  png logo files are served before jpg logo files
		/// </summary>
		/// <returns></returns>
		[HttpGet("logo")]
		[ResponseCache(Duration = 0)]
		public IActionResult GetCustLogo()
		{
			var logoFile = _userApp.GetCustomerLogo(_currentUser.Username);
			if (logoFile == null) return NotFound();
			var result = new PhysicalFileResult(logoFile.FullName, "image/" + Path.GetExtension(logoFile.FullName).ToLower());
			return result;
		}

		[HttpPost("Password")]
		public IActionResult PasswordReset([FromBody]  string newPassword)
		{
			if (_currentUser == null) return NotFound();
			try
			{
				if (string.IsNullOrEmpty(newPassword))
				{
					newPassword = Utils.GeneratePassword(10);
				}
				_userApp.SetPassword((IUser)_currentUser, newPassword);
				_userApp.Save(_currentUser);
				return Ok();
			}
			catch (Exception)
			{
				return BadRequest();
			}
		}

		private bool HasSameDetail(ICustomerUser customer, CustomerUserDto update)
		{
			if (!(CompareString(customer.Contact1.Name, update.Contact1.Name) &&
					CompareString(customer.Contact1.Phone, update.Contact1.Phone) &&
					CompareString(customer.Contact1.Mobile, update.Contact1.Mobile) &&
					CompareString(customer.Contact1.Fax, update.Contact1.Fax) &&
					CompareString(customer.Contact1.FaxAreaCode, update.Contact1.FaxAreaCode) &&
					CompareString(customer.Contact1.AreaCode, update.Contact1.AreaCode) &&
					CompareString(customer.BillingAddress.Address1, update.BillingAddress.Address1) &&
					CompareString(customer.BillingAddress.Address2, update.BillingAddress.Address2) &&
					CompareString(customer.BillingAddress.Address3, update.BillingAddress.Address3) &&
					CompareString(customer.BillingAddress.Postcode, update.BillingAddress.Postcode) &&
					CompareString(customer.BillingAddress.State, update.BillingAddress.State) &&
					CompareString(customer.BillingAddress.Country, update.BillingAddress.Country) &&
					CompareString(customer.BillingAddress.City, update.BillingAddress.City) &&
					CompareString(customer.Email, update.Email) &&
					CompareString(customer.OtherEmail, update.OtherEmail) &&
					CompareString(customer.AccountEmail, update.AccountEmail)))
			{
				return false;
			}

			if (update.PostalIsBilling)
			{
				if (!(
				CompareString(customer.PostalAddress.Address1, update.BillingAddress.Address1) &&
				CompareString(customer.PostalAddress.Address2, update.BillingAddress.Address2) &&
				CompareString(customer.PostalAddress.Address3, update.BillingAddress.Address3) &&
				CompareString(customer.PostalAddress.Postcode, update.BillingAddress.Postcode) &&
				CompareString(customer.PostalAddress.State, update.BillingAddress.State) &&
				CompareString(customer.PostalAddress.Country, update.BillingAddress.Country) &&
				CompareString(customer.PostalAddress.City, update.BillingAddress.City)))
				{
					return false;
				}
			}
			else
			{
				if (!(CompareString(customer.PostalAddress.Address1, update.PostalAddress.Address1) &&
					  CompareString(customer.PostalAddress.Address2, update.PostalAddress.Address2) &&
					  CompareString(customer.PostalAddress.Address3, update.PostalAddress.Address3) &&
					  CompareString(customer.PostalAddress.Postcode, update.PostalAddress.Postcode) &&
					  CompareString(customer.PostalAddress.State, update.PostalAddress.State) &&
					  CompareString(customer.PostalAddress.Country, update.PostalAddress.Country) &&
					  CompareString(customer.PostalAddress.City, update.PostalAddress.City)))
				{
					return false;
				}
			}

			return true;
		}

		private bool CompareString(string data1, string data2)
		{
			return string.Compare(data1.Replace(" ", ""), data2.Replace(" ", ""), StringComparison.OrdinalIgnoreCase) == 0;
		}

		[HttpGet("FavouriteDeliveryDetails/List")]
		[HttpGet("FavouriteDeliveryDetails/List/{cid:int}")]
		public IActionResult GetFavouriteAddresses(int? cid)
		{
			int id = _currentUser?.Id ?? cid ?? 0;
			if (cid.HasValue &&   cid.Value > 0) id = cid.Value;
			List<DeliveryDetails> favAddresses = _session.Query<DeliveryDetails>().Where(_ => _.CustomerId == id).ToList();
			return new ObjectResult(favAddresses);
		}

		[HttpPost("FavouriteDeliveryDetails/Add")]
		public IActionResult AddFA([FromBody]  DeliveryDetails dto)
		{
			dto.CustomerId = _currentUser.Id;
			_baseApp.Save<DeliveryDetails>(dto);

			return GetFavouriteAddresses(_currentUser.Id);
		}

		[HttpPost("FavouriteDeliveryDetails/Delete")]
		public IActionResult DeleteFA([FromBody]  DeliveryDetails dto)
		{
			var dd = _baseApp.Get<DeliveryDetails>(dto.Id);
			_baseApp.Delete(dd);
			return GetFavouriteAddresses(_currentUser.Id);
		}

		[HttpGet("Contacts/List")]
		public IActionResult GetContactsList()
		{
			var favAddresses = _currentUser.Contacts.OrderBy(_ => _.Name).ToList();
			return new ObjectResult(favAddresses);
		}

		[HttpGet("BusinessTypes")]
		public IActionResult GetCustomerBusinessTypes()
		{
			var q = _session.CreateSQLQuery("Select Title From CustomerBusinessTypes order by 1").List<String>();
			return new OkObjectResult(q);
		}



		[HttpGet("{id:int}/si")]
		[Authorize(Roles = "Staff,Customer")]
		public IActionResult SalesInfo(int id, 
			[FromServices] lep.promotion.IPromotionApplication _promoApp)
		{
			ICustomerUser customer = _userApp.GetCustomerUser(id);
			if (customer == null)
				return BadRequest();
		
			var offer = _promoApp.ValidPromotionsForCustomer(customer.Username).ToList()
				 .Where(_ => _.Promotion.PromotionCode.Contains("My Reward" )  ).FirstOrDefault();


			var Name = customer.Name;
			var MAT = customer.MAT;
			var MAT3ma = customer.MAT3ma;
			var PCode = customer.ProductPriceCode;
			var FCode = customer.FreightPriceCode;

			bool ShowRewardTable = true;
			if(PCode != "P0" || FCode!= "F0")
			{
				ShowRewardTable = false;
			}
		 
			dynamic result = new { Name, MAT, MAT3ma, PCode, FCode, offer , ShowRewardTable }; 
			return new OkObjectResult(result);
			
		}

		/*
		 
		 [HttpGet("{id:int}/si")]
		[Authorize(Roles = "Staff,Customer")]
		public IActionResult SalesInfo(int id, 
			[FromServices] lep.promotion.IPromotionApplication _promoApp)
		{
			ICustomerUser customer = null;

			var sql = $@"select   Name , MAT, MAT3ma, [ProductPriceCode] as PCode , [FreightPriceCode] as FCode from Customer where CustomerId = {id}";
			var x = _session.CreateSQLQuery(sql).List<object[]>();

			var customerOffer = _promoApp.ValidPromotionsForCustomer(id).ToList()
				 .Where(_ => _.Promotion.PromotionCode.Contains("REWARD")).FirstOrDefault(); 

			if (x.Any())
			{
				var c = x[0];
				dynamic result = new {
					Name = c[0],
					MAT = c[1],
					MAT3ma = c[2],
					PCode = c[3],
					FCode = c[4],
					offer = customerOffer
				}; 
				return new OkObjectResult(result);
			}


			return BadRequest();
		}
		 */


		[HttpGet("Settings/ppc/matrix3")]
		public IActionResult GetMatrix3([FromServices] lep.job.IJobApplication _jobApp) //int? Id = null
		{




			var sizesD = _jobApp.ListPaperSize().ToDictionary(x => x.Id, x => x.Name);
			var stocksD = _jobApp.ListStock().ToDictionary(x => x.Id, x => x.Name);

			var deniedTs = new List<int>(); //; _currentUser.PrintPortalSettings.DeniedTemplates.ToList();
			deniedTs.AddRange(new List<int>() { 3, 4, 5, 6, 13, 6020, 12, 30, 2, 20, 25, 38, 19, 24 });

			var templateList = _jobApp.ListAllTemplates().Where(_ => !deniedTs.Contains(_.Id) );




			var templatesD = templateList.ToDictionary(x => x.Id, x => x.Name);
			var templatesL = templateList.Select(x => new { Id = x.Id, Name = x.Name }).ToList();
			var matrix = (from t in templateList
						  where t.Id != 2 || t.Id != 12
						  //|| t.Id != 14 || t.Id != 7 || t.Id !=8 || t.Id !=30
						  let sizeOptions = _jobApp.ListSizeOptions(t)
						  select new
						  {
							  Id = t.Id,
							  // Name = t.Name,
							  Sizes = (from s in sizeOptions
										   //where !s.PaperSize.Name.Contains("Custom")
									   select s.PaperSize.Id).ToList(),

							  Stocks = (from s in sizeOptions
										from st in s.StockOptions
										orderby st.Stock.GSM
										select st.Stock.Id).Distinct().ToList(),
							  //select new { Id = st.Stock.Id }).Distinct().ToList()
						  }).ToDictionary(x => x.Id, x => new { Sizes = x.Sizes, Stocks = x.Stocks });

			var result = new
			{
				sizesD,
				stocksD,
				templatesD,
				templatesL,
				matrix
			};
			return new OkObjectResult(result);
		}
	}
}
