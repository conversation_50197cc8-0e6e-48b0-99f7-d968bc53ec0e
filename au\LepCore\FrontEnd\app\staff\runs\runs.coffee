appStaff = angular.module('app.staff')

pressDetailsUpdateFields = "PressDetails,Proofs,Scoring,ScoringInstructions,Perforating,PerforatingInstructions,DieCutting,RequestedPackaging,ActualPackaging,CustomDieCut,SpecialInstructions,ProductionInstructions"
runUpdateFields = "Status,Urgent,ManuallyManage,JobIdToSlots,NumOfPressSheets,PrintType,QtyList,TSlots,StockId"

nextDayDespatchTemplateIds = [4,13,18,19,20,21,22,26]
sameDayDespatchTemplateIds = [23,27,29]

keepFields = (obj, strlist) ->
    list = strlist.split(',')
    for k,v of obj
        if list.indexOf(k) == -1
            delete obj[k]

###
appStaff.directive 'jobSlot', ->
    {
    restrict: 'EA'
    template: """
        <span class="dropdown">
            <span class="dropdown-toggle textbox" type="button" data-toggle="dropdown"
            aria-haspopup="true" aria-expanded="true">
                {{ job.Slots[i] || '???' }}
            </span>
            <div class="dropdown-menu" >
                    <table style="table table-condenced">
                        <tr ng-repeat="row in [1,2,3,4,5,6]">
                            <td ng-repeat="col in [1,2,3,4,5,6,7]"  class="slot">
                                <span ng-if="!isSlotUsed(row, col, run)">
                                    <a ng-click="setSlot(run, job, row, col)">{{::(((row-1)*7)+col)}}</a>
                                </span> &nbsp;
                            </td>
                        </tr>
                        <tr>
                            <td colspan="7" align="center">
                                <a ng-click="clearSlot(run, job, i)"> clear slot </a>
                            </td>
                        <tr/>
                    </table>
            </div>
        </span>
            """
    link: (scope, elem, attrs, ngModelCtrl) ->
        scope.i  = attrs.i

        scope.isSlotUsed = (row, col, run) ->
            slot = ((row-1) * 7)  + col
            for j in run.Jobs
                for s in j.Slots
                    if s is slot
                        return true
            return false

        scope.setSlot = (run, job, row, col)->
            slot = ((row-1) * 7)  + col
            job.Slots[scope.i] = slot

            run.JobIdToSlots = []
            for j in run.Jobs
                for s in j.Slots
                    run.JobIdToSlots.push({JobId: j.Id, Slot: s})

        scope.clearSlot = (run, job, i ) ->
            scope.setSlot(run, job, 1, 0)

    }
###
gcd2 = (a, b) ->
   if b == 0 then return a
   gcd2 b, a % b

gcd = (a) ->  a.reduce gcd2

appStaff.controller 'StaffRunsEditController', [
    '$scope', '$state', '$stateParams', 'lepApi2', '$http', '$location', 'ngDialog', 'run', '$localStorage'	 , '$q','$interval', 'JobService',
    ($scope,   $state,   $stateParams,   lepApi2,   $http,   $location,   ngDialog,   run, $localStorage, $q ,$interval, JobService  ) ->

        $scope.$root.title = "R#{run.Id}"

        if run.Visibility.overloadDiv  then toastr.error "Run contains {{run.UsedBC}} slot business card."
        if run.Visibility.LayoutInst   then toastr.warning "Upload layout file/s."
        if run.Visibility.FillingInst  then toastr.success "No action required - run is filling"
        if run.Visibility.ProgressInst then toastr.success "No action required - run is in progress."
        if run.Visibility.CompleteInst then toastr.success "No action required - run is complete."

        # loop through run jobs and assign slots to them from run slots
        for j in run.Jobs
            j.css = ""
            if j.PrintType is 'D'
                j.css += " DigitalStyle "
            if (nextDayDespatchTemplateIds.indexOf(j.TemplateId) > -1)
                j.css += " nextDayDespatch "
            if (sameDayDespatchTemplateIds.indexOf(j.TemplateId) > -1)
                j.css += " sameDayDespatch "


            j.Slots = []
            for x in run.Slots
                if x.JobId == j.Id
                    j.Slots.push(x.Slot)
            j.Slots.sort((x,y)-> x-y)
            j.NumberOfSlots = j.Slots.length
            j.SlotX = j.Slots[0] || 999

        qtys     = _.map(run.Jobs, 'Quantity')
        qtysC    = _.countBy(run.Jobs, 'Quantity');
        qtysUniq = _.uniq(qtys, (x)->rfn(x))
        base     = gcd qtysUniq
        $scope.qtys = _.countBy(run.Jobs, 'Quantity');


        ql = {}
        if run.QtyList
            ql = JSON.parse(run.QtyList)
        else
            for q in qtys
                ql[q] = {}
                ql[q].Count = qtysC[q]
                ql[q].Ups   = 0
                ql[q].Ts    = 0
        $scope.ql = ql

        $scope.updSum = () ->
            for j in run.Jobs
                j.NumberOfSlots = (j.BaseSpace * (ql[j.Quantity]?.Ups || 1))
            for q in qtys
                ql[q] = ql[q] || {}
                ql[q].Ts = 0
                for j in run.Jobs
                    if j.Quantity is q
                        ql[q].Ts += j.NumberOfSlots

            $scope.TSlots = _.sum(_.map(ql, "Ts"))

        freeSlots = []
        if run.PrintType is 'D'
            $scope.maxSlots = 21
        if run.PrintType is 'O'
            $scope.maxSlots = 42


        $scope.runStatusOptions =
            'O':
                '0': 'Filling'
                '1': 'Layout required'
                '2': 'Layout done'
                '3': 'Approved for plating'
                '5': 'Plating done'
                '6': 'Press done'
                '7': 'Finishing'
                '8': 'Finishing done'
                '9': 'Packing done'
                '10': 'Outwork'
            'D':
                '0': 'Filling'
                '1': 'Layout required'
                '2': 'Layout done'
                '4': 'DPC Pre Production'
                '6': 'Press done'
                '7': 'Finishing'
                '8': 'Finishing done'
                '9': 'Packing done'
                '10': 'Outwork'

        $scope.freeSlots = freeSlots
        freeSlotsChk = undefined
        if run.Status >=  1  and run.IsBusinessCard  # Layout Required  and a bc
            freeSlotsChk = $interval(() ->
                if run.PrintType is 'D'
                    freeSlots = [1..21]
                if run.PrintType is 'O'
                    freeSlots = [1..42]
                for j in run.Jobs
                    freeSlots = _.differenceBy(freeSlots, j.Slots)
                    $scope.freeSlots = freeSlots
            , 1000)

            $scope.$on '$destroy', () ->
            if angular.isDefined(freeSlotsChk)
                $interval.cancel(freeSlotsChk)
                freeSlotsChk = undefined
            $scope.updSum()

        $scope.updSum()

        # Load stocks for the dropdown
        JobService.getAllStocksForRuns().then (stocks) ->
            $scope.stocks = stocks
            # Set the initial StockId value from the run's Stock object
            if run.Stock and run.Stock.Id
                $scope.run.StockId = run.Stock.Id
                $scope.originalStockId = run.Stock.Id

        $scope.run = run

        $scope.requiredPositions = []

        if run.Visibility.nonMagPanel
            $scope.requiredPositions = ['FrontLayout','BackLayout']
        if run.Visibility.magCoverPanel
            $scope.requiredPositions = ['OutsideFront','InsideFront', 'InsideBack','OutsideBack']

        $scope.files         = []
        $scope.SortField     = $localStorage?.search[$state.current.name]?.SortField ||   'Id';   # set the default sort type
        $scope.SortDir       = $localStorage?.search[$state.current.name]?.SortDir ||    false;  # set the default sort order
        #$scope.searchFish   = '';     # set the default search/filter term

        $scope.toggleSort = (sort) ->
            $scope.SortDir =  sort == $scope.SortField and !$scope.SortDir
            $scope.SortField = sort

            $localStorage.search[$state.current.name] = {}
            $localStorage.search[$state.current.name].SortField = $scope.SortField
            $localStorage.search[$state.current.name].SortDir  	= $scope.SortDir


        $scope.$watch 'run.Status', (n) ->
            $scope.slotNumbersReqd =  n > 1 # RunStatusOptions.LayoutRequired;

        # save run core func. used in multiple places
        save = () ->
            alreadyUsed = []
            run.JobIdToSlots = []
            for j in run.Jobs
                for s in j.Slots
                    if typeof(s) == 'string'
                        si = s.trim()
                        if si is "" then continue
                        si = parseInt(si,10)
                        if isNaN(si)
                            return $q.reject("Slot #{s} is not a number")

                    si = parseInt(s,10)
                    if alreadyUsed.indexOf(si) > -1
                        return $q.reject("Slot #{s} is already used!")
                    if si > 50
                        return $q.reject("Slot # cant be greater than 50")

                    run.JobIdToSlots.push({JobId: j.Id, Slot: si})
                    alreadyUsed.push(si)

            payload = angular.copy( $scope.run )
            if payload.Status is 0
                payload.QtyList = null
                payload.JobIdToSlots = []
            else
                payload.QtyList = JSON.stringify(ql)
                payload.TSlots = $scope.TSlots

            keepFields(payload, runUpdateFields)
            lepApi2.post("runs/#{$stateParams.runId}", payload)

        # save run
        $scope.saveRun = () ->
            save().then((d) ->
                toastr.success('run updated')
                $state.reload()
            , (r) ->
                toastr.error('Can not update run as \n' + r)
                #console.debug(r)
            )

        # cancel button click take users to to the runs edit page
        $scope.cancel  = () ->
            lepApi2.post("runs/#{$stateParams.runId}/ClearMeAsOperator").then(()->
                $state.go('staff.runs', {}))


        # print job sheets
        $scope.printJobSheets  = () ->
            lepApi2.get("runs/#{$stateParams.runId}/Print/JobSheet")

        $scope.printBCRefSheet  = (facility) ->
            lepApi2.get("runs/#{$stateParams.runId}/Print/BCRef?facility=#{facility}")

        $scope.downloadBCRef = () ->
            lepApi2.download("api/runs/#{$stateParams.runId}/Print/BCRefDownload");

        $scope.printFurtherProcessingList  = () ->
            lepApi2.get("runs/#{$stateParams.runId}/Print/FurtherProcessingList")


        #$scope.btn_upload = ->
        #	if !$scope.files.length then return
        #
        #	formData = new FormData
        #	#now add all of the assigned files
        #	i = 0
        #	while i < $scope.files.length
        #		#add each file to the form data and iteratively name them
        #		formData.append $scope.requiredPositions[i], $scope.files[i].file
        #		i++
        #
        #	runId = $scope.run.Id
        #	$http(
        #		method          : 'POST'
        #		url             : "/Api/Runs/#{runId}/artworks"
        #		headers         : {'Content-Type': undefined}
        #		transformRequest: angular.identity
        #		data            : formData
        #	).then((data, status, headers, config) ->
        #			$scope.artworks = data
        #			#console.debug(arguments)
        #			toastr.success('uploaded files successfully')
        #			$scope.upload_artwork = false
        #		,(data, status, headers, config) ->
        #			alert 'failed!'
        #	)

#
        $scope.getHalfQty =  (IsHalfBC, Quantity) ->
             if !IsHalfBC && Quantity <= 500 then 'Yes' else  ''
        $scope.now = new Date()

        $scope.getJob = (id) ->
            _.find($scope.run.Jobs, {Id:id})

        $scope.$watch('run.Visibility.artworkFolderNavigateUrl', (newVal, oldVal) ->
            setTimeout(() ->
                # Construct the URL when the artworkFolderNavigateUrl changes
                platform = navigator.platform.toUpperCase()
                isMac = platform.indexOf('MAC') >= 0
                isWindows = platform.indexOf('WIN') >= 0

                if isWindows
                    artworkUrl =  'localexplorer:' +  newVal.replace("\\\\dfs01\\resource\\", "P:\\").replace(/\//g, "\\")
                else if isMac
                    artworkUrl =  'file:' +  newVal.replace("\\\\dfs01\\resource\\", "/resource/").replace(/\\/g, "\/")

                # Use jQuery to update the href dynamically
                $('#artwork-link').attr('href', artworkUrl)
            , 500)
        )

        return @
    ]




appStaff.controller 'StaffRunsPrintBCRunSheetController', [
    '$scope', '$state', '$stateParams', 'lepApi2', '$http', '$location', 'ngDialog', 'run','$window', '$timeout',
    ($scope,   $state,   $stateParams,   lepApi2,   $http,   $location,   ngDialog,   run, $window, $timeout) ->

        $scope.$root.title = "R#{run.Id}"

        # loop through run jobs and assign slots to them from run slots
        for j in run.Jobs
            j.Slots = []
            for x in run.Slots
                if x.JobId == j.Id
                    j.Slots.push(x.Slot)
            j.Slots.sort((x,y)-> x-y)
            j.SlotX = j.Slots[0] || 999

        $scope.run = run

        $scope.SortField     = 'SlotX';   # set the default sort type
        $scope.SortDir       = false;  # set the default sort order
        #$scope.searchFish   = '';     # set the default search/filter term

        $scope.toggleSort = (sort) ->
            $scope.SortDir =  sort == $scope.SortField and !$scope.SortDir
            $scope.SortField = sort

        # cancel button click take users to to the runs edit page
        $scope.cancel  = () -> $state.go('staff.runs', {})

#
        $scope.getHalfQty =  (IsHalfBC, Quantity) ->
             if !IsHalfBC && Quantity <= 500 then 'Yes' else  ''
        $scope.now = new Date()

        $scope.getJob = (id) ->
            _.find($scope.run.Jobs, {Id:id})

        $scope.$on '$destroy', ()  ->
             clearUrl = "runs/#{run.Id}/ClearMeAsOperator"
             lepApi2.post(clearUrl);

        #$timeout(()->
        #	toastr.remove()
        #	$window.print()
        #, 1000)

        return @
    ]



appStaff.controller 'StaffRunsJobspecController', [
    '$scope', 'lepApi2', '$location', '$state', '$stateParams', 'job'
    ($scope , lepApi2, $location, $state, $stateParams, job) ->

        $scope.job = job
        $scope.runId = $stateParams.runId

        # save job spec
        save  = () ->
            payload = angular.copy( $scope.job )
            keepFields(payload, pressDetailsUpdateFields)

            lepApi2.post("runs/#{$stateParams.runId}/jobspec/#{job.Id}", payload)

        # save job spec and reload state so the form has updated data
        $scope.save    = () ->
            save().then () ->
                toastr.success('Updated job details')
                $state.reload()

        # cancel button click take users to to the runs edit page
        $scope.cancel  = () -> $state.go('staff.run-edit', {runId:$scope.runId})

        # refresh button reloads page
        $scope.refresh = () -> $state.reload()

        # save the job and move to next job on the line
        $scope.saveNext = () ->
            save().then () ->
                $state.go('staff.run-edit-jobspec', {runId: $scope.runId, jobId: job.NextJobInRun, srcJobId: job.Id })

        return @
    ]


