app = angular.module('app')
app.controller 'YourSettingsController', [
    '$scope', '$state', 'lepApi2', 'Utils','$http',
    ($scope, $state, lepApi2, Utils, $http) ->
        $scope.vm = {}
        url = 'cust/settings'
        vmr = null
        vmo = null
        $scope.file = {}
        $scope.logo = "/api/cust/logo"

        $scope.sortableOptions = {
            update: (e, ui)  ->
        }
        lepApi2.get("cust/BusinessTypes",          null, true).then (r) ->
            r = _.filter(r, (bt) -> bt != 'NOT PRINT RESELLER')
            $scope.BusinessTypes          = r



        $scope.load = () ->
            lepApi2.get(url).then (r) ->
                vmr = r
                vmo = angular.copy(vmr)
                $scope.vm = angular.copy(vmo)
                $scope.logo = "/api/Staff/Customers/#{vmr.Id}/logo?t=" + (+new Date())

        $scope.makePostalSameAsBilling = (b) ->
            if b then $scope.vm.PostalAddress = angular.copy($scope.vm.BillingAddress)

        $scope.save = () ->
            lepApi2.post(url, $scope.vm).then (r) ->
                toastr.success "Your settings has been updated"
                $state.reload()


        $scope.load()

        $scope.updatePassword = (newpassword) ->
            lepApi2.post 'cust/password', JSON.stringify(newpassword)
            .then (r) ->
                toastr.info('password updated')

        $scope.uploadLogo = () ->
            Utils.browseForLogoFile().then (file) ->
                if !file then return

                formData = new FormData
                formData.append 'file', file.file
                $http(
                    method: 'POST'
                    url: "api/Cust/logo"
                    headers: {'Content-Type': undefined}
                    transformRequest: angular.identity
                    data: formData
                ).then((data, status, headers, config) ->
                    $scope.logo = "/api/Staff/Customers/#{vmo.Id}/logo?t=" + (+new Date())
                    file = null
                , (data, status, headers, config) ->
                    alert 'failed!'
                )
        
        $scope.$watch('vm.SendSamples', (n, o) ->
            if n
                toastr.info('This will apply to all jobs ordered and incur a $5 fee per job')
            else
                toastr.clear()
        )
        $scope.$watch('vm.BillingAddress', (n, o) ->
            if !n then return
            if  $scope.vm.PostalIsBilling
                $scope.vm.PostalAddress = angular.copy($scope.vm.BillingAddress)
        , true)


]


app = angular.module('app')
app.controller 'YourSettingsPPController', [
    '$scope', '$state', 'lepApi2', 'Utils',
    ($scope, $state, lepApi2, Utils) ->
        $scope.vm = {}
        url = 'cust/settings/pp'
        vmr = null
        vmo = null

        $scope.load = () ->
            lepApi2.get(url).then (r) ->
                vmr = r
                vmo = angular.copy(vmr)
                $scope.vm = angular.copy(vmo)

        $scope.save = () ->
            lepApi2.post(url, $scope.vm).then (r) ->
                toastr.success "Your settings has been updated"
                $state.reload()


        $scope.load()


]
