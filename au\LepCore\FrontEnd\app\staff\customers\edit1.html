﻿<div>
    <div><a class="pull-right" ui-sref="staff.customers-view-wl({id: vm.Id})">Print Portal setup</a> <span
            style="font-weight: bold;font-size: 24px;margin: 0">{{vm.Name}}</span></div>
    <form name="form">
        <style>
            input.t {
                display: none;
            }

            /* hide radio buttons */
            input.t+label {
                display: inline-block
            }

            /* show labels in line */
            input.t~.tab {
                display: none
            }

            /* hide contents */
            /* show contents only for selected tab */
            #tab1:checked~.tab.content1,
            #tab2:checked~.tab.content2,
            #tab3:checked~.tab.content3,
            #tab4:checked~.tab.content4,
            #tab5:checked~.tab.content5,
            #tab6:checked~.tab.content6,
            #tab7:checked~.tab.content7,
            #tab8:checked~.tab.content8 {
                display: block;
            }

            input.t+label {
                /* box with rounded corner */
                border: 1px solid #ddd;
                background: #f3f4f7;
                padding: 4px 12px;
                border-radius: 2px 2px 0 0;
                position: relative;
                top: 1px;
                margin: 0;
            }

            input.t:checked+label {
                /* white background for selected tab */
                background: #FFF;
                font-weight: bold;
                border-bottom: 1px solid transparent;
            }

            input.t~.tab {
                /* grey line between tab and contents */
                border: 1px solid #ddd;
                padding: 12px;
                background-color: white;
                margin: 0 0 30px 0;
            }
        </style><input type="radio" name="tabs" id="tab1" class="t" checked="checked"><label
            for="tab1">Customer</label><input type="radio" name="tabs" id="tab2" class="t"><label
            for="tab2">Email</label><input type="radio" name="tabs" id="tab3" class="t"><label
            for="tab3">Contacts</label><input type="radio" name="tabs" id="tab4" class="t"><label
            for="tab4">Address</label><input type="radio" name="tabs" id="tab5" class="t"><label
            for="tab5">Other</label><input type="radio" name="tabs" id="tab6" class="t"><label
            for="tab6">Sales</label><input type="radio" name="tabs" id="tab7" class="t"><label
            for="tab7">Notes</label><input type="radio" name="tabs" id="tab8" class="t"><label
            for="tab8">Invoices</label>
        <div class="tab content1">
            <div class="form-horizontal">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group form-group-sm">
                            <label class="col-sm-4 control-label"
                                   for="bus-name">Business Name *</label>
                            <div class="col-sm-8">
                                <input name="Name" type="text" class="form-control input"
                                       ng-maxlength="200" ng-model="vm.Name" ng-model-options="{allowInvalid:true}"
                                       ng-required="true" lep-unique-businessname user-id="vm.Id">
                                <div role="alert" class="help-block">
                                    <div ng-show="form.Name.$error.required">required</div>
                                    <div ng-show="form.Name.$error.maxlength">too long</div>
                                    <div ng-show="form.Name.$error.unique">already taken</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-4 control-label"
                                   for="username">Username *</label>
                            <div class="col-sm-8 readonly-text">
                                <input name="UserName" type="text"
                                       ng-model="vm.Username" ng-model-options="{allowInvalid:true}"
                                       ng-disabled="vm.Id != 0" class="form-control input" ng-required="true"
                                       ng-minlength="5" ng-maxlength="40" lep-unique-username user-id="vm.Id">
                                <div role="alert" class="help-block">
                                    <div ng-show="form.UserName.$error.required">required</div>
                                    <div ng-show="form.UserName.$error.minlength">too short</div>
                                    <div ng-show="form.UserName.$error.maxlength">too long</div>
                                    <div ng-show="form.UserName.$error.unique">already taken</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-4 control-label"
                                   for="order-no">Payment terms</label>
                            <div class="col-sm-8">
                                <div>
                                    <label>
                                        <input name="PaymentTerms" type="radio" ng-model="vm.PaymentTerms"
                                               ng-value="0 * 1"> On Account
                                    </label>
                                </div>
                                <div>
                                    <label>
                                        <input name="PaymentTerms" type="radio" ng-model="vm.PaymentTerms"
                                               ng-value="1 * 1"> Cash Before Dispatch (COD)
                                    </label>
                                </div>
                                <div>
                                    <label>
                                        <input name="PaymentTerms" type="radio" ng-model="vm.PaymentTerms"
                                               ng-value="2 * 1"> Pre-pay
                                    </label>
                                </div>
                                <div>
                                    <label>
                                        <input name="PaymentTerms" type="radio" ng-model="vm.PaymentTerms"
                                               ng-value="3 * 1"> On Hold
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-4 control-label">Credit limit</label>
                            <div class="col-sm-8">
                                {{::vm.CreditLimit | currency}}
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-4 control-label">MYOB balance</label>
                            <div class="col-sm-8">
                                {{::vm.MYOBBalance | currency}}
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-4 control-label">MYOB past due</label>
                            <div class="col-sm-8">
                                {{::vm.MYOBPastDue | currency}}
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-4 control-label">
                                LEPOnline
                                balance
                            </label>
                            <div class="col-sm-8">
                                {{::vm.LEPOnlineBalance | currency}}
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-4 control-label">
                                Available
                                balance
                            </label>
                            <div class="col-sm-8">
                                    {{::vm.AvaiableBalance| currency}} <span class="text-muted">
                                        ({{::vm.CreditLimit | currency}} - ({{::vm.MYOBBalance | currency}}+{{::vm.LEPOnlineBalance | currency}}))
                                    </span>
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-4 control-label"
                                   for="order-no">Charge GST</label>
                            <div class="col-sm-8">
                                <label class="">
                                    <input name="checkbox" type="checkbox"
                                           ng-model="vm.IsChargedGST">
                                </label>
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-4 control-label" for="notes">
                                Customer
                                Logo
                            </label>
                            <div class="col-sm-8">
                                <img ng-src="{{logo}}" style="max-width: 50%"
                                     no-image="images/no-image.png">
                                <div ng-if="vm.Id != 0">
                                    <a class="btn" ng-click="uploadCustomerLogo()">
                                        <i class="glyphicon glyphicon-cloud-upload"></i> Upload Customers logo
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label">Customer #</label>
                            <div class="col-sm-8 readonly-text bold">{{::vm.CustomerNr}}</div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label"
                                for="MYOB">MYOB</label>
                            <div class="col-sm-8"><input name="MYOB" type="text" class="form-control input"
                                    ng-model="vm.MYOB" ng-model-options="{allowInvalid:true}" lep-unique-myob
                                    user-id="vm.Id">
                                <div ng-shows="form.MYOB.$error" role="alert" class="help-block">
                                    <div ng-show="unique">already taken</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label"
                                for="prod-price-code">Business type</label>
                            <div class="col-sm-8"><select class="form-control input" ng-model="vm.BusinessType"
                                    ng-options="k.Name  as k.Name for k in BusinessTypes"></select></div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label" for="abn">ABN
                                *</label>
                            <div class="col-sm-8"><input name="abn" type="text" class="form-control input"
                                    ng-model="vm.ABN" abn>
                                <div role="alert" class="help-block">
                                    <div ng-show="form.abn.$error.required">required</div>
                                    <div ng-show="form.abn.$error.abn">not a valid ABN</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label"
                                for="order-no">System Access</label>
                            <div class="col-sm-8">
                                <div><label><input name="SystemAccess" type="radio" ng-model="vm.IsEnabled"
                                            ng-value="true"> Allowed</label></div>
                                <div><label><input name="SystemAccess" type="radio" ng-model="vm.IsEnabled"
                                            ng-value="false"> Denied</label></div>
                            </div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label" for="site-loc">Site
                                Location</label>
                            <div class="col-sm-8"><select ng-model="vm.SiteLocation" id="site-loc"
                                    class="form-control input"
                                    ng-options="k*1 as v for (k,v) in $root.enums.ValueDesc.SiteLocation"></select>
                            </div>
                        </div>
                        <div class="form-group form-group-sm"
                            title="if ticked, on a new job, 'Send samples' will be ticked by default"><label
                                class="col-sm-4 control-label" for="SendSamples">Send samples</label>
                            <div class="col-sm-8">
                                <div class="checkbox"><label class=""><input id="SendSamples" type="checkbox"
                                            ng-model="vm.SendSamples"></label></div>
                            </div>
                        </div>
                        <div lep-rewards cid="vm.Id" vertical="true"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab content2">
            <div class="form-horizontal">
                <div class="form-group form-group-sm"><label class="col-sm-4 control-label"
                        for="job-notifiications">Send Job Status Notifications to *</label>
                    <div class="col-sm-6"><input id="job-notifiications" type="text" class="form-control input"
                            ng-model="vm.Email" ng-required="true"></div>
                </div>
                <div class="form-group form-group-sm"><label class="col-sm-4 control-label"
                        for="notif-type">Notification Type</label>
                    <div class="col-sm-6"><select ng-model="vm.NotificationType" ng-required="true" id="notif-type"
                            class="form-control input"
                            ng-options="k*1 as v for (k,v) in $root.enums.ValueDesc.NotificationType"></select></div>
                </div>
                <div class="form-group form-group-sm"><label class="col-sm-4 control-label" for="send-account">Send
                        Accounts to</label>
                    <div class="col-sm-6"><input id="send-account" type="text" class="form-control input"
                            ng-model="vm.AccountEmail"></div>
                </div>
                <div class="form-group form-group-sm"><label class="col-sm-4 control-label" for="other-materials">Send
                        all other materials including promotions to</label>
                    <div class="col-sm-6"><input id="other-materials" type="text" class="form-control input"
                            ng-model="vm.OtherEmail"></div>
                </div>
            </div>
        </div>
        <div class="tab content3">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-horizontal">
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label"
                                for="job-notifiications">Company Phone</label>
                            <div class="col-sm-6"><input type="text" class="form-control input" ng-model="vm.Phone">
                            </div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label"
                                for="notif-type">Company Mobile</label>
                            <div class="col-sm-6"><input type="text" class="form-control input" ng-model="vm.Mobile">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div lep-contact-list lep-contact-list-details contacts="vm.Contacts" receive-marketing-emails="true"></div>
        </div>
        <div class="tab content4">
            <div class="form-horizontal">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group form-group-sm">
                            <div class="col-sm-9">
                                <h6>Billing address</h6>
                            </div>
                        </div>
                        <div address-details address="vm.BillingAddress"></div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group form-group-sm">
                            <div class="col-sm-9">
                                <h6>Delivery address</h6>
                            </div>
                        </div>
                        <div>
                            <div address-details address="vm.PostalAddress" noteditable="vm.PostalIsBilling"></div>
                        </div>
                        <div class="form-group form-group-sm">
                            <div class="col-sm-10 col-sm-offset-2"><label class="checkbox"><input name="checkbox"
                                        type="checkbox" ng-model="vm.PostalIsBilling"
                                        ng-change="makePostalSameAsBilling(vm.PostalIsBilling)"> Customer delivery
                                    address is the same as billing address</label></div>
                        </div>
                        <div class="form-group form-group-sm">
                            <div class="col-sm-10 col-sm-offset-2 form-control-static">Customer delivery address
                                Production Facility is {{vm.DeliveryAddressProductionFaclity}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab content5">
            <div ng-if="vm.Id != 0" leppane="Merge customer">
                <div class="form-horizontal">
                    <div class="row">
                        <div class="col-sm-6">
                            <fieldset>
                                <div class="form-group form-group-sm"><label class="col-sm-4 control-label"
                                        for="username">Merge to Username</label>
                                    <div class="col-sm-8">
                                        <div class="input-group"><input type="text" class="form-control input"
                                                ng-model="toCust"> <span class="input-group-btn"><button
                                                    ng-click="mergeCustomer(vm.Id, toCust)"
                                                    class="btn">Merge</button></span></div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                </div>
            </div>
            <div leppane="Password" visible="true">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-horizontal">
                            <div class="form-group form-group-sm"><label class="control-label col-sm-4">Password</label>
                                <div class="col-sm-8"><input class="form-control" type="text" ng-model="vm.Password">
                                </div>
                            </div>
                            <div class="form-group form-group-sm"><label class="control-label col-sm-4">Confirm
                                    Password</label>
                                <div class="col-sm-8"><input class="form-control" type="text" ng-model="vm.Password1">
                                </div>
                            </div>
                            <div class="form-group form-group-sm"><label class="control-label col-sm-4"></label>
                                <div class="col-sm-5"><button class="btn btn-default" type="button"
                                        ng-click="updatePassword(vm.Password)"
                                        ng-hide="!vm.Password || !(vm.Password == vm.Password1)">Update
                                        password</button> <button class="btn" ng-click="resetPassword()"><span
                                            ng-if="vm.Password  && vm.Password == vm.Password1">Set password
                                        </span><span ng-if="!vm.Password || vm.Password != vm.Password1">Reset
                                            password</span></button></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div leppane="Other" visible="true">
                <div class="row form-horizontal">
                    <div class="col-sm-12">
                        <div class="form-group form-group-sm"><label class="control-label col-xs-2">Templates denied for
                                this customer</label>
                            <div class="col-xs-10">
                                <ui-select multiple="multiple" ng-model="vm.DeniedTemplates" theme="bootstrap"
                                    close-on-select="false" style="width: 100%;height: 100px"
                                    title="Choose a category to deny">
                                    <ui-select-match placeholder="Select templates...">{{$item.Name}}</ui-select-match>
                                    <ui-select-choices repeat="c.Id as c in allTemplatesName | filter:$select.search">
                                        {{c.Name}}</ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group form-group-sm"><label class="control-label col-xs-2"></label></div>
                </div>
                <div class="row form-horizontal">
                    <div class="col-sm-6">
                        <div class="form-group form-group-sm"><label class="col-xs-4 control-label">Sample kit sent
                                on</label>
                            <div class="col-xs-4"><input type="text" size="10" class="form-control"
                                    ng-model="vm.SampleKitSentOn" data-date-format="dd-MMM-yyyy" data-autoclose="1"
                                    placeholder="Date" bs-datepicker></div>
                            <div am-time-ago="vm.SampleKitSentOn" class="col-xs-4 text-muted form-control-static"></div>
                        </div>
                    </div><br>
                </div>
                <div class="row form-horizontal">
                    <div class="col-sm-12">
                        <div class="form-group form-group-sm"><label class="col-xs-2 control-label">Notes</label>
                            <div class="col-xs-10"><textarea id="notes0" rows="6" class="form-control input"
                                    ng-model="vm.Notes" auto-grow1></textarea></div>
                        </div>
                    </div>
                </div>
                <div class="row form-horizontal">
                    <div class="col-sm-12">
                        <div class="form-group form-group-sm"><label class="col-xs-2 control-label">Production
                                Notes</label>
                            <div class="col-xs-10"><textarea id="notes1" rows="6" class="form-control input"
                                    ng-model="vm.ProductionNotes" auto-grow1></textarea></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab content6">
            <div class="form-horizontal">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group form-group-sm"><label class="col-xs-4 control-label"
                                title="Moving Annual Turnover">Moving Annual Turnover (MAT)</label>
                            <div class="col-xs-4 form-control-static">{{vm.MAT | currency}}</div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-xs-4 control-label"
                                title="Monthly Avg Spend (3 months)">Monthly Avg Spend (3 months)</label>
                            <div class="col-xs-4 form-control-static">{{vm.MAT3ma | currency}}</div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label"
                                    for="prod-price-code">Product Price Code*</label>
                                <div class="col-sm-8"><select class="form-control input" ng-model="vm.ProductPriceCode"
                                        ng-options="k  as (k +' - ' + v) for (k,v) in CustomerProductPricing"
                                        ng-disabled="!($root.globals.IsSA)"></select>
                                </div>
                            </div>
                            <div class="form-group form-group-sm"><label class="col-sm-4 control-label"
                                    for="freight-price-code">Freight Price Code*</label>
                                <div class="col-sm-8"><select class="form-control input" ng-model="vm.FreightPriceCode"
                                        ng-options="k  as (k +' - ' + v) for (k,v) in CustomerFreightPricing"
                                        ng-disabled="!($root.globals.IsSA)"
                                        ></select>
                                </div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-xs-4 control-label">First order
                                date</label>
                            <div class="col-xs-4"><input type="text" size="10" class="form-control"
                                    ng-model="vm.FirstOrderDate" data-date-format="dd-MMM-yy" data-autoclose="1"
                                    placeholder="Date" bs-datepicker></div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-xs-4 control-label">Last order
                                date</label>
                            <div class="col-xs-8 form-control-static">{{vm.LastOrderDate | datex:'dd-MMM-yyyy HH:mm'}}
                                <span am-time-ago="vm.LastOrderDate" class="text-muted"></span></div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-xs-4 control-label">Last Login
                                date</label>
                            <div class="col-xs-8 form-control-static">{{vm.LastLoginDate | datex:'dd-MMM-yyyy HH:mm'
                                }} <span am-time-ago="vm.LastLoginDate" class="text-muted"></span></div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-xs-4 control-label">Returned lapse
                                date</label>
                            <div class="col-xs-4"><input type="text" size="10" class="form-control"
                                    ng-model="vm.ReturnedLapseDate" data-date-format="dd-MMM-yy" data-autoclose="1"
                                    placeholder="Date" bs-datepicker></div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label" for="">Customer
                                status</label>
                            <div class="col-sm-8"><select class="form-control input" ng-model="vm.CustomerStatus"
                                    ng-options="k for k in CustomerStatus">
                                    <option value="">-- Select --</option>
                                </select></div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label" for="">Franchise
                                Code</label>
                            <div class="col-sm-8"><select class="form-control input" ng-model="vm.FranchiseCode"
                                    ng-options="k.code  as k.name for k in Franchise"
                                    ng-disabled="vm.Id != 0 && !$root.globals.IsA">
                                    <option value="">-- Select --</option>
                                </select></div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label" for="">Sales
                                consultant</label>
                            <div class="col-sm-8"><select class="form-control input" ng-model="vm.SalesConsultant"
                                    ng-options="k.Name  as k.Name for k in SalesConsultant"
                                    ng-disabled="vm.Id != 0 && !$root.globals.IsA">
                                    <option value="">-- Select --</option>
                                </select></div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label" for="">Customer
                                Category Priority</label>
                            <div class="col-sm-8"><select class="form-control input" ng-model="vm.Potential"
                                    ng-options="k  as k for k in ['A','B', 'C', 'D']">
                                    <option value="">-- Select --</option>
                                </select></div>
                        </div>
                        <div class="form-group form-group-sm"><label class="col-sm-4 control-label"
                                for="">Offers</label>
                            <div class="col-sm-8">
                                <table ng-if="vm.Offers.length" class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Promo</th>
                                            <th class="nowrap">Offered Date</th>
                                            <th class="nowrap">Offer ends Est</th>
                                            <th class="nowrap">Date Taken up</th>
                                            <th class="nowrap">Allow reuse</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="o in  vm.Offers" class="{{::o.Css}}">
                                            <td class="nowrap"><b>{{::o.Promotion.PromotionCode}}</b></td>
                                            <td class="nowrap">{{o.DateOffered | date:'dd/MMM/yyyy'}}</td>
                                            <td class="nowrap">{{o.DateOfferEnds | date:'dd/MMM/yyyy'}}</td>
                                            <td class="nowrap">{{::o.DateTakenUp | date:'dd/MMM/yyyy'}}</td>
                                            <td class="nowrap"><input class="checkbox" type="checkbox"
                                                    ng-model="o.AllowReuse"></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab content7">
            <div class="form-horizontal">
                <div class="row">
                    <div class="col-sm-8">
                        <div class="form-group form-group-sm">
                            <div class="col-sm-12">
                                <table style="width: 100%">
                                    <tr>
                                        <td colspan="2"><textarea id="notes" rows="4" class="form-control input"
                                                ng-model="vm.NoteText" auto-grow1></textarea></td>
                                    </tr>
                                    <tr>
                                        <td><button ng-click="addCustomerNote(vm.NoteText)" class="btn-default"
                                                ng-disabled="!vm.NoteText">Add note</button><label class=""><span
                                                    class="btn btn-sm btn-default"><i
                                                        class="glyphicon glyphicon-paperclip"></i> Attach...</span>
                                                <input type="file" id="noteAttachment" ng-file-model="noteAttachment"
                                                    class="form-control-static" style="display: none">
                                                {{noteAttachment.name}}</label></td>
                                        <td></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <table class="table table-condensed table-striped table-hover"
                            style="font-size: smaller;font-family: monospace">
                            <tr ng-repeat="o in Last10Orders">
                                <td><a ui-sref="staff.order.view({orderId:o[0]})" target="_blank">O{{::o[0]}}</a></td>
                                <td>{{::o[1]}}</td>
                                <td><a ui-sref="staff.order.job({orderId:o[0], jobId:o[2]})"
                                        target="_blank">J{{::o[2]}}</a></td>
                                <td>{{::o[3]}}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-sm-12">
                        <table class="table table-striped table-condensed">
                            <tr>
                                <th>By</th>
                                <th>On</th>
                                <th>Note</th>
                                <th>Action</th>
                            </tr>
                            <tr ng-repeat="n in CustomerNotes">
                                <td class="nowrap">{{::n.CreatedBy}}</td>
                                <td class="nowrap">{{::n.CreatedOn | date:'dd-MMM-yy HH:mm'}}</td>
                                <td>{{::n.NoteText}} <span ng-if="n.IsDocument" style="margin-left: 10px"><a
                                            ng-click="$parent.$parent.downloadNoteDcoment(n.Id)"><i
                                                class="glyphicon glyphicon-download"></i> {{::n.FileName}}</a>
                                    </span><span ng-if="n.IsPhoneRecord" style="margin-left: 10px"><br><audio controls
                                            preload="none" ng-src="{{pathToRecord(n)}}">audio not
                                            supported</audio></span></td>
                                <td><span ng-if="::$root.globals.IsA"
                                        ng-click="editNote(n.Id, n.NoteText)">Edit</span></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab content8">
            <div class="form-horizontal">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group form-group-sm"><label class="control-label col-xs-2"></label>
                            <div class="col-xs-10"><a ng-click="createInvoice()"><i
                                        class="glyphicon glyphicon-plus"></i> Create Invoice</a></div>
                        </div>
                        <div class="form-group form-group-sm" ng-if=" vm.ExtraFiles.length"><label
                                class="control-label col-xs-2">Invoices</label>
                            <div class="col-xs-10">
                                <table class="table table-condensed">
                                    <tr ng-repeat="t in vm.ExtraFiles track by $index">
                                        <td><a ng-click="downloadCustomerExtraFile(vm.Id, t)">{{t}}</a></td>
                                        <td nowrap="nowrap">{{::t.CreationTime | date:'dd-MMM-yy HH:mm'}}</td>
                                        <td style="white-space: pre-wrap"><a ng-click="emailExtraFile(vm.Id, t)"><i
                                                    class="glyphicon glyphicon-send"></i></a></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <div class="form-horizontal col-sm-12">
                    <div class="row">
                        <div class="form-actions pull-right"><a ui-sref="staff.customers" class="btn"><i
                                    class="glyphicon glyphicon-chevron-left"></i> Back </a><button type="submit"
                                class="btn btn-default" ng-click="save()"><i
                                    class="glyphicon glyphicon-floppy-save"></i> Save Customer</button></div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <script>
    document.addEventListener("play", 
        function(e){
            var audios = document.getElementsByTagName("audio");
            for (var i = 0, len = audios.length; i < len; i++) {
                if (audios[i] != e.target) {
                    audios[i].pause();
                }
            }
        }
    , true);
    </script>
</div>
