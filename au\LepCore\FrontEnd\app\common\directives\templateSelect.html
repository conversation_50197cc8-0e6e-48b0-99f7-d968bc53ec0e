<div class="form-horizontal">
    <!--<pre>
    {{jobOptionId |json}}
    {{vm | json}}

    </pre>-->
    <!-- LEVEL 1 -->
    <div class="form-group ng-class:{'has-error':!vm.Category }">
        <label class="col-xs-3 control-label">{{key0(tree.topdown)}}</label>
        <div class="col-xs-7">
            <select id="category" class="form-control" ng-model="vm.Category"
                    ng-options="v as k for (k,v) in  tree.topdown.Category "
                    ng-change="vm.Category2=null;vm.Category3=null;tryExtractValue(v)">
                <option value="">select...</option>
            </select>
        </div>
    </div>

    <!-- LEVEL 2  {{ (values0(category))}}  -->
    <div ng-show="keys(values0(vm.Category)).length>=1" class="form-group  ng-class:{'has-error':!vm.Category2 }">
        <label class="col-xs-3 control-label"> {{keys(vm.Category)[0] || ''}} </label>
        <div class="col-xs-7">


            <span ng-if="$root.globals.IsSubCustomer !=undefined &&  $root.globals.IsSubCustomer===true">
                <span ng-repeat="(k,v) in (values0(vm.Category))">
                    <label class="btn btn-sm xx {{k}}" style="margin-right:2px; ">

                        {{k}} <input name="Production" type="radio" class="radio-inline dm" ng-value="v"
                                     ng-model="$parent.vm.Category2" ng-change="tryExtractValue(v)"
                                     ng-checked="$parent.vm.Category2==v" />
                    </label>
                </span>
            </span>

            <span ng-if="$root.globals.IsSubCustomer===undefined">
                <span ng-repeat="(k,v) in (values0(vm.Category))">

                    <span ng-hide="$root.globals.inHolidays && (k!= 'Standard') && (!$root.globals.IsStaff)">
                        <label class="btn btn-sm xx {{k}}" style="margin-right:2px; "
                               bs-tooltip data-title="stocks" html="true" data-container="body"
                               data-placement="top auto" data-auto-close="1" data-delay="0" data-custom-class="hidepop"
                               data-template-url="stocksFromTemplate.html">

                            {{k}} <input name="Production" type="radio" class="radio-inline dm" ng-value="v"
                                         ng-model="$parent.vm.Category2" ng-change="tryExtractValue(v)"
                                         ng-checked="$parent.vm.Category2==v" />
                            
                        </label>

                    </span>
                
                </span>
                <div ng-show="keys(values0(vm.Category)).length>1">
                     <a href="https://www.lepcolourprinters.com.au/faster/" target="_blank">NDD/SDD product options</a>
                </div>
            </span>

            <!--ng-disabled="[18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29].indexOf(v) > -1"-->

        </div>
    </div>


    <!--LEVEL 3   {{ (values0($parent.category2))}}-->
    <div ng-show="keys(values0(vm.Category2)).length &&  keys(values0(vm.Category2)).length < 3  " class="form-group ng-class:{'has-error':!vm.Category3 }">
        <label class="col-xs-3 control-label">{{keys(vm.Category2)[0] || ''}} </label>
        <div class="col-xs-7 form-control-static">
            <label class="" ng-repeat="(k,v) in (values0(vm.Category2))" style="margin-right:5px;">
                <input name="dm2" type="radio" class="radio-inline dm"
                       ng-model="$parent.vm.Category3" ng-value="v"
                       ng-change="tryExtractValue(v)" /> {{k}}
            </label>
        </div>
    </div>

    <!--LEVEL 3   {{ (values0($parent.category2))}}-->
    <div ng-show="keys(values0(vm.Category2)).length && keys(values0(vm.Category2)).length >= 3 " class="form-group ng-class:{'has-error':!vm.Category3 }">
        <label class="col-xs-3 control-label">{{keys(vm.Category2)[0] || ''}} </label>
        <div class="col-xs-7 form-control-static">

            <select id="dm2" class="form-control" ng-model="vm.Category3"
                    ng-options="v as k for (k,v) in  (values0(vm.Category2)) "
                    ng-change="tryExtractValue(vm.Category3)">
                <option value="">select...</option>
            </select>



        </div>
    </div>




    <script type="text/ng-template" id="stocksFromTemplate.html">
        <div class="popover" style="width:500px; font-size: small">
            <div class="arrow"></div>
            <!--<div class="popover-title">
                Stocks available
            </div>-->

            <div class="popover-content" ng-init="xxx = getStockOptionsOnTemplate(v)">


                <ul class="bold">
                    <!--<li>
                        <b style="color:red; font-weight:bold;">Next day and Same day dispatch options not available during Christmas holidays</b>
                    </li>-->
                    <li ng-if="k=='Standard'">Submit Print ready artwork when you want.</li>
                    <li ng-if="k=='Next day'">Print ready artwork must be submitted by 2pm AEST</li>
                    <li ng-if="k=='Same day'">Print ready artwork must be submitted by 9am AEST</li>
                </ul>
                <ul>
                    <li class="nowrap" ng-repeat="t in  listOfStockOptionsOnTemplate">{{t.Name}}</li>
                </ul>

            </div>
        </div>
    </script>


</div>
