appCore = angular.module('app.core')

appCore.directive 'leppane', ->
	{
	restrict: 'EA'
	transclude: true
	scope: {}
	link: ($scope, $elem, $attrs) ->
		#$scope.visible = !!$attrs.visible
		$scope.title = $attrs.leppane

	controller:[ '$scope',  '$element', '$attrs', '$state', '$sessionStorage', ($scope,  $element, $attrs, $state, $sessionStorage) ->
		expander =  $sessionStorage.expander || {}
		key = ($state.current.name + '--' + $attrs.leppane.replace(' ', '-'))
		visible = null
		ekey  = expander[key]
		if ekey is undefined
			visible = true
		else
			visible = ekey


		$scope.visible = visible  #|| $attrs.visible == "true"
		$sessionStorage.expander = expander

		$scope.$watch 'visible', (n,o) ->
			$sessionStorage.expander[key] = n
			return
		return
	]

	templateUrl: 'common/directives/expander.component.html'
	}
