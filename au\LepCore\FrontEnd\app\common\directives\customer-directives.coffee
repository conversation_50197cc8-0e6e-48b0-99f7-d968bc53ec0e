app = angular.module('app')

app.directive 'lepUniqueUsername', ['$http', '$q', ($http, $q) -> {
    restrict: 'A'
    require: 'ngModel'
    link: (scope, element, attrs, ngModel) ->
        ngModel.$asyncValidators.unique =  (modelValue, viewValue) ->
            d = $q.defer()
            id = scope.$eval(attrs.userId) || 0
            $http.post("/api/Users/<USER>/IsValidUsername", JSON.stringify(viewValue))
            .then (r) ->  if r.data then  d.resolve(true) else d.reject(false)
            return d.promise
    }
]

app.directive 'lepUniqueBusinessname', ['$http', '$q', ($http, $q) -> {
    restrict: 'A'
    require: 'ngModel'
    link: (scope, element, attrs, ngModel) ->
        ngModel.$asyncValidators.unique =  (modelValue, viewValue) ->
            d = $q.defer()
            id = scope.$eval(attrs.userId) || 0
            $http.post("/api/Users/<USER>/IsValidBusinessName", JSON.stringify(viewValue))
            .then (r) ->  if r.data then  d.resolve(true) else d.reject(false)
            return d.promise

    }
]


app.directive 'lepUniqueMyob', ['$http', '$q', ($http, $q) ->  {
    restrict: 'A'
    require: 'ngModel'
    link: (scope, element, attrs, ngModel) ->
        ngModel.$asyncValidators.unique =  (modelValue, viewValue) ->
            d = $q.defer()
            id = scope.$eval(attrs.userId) || 0
            $http.post("/api/Users/<USER>/IsValidMYOB", JSON.stringify(viewValue))
            .then (r) ->  if r.data then  d.resolve(true) else d.reject(false)
            return d.promise
    }
]
