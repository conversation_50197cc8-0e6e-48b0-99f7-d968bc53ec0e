<div id="specprint" name="specprint">
	<div leppane="Job details" visible="true">
		<div class="row   form-horizontal">
			<div class="col-sm-6">
				<!-- Job Name -->
				<div class="form-group">
					<label class="col-sm-3 control-label ">Job Name</label>
					<div class="col-sm-8  form-control-static">{{::job.Name}}</div>
				</div>

				<!-- quantity -->
				<div class="form-group">
					<label for="quantity" class="control-label col-sm-3">Quantity</label>

					<div class="col-sm-8  form-control-static">
						{{::job.Quantity}}

						<strong>1/Sided</strong>&nbsp;
						<span class="aspNetDisabled"><input id="" type="checkbox" name="" disabled="disabled"></span>
						<strong>2/Sided</strong>&nbsp;
						<span class="aspNetDisabled"><input id="" type="checkbox" name="" checked="checked" disabled="disabled"></span>
					</div>
				</div>

				<!-- size -->
				<div class="form-group">

					<label for="size" class="control-label  col-sm-3">Size</label>
					<div class="col-sm-8  form-control-static">
						{{::job.FinishedSize.PaperSize.Name}} <br>
						<b>Width</b> {{::job.FinishedSize.Width}}
						<b>Height</b> {{::job.FinishedSize.Height}}
					</div>

				</div>

				<!-- Proofs required? -->
				<div class="form-group">
					<label class="control-label col-sm-3">Proofs required?</label>
					<span class="col-sm-8  form-control-static">
						<input id="" type="checkbox"
							   ng-model="job.Proofs.ProofsRequired">
					</span>
				</div>

				<!-- No of Proofs sent -->
				<div class="form-group">

					<label class="control-label  col-sm-3">No of Proofs sent</label>
					<div class="col-sm-6">
						<div class="input-group">
							<span class="input-group-addon">A1</span><input ng-model="job.Proofs.NumProofsSentA1" type="text" maxlength="2" id="" class="form-control">
							<span class="input-group-addon">A2</span><input ng-model="job.Proofs.NumProofsSentA2" type="text" maxlength="2" class="form-control">
							<span class="input-group-addon">A3</span><input ng-model="job.Proofs.NumProofsSentA3" type="text" maxlength="2" class="form-control">
						</div>
					</div>
				</div>
			</div>


			<div class="col-sm-6">
				<div class="form-group">
					<label class="control-label col-sm-3">Received on</label>
					<div class="col-sm-8   form-control-static"> {{::job.ReceivedDate | date}}</div>
				</div>
				<div class="form-group">
					<label class="control-label  col-sm-3">Job Number</label>
					<div class="col-sm-8   form-control-static">
						{{::job.Barcode}}

						<div class="barcode">*{{::job.Barcode}}*</div>
					</div>
				</div>
			</div>
		</div>
	</div>


	<div leppane="Press">
		<div class="row">
			<div class="col-sm-8">
				<table cellpadding="0" cellspacing="0" border="0" class="table">
					<tbody>
						<tr>
							<th style="width: 40%">
								Stock
							</th>
							<th style="width: 15%">
								Sect
							</th>
							<th style="width: 15%">
								Size
							</th>
							<th style="width: 15%">
								Method
							</th>
							<th style="width: 25%">
								Qty
							</th>
							<th></th>
						</tr>
						<tr ng-repeat="pd in job.PressDetails">
							<td class="form-group">
								<select class="form-control" ng-model="pd.Stock"
										ng-options="s as s for s in job.Stocks">
									<option value=""></option>
								</select>
							</td>
							<td>
								<input type="text" class="form-control" ng-model="pd.Sect">
							</td>
							<td>
								<select class="form-control" ng-model="pd.Size">
									<option value=""></option>
									<option value="A1">A1</option>
									<option value="A2">A2</option>
									<option value="A3">A3</option>
								</select>
								<span class="oldValuesInError"></span>

							</td>
							<td>
								<select class="form-control" ng-model="pd.Method">
									<option value=""></option>
									<option value="SS">SS</option>
									<option value="SW">SW</option>
									<!-- <option value="Tumble">Tumble</option> -->
									<option value="Turn">Turn</option>
							        <option value="PFA">PFA</option>
                                    <option value="STR">STR</option>
								</select>

							</td>
							<td>
								<input type="text" class="form-control" ng-model="pd.Qty">
							</td>
							<td>
								<a ng-click="job.PressDetails.splice($index, 1)"> <i class="glyphicon glyphicon-remove"></i>  </a>
							</td>
						</tr>

					</tbody>
				</table>
				<a ng-click="job.PressDetails.push({})"> <i class="glyphicon glyphicon-add"></i>add press detail </a>
			</div>



			<!--<div class="col-sm-4">
				<pre>{{job.PressDetails|json}}</pre>
			</div>-->
		</div>
	</div>

	<div leppane="Finishing">

		<div class="row   form-horizontal">
			<div class="col-sm-6">
				<div class="form-group">
					<label class="control-label col-sm-3">Finished Size</label>
					<div class="col-sm-7  form-control-static">
						{{::job.FinishedSize.PaperSize.Name}},  Width:{{::job.FinishedSize.Width}} Height:{{::job.FinishedSize.Height}}
					</div>
				</div>

				<div class="form-group" ng-show="::job.FoldedSize != null">
					<label for="folding" class="control-label col-sm-3">Folding</label>
					<div class="col-sm-7 form-control-static">
						{{::job.FoldedSize.PaperSize.Name}}, Width:{{::job.FoldedSize.Width}} Height:{{::job.FoldedSize.Height}}
					</div>
				</div>

				<div class="form-group">
					<label class="control-label col-sm-3">Scoring</label>
					<div class="col-sm-6">
						<div class="input-group">
							<span class="input-group-addon">
								<input type="checkbox" ng-model="job.Scoring">
							</span>
							<input type="text" class="form-control" ng-model="job.ScoringInstructions">
						</div>
					</div>
				</div>

				<div class="form-group ">
					<label class="control-label col-sm-3">Perforation</label>
					<div class="col-sm-6">
						<div class="input-group">
							<span class="input-group-addon">
								<input id="" type="checkbox" ng-model="job.Perforating">
							</span>
							<input name="" type="text" class="form-control" ng-model="job.PerforatingInstructions">
						</div>
					</div>
				</div>



				<div class="form-group ">
					<label class="control-label col-sm-3">Die Cutting</label>
					<div class="col-sm-6">
						<div class="">
							<input name="" type="text" class="form-control" ng-model="job.DieCutting">
						</div>
					</div>
				</div>

				<div class="form-group">
					<label for="cello" class="control-label  col-sm-3">Celloglaze</label>
					<div class="col-sm-6  form-control-static">
						<span>Front: </span> {{::enums.ValueDesc.JobCelloglazeOptions[job.FrontCelloglaze]}}
						&nbsp;&nbsp;
						<span>Back: </span>{{::enums.ValueDesc.JobCelloglazeOptions[job.BackCelloglaze]}}
					</div>
				</div>

				<div class="form-group">
					<label class="control-label   col-sm-3">Finished by</label>
					<div class="col-sm-6">
						<div class="form-control-static">{{job.FinishedBy.FirstName}} {{job.FinishedBy.LastName}}</div>
					</div>
				</div>


			</div>

			<!--  second column -->
			<div class="col-sm-6">
				<div class="form-group">
					<label class="control-label">Special instructions</label>
					<textarea class="form-control " rows="6" ng-model="job.SpecialInstructions" auto-grow></textarea>
				</div>

				<div class="form-group">
					<label class="control-label">Production instructions</label>
					<textarea class="form-control " rows="6" ng-model="job.ProductionInstructions" auto-grow></textarea>
				</div>



			</div>
		</div>

	</div>


	<div leppane="Packing/Dispatch">

		<div class="row">
			<div class="col-sm-6">
				<div class="form-group">
					<label for="" class="control-label">Requested Packaging</label>
					<textarea rows="6" class="form-control" ng-model="job.RequestedPackaging" auto-grow></textarea>
				</div>
			</div>

			<div class="col-sm-6">
				<div class="form-group">
					<label for="packing" class="control-label">Packing Details </label>
					<textarea name="" rows="6" class="form-control" ng-model="job.ActualPackaging" auto-grow></textarea>
				</div>
			</div>

			<div class="col-sm-6">
				<div class="form-group row">
					<label class="control-label col-sm-3">Order No</label>
					<div class="col-sm-6  form-control-static">{{::job.Order.Id}}</div>
				</div>

				<div class="form-group row">
					<label class="control-label col-sm-3">Jobs</label>
					<div class="col-sm-6  form-control-static">
						<table>
							<tr ng-repeat="otherJobs in job.Order.Jobs">
								<td>
									<a ui-sref="staff.order.job({jobId: otherJobs.Id, orderId: job.Order.Id})">
										{{::otherJobs.JobInfo}}
									</a>
								</td>
							</tr>

						</table>
					</div>
				</div>

				<div class="form-group row">
					<label class="control-label  col-sm-3">Date Finished</label>
					<div class="col-sm-6  form-control-static"> {{::job.FinishedDate | datex:'dd-MM-yyyy'}}  </div>
				</div>
			</div>

		</div>
	</div>


	<div leppane="Customer">
		<div class="row">
			<div class="col-sm-6">
				<div class="form-group">
					<label class="control-label col-sm-3">Customer</label>
					<span class="col-sm-8  form-control-static">
						<a ui-sref="staff.customers-view({id: job.Order.CustomerId})">
							{{::job.OrderCustomerName}}
						</a>


					</span>
				</div>
				<div class="form-group">
					<label class="control-label col-sm-3">Contact</label>
					<span class="col-sm-8   form-control-static">{{::job.OrderCustomerContact1Name}}</span>
				</div>
			</div>

			<div class="col-sm-6">
				<div class="form-group">
					<label class="control-label col-sm-3">Phone</label>
					<span class="col-sm-8  form-control-static">{{::job.OrderCustomerContact1AreaCode}} {{::job.OrderCustomerContact1Phone}}</span>
				</div>
				<div class="form-group">
					<label class="control-label col-sm-3">Mobile</label>
					<span class="col-sm-8  form-control-static">{{::job.OrderCustomerContact1Mobile}}</span>
				</div>
			</div>
		</div>
	</div>



	<div class="buttons-large pull-right" id="buttonRow">
		<a class="btn" id="cancelButton" ng-click="cancel()">
			<i class="glyphicon glyphicon-chevron-left"></i>
			back to orders in run list
		</a>
		<button class="btn" id="refreshButton" ng-click="refresh()"> Refresh</button>
		<button class="btn" id="printButton" ng-click="print()">Print Job</button>
		<button class="btn" id="saveButton" ng-click="save()">Save Job</button>
		<button class="btn" id="saveNextButton" ng-click="saveNext()">Save &amp; Next Job</button>

	</div>




</div>
