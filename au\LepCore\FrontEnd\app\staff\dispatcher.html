<div leppadne="Dispatcher" visible="true">

    <style>
        input#edtSearch::placeholder {
            font-size: 12px;
            text-align: right;
            font-weight: normal;
        }
        .green {
            background: greenyellow;
        }

        #t td {
            vertical-align: top;
        }

        #t pre {
            background: #fff;
            color: auto;
            white-space: pre !important;
            font-size: small;
            padding: 10px;
            word-break: break-all;
            word-wrap: break-word;
            font-size: 14pt;
            border-radius: 6px;
            border: none;
            font-weight: 900;
            color: #464646;
            font-family: consolas;
            line-height: 1.2;
            margin-right: 10px;
        }

        #t .modal-header {
            border: none;
        }

        #currDispName, #edtSearch {
            font-size: 16pt;
            font-family: Arial;
            font-weight: 900;
            padding: 2px 4px;
        }
        .w220px {
            width:220px;
        }
        .w80px {
            width: 80px;
        }
        #edtSearch:focus {
            border: 1px solid #38a9f0;
            -webkit-box-shadow: 0px 0px 5px rgba(56, 169, 240, 0.75);
            -moz-box-shadow: 0px 0px 5px rgba(56, 169, 240, 0.75);
            box-shadow: 0px 1px 18px 11px rgba(56, 169, 240, 0.75);
        }
    </style>

    <div class="row">
        <div class="col-sm-6">   </div>
        <div class="col-sm-6">
        </div>
        <!--
        <label ng-repeat="(k,v) in dispatchers" class="control-label btn">
           <input type="radio" name="currDispName"
                   ng-model="$parent.currDispName" ng-value="k" /> {{k}}
        </label>
        -->
    </div>

    <div class="row" id="t">
        <div class="col-md-12">

            <table >
                <tr>
                    <td width="1%"></td>
                    <td width="49%">
                        <select id="currDispName" ng-model="currDispName" ng-options="k as  ('Dispatcher @'+k) for (k,v) in dispatchers">
                        </select>
                    </td>
                    <td width="50%">
                        <input id="edtSearch" type="text" tabindex="0"
                               ng-model="edtSearch"
                               ng-model-options="{updateOn: 'keyup'}"
                               update-on-enter
                               onfocus="this.select();" onm0ouseup0="return false;"
                               placeholder="Scan a job number..." />

                        <a class="btn" ng-show="jobId" ng-click="scan2(jobId)">Refresh</a>

                        <!-- <a class="btn" ng-show="jobId" ui-sref="staff.order-freight({orderId: order.Id, lastScannedJob: jobId})"
                           ng-mouseover="refreshRequired=true"
                           target="freight">View Freight</a> -->

                        <a ng-if="!order.HasSplitDelivery" ui-sref="staff.order-freight({orderId: order.Id, lastScannedJob: jobId})"                            
                            class="btn" title="view freight"   target="freight"  ng-mouseover="refreshRequired=true">View freight</a>
                        <a ng-if="order.HasSplitDelivery"  ui-sref="staff.order.jobsplit({orderId: order.Id, jobId: order.Jobs[0].Id,  lastScannedJob: jobId})" 
                            class="btn" title="view split freight"   target="freight"  ng-mouseover="refreshRequired=true">View Split freight</a>
                       

                        <!--<a class="btn" ng-show="jobId" ng-click="showFreight()" target="freight">View Freight 2</a>-->
                    </td>
                </tr>
                <tbody ng-if="!order.HasSplitDelivery && jobId">
                    <tr>  <td width="1%"></td>
                        <td class="pre">
                        <pre>
Receiver Name  : {{order.ReceiverName  }}
Receiver Phone : {{order.ReceiverPhone }}
Purchase Order : {{order.PurchaseOrder}}

Address 1      : {{order.DeliveryAddress.Address1}}
Address 2      : {{order.DeliveryAddress.Address2}}
Address 3      : {{order.DeliveryAddress.Address3}}
City           : {{order.DeliveryAddress.City}}
State          : {{order.DeliveryAddress.State}}
Postcode       : {{order.DeliveryAddress.Postcode}}
Country        : {{order.DeliveryAddress.Country}}

Contact Email  : {{order.Contact.Email}}
Contact Phone  : {{order.Contact.Phone}}

Delivery Instructions:
{{order.DeliveryInstructions}}</pre>
                    </td>
                    <td ng-if="!order.HasSplitDelivery" >
                        <pre>

Customer : {{order.CustomerName}}
Order    : {{order.Id}}
Weight   : {{totalWeight}} Kg total
Job Id   : {{jobId}}
Job Name : {{jobName}}

Courier: {{order.Courier}}
Overide: {{overrideCourier}}

Send Samples : {{job.SendSamples}}

{{packingStr2}}
    </pre>
                        </td>
                    </tr>

                    <tr ng-show="jobId">
                        <td colspan="2">
                            <!-- <a class="btn btn-default" ng-show="order.Id"
                               ui-sref="staff.order-freight({orderId: order.Id})" target="freight">View Freight</a> -->
    
                            <a ng-if="!order.HasSplitDelivery" ui-sref="staff.order-freight({orderId: order.Id, lastScannedJob: jobId})"
                               class="btn" title="view freight"   target="freight"  ng-mouseover="refreshRequired=true">View freight</a>
                            
                            <a ng-if="order.HasSplitDelivery"  ui-sref="staff.order.jobsplit({orderId: order.Id, jobId: order.Jobs[0].Id,  lastScannedJob: jobId})" 
                                class="btn" title="view split freight"   target="freight"  ng-mouseover="refreshRequired=true">View Split freight</a>
    
    
                            <a class="btn btn-default" title="Caton Label Printer" ng-click="cartonLabelMailHousePrinter()">MailHouse Carton Label</a>
    
                            <button class="btn btn-default"
                                ng-class="{'btn-danger':order.CustomerLogoRequired}"
                                ng-show="order.CustomerLogoRequired"
                                ng-click="print('LogoLabel', true)">
                                    <i class="glyphicon glyphicon-print"></i>
                                    Print Logo
                            </button>
    
                            <button class="btn btn-default"
                                ng-click="print('SampleLabel')"
                                ng-class="{'btn-danger':jobSendSample}">
                                <i class="glyphicon glyphicon-print"></i>
                                Print Sample Label
                            </button>
    
    
                            <button class="btn btn-default" ng-click="print('AddressA4Label')">
                            <i class="glyphicon glyphicon-print"></i>
                            Print A4 Label</button>
    
                            <button  ng-if="!order.HasSplitDelivery && jobId" 
                                    class="btn btn-default pull-right bold btn-green"
                                    ng-click="btnGenerateConsignmentForOrderClick()"
                                    ng-show="order.Visibility.btnGenerate"
                                    style="font-size: 28px; background-color: #346936; color: white;">
                                <i class="glyphicon glyphicon-globe"></i>
                                Generate Consignment
                            </button>
    
    
                            <button  ng-if="order.HasSplitDelivery && jobId" 
                                    class="btn btn-default pull-right bold btn-green"
                                    ng-click="btnGenerateConsignmentForJobWithSplitDeliveryClick()"
                                    ng-show="order.Visibility.btnGenerate"
                                    style="font-size: 28px; background-color: #346936; color: white;">
                                <i class="glyphicon glyphicon-globe"></i>
                                Generate Consignment
                            </button>
    
                        </td>
                    </tr>


                </tbody>

                

                <tbody ng-if="order.HasSplitDelivery && jobId" >
                    <tr  ng-repeat="a in order.Jobs[0].Splits track by $index" ng-click="$parent.$parent.sIdx = $index">
                        <td width="1%">
                            <input type="radio" ng-model="$parent.$parent.sIdx" ng-value="{{$index}}"  name="sIdx" />
                        </td>
                        <td>
                            <pre>
Split {{$index+1}}
Quanity        : {{a.Quantity}}
Receiver Name  : {{a.RecipientName  }}
Receiver Phone : {{a.RecipientPhone }}
Purchase Order : {{order.PurchaseOrder}}

Address 1      : {{a.Address.Address1}}
Address 2      : {{a.Address.Address2}}
Address 3      : {{a.Address.Address3}}
City           : {{a.Address.City}}
State          : {{a.Address.State}}
Postcode       : {{a.Address.Postcode}}
Country        : {{a.Address.Country}}


Delivery Instructions:
{{a.DeliveryInstructions}}
                            </pre>
                        </td>
                        <td>
                             <pre>

Customer : {{order.CustomerName}}
Order    : {{order.Id}}
Weight   : {{totalWeight}} Kg total
Job Id   : {{jobId}}
Job Name : {{jobName}}

Courier: {{a.Courier}}

Send Samples : {{a.SendSamples}}

{{a.PackagesStr}}
                            </pre>
                        </td>
                    </tr>

                    <tr ng-show="jobId">
                        <td width="1%"></td>
                        <td colspan="2">
                            <!-- <a class="btn btn-default" ng-show="order.Id"
                               ui-sref="staff.order-freight({orderId: order.Id})" target="freight">View Freight</a> -->
    
                            <a ng-if="!order.HasSplitDelivery" ui-sref="staff.order-freight({orderId: order.Id, lastScannedJob: jobId})"
                               class="btn" title="view freight"  target="freight"  ng-mouseover="refreshRequired=true">View freight</a>
                            
                            <a ng-if="order.HasSplitDelivery"  ui-sref="staff.order.jobsplit({orderId: order.Id, jobId: order.Jobs[0].Id,  lastScannedJob: jobId})" 
                                class="btn" title="view split freight"  target="freight"  ng-mouseover="refreshRequired=true">View Split freight</a>
    
    
                            <a class="btn btn-default" title="Caton Label Printer" ng-click="cartonLabelMailHousePrinter()">MailHouse Carton Label</a>
    
                            <button class="btn btn-default"
                                ng-click="print('LogoLabel', true)">
                                    <i class="glyphicon glyphicon-print"></i>
                                    Print Logo Label
                            </button>
    
                            <button class="btn btn-default"
                                ng-click="print('SampleLabel')"
                                ng-class="{'btn-danger':jobSendSample}">
                                <i class="glyphicon glyphicon-print"></i>
                                Print Sample Label
                            </button>
    
    
                            <button class="btn btn-default" ng-click="print('AddressA4Label')">
                            <i class="glyphicon glyphicon-print"></i>
                            Print A4 Label</button>
    
                            <button  ng-if="!order.HasSplitDelivery && jobId" 
                                    class="btn btn-default pull-right bold btn-green"
                                    ng-click="btnGenerateConsignmentForOrderClick()"
                                    ng-show="order.Visibility.btnGenerate"
                                    style="font-size: 28px; background-color: #346936; color: white;">
                                <i class="glyphicon glyphicon-globe"></i>
                                Generate Consignment
                            </button>
    
    
                            <button  ng-if="order.HasSplitDelivery && jobId" 
                                    class="btn btn-default pull-right bold btn-green"
                                    ng-click="btnGenerateConsignmentForJobWithSplitDeliveryClick()"
                                    ng-show="order.Visibility.btnGenerate"
                                    style="font-size: 28px; background-color: #346936; color: white;">
                                <i class="glyphicon glyphicon-globe"></i>
                                Generate Consignment
                            </button>
    
                        </td>
                    </tr>

                </tbody>

                




                <tr></tr>
            </table>
        </div>
    </div>

    <script type="text/ng-template" id="dlgWhatToDo">
        <div>
            <div class="modal-header" style="border: none;">
                <h3 class="modal-title">
                    <div class="col-sm-12"
                         style="font-size:40pt; color:{{order.Visibility.color}}">
                        {{order.Visibility.msg}}
                    </div>
                </h3>
            </div>
            <div class="modal-body" >
                <table class="table tableBodyScroll500" style="font-size:20pt">
                    <!--<thead>
                        <tr>
                            <th class="w80px"></th>
                            <th class="w220px">Status</thclass="w220px">
                            <th class="w220px">Id</th>
                            <th>Name</th>
                        </tr>
                    </thead>-->

                    <tbody>
                        <tr ng-repeat="job in order.Jobs track by $index"
                            ng-class="{'bold current':(order.Visibility.jobId == job.Id)}">
                            <td class="w80px">
                            <i class="glyphicon glyphicon-arrow-right" ng-show="order.Visibility.jobId == job.Id"></i>
                            </td>
                            <td class="nowrap w220px"  >{{job.StatusS}}</td>
                            <td  class="w220px">{{job.Id}}</td>
                            <td>{{job.Name}}</td>
                        </tr>
                        <tr>
                            <td></td>
                            <td colspan="3"><pre style="border:none;">{{packingStr2}}</pre></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <div class="row">
                    <button class=" btn btn-default" style="color:#666;font-size:25pt; width:100%; height:100px;"
                            tabindex="1"
                            ng-click="closeThisDialog(SpecialInstructions)">
                        <i class="glyphicon glyphicon-ok-circle"></i> Okay
                    </button>
                </div>
            </div>
        </div>

    </script>
    <div class="row">
        <div class="col-sm-12">
            <div connotes order="order">

            </div>

            <div ng-click="syncConnotes()" class="btn btn-sm" ng-if="jobId"> <i class="glyphicon glyphicon-refresh"></i> Pull Connotes from SF</div>
        </div>
    </div>
</div>
