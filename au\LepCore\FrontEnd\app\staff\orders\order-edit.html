﻿<div>

    <!--<div>

        <div class="alert alert-warning" role="alert" ng-show="order.Visibility.RdyArtworkInst">
            <strong>Warning!</strong>  Upload ready artwork.
        </div>

        <div id="ApprovalInst" class="alert alert-warning" ng-show="order.Visibility.ApprovalInst">
            Send to client for approval/review.
        </div>

        <div id="ReadyInst" class="alert alert-warning" ng-show="order.Visibility.ReadyInst">
            Send ready order for layout.
        </div>

        <div id="ExpensiveInst" class="alert alert-warning" ng-show="order.Visibility.ExpensiveInst">
            Ask a manager to approve this expensive order.
        </div>

        <div id="estPnl" class="alert alert-success" ng-show="order.Visibility.estPnl">
            {{::order.Visibility.minDayText}}
        </div>

        <div id="NoActionInst" class="alert alert-success" ng-show="order.Visibility.NoActionInst">
            No action required.
        </div>
    </div>-->

    <div leppane="Order details" visible="true">

        <!-- common order details -->
        <div class="row form-horizontal ">
            <div class="col-sm-6">

                <div class="form-group">
                    <label class="rlabel col-sm-4 control-label" for="person">Order # </label>
                    <div class="col-sm-8 form-control-static bold">{{::order.Id}}</div>
                </div>

                <div class="form-group">
                    <label class="rlabel col-sm-4  control-label ">Customer Name</label>
                    <div class="col-sm-8  form-control-static bold">
                        <a ui-sref="staff.customers-view({id: order.CustomerId})" target="_blank">{{::order.CustomerName}}</a>
                         
                        
                    
                    </div>
                </div>

                <div class="form-group">
                    <label class="rlabel col-sm-4  control-label ">Payment terms</label>
                    <div class="col-sm-8  form-control-static">{{::enums.ValueDesc.PaymentTermsOptions[order.CustomerPaymentTerms]}}  </div>
                </div>

                <div class="form-group" ng-show="::order.DispatchDate">
                    <label class="rlabel  col-sm-4  control-label ">Dispatch date</label>
                    <div class="col-sm-8  form-control-static">
                        {{::order.DispatchDate | datex:'dd-MM-yy HH:mm'}}
                        <span am-time-ago="::order.DispatchDate" class="text-muted"></span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label col-sm-4   nowrap" for="person">Purchase Order#</label>
                    <div class="col-sm-8">
                        <input class="form-control" type="text" ng-model="order.PurchaseOrder" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label checkbox col-sm-4" for="paidForThisOrder">Is paid?</label>
                    <div class="col-sm-8">
                        <label class="control-label">
                            <input name="paidForThisOrder" class="checkbox-inline" type="checkbox" ng-model="order.CustomerHasPaidForThisOrder" />
                            Customer has paid for this order
                        </label>
                    </div>
                </div>

                <div class="form-group" ng-show="::(order.DispatchEst && !order.RevisedDispatchDate)">
                    <label class="rlabel  col-sm-4  control-label ">Est. dispatch </label>
                    <div class="col-sm-8  form-control-static">
                        {{::order.DispatchEst | datex:'dd-MM-yy HH:mm'}},
                        <span am-time-ago="::order.DispatchEst" class="text-muted"></span>
                    </div>
                </div>

                <div class="form-group" ng-show="::order.RevisedDispatchDate">
                    <label class="col-xs-4 control-label ">Revised dispatch </label>
                    <div class="col-xs-8   form-control-static">
                        {{::order.RevisedDispatchDate | datex:'dd-MMM-yy  HH:mm'}}, <span am-time-ago="::order.RevisedDispatchDate" class="text-muted"></span>
                    </div>
                </div>

                <div class="form-group" ng-show="::order.FinishDate">
                    <label class="rlabel  col-sm-4  control-label ">Finish date</label>
                    <div class="col-sm-8  form-control-static">
                        {{::order.FinishDate | datex:'dd-MM-yy HH:mm'}},
                        <span am-time-ago="::order.FinishDate" class="text-muted"></span>
                    </div>
                </div>
            </div>

            <div class="col-sm-6" ng-show="true">

                <div class="form-group">
                    <label class="rlabel col-sm-4  control-label ">Status</label>
                    <div class="col-sm-8  form-control-static">{{::order.StatusS}} - <span class="nowrap" ng-bind-html="::(order.ExtraStatus)"></span></div>
                </div>

                <div class="form-group" ng-show="::order.SubmissionDate">
                    <label class="rlabel  col-sm-4  control-label ">Date Submitted</label>
                    <div class="col-sm-8  form-control-static">
                        {{::order.SubmissionDate | datex:'dd-MM-yy HH:mm'}}
                        <span am-time-ago="::order.SubmissionDate" class="text-muted"></span>
                    </div>
                </div>

                <div class="form-group" ng-show="::order.Visibility.quotePnlVisible">
                    <label class="control-label col-sm-4"></label>
                    <div class="col-sm-8" style="color:red">

                        Special Price. No further discount allowed
                    </div>
                </div>

                <div class="form-group" ng-show="::order.Visibility.promotionPnlVisible">
                    <label class="control-label col-sm-4">Promo Code</label>
                    <div class="col-sm-8">

                        <div class="input-group">

                            <input type="text" class="form-control" placeholder="" ng-model="order.PromotionPromotionCode">
                            <span class="input-group-btn">
                                <button id="applyPromo" class="btn  " ng-click="applyPromotion(order.PromotionPromotionCode)">Apply</button>
                            </span>
                        </div>
                        <div class="pre">{{::order.promotionApplyLog}}</div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label  col-sm-4">Product Price Code</label>
                    <div class="col-sm-8">
                        <div ng-repeat="x in order.DescProductPriceCodes">
                            {{x.Code}}:  {{x.Description}} / {{x.JobType}} {{x.Margin}}%
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label  col-sm-4">Freight Price Code</label>
                    <div class="col-sm-8">
                        {{order.DescFreightPriceCode.Code}}:  {{order.DescFreightPriceCode.Description}} / {{order.DescFreightPriceCode.Margin}}%
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label  col-sm-4">Is Quote</label>
                    <div class="col-sm-8">
                        <input name="isQuote" id="isQuote" class="checkbox" type="checkbox" ng-model="order.IsQuote" ng-change="saveOrder()" />
                    </div>
                </div>

                <div class="form-group" ng-show="order.Payments.length">

                    <label class="control-label  col-sm-4">Payments made</label>
                    <div class="col-sm-8 form-control-static">

                        <div ng-repeat="p in ::order.Payments">{{p.PaidAmount | currency}} on {{p.At | date:'dd-MMM-yyyy hh:mm' }} </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div leppane="Contact" visible="false">
        <div class="row">
            <div class="col-sm-12">
                <div class="help-block">  To change the contact for this order - override the usual contact person and number.</div>
                <div lep-contact-details contact="order.Contact" disabled="!order.Visibility.contactPnl" email-required="false"></div>
            </div>
        </div>
    </div> 
    <div ng-if="!order.HasSplitDelivery">
    <div leppane="Delivery details" visible="false" >
        <!--Extra fields to enter Recepient and contact number if for customer's customer-->
        <div class="row form-horizontal">
            <div class="col-sm-6">
                <!--  -->
                <div class="form-group form-group-sm">
                    <label class="control-label  col-sm-4" for="person">Recipient Name </label>
                    <div class="col-sm-8"><input class="form-control" type="text" ng-model="order.RecipientName" maxlength="30" size="30" /></div>
                </div>
                <!--  -->
                <div class="form-group form-group-sm">
                    <label class="control-label  col-sm-4" for="person">Recipient Phone</label>
                    <div class="col-sm-8">
                        <input class="form-control" type="text" ng-model="order.RecipientPhone" />
                    </div>
                </div>
                <!--  -->
                <div all-input-disabled="{{!order.Visibility.custAttachLogoChkEnabled}}">
                    <div class="form-group form-group-sm">
                        <label class="xx control-label checkbox col-sm-4" for="person">Attach logo label</label>
                        <div class="col-sm-8"><input class="checkbox" type="checkbox" ng-model="order.CustomerLogoRequired" /></div>
                    </div>
                    <!--  -->
                    <div class="form-group form-group-sm">
                        <label class="control-label  col-sm-4" for="person">Reference </label>
                        <div class="col-sm-8">
                            <input class="form-control" type="text" ng-model="order.CustomerLogoYourRef"
                                   ng-disabled="!(order.Visibility.custAttachLogoChkEnabled && order.CustomerLogoRequired)" />
                        </div>
                    </div>
                </div>

                <div class="form-group form-group-sm">
                    <label class="control-label  col-sm-4" for="person">Delivery instructions </label>
                    <div class="col-sm-8">
                        <textarea class="form-control input" rows="6" ng-model="order.DeliveryInstructions" auto-grow></textarea>
                    </div>
                </div>


                <div class="form-group form-group-sm">
                    <label class="col-xs-4 control-label" for="customer">Pack</label>
                    <div class="col-xs-8">
                        <label class="control-label"><input type="radio" ng-model="order.PackWithoutPallets" data-ng-value="false" /> Palletized </label>&nbsp;&nbsp;
                        <label class="control-label"><input type="radio" ng-model="order.PackWithoutPallets" data-ng-value="true" /> Loose Cartons</label>&nbsp;&nbsp;
                    </div>
                </div>


            </div>

            <div class="col-sm-6" all-input-disabled="{{!order.Visibility.addressPnl}}">
                <!--Logo requirements and extra references-->
                <div address-details address="order.DeliveryAddress" noteditable="!order.Visibility.addressPnl"></div>
                <a class="btn pull-right" ng-show="::order.Visibility.resetAddress" ng-click="orderCommand('resetAddress')">Reset address to customers postal address</a>
            </div>
        </div>
    </div>
    </div>
    <div class="row">
        <div class="col-sm-12">
            <a class="btn"    id="artwork-link"  target="_self" 
              > 
                <i class="glyphicon glyphicon-folder-open"></i> Open artwork folder</a>
            <a class="btn btn-info btn-sm bg-orange"
                ng-if="order.Visibility.splitDeliveryPossible == true && order.Visibility.splitCount == 0"
                ng-init="j0Id =order.Jobs[0].Id " ui-sref="^.jobsplit({orderId: order.Id, jobId: j0Id})"> Split delivery for job
                {{j0Id}}</a>
            

        </div>
    </div>

    <div leppane="Jobs" visible="true">

        <table class="table table-striped">
            <tbody>
                <tr class="jobrow">
                    <th></th>
                    <th>Status</th>
                    <th>Job#</th>
                    <th>Print</th>
                    <th>Facility</th>
                    <th>Job Name</th>
                    <th class="rlabel">Quantity</th>
                    <th>Type</th>
                    <th class="rlabel">Price</th>
                    <th class="actions">Action</th>
                </tr>
                <tr class="jobrow" ng-repeat="j in order.Jobs | orderBy : 'Id'">

                    <td class="" style="padding: 2px; vertical-align: middle; text-align: center; width: 120px; white-space: nowrap;">

                        
                        <input type="checkbox" checklist-model="selectedJobs" checklist-value="j.Id" style="transform: scale(2); cursor: hand;margin-right:4px;"/> 

                        <span ng-show="::(j.Visibility.Thumbs.length>0)">
                            <img ng-repeat="t in j.Visibility.Thumbs track by $index"
                                 ng-src="/api/orders/Job/{{::j.Id}}/thumb/{{t}}"
                                 class="template-img1 grow axxx" />
                        </span>

                        <img ng-if="::(j.Visibility.Thumbs.length==0)"
                             ng-src="{{$root.imgLookup(j.TemplateId)}}"
                             class="template-img1 " />

                        <i ng-style="{color: j.StatusCss}" ng-show="j.Visibility.artworkRequiredMsg && !j.Files"
                           style="font-size: 30px;"
                           bs-tooltip placement="left" trigger="hover" toggle="tooltip" html="true"
                           data-title="This job has no artwork! <br />Drag and drop your PDF artwork into this job">
                            <img src="/images/upload.png" width="40">
                        </i>

                        <i style="color: darkgreen;" ng-show="j.Files"
                           bs-tooltip placement="left" trigger="hover" toggle="tooltip" html="true"
                           data-title="You have nominated <b>{{j.Files[0].Name}}</b> to this job.<br /> Press Save Order button to save changes.<br />
						   To change this drag and drop a new file.">
                            <img src="/images/tick.png" width="32">
                        </i>
                    </td>

                    <td class="nowrap">
                        <i style="color: red; font-size: 12pt" ng-show="::j.Visibility.artworkRequiredMsg" class="glyphicon glyphicon-warning-sign" title="Artwork required!"></i>

                        <i style="color: red; font-size: 12pt; text-decoration: line-through;" ng-show="::j.AACNotPerformed===true" title="AAC not performed">AAC</i>

                        <span style="color: {{::j.StatusCss}};">{{::j.StatusS}}</span>
                    </td>

                    <td class="nowrap actions">
                        <a ui-sref="^.job({jobId: j.Id})" class="bold">{{::j.Id}}</a>
                        <span job-scan id="j.Id" routes="j.Visibility.Progress.routesAllList"></span>

                        <span job-print-label jid="j.Id"></span>
                    </td>
                    <td>{{::j.PrintType | printtype}}</td>
                    <td nowrap="nowrap">{{::enums.ValueDesc.Facility[j.Facility]}}</td>
                    <td>{{::j.Name}}</td>
                    <td class="rlabel" width="50">{{::j.Quantity | number : 0 }}</td>
                    <td nowrap="nowrap">{{::j.TemplateName}}</td>
                    <td class="rlabel nowrap" width="70" align="right">
                        <sapn ng-if="::(j.Price != 0 || j.Price != '')">{{::j.Price | currency }}</sapn>
                        <sapn ng-if="::(j.Price == '')">Quote required</sapn>
                    </td>
                    <td class="actions" nowrap="nowrap">
                        <a class="" ui-sref="^.job({jobId: j.Id})">View</a>
                        <!-- these need to be checked for permissions -->
                        <a class="" ng-show="::j.Visibility.copy" ng-click="copyJobNTimes(j.Id)">Copy</a>
                        <a class="" ng-show="::j.Visibility.withdraw" ng-click="jobCommand('Withdraw',j.Id)">Withdraw</a>
                        <a class="" ng-show="::j.Visibility.reactivate" ng-click="jobCommand('Reactivate',j.Id)">Reactivate</a>
                        <a class="" ng-show="::j.Visibility.reorder" ng-click="jobCommand('Reorder',j.Id)">Reorder</a>
                        <a class="" ng-show="::j.Visibility.deleteButton" ng-click="jobCommand('Delete',j.Id)">Delete</a>

                        <a class="" ng-show="::j.Visibility.restart" ui-sref="staff.job-ncr({orderId: order.Id, jobId: j.Id, ncrCmd: 'Restart'})">Restart</a>
                        <a class="" ng-show="::j.Visibility.reprintButton" ui-sref="staff.job-ncr({orderId: order.Id, jobId: j.Id, ncrCmd: 'Reprint'})">Reprint</a>

                        <a class="" ng-show="::j.Visibility.approvalButton" ng-click="jobCommand('Approve',j.Id)">Approve</a>
                    </td>
                </tr>

                <tr ng-if="::((order.PriceOfJobs === '' || order.PriceOfJobs === null)  )">
                    <td colspan="8" class="rlabel">
                        Subject to Quote.
                    </td>
                    <td class="total rlabel">
                        TBD
                    </td>
                    <td></td>
                </tr>

                <tr ng-if="::order.Visibility.promoBenifit">
                    <td colspan="8" class="rlabel">
                        - {{::order.Visibility.promoBenifit}}
                    </td>
                    <td class="total rlabel">
                        - {{::order.Visibility.promoBenifitAmount | currency}}
                    </td>
                    <td></td>
                </tr>

                <tr id="freightTr">

                    <td colspan="8" class="rlabel">
                        Freight (Inc. handling)
                    </td>
                    <td class="rlabel">
                        {{::order.PackDetail.Price | currency}}
                    </td>
                    <td class="actions">
                        
                        
                        <a ng-if="!order.HasSplitDelivery"  ui-sref="staff.order-freight({orderId: order.Id})" class="" title="view freight">View freight</a>
                        <a ng-if="order.HasSplitDelivery"  ui-sref="staff.order.jobsplit({orderId: order.Id, jobId: order.Jobs[0].Id})" class="" title="view freight">View Split freight</a>
                    
                    </td>

                        
                </tr>

                <tr ng-if="::(!(order.PriceOfJobs === '' || order.PriceOfJobs === null)  )">
                    <td></td>
                    <td colspan="7" class="rlabel"></td>
                    <td class="total rlabel">
                        {{::order.PriceOfJobs | currency }}
                    </td>
                    <td></td>
                </tr>

                <tr id="">
                    <td colspan="8" class="rlabel">

                        GST( {{::order.GST}} %)
                    </td>
                    <td class="total rlabel">
                        {{::order.PriceOfGST | currency}}
                    </td>
                    <td></td>
                </tr>

                <tr>
                    <td colspan="8" class="price rlabel">Total (Inc. GST)</td>
                    <td colspan="1" class="total rlabel price">{{::order.Price | currency}}</td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="row">
        <div class="col-sm-12">
            <div ng-show="selectedJobs.length">
                {{selectedJobs | json}}    
                     
                <button class="btn " id="rejectionOfVariationRequest" 
                ng-click="jobsCommand('RejectionOfVariationRequest', selectedJobs)">
                Reject Variation
            </button>
            </div>
            <div class="form-actions pull-right">
                <a class="btn" ng-click="backToOrders()"><i class="glyphicon glyphicon-chevron-left "></i> Back to orders</a>


                <div class="btn-group">
                    <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown"
                     aria-haspopup="true" aria-expanded="false">
                     <i class="glyphicon glyphicon-print"></i> Order
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" ng-click="getPdf()">Pdf format</a></li>
                        <li><a class="dropdown-item" ng-click="getPdf(1)">Html format</a></li>
                    </ul>
                </div>



                <a class="btn" ng-click="printLogo()">
                    <span class="glyphicon glyphicon-print"></span> Logo
                </a>

                <a class="btn" ng-show="::order.Visibility.addButton"
                        ui-sref="staff.order.addnewjob({orderId:order.Id})">
                    <i class="glyphicon glyphicon-plus"></i>
                    Add new job
                </a>

                <a class="btn" ng-show="::order.Visibility.deleteButton" ng-click="deleteOrder()">
                    <i class="glyphicon glyphicon-trash"></i>
                    Delete
                </a>

                <a class="btn " ng-show="::order.Visibility.withdrawButton" ng-click="orderWithdraw('Withdraw')">
                    Withdraw
                </a>

                <a class="btn  " ng-show="::order.Visibility.reactivateButton" ng-click="orderCommand('Reactivate')">
                    Reactivate
                </a>



                <a type="button" class="btn " ng-show="::order.Visibility.approveButton" ng-click="orderCommand('Approve')">
                    Approve Expensive Order
                </a>

                <a class="btn" ng-show="::order.Visibility.returnButton" ng-click="orderCommand('return')">
                    Return to customer
                </a>



                <a class="btn " ng-show="::order.Visibility.copyButton" ng-click="copyOrder()">
                    Copy order
                </a>

                <a class="btn " ng-show="::order.Visibility.reorder" ng-click="reorderOrder()">
                    Re-order order
                </a>
                

                <a class="btn" ui-sref="staff.order-ncr({orderId: order.Id, jobId: j.Id, ncrCmd: 'Reprint'})">Reprint</a>


                <a class="btn " ng-show="$root.globals.IsA || $root.globals.IsAccounts" ng-click="orderRefund()">
                    Refund
                </a>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <button type="button" class="btn btn-default" ng-show="::order.Visibility.saveButton" ng-click="saveOrder()">
                    <span class="glyphicon glyphicon-floppy-save"></span>
                    Save order
                </button>
                <button type="button" class="btn btn-default bold" ng-show="::order.Visibility.submitButton" ng-click="orderCommand('submit')">
                    <span class="glyphicon glyphicon-send"></span>
                    Submit order
                </button>
                <button type="button" class="btn btn-default" ng-show="::order.Visibility.readyButton" ng-click="orderCommand('submitReady')">
                    <span class="glyphicon glyphicon-send"></span>
                    Submit ready order
                </button>

            </div>
        </div>
    </div>



</div>

<br/><br/><br/>
<div class="row">
    <div class="col-sm-6">

<h4>Order Packing</h4>
<pre>{{order.PackDetail.FGPackageStr}}

{{order.PackDetail.PMPackageStr}}</pre>

    </div>
    <div class="col-sm-6">
    <div  >
        <h4>Invoices/Credit Notes</h4>
            <!--
            <button class="btn btn-sm btn-default" ng-click="uploadExtraFile(job.Id)">Upload extra files <i class="glyphicon glyphicon-upload"></i></button>
            -->

            <table class="table table-condensed">
                <tr ng-repeat="t in order.Visibility.ExtraFiles track by $index">
                    <td>
                        <a ng-click="downloadOrderExtraFile(order.Id, t)">{{t}}</a>
                    </td>
                    <td nowrap>{{::t.CreationTime | date:'dd-MMM-yy HH:mm'}} </td>
                    <td style="white-space: pre-wrap">
                        <!--
                        <a ng-click="deleteExtraFile(job.Id, t.Name)"> <i class="glyphicon glyphicon-remove-circle"></i></a>
                        -->
                        <a ng-click="emailExtraFile(order.Id, t)">  <i class="glyphicon glyphicon-send"></i></a>
                    </td>
                </tr>
            </table>


    </div>
    <style>
     </style>
    
    

    </div>
</div>

<div ng-if="::(order.Visibility.PODReqd)" class="col-md-6">
    <h4>Proof of delivery</h4> 
        <div>
           
            <input id="pickerName" ng-model="pickerName" type="text" class="form-control form-control-sm"
             ng-change="declPickup=('I '+ pickerName +' confirm picking up Order ' + order.Id + ' in full.') "
             ng-model-options="{debounce: 400}"
             ng-required="true" placeholder="Enter Name of Person Picking up"
             />

            <signature-pad accept="accept" clear="clear" height="300" width="800" dataurl="dataurl"
                declaration="declPickup"></signature-pad>
        </div>
        <div class="buttons">
            <button ng-click="clear()" class="btn" ng-show="pickerName">Clear</button>
            <button ng-click="saveSignature(dataurl)" ng-show="pickerName" class="btn btn-default">Sign</button>
        </div>

    <!-- <div class="result" ng-show="signature">
        <img ng-src="{{ signature.dataUrl }}">
    </div> -->
    <!-- <img ng-src="{{dataurl }}"> -->
    
</div>



    <br><br><br>


