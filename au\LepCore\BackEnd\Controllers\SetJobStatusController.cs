using lep;
using lep.configuration;
using lep.despatch.impl;
using lep.job;
using lep.security;
using lep.user;
using LepCore.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;

namespace LepCore.src.Controllers
{
	[AllowAnonymous]
	public class SetJobStatusController : Controller
	{
		public SetJobStatusController(
			PrintEngine printEngine,
			IJobApplication jobApplication,
			ISecurityApplication securityApp,
			IUserApplication userApp
			)
		{
			_printEngine = printEngine;
			_jobApp = jobApplication;
			
			_securityApp = securityApp;
			_userApp = userApp;
		}

		private IUserApplication _userApp;
		private PrintEngine _printEngine;
		private ISecurityApplication _securityApp;

		//private IList<string> _permitAddress;
		private IJobApplication _jobApp;
		
		private IJob _job;

		[HttpGet]
		[HttpPost]
		[Route("setstatus.aspx")]
		public IActionResult Index([FromQuery] int id, [FromQuery] string status)
		{
			return Page_Load(id, status);
		}
				
		private IActionResult Page_Load(int jobid, string status)
		{
			if (!IsValidAddress())
			{
				return GenerateErrorMsg("invalid address");
			}

			if (jobid > 0)
			{
				_job = _jobApp.GetJob(jobid);
			}

			if (_job == null)
			{
				return GenerateErrorMsg("job not found");
			}

			FileInfo file = new FileInfo(string.Format("{0}/success.txt", LepGlobal.Instance.ArtworkDirectory(_job, false).FullName));
			if (file.Exists)
			{
				file.Delete();
			}
			file = new FileInfo(string.Format("{0}/error.txt", LepGlobal.Instance.ArtworkDirectory(_job, false).FullName));
			if (file.Exists)
			{
				file.Delete();
			}

			try
			{
				SetJobStatus(status);
			}
			catch (Exception ex)
			{
				return GenerateErrorMsg(ex.Message);
			}

			return GenerateSuccessMsg();
		}

		private IActionResult GenerateErrorMsg(string error)
		{
			FileInfo errorFile = new FileInfo(string.Format("{0}/error.txt", LepGlobal.Instance.ArtworkDirectory(_job, false).FullName));
			StreamWriter writer = errorFile.CreateText();
			writer.Write(error);
			writer.Close();
			return new ObjectResult(new { success = false, message = error });
			//Response.Write(string.Format(@"{{""success"":false,""message"":""{0}""}}", error));
			//Response.End();
		}

		private IActionResult GenerateSuccessMsg()
		{
			FileInfo successFile = new FileInfo(string.Format("{0}/success.txt", LepGlobal.Instance.ArtworkDirectory(_job, false).FullName));
			StreamWriter writer = successFile.CreateText();
			writer.Write("job status changed");
			writer.Close();
			return new ObjectResult(new { success = true });
			//Response.Write(@"{""success"":true}");
			//Response.End();
		}

		private bool IsValidAddress()
		{
			return true;

			//if (Request.UserHostAddress == "::1")
			//    return true;

			//foreach (string permit in permitAddress) {
			//    if ((new Regex(permit)).IsMatch(Request.UserHostAddress)) {
			//        return true;
			//    }
			//}
			//return false;
		}

		private void SetJobStatus(string status)
		{
			var systemUser = _userApp.GetSystemUser();
			var IPAddress = Request.HttpContext.Connection.RemoteIpAddress.ToString();
			var staff = _userApp.GetStaffByIP(IPAddress);
			_jobApp.TrySetPreflightDone(_job, status, staff ?? systemUser);
		}
	}
}
