﻿//using System.Data.SqlClient;

namespace LepCore.Hubs
{
	//public class SqlDepSetup : ISqlDepSetup
	//{
	//    readonly IConnectionManager connectionManager;
	//    IConfigurationRoot _configuration;
	//    SqlDependency dependency;
	//    IHubContext hitCounterHub;
	//    string sql = "select [Id],[dateModified] from [dbo].[order] order by [DateModified] desc";
	//    SqlConnection sqlConnection;

	//    public SqlDepSetup(IConfigurationRoot configuration, IConnectionManager connectionManager)
	//    {
	//        this.connectionManager = connectionManager;
	//        _configuration = configuration;
	//        hitCounterHub = connectionManager.GetHubContext<HitCounter>();
	//        sqlConnection = new SqlConnection(configuration["Nhibernate:con"]);
	//        sqlConnection.Open();
	//    }


	//    public void setUpOrderNotification()
	//    {
	//        SqlCommand command = new SqlCommand(sql, sqlConnection);
	//        dependency = new SqlDependency(command);
	//        dependency.OnChange += SqlDependencyOnChange;
	//        using (SqlDataReader reader = command.ExecuteReader())
	//        {
	//        }
	//    }

	//    private void SqlDependencyOnChange(object sender, SqlNotificationEventArgs eventArgs)
	//    {
	//        hitCounterHub = connectionManager.GetHubContext<HitCounter>();
	//        hitCounterHub.Clients.All.orderUpdate(eventArgs);
	//        setUpOrderNotification();
	//    }
	//}
}
