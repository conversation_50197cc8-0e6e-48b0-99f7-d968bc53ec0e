do (window, angular)  ->
    app = angular.module('app')

    app.directive 'lepJobSplitDelivery', () ->
        restrict		: 'EA'
        scope			 : { job : '='}
        templateUrl : 'common/directives/split-delivery.html'
        controller	: 'JobSplitDeliveryController'

    app.controller 'JobSplitDeliveryController', [
        '$scope', '$rootScope', 'OrderService', 'JobService', '$location', 'enums', 'Utils', '$log', '$http', '$state', 'cfpLoadingBar', 'ngDialog', 'lepApi2'
        ($scope , $rootScope,    OrderService,   JobService ,  $location,   enums,   utils,   $log,   $http,   $state,  cfpLoadingBar,    ngDialog, lepApi2) ->
            # $scope.pos2label       = utils.pos2label
            $scope.order = $scope.$parent.order
            job                    = $scope.job
            job.Splits             = job.Splits || []

            # if job has no split ask for how many splits required
            if job.Splits.length == 0
                bootbox.prompt({
                    size: "small",
                    closeButton: false,
                    title: "How many delivery addresses do you need?",
                    callback: (result) ->
                        #result = String containing user input if OK clicked or null if Cancel clicked
                        if !result then return
                        n = parseInt(result,10)
                        if !n then return
                        $scope.$apply ()->
                            for i in [1..n]
                                blankEntry=
                                    Id: i
                                    Address:
                                        Country: "AU",
                                    BrochureDistPackInfo:
                                        PackingInstruction: 0,
                                        MailHouse: null
                                #cd blankEntry
                                $scope.job.Splits.push(blankEntry)
                        true
                })

            # get list of mail houses
            JobService.getBrohureMailHouses(job.Template.Id).then (r)->
                $scope.brohureMailHouses = r
            # get list of packages so we can show them in the drop down
            lepApi2.get('packages/all', null, true).then (r) ->
                $scope.packages = r

            $scope.total           = 0
            $scope.totalsDontMatch = false
            $scope.sumOfFreight    = 0
            $scope.createAddress   = () ->  job.Splits.push({Quantity: 0, Address: {}})
            $scope.removeAddress   = (i) -> job.Splits.splice(i, 1)

            $scope.totalsMatch = () ->
                $scope.total = _.sumBy($scope.job.Splits,'Quantity')
                $scope.totalsDontMatch =  $scope.total != $scope.job.Quantity
                return !$scope.totalsDontMatch


            $scope.changeMailHouse = (i, mh) ->
                if !mh
                    job.Splits[i].BrochureDistPackInfo = { PackingInstruction: 0 }

            blankOutSplit = (idx) ->
                job.Splits[idx].Courier = null
                job.Splits[idx].CustomerCharge = 0
                job.Splits[idx].Rates2 = []
                job.Splits[idx].Packages = []
            
            
            # watch splits and see if address or quantity changed
            # if changed null out courier and rates
            $scope.$watch 'job.Splits', (oa,na) ->
                ol = oa.length
                nl = na.length
                if ol != nl then return

                refreshFreight = false
                for n, i in na
                    o = oa[i]
                    if o.Address.Postcode != n.Address.Postcode or o.Address.City != n.Address.City
                        blankOutSplit(i)
                        refreshFreight = true

                    if n.Quantity != o.Quantity
                        blankOutSplit(i)
                        refreshFreight = true

                    if n.BrochureDistPackInfo?.Mailhouse?.Id != o.BrochureDistPackInfo?.Mailhouse?.Id
                        blankOutSplit(i)
                        refreshFreight = true

                    if n.BrochureDistPackInfo?.PackingInstruction != o.BrochureDistPackInfo?.PackingInstruction
                        blankOutSplit(i)
                        refreshFreight = true

                $scope.total = _.sumBy($scope.job.Splits,'Quantity')
                $scope.totalsDontMatch =  $scope.total != $scope.job.Quantity
                $scope.sumOfFreight = parseFloat(_.sumBy(job.Splits, (x)->x.CustomerCharge||0).toFixed(2))
                $scope.freightIncHandling = $scope.sumOfFreight + (job.Splits.length * 25)


                if !$scope.totalsDontMatch && $scope.splitsForm.$valid && refreshFreight
                    $scope.getSuggestions()
            , true

            $scope.changeRate = (a) ->
                if !a.$Courier then return
                a.Courier = a.$Courier.Value
                a.CustomerCharge = a.$Courier.CustomerCharge


            # after user has filled in the form with recipient and address
            # do a validation and get rates to show
            $scope.getSuggestions = () ->
                $scope.total = _.sumBy($scope.job.Splits,'Quantity')
                $scope.totalsDontMatch =  $scope.total != $scope.job.Quantity
                if  $scope.totalsDontMatch then return
                $scope.updatingCourier = true
                
                $http.post("/api/JobSplitDelivery/GetSuggestions/#{job.Id}", job.Splits)
                .then (res) ->
                    r = res.data
                    for s in r.Splits
                        s.Rates2 = OrderService.formatRates(s.Rates, r.Facility)
                        #cd s
                    job.Splits = r.Splits
                    
                    $scope.updatingCourier = false
                    


            # when user presses save
            # do basic validation, see if all courier selection has been made
            # then call save to save data
            $scope.save = () ->
                p = angular.copy(job.Splits)
                for s in p
                    delete s.Rates
                    delete s.Rates2
                $http.post("/api/JobSplitDelivery/Save/#{job.Id}", p)
                .then (res) ->
                    toastr.success("Saved")

            $scope.cancelSplit = () ->
                prompt = "Cancel Splitting Delivery for this Job/Order?"
                bootbox.confirm({
                    size: "small",
                    closeButton: false,
                    message: prompt,
                    callback:   (r) ->
                        if not r then return
                        $http.post("/api/JobSplitDelivery/CancelSplit/#{job.Id}")
                        .then (res) ->
                            toastr.success("Reverted to Single Delivery for order")
                            IsCustomer = $scope.$root.globals.IsCustomer
                            orderState = if IsCustomer then "cust.order.view" else "staff.order.view"
                            $state.go(orderState, {orderId: job.OrderId}, {reload:true})
                })

            custFavouriteDeliveryDetails = "cust/FavouriteDeliveryDetails"
            $scope.openFavouriteDeliveryDetails = (i) ->
                url = custFavouriteDeliveryDetails + "/List"
                if $scope.$root.globals.IsStaff
                    url += "/" + job.OrderCustomerId
                lepApi2.get(url).then (r) ->
                    $scope.favAddresses = r
                    dialog = ngDialog.open(
                        template:   'cust/SelectAddress.html',
                        className:  'ngdialog-theme-default custom-width-900',
                        width: '90%'
                        height: '90%'
                        trapFocus: true
                        scope: $scope
                    )
                    dialog.closePromise.then( (r) ->
                        v = r.value
                        if v is '$escape' or v is '$document' or v is '$closeButton' or !v then return
                        job.Splits[i].Address = {}
                        job.Splits[i].Address = v.Address
                        job.Splits[i].RecipientName   =  v.RecipientName
                        job.Splits[i].RecipientPhone  =  v.RecipientPhone
                        job.Splits[i].DeliveryInstructions  =  v.DeliveryInstructions
                    )


            $scope.packageChanged = (p, i) ->


            # nested package in side package tree functions
            # deletes a package node
            $scope.deleteNode  = (p,i) ->
                #p.Items = p.Items ||[]
                if p.splice
                    if p[i].Items and p[i].Items.length
                        si = angular.copy(p[i].Items)
                        p.splice(i,1)
                        for ii in si
                            p.push(ii)
                    else
                        p.splice(i,1)
                else if p.Items.splice
                    if p.Items[i]
                        si = angular.copy(p.Items[i].Items)
                        p.Items.splice(i,1)
                        if si then p.Items.push(ii) for ii in si
                            

            
            # delete package node along with children
            $scope.deleteNode2  = (p,i) ->
                if p.splice then p.splice(0,1)
                else if p.Items.splice then p.Items.splice(0,1)

            # add a child not on the given node
            $scope.addNodeUnder = (p,i) ->
                p.Items = p.Items ||[]
                newNode = {JobId: p.JobId}
                p.Items.push(newNode)

            # replicate same node multiple times under parent
            $scope.replicateNode = (s) ->
                pcakageToReplicate  = angular.copy(s.$parent.package)
                nodeP = s.$parent.parent
                n = parseInt(window.prompt('How many times do you want to replicate this package?', 1))
                if !isNaN(n)
                    while n--
                        if nodeP.Items then nodeP.Items.push(pcakageToReplicate)
                        else nodeP.push(pcakageToReplicate)


            $scope.wrapIn = (arr) ->
                if arr.length < 2 then return
                temp = arr.slice()
                kgs = _.sumBy( temp, 'Weight')
                arr = []
                arr.push({Items: temp, Weight: kgs})
                arr

            $scope.logNodeAndParent = (p,i) ->


            return

    ]
