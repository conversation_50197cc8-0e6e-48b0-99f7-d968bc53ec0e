{
  "IMP": {
    "HotfolderPath": "\\\\ppp01\\HotFolderRoot\\MyLEP"
  },
  "ApplicationInsights": {
    "InstrumentationKey": ""
  },

  "Logging": {
    "IncludeScopes": false,
    "LogLevel": {
      "Default": "Error",
      "System": "Error",
      "Microsoft": "Error",
      "Microsoft.AspNetCore.Mvc": "Error",
      "Microsoft.AspNetCore.Authentication": "Error",
      "Microsoft.AspNetCore.Routing.RouteBase": "Error",
      "Microsoft.AspNetCore.Server.Kestrel": "Error",
      "NHibernate": "Warn",
      "NHibernate.SQL": "DEBUG",
      "Microsoft.AspNetCore.SignalR": "None",
      "Microsoft.AspNetCore.Hosting": "None",
      "Microsoft.AspNetCore.StaticFiles": "None"
    }
  },
  "jobboard": {
    "messagequeue": ".\\Private$\\leponline.jobboardqueue"
  },

  "CustomerLogoDirectory": "\\\\icarus.icemedia.com.au\\lepdata\\logo",
  "DataDirectory": "\\\\icarus.icemedia.com.au\\lepdata",
  "OldDataDirectory": "\\\\icarus.icemedia.com.au\\lepdata\\oldorders",
  "DataDirectoryPC": "\\\\icarus.icemedia.com.au\\lepdata",
  "DataDirectoryMac": "/Volumes/LEPDATA",

  //"AbsolutePathURL": "http://lepold.localtest.me",

  "lepcrm.webservice.url": "http://crm.lep.icemedia.com.au/LEPDummyCRMService.asmx",
  "compdata.webservice.url": "http://f1.lepcolourprinters.com.au:8089/iFreightChargeEnquiryService.svc",

  "AbsolutePathURL": "http://localhost:5000",

  "email": {
    "sendMail": false,
    "server"  : "smtp.office365.com",
    "port"    : "587",
    "username": "<EMAIL>",
    "password": "11_Sandalwood",
  },
  "print.messagequeue": ".\\Private$\\leponline.messageprintqueue",
  "jobboard.messagequeue": ".\\Private$\\leponline.jobboardqueue",

  "Nhibernate": {
    "Con": "Data Source=PC11532; user id=sa; password=***********; Initial Catalog=LepCore3;MultipleActiveResultSets=true"
  }
}
