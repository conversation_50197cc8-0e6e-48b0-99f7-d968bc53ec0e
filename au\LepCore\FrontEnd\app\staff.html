﻿<!DOCTYPE html>
<!--[if lt IE 7]>
<html lang="en" ng-app="app" class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>
<html lang="en" ng-app="app" class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>
<html lang="en" ng-app="app" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!-->
<html lang="en" ng-app="app" class="no-js">
<!--<![endif]-->
<head>



	<!-- Google Tag Manager -->
	<script>
	//(function (w, d, s, l, i) {
	//	w[l] = w[l] || []; w[l].push({
	//		'gtm.start':
	//			new Date().getTime(), event: 'gtm.js'
	//	}); var f = d.getElementsByTagName(s)[0],
	//		j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
	//			'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
	//	})(window, document, 'script', 'dataLayer', 'GTM-T7BRRXF');
	</script>
	<!-- End Google Tag Manager -->




	<link rel="apple-touch-icon" sizes="57x57" href="/images/favicon/apple-touch-icon-57x57.png">
	<link rel="apple-touch-icon" sizes="60x60" href="/images/favicon/apple-touch-icon-60x60.png">
	<link rel="apple-touch-icon" sizes="72x72" href="/images/favicon/apple-touch-icon-72x72.png">
	<link rel="apple-touch-icon" sizes="76x76" href="/images/favicon/apple-touch-icon-76x76.png">
	<link rel="apple-touch-icon" sizes="114x114" href="/images/favicon/apple-touch-icon-114x114.png">
	<link rel="apple-touch-icon" sizes="120x120" href="/images/favicon/apple-touch-icon-120x120.png">
	<link rel="apple-touch-icon" sizes="144x144" href="/images/favicon/apple-touch-icon-144x144.png">
	<link rel="apple-touch-icon" sizes="152x152" href="/images/favicon/apple-touch-icon-152x152.png">
	<link rel="apple-touch-icon" sizes="180x180" href="/images/favicon/apple-touch-icon-180x180.png">
	<link rel="icon" type="image/png" sizes="32x32" href="/images/favicon/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="192x192" href="/images/favicon/android-chrome-192x192.png">
	<link rel="icon" type="image/png" sizes="16x16" href="/images/favicon/favicon-16x16.png">
	<link rel="manifest" href="/images/favicon/manifest.json">
	<link rel="mask-icon" href="/images/favicon/safari-pinned-tab.svg" color="#5bbad5">
	<link rel="shortcut icon" href="/images/favicon/favicon.ico">
	<meta name="msapplication-TileColor" content="#da532c">
	<meta name="msapplication-TileImage" content="/images/favicon/mstile-144x144.png">
	<meta name="msapplication-config" content="/images/favicon/browserconfig.xml">
	<meta name="theme-color" content="#ffffff">

	<meta name="description" content="Online ordering 24/7 with instant quotes on millions of product options, prices and quantities.">


	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title data-ng-bind="title">My LEP Login</title>
	<meta charset="utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge, chrome=1" />
	<!--<meta name="viewport"
		  content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no" />-->

	<style>
		.ng-hide {
			display: none !important;
		}

		[ng\:cloak], [ng-cloak], [data-ng-cloak], [x-ng-cloak], .ng-cloak, .x-ng-cloak {
			display: none !important;
		}
	</style>
	<script>
		/*
		*/
		(function (window) {
			if (window.location.hostname.match(/localhost/)) {
				var head = document.getElementsByTagName('head')[0];
				var sTag = document.createElement('script');
				sTag.type = 'text/javascript';
				sTag.src = 'http://localhost:35729/livereload.js';
				head.appendChild(sTag);
			}
		}(window));


	</script>

	<!--<base href="index.html"/>-->
	<script>
		// Must be first. IE10 mobile viewport fix
		if ("-ms-user-select" in document.documentElement.style && navigator.userAgent.match(/IEMobile\/10\.0/)) {
			var msViewportStyle = document.createElement("style");
			var mq = "@@-ms-viewport{width:auto!important}";
			msViewportStyle.appendChild(document.createTextNode(mq));
			document.getElementsByTagName("head")[0].appendChild(msViewportStyle);
		}
	</script>


	<!-- bower:css -->
	<!-- endinject -->
	<!-- inject:css -->
	<!-- endinject -->




	<style>
		/*@font-face {
			font-family: Myriad;
			src: url('/fonts/MyriadPro-Regular.otf') format('opentype');
		}*/

		@font-face {
			font-family: 'Conv_EnzoOT-Medi';
			src: url('/fonts/EnzoOT-Medi.eot');
			src: local('☺'), url('/fonts/EnzoOT-Medi.woff') format('woff'), url('/fonts/EnzoOT-Medi.ttf') format('truetype'), url('/fonts/EnzoOT-Medi.svg') format('svg');
			font-weight: normal;
			font-style: normal;
		}

		@font-face {
			font-family: 'Conv_EnzoOT-MediIta';
			src: url('/fonts/EnzoOT-MediIta.eot');
			src: local('☺'), url('/fonts/EnzoOT-MediIta.woff') format('woff'), url('/fonts/EnzoOT-MediIta.ttf') format('truetype'), url('/fonts/EnzoOT-MediIta.svg') format('svg');
			font-weight: normal;
			font-style: normal;
		}

		@font-face {
			font-family: 'Conv_EnzoOT-Bold';
			src: url('/fonts/EnzoOT-Bold.eot');
			src: local('☺'), url('/fonts/EnzoOT-Bold.woff') format('woff'), url('/fonts/EnzoOT-Bold.ttf') format('truetype'), url('/fonts/EnzoOT-Bold.svg') format('svg');
			font-weight: normal;
			font-style: normal;
		}

		@font-face {
			font-family: 'Conv_EnzoOT-BoldIta';
			src: url('/fonts/EnzoOT-BoldIta.eot');
			src: local('☺'), url('/fonts/EnzoOT-BoldIta.woff') format('woff'), url('/fonts/EnzoOT-BoldIta.ttf') format('truetype'), url('/fonts/EnzoOT-BoldIta.svg') format('svg');
			font-weight: normal;
			font-style: normal;
		}

		@font-face {
			font-family: 'ui-grid';
			src: url('ui-grid.eot');
			src: url('/fonts/ui-grid.eot#iefix') format('embedded-opentype'), url('/fonts/ui-grid.woff') format('woff'), url('/fonts/ui-grid.ttf') format('truetype'), url('/fonts/ui-grid.svg?#ui-grid') format('svg');
			font-weight: normal;
			font-style: normal;
		}
	</style>
</head>


<body class="ng-cloak ng-class:{'login': $state.current.class == 'login'}" ng-controller="RootController">






	<!-- Google Tag Manager (noscript) -->
	<noscript>
		<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T7BRRXF"
				height="0" width="0" style="display:none;visibility:hidden"></iframe>
	</noscript>
	<!-- End Google Tag Manager (noscript) -->

	<a name="top"></a>

	<nav class="navbar main-navigation" role="navigation" ng-if="globals.User != undefined">
		<div class="container">
			<div class="navbar-static-top">
				<button type="button" class="navbar-toggle" data-toggle="collapse"
						data-target="#bs-example-navbar-collapse-1">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>
				<span class="navbar-brand">
					<img src="images/LEP_Logo.png" alt="LEP Online" />
				</span>
			</div>

			<div class="usermenu pull-right">
				<ul class="nav  nav-pills ">
					<li class="dropdown">
						<a ng-if="globals.IsCustomer"
						   ui-sref="cust.settings" class="dropdown-toggle" data-toggle="dropdown">
							<i class="glyphicon glyphicon-cog"></i>
							My Account Details
						</a>

						<a ng-if="globals.IsStaff"
						   ui-sref="staff.setup.staff-view({id: globals.User.Id})"
						   href="#" class="dropdown-toggle" data-toggle="dropdown">
							<i class="glyphicon glyphicon-user"></i>
							{{ globals.User.Contact1.Name}}
							{{ globals.User.FirstName}} {{ globals.User.LastName}}
						</a>
					</li>
					<li>
						<a href="http://www.lepcolourprinters.com.au/help-centre" target="_blank">
							<i class="glyphicon glyphicon-question-sign"></i>
							Help
						</a>
					</li>
					<li>
						<a href="" ng-click="logout()" ui-sref-active="active-nav">
							Logout <i class="glyphicon glyphicon-log-out"></i>
						</a>
					</li>
				</ul>
			</div>
			<div class="mainmenu pull-right">
				<div ng-if="globals.IsStaff">
					<div ng-include="'staff/menu.html'"></div>
				</div>
				<div ng-if="globals.IsCustomer">
					<div ng-include="'cust/menu.html'"></div>
				</div>
			</div>
		</div>
	</nav>
	<div ng-show="percentage>0.000001 && percentage < 100" class="percentage">
		Please wait, uploaded {{percentage}}%...
	</div>

	<div class="content container ng-class:{'staffView': globals.IsStaff}" ui-view>
	</div>

	<br /><br /><br />


	<footer id="footer" ng-if="globals.User != undefined">
		<div class="container">

			<div class="row">
				<div class="col-sm-6">
					<div ng-if="globals.IsStaff">
						<div ng-include="'footer-staff.html'"></div>
					</div>
					<div ng-if="globals.IsCustomer">
						<div ng-include="'footer-cust.html'"></div>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="row">
						<div class="col-sm-12" style="text-align: right">
							<p>

								version 5.11.11 Copyright © 2017 | LEP Colour Printers is a registered Trademark.
								<br />
								<a href="#" ng-show="globals.IsStaff" ng-click="getUserCount()" style="cursor:pointer">{{userCount}}</a>
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</footer>

	<!-- bower:js -->
	<!-- endinject -->

	<!-- inject:js -->
	<!-- endinject -->


	<!-- appcore:js -->
	<!-- endinject -->
	<!-- tplcore:js -->
	<!-- endinject -->


	<!-- appstaff:js -->
	<!-- endinject -->
	<!-- tplstaff:js -->
	<!-- endinject -->


	<!-- templates:js -->
	<!-- endinject -->

	<!-- inject_staff:js -->
	<!-- endinject -->

	<!-- templates_staff:js -->
	<!-- endinject -->

	<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/bs/dt-1.10.13/fc-3.2.2/fh-3.1.2/r-2.1.0/datatables.min.css" />
	<script type="text/javascript" src="https://cdn.datatables.net/v/bs/dt-1.10.13/fc-3.2.2/fh-3.1.2/r-2.1.0/datatables.min.js"></script>
	<script type="text/javascript" src="https://cdn.datatables.net/colreorder/1.5.1/js/dataTables.colReorder.min.js"></script>
	<!--<script src="/signalr/hubs"></script>-->
	<div class="iframeContainer"></div>
	<!-- Start of Zendesk Widget script -->
	<script>
						 ///*<![CDATA[*/
						 //window.zEmbed ||
						 //	function (e, t) {
						 //		var n, o, d, i, s, a = [], r = document.createElement("iframe");
						 //		window.zEmbed = function () { a.push(arguments) }, window.zE = window.zE || window.zEmbed, r.src =
						 //			"javascript:false", r.title = "", r.role = "presentation", (r.frameElement || r).style.cssText =
						 //			"display: none", d = document.getElementsByTagName("script"), d =
						 //			d[d.length - 1], d.parentNode.insertBefore(r, d), i = r.contentWindow, s = i.document;
						 //		try {
						 //			o = s
						 //		} catch (c) {
						 //			n = document.domain, r.src = 'javascript:var d=document.open();d.domain="' + n + '";void(0);', o = s
						 //		}
						 //		o.open()._l = function () {
						 //			var o = this.createElement("script");
						 //			n && (this.domain = n), o.id = "js-iframe-async", o.src = e, this.t = +new Date, this.zendeskHost =
						 //				t, this.zEQueue = a, this.body.appendChild(o)
						 //		}, o.write('<body onload="document._l();">'), o.close()
						 //	}("//assets.zendesk.com/embeddable_framework/main.js", "lepcolourprinters.zendesk.com"); /*]]>*/
	</script>
	<!-- End of Zendesk Widget script -->


	<script>
						 //zE(function () {
						 //	zE.hide();
						 //});
	</script>

</body>
</html>
