<!-- Artwork not required, show list of artworks -->
<div ng-form="artworkForm" ng-init="upload_artwork=false" class="form-horizontal">

    <div ng-show="job.Visibility.previewButton">{{::job.Visibility.previewButtonText}}</div>
    <!--<div id="removeArtButton" >remove this artwork?</div>
    -->
    <div ng-show="job.Visibility.artworkRequiredMsg" class="alert alert-warning">Artwork required</div>
    <div ng-show="job.Visibility.awaitingArtwork" class="alert alert-warning">Awaiting artwork</div>

    <!--<div id="artworkMultiPage"  ></div>-->
    <!--<div id="artworkWarning"></div>	<pre>{{job.Artworks | json}}</pre>-->

    <div ng-repeat="a in job.Artworks">
        <div class="form-group">
            <div class="col-xs-3 control-label">
                {{a.Supplied? "Supplied " : ""}}
                {{a.Ready? "Ready " : ""}}
                {{pos2label(a.Position)}}
            </div>
            <div class="col-xs-7 form-control-static">
                {{a.Supplied || a.Ready }}

                &nbsp;&nbsp;&nbsp;
                <a ng-click="downloadArtwork(job.Id, a.Id, a.Supplied != '')" title="Download artwork">
                    <i class="glyphicon glyphicon-cloud-download"></i> download
                </a>
                &nbsp;&nbsp;&nbsp;
                <a ng-click="jobCommand('RemoveArtworks',j.Id)" title="Remove artwork" ng-show="(job.Visibility.removeArtButtonVisible)">
                    <i class="glyphicon glyphicon-remove-sign"></i> remove
                </a>
            </div>
        </div>
    </div>

    <div ng-hide="!job.Visibility.IsArtworkEditable" class="form-group
          ng-class:{'has-error': ((job.UploadType=='singleart' ||  job.UploadType=='multiart') && artworkConfirmed != true)}">
        <label class="col-xs-3 control-label">
            Add artwork file(s)
        </label>
        <div class="col-xs-7">
            <label class="radio-inline">
                <input type="radio" name="artworktype" ng-model="job.UploadType" value="singleart">Single File
            </label>
            <label class="radio-inline">
                <input type="radio" name="artworktype" ng-model="job.UploadType" value="multiart">Multiple File
            </label>
            <label class="radio-inline">
                <input type="radio" name="artworktype" ng-model="job.UploadType" value="later">Later
            </label>
        </div>
    </div>

    <div ng-show="job.UploadType!='later'">
        <div class="form-group" ng-repeat="p in requiredPositions">
            <label class="col-xs-3 control-label">{{pos2label(p)}}</label>
            <div class="col-xs-7 form-control-static">
                <a ng-click="btn_remove(file)" title="Remove from list to be uploaded">
                    <i class="icon-remove"></i>
                </a>
                <input type="file" class="inputfile"
                       ng-file-model-pdf="files[$index]" name="{{p}}" accept=".pdf" id="{{p}}" />
                <!-- attach-artwork="job" attach-artwork-enabled="job.Visibility.IsArtworkEditable" -->
                <label for="{{p}}">
                    <span><i class="glyphicon   glyphicon-paperclip"></i> Browse for file&hellip;</span>
                </label>

                <!--<progress style="width: 400px;" value="{{files[$index].loaded}}" max="{{files[$index].size}}"></progress> -->
            </div>
        </div>

        <div class="col-sm-12 rpad0" ng-show="files.length > 0 ">
            <div class="well well-sm notice" style="font-size: smaller; padding: 20px;">
                <b style="color: red; font-weight: 800;"> I have </b>
                <ul style="padding: 0px;">
                    <li>checked these files to make sure they are PRESS READY, that means:</li>
                    <ul>
                        <li>There are no low resolution images, RGB or Pantone colours.</li>
                        <li>All artwork is correctly sized with the required amounts of bleed.</li>
                        <li>All fonts have been converted to curves.</li>
                        <li>All transparency effects have been flattened.</li>
                    </ul>

                    <li ng-if="!IsBarneys">Read, understood and accept LEP's <a href="/images/pdfs/LEP_Terms_Conditions.pdf" target="_blank">Terms &amp; Conditions</a> of supply</li>

                    <li ng-if="IsBarneys">Read, understood and accept Barney's <a href="https://barneysprinting.com.au/terms-conditions/" target="_blank">Terms &amp; Conditions</a> of supply</li>
                </ul>

                <div class="" style="height: 30px; padding-left: 13px; color:red; font-weight:700; ">
                    <label class="checkbox">
                        <input type="checkbox" ng-required="(job.UploadType == 'singleart' || job.UploadType == 'multiart')" ng-model="artworkConfirmed" />
                        I confirm the above
                    </label>
                </div>
            </div>
        </div>

        <div class="form-group" ng-hide="!job.Visibility.IsArtworkEditable"
             ng-if="!job.Visibility.saveButton && job.Visibility.IsArtworkEditable">

            <label class="col-xs-3 control-label"> </label>

            <div class="col-xs-6 ">
                <button ng-click="btn_upload()"> upload </button>
            </div>
        </div>
    </div>

    <!--<div>
            <div style="float: left;">
                <div class="btn btn-sm" ng-click="btn_upload()">Save</div>
            </div>
        </div>    <pre>
    {{job.Artworks | json}}
        </pre>
    -->
    
</div>
