using lep.configuration;
using lep.job;
using lep.order;
using lep.job.impl;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using Serilog;
using lep;
using lep.user;

namespace LepCore
{
	public class CachedAccessTo
	{
		IMemoryCache _cache;
		MemoryCacheEntryOptions _cacheEntryOptions;
		MemoryCacheEntryOptions _cacheEntryOptions1h;
		public CachedAccessTo(IMemoryCache cache)
		{
			_cache = cache;
			_cacheEntryOptions = new MemoryCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(5));
			_cacheEntryOptions1h = new MemoryCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromHours(1));
		}


		public string GetOrderInUseBy(int orderId)
		{
			var key = $"orderInUse_{orderId}";
			string inUseBy = string.Empty;
			_cache.TryGetValue(key, out inUseBy);
			return inUseBy;
		}
		public void SetOrderInUseBy(int orderId, string currentUserName)
		{
			var key = $"orderInUse_{orderId}";
			_cache.Set(key, currentUserName, _cacheEntryOptions1h);    // Save data in cache.
		}


		public IList<string> OrderExtraFiles(IOrder order)
		{
			IList<string> extraFiles;
			var key = $"orderExtraFiles_{order.Id}_{order.DateModified}";
			// Look for cache key.
			if (!_cache.TryGetValue(key, out extraFiles))
			{
				extraFiles = LepGlobal.Instance.GetOrderExtraFiles(order);  // Key not in cache, so get data.
				_cache.Set(key, extraFiles, _cacheEntryOptions);    // Save data in cache.
																   //_log.LogInformation($"cached thumbs for job {job.Id}");
			}
			else
			{
				//Log.Information("{CachedAccessTo} reusing {object} for {OrderId}", "CachedAccessTo", "OrderExtraFiles", order.Id);
			}

			return extraFiles;
		}


		public IList<string> CustomerExtraFiles(ICustomerUser customer)
		{
			IList<string> extraFiles;
			var key = $"custExtraFiles_{customer.Id}";
			// Look for cache key.
			if (!_cache.TryGetValue(key, out extraFiles))
			{
				extraFiles = LepGlobal.Instance.GetCustomerExtraFiles(customer);  // Key not in cache, so get data.
				_cache.Set(key, extraFiles, _cacheEntryOptions);    // Save data in cache.
																	//_log.LogInformation($"cached thumbs for job {job.Id}");
			}
			else
			{
				//Log.Information("{CachedAccessTo} reusing {object} for {OrderId}", "CachedAccessTo", "OrderExtraFiles", order.Id);
			}

			return extraFiles;
		}

		public IList<FileInfo> Thumbnails(IJob job)
		{
			IList<FileInfo> thumbs;
			 
			var key = $"thumbs_{job.Id}_{job.DateModified}_{job.Order?.DateModified}";

			if ((DateTime.Now - job.DateModified).TotalMinutes < 5)
			{
				_cache.Remove(key);
			}

			// Look for cache key.
			if (!_cache.TryGetValue(key, out thumbs))
			{
				thumbs = LepGlobal.Instance.GetThumbs(job);          // Key not in cache, so get data.
				_cache.Set(key, thumbs, _cacheEntryOptions); // Save data in cache.
															 //_log.LogInformation($"cached thumbs for job {job.Id}");
			}
			else
			{
				//_log.LogInformation($"reusing thumbs for job {job.Id}");
				//Log.Information("{CachedAccessTo} reusing {object} for {JobId}", "CachedAccessTo", "Thumbs", job.Id);
			}

			return thumbs;
		}


		public JobProgress JobProgress(IJob job)
		{
			JobProgress progress;
			var key = $"progress_{job.Id}_{job.DateModified}_{job.Order?.DateModified}";
			// Look for cache key.
			if (!_cache.TryGetValue(key, out progress))
			{
				progress = job.GetProgress();        // Key not in cache, so get data.
				_cache.Set(key, progress, _cacheEntryOptions); // Save data in cache.
															   //_log.LogInformation($"cached progress for job {job.Id}");
			}
			else
			{
				//Log.Information("{CachedAccessTo} reusing {object} for {JobId}", "CachedAccessTo", "JobProgress", job.Id);
				//_log.LogInformation($"reusing progress for job {job.Id}");
			}

			return progress;
		}
	}
}
