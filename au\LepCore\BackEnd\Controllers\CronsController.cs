namespace LepCore.Controllers
{
	//public class CronRunner
	//{
	//	// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
	//	string absolutePathURL;
	//	System.Timers.Timer _timer;
	//	private HttpClient _httpClient;

	//	public CronRunner(IConfigurationRoot configuration)
	//	{
	//		absolutePathURL = configuration["AbsolutePathURL"];

	//		ServicePointManager.SecurityProtocol =
	//				SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
	//		ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
	//		var applicationJson = "application/json";
	//		_httpClient = new HttpClient();
	//		_httpClient.BaseAddress = new Uri(absolutePathURL);
	//		_httpClient.DefaultRequestHeaders.ConnectionClose = true;
	//		_httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(applicationJson));
	//		_httpClient.Timeout = Timeout.InfiniteTimeSpan;
	//	}

	//	public void RunPeriodic()
	//	{
	//		_timer = new System.Timers.Timer(1000 * 180);
	//		_timer.Elapsed += (sender, r) => Run();
	//		_timer.Start();
	//	}

	//	public void Run()
	//	{
	//		try
	//		{
	//			var scanResult = _httpClient.PostAsync("/api/Crons/run", new StringContent("")).Result;
	//			var msg = scanResult.Content.ReadAsStringAsync().Result;

	//		}
	//		catch (Exception ex)
	//		{
	//			var m = ex.Message;
	//			Log.Error(ex.Message, ex);
	//		}

	//	}

	//}

	//[Route("api/[controller]")]
	//[AllowAnonymous]
	//public class CronsController : Controller
	//{
	//	// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
	//	private static Mutex mutex = new Mutex();

	//	public CronsController()
	//	{
	//	}

	//	void RunFunc(Action a, String message)
	//	{
	//		Stopwatch sw = Stopwatch.StartNew();

	//		try
	//		{
	//			a();
	//		}
	//		catch (Exception ex)
	//		{
	//			Log.Error(ex.Message, ex);
	//		}

	//		sw.Stop();
	//		log.InfoFormat("CRON Job {0,20} finished in: {1}ms", message, sw.Elapsed.TotalMilliseconds);

	//	}

	//	[HttpGet("run")]
	//	[HttpPost("run")]
	//	public IActionResult Run(
	//			[FromServices] lep.run.impl.RunEngine runEngine,
	//			[FromServices] lep.jobmonitor.impl.JobBoardDTOHelper jobBoardDTOHelper,
	//			[FromServices] lep.job.impl.JobUpdateEngine jobUpdateEngine,
	//			[FromServices] lep.despatch.impl.PrintEngine printEngine,
	//			[FromServices] lep.order.IOrderApplication orderApplication,
	//			[FromServices] lep.ConsignmentNotesMailerApplication consignmentNotesMailerApplication,
	//			[FromServices] lep.run.IRunApplication runApplication//,
	//			//[FromServices] IHubContext<JobBoardHub> jbHubContext
	//		)
	//	{
	//		try
	//		{
	//			mutex.WaitOne();   // Wait until it is safe to enter.
	//			Log.Information("CRON Jobs end point hit");
	//			Stopwatch sw = Stopwatch.StartNew();
	//			RunFunc(jobUpdateEngine.CronTask, "jobUpdateEngine ");
	//			RunFunc(runEngine.CronTask, "AutoJobRunAllocation");
	//			RunFunc(printEngine.ReceivePrintQueue, "Print Engine");
	//			RunFunc(orderApplication.CronTask_ArchiveOrders, "ArchiveOrders");
	//			RunFunc(jobBoardDTOHelper.CreateJobBoard, "CreateJobBoard");
	//			RunFunc(consignmentNotesMailerApplication.BringInSupplyMasterConnnotes, "Import Connotes from Supply Master");
	//			RunFunc(consignmentNotesMailerApplication.CronTask, "Send consignment notes");
	//		}
	//		finally
	//		{
	//			mutex.ReleaseMutex();    // Release the Mutex.
	//		}
	//		return Ok();
	//	}

	//}
}
