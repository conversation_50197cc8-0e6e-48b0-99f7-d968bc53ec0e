﻿<div class="form-horizontal" ng-form="JobDetailsForm">
    <div class="jobDetailsForm" ng-rxx id="JobDetailsForm">
        <!-- TEMPLATE job.Template.Id -->

        <fieldset ng-disabled="!(job.Visibility.optionPnlEnabled)">
            <!-- Category, Product, Delivery options -->
            <div template-select job-option-id="job.Template.Id" job-category="job.Category"
                remove-other-products="removeOtherProducts" dcats="dcats" is-white-label="job.IsWhiteLabel"></div>

            <!-- Envelope Type  -->
            <div class="form-group ng-class:{'has-error' : (!job.EnvelopeType)}" ng-show="vis.envelopeType">
                <label class="col-xs-3 control-label" for="EnvelopeType">Type</label>
                <div class="col-xs-7">

                    <select id="EnvelopeType" name="EnvelopeType" class="form-control input" ng-hide=""
                        ng-model="job.EnvelopeType" ng-options="v as v for (k,v) in $root.enums.ValueDesc.EnvelopeType"
                        ng-required="vis.envelopeType">

                        <option value="">Select...</option>
                    </select>
                </div>
            </div>

            <!--Trim Size-->
            <div class="form-group">
                <label class="col-xs-3 control-label" for="sizeLst">Trim Size</label>
                <div class="col-xs-7">
                    <select id="sizeLst" name="Size" class="form-control input"
                        ng-options="s as s.Name for s in sizeOptions track by s.Id"
                        ng-model="job.FinishedSize.PaperSize" lep-required-id="true">
                        <option value="">select...</option>
                    </select>
                </div>
            </div>

            <!-- Orientation -->
            <div class="form-group ng-class:{'has-error' : (job.Rotation==undefined)}" ng-show="vis.rotation">
                <label class="col-xs-3 control-label">Orientation</label>
                <div class="col-xs-7 ">
                    <span class="radio-inline" ng-show="vis.portrait" style="margin-right:10px;">
                        <label>
                            <input type="radio" name="Orientation" ng-model="job.Rotation" ng-value="0 * 1"
                                ng-required="true">{{labeldata["portrait"]}}
                        </label>
                    </span>
                    <span class="radio-inline" ng-show="vis.landscape" style="margin-left:0;">
                        <label>
                            <input type="radio" name="Orientation" ng-model="job.Rotation" ng-value="1 * 1"
                                ng-required="true">{{labeldata["landscape"]}}
                        </label>
                    </span>
                </div>
            </div>

            <!-- Width and height -->
            <div class="form-group" ng-show="job.FinishedSize.PaperSize &&  job.FinishedSize.PaperSize.Id">
                <div class="col-xs-3 col-md-3  control-label"></div>
                <div class="col-xs-7 col-md-7">
                    <div class="input-group">
                        <span class="input-group-addon">{{labeldata["width"]}} </span>
                        <input class="form-control" type="number" ng-model="job.FinishedSize.Width"
                            ng-disabled="job.FinishedSize.PaperSize.Name != 'Custom'" value-needs-to-pass="gt30"
                            maxlength="5" tile="mm" id="width" numbers-only name="FinishedSizeWidth" />
                        <span class="input-group-addon">{{labeldata["height"]}} </span>
                        <input class="form-control" type="number" ng-model="job.FinishedSize.Height"
                            ng-disabled="job.FinishedSize.PaperSize.Name != 'Custom'" value-needs-to-pass="gt30"
                            maxlength="5" title="mm" id="height" numbers-only name="FinishedSizeHeight" />
                    </div>
                </div>
            </div>

            <!--<div class="form-group " ng-show="sizeMsg">
                <div class="col-xs-3 control-label"></div>
                <div class="col-xs-7 form-control-static">
                    {{sizeMsg}}
                </div>
            </div>
            -->

            <!-- Pages -->
            <div class="form-group " ng-show="vis.page">
                <label class="col-xs-3 control-label" for="">{{labeldata.pages}}
                <span style="font-size: x-small;">{{labeldata.pages1}}</span>
                </label>
                <div class="col-xs-7">

                    <div class="input-group">


                        <select class="form-control input" id="numberOfPages" name="numberOfPages" style="width:70% "
                            ng-options="q*1 as q for q in numberOfPages[job.Template.Id]" ng-model="job.Pages"
                            lep-required-id="vis.page">
                            <option value="">select...</option>
                        </select>
                        

                        <input ng-if="$root.globals.IsStaff" class="form-control" type="number" ng-model="job.Pages"
                            numbers-only name="numberOfPages2" style="width:25% " />

                    </div>
                </div>
            </div>

            <!-- Stock job.Stock.Id -->
            <div class="form-group " ng-if="vis.stock">
                <label class="col-xs-3 control-label" for="Stock">{{labeldata["stock"]}} </label>
                <div class="col-xs-7">

                    <!-- remove in future $root.globals.IsStaff == true &&  -->
                    <span ng-if="(!priceCalc)
                    && (job.Stock.Id == 18  || job.Stock.Id == 13  || job.Stock.Id == 19
                        || (job.Stock.Id == 91 && (job.Template.Id == 9 || job.Template.Id == 21))
                    ) ">
                        {{job.Stock.Name}}
                    </span>

                    <!-- (so.GSM + ' GSM')  -->
                    <select id="Stock" name="Stock" class="form-control input"
                        ng-options="so as so.Name for so in stockOptions track by so.Id"
                        ng-model="job.Stock" lep-required-id="true">
                        <option value="">select...</option>
                    </select>
                    <!-- -->

                    <a ng-if="$root.globals.IsStaff" title="Override Stock" ng-click="overrideStock('job.Stock')"
                        style="position: relative;top: -25px;left: 102%; color:gray;">
                        <i class="glyphicon glyphicon-wrench"></i>
                    </a>

                    <span class="help-block " ng-if="[1,2,26,27].indexOf(job.Template.Id) > -1">
                        <!--<a href="/images/pdfs/2019 Bus Card Cello Options.pdf" target="_blank" class=" pull-right">
                        Business Card Cello Options
                    </a>-->

                        <a href="/images/pdfs/Bus%20Card%20Cello%20Options.pdf" target="_blank" class=" pull-right">
                            Business Card Cello Options
                        </a>
                    </span>
                </div>
            </div>


            <!-- Stock Override -->
            <div class="form-group" ng-if="vis.stock && job.StockOverride.Name">
                <label class="col-xs-3 control-label" for="Stock">Override</label>
                <div class="col-xs-7 form-control-static bold">
                    {{job.StockOverride.Name}}
                </div>
                <a ng-if="$root.globals.IsStaff && job.StockOverride.Id" ng-click="job.StockOverride = null"
                    title="Undo Stock Override" style="position: relative; top:9px; left:-9px;color:gray;">
                    <i class="glyphicon glyphicon-remove"></i>
                </a>
            </div>


            <!-- Cover Stock -->
            <div class="form-group " ng-show="vis.coverstock">
                <label class="col-xs-3 control-label" for="coverStockLst">Cover Stock</label>
                <div class="col-xs-7">
                    <select id="coverStockLst" name="coverStockLst" class="form-control input"
                            ng-options="s as s.Name for s in lookups.stocksForCover  track by s.Id"
                            ng-model="job.StockForCover"
                            lep-required-id="vis.coverstock">
                        <option value="">select...</option>
                    </select>

                    <a ng-if="$root.globals.IsStaff" title="Override Cover Stock"
                    ng-click="overrideStock('job.StockForCover')"
                    style="position: relative;top: -25px;left: 102%; color:gray;">
                        <i class="glyphicon glyphicon-wrench"></i>
                    </a>
                </div>
            </div>
     
            <!-- For Wiro this is inner front cover stock -->
            <div class="form-group row" ng-show="vis.wiro">
                <label class="col-xs-3 control-label" for="coverStockLstBack">Inner Front Cover</label>
                <div class="col-xs-7">
                    <div class="input-group">
                        <span class="input-group-addon x">Stock</span>
                        <select id="coverStockLstFront" name="coverStockLstFront" class="form-control input"
                            ng-options="s as s.Name    for s in lookups.stocksForCover  track by s.Id"
                            ng-model="job.WiroInfo.InnerFrontStockForCover" >
                            <option value="" selected>None</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="form-group row" ng-show="vis.wiro && job.WiroInfo.InnerFrontStockForCover">
                <label class="col-xs-3 control-label" for="coverStockLstBack"></label>
                <div class="col-xs-7">
                    <div class="input-group">
                        <span class="input-group-addon x">Cello</span>
                        <select id="InnerFrontCello" name="InnerFrontCello" class="form-control input"
                            ng-options="k as k for  k in lookups.celloOptionsW"
                            ng-model="job.WiroInfo.InnerFrontCello"
                            >
                            <option value="" selected>None</option>
                        </select>
                        <!-- ng-change="changeCello(selectedCello)" va3lue-n3eeds-to-pass="validCelloFn"
                            lep-required-id="vis.cello"  -->
                        <!-- <span ng-if="$root.globals.IsStaff == true"><br />
                            {{$root.enums.ValueDesc.JobCelloglazeOptions[job.FrontCelloglaze]}} -
                            {{$root.enums.ValueDesc.JobCelloglazeOptions[job.BackCelloglaze]}}
                        </span> -->
                    </div>
                </div>
            </div>

            <!-- Wiro Only Cover Stock Back -->
            <!-- For Wiro this is inner Back cover stock -->
            <div class="form-group row" ng-show="vis.wiro">
                <label class="col-xs-3 control-label" for="coverStockLstBack">Inner Back Cover</label>
                <div class="col-xs-7">
                    <div class="input-group">
                        <span class="input-group-addon x">Stock</span>
                        <select id="coverStockLstBack" name="coverStockLstBack" class="form-control input"
                            ng-options="s as s.Name    for s in lookups.stocksForCover  track by s.Id"
                            ng-model="job.WiroInfo.InnerBackStockForCover"  >
                            <option value="" selected>None</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group row" ng-show="vis.wiro && job.WiroInfo.InnerBackStockForCover">
                <label class="col-xs-3 control-label" for="coverStockLstBack"></label>
                <div class="col-xs-7">
                    <div class="input-group">
                        <span class="input-group-addon x">Cello</span>
                        <select id="InnerBackCello" name="InnerBackCello" class="form-control input"
                        ng-options="k as k for  k in lookups.celloOptionsW"
                             ng-model="job.WiroInfo.InnerBackCello"
                             >
                            <option value="" selected>None</option>
                        </select>
                        <!--  ng-change="changeCello(selectedCello)" va3lue-n3eeds-to-pass="validCelloFn"
                            lep-required-id="vis.cello" -->
                        <!-- <span ng-if="$root.globals.IsStaff == true"><br />
                            {{$root.enums.ValueDesc.JobCelloglazeOptions[job.FrontCelloglaze]}} -
                            {{$root.enums.ValueDesc.JobCelloglazeOptions[job.BackCelloglaze]}}
                        </span> -->


                    </div>
                </div>
            </div>


            <!-- StockForCover Override  -->
            <div class="form-group" ng-if="vis.stock && job.StockForCoverOverride.Name">
                <label class="col-xs-3 control-label" for="Stock">Override</label>
                <div class="col-xs-7 form-control-static bold">
                    {{job.StockForCoverOverride.Name}}
                </div>
                <a ng-if="$root.globals.IsStaff && job.StockForCoverOverride.Id"
                    ng-click="job.StockForCoverOverride = null" title="Undo Cover Stock Override"
                    style="position: relative; top:6px; left:-9px;color:gray;">
                    <i class="glyphicon glyphicon-remove"></i>
                </a>
            </div>


            <!-- Outer Front -->
            <div class="form-group" ng-if="vis.wiro">
                <label class="col-xs-3 control-label" for="outerFront">Outer Front Cover</label>
                <div class="col-xs-7">
                    <select id="outerFront" name="outerFront" class="form-control input"
                        ng-model="job.WiroInfo.OuterFront" >
                        <option value="" selected>None</option>
                        <option>Clear PVC</option>
                        <option>Matt PVC</option>
                    </select>

                    <!-- <span class="radio-inline" ng-repeat="option in lookups.outerFrontOptions">
                        <label>
                            <input type="radio" 
                            ng-model="job.WiroInfo.OuterFront" ng-required="true"
                            ng-value="option" ng-checked="$first" name="outerFront">{{::option}}
                        </label>
                    </span> -->
                </div>
            </div>

            <!-- Outer Back -->
            <div class="form-group" ng-if="vis.wiro">
                <label class="col-xs-3 control-label" for="outerBack">Outer Back Cover</label>
                <div class="col-xs-7">
                    <select id="outerBack" name="outerBack" class="form-control input"
                        ng-model="job.WiroInfo.OuterBack">
                        <option value="" selected>None</option>
                        <option>Clear PVC</option>
                        <option>Black Leather grain</option>
                        <option>Blue Leather grain</option>
                    </select>
                        <!-- <span class="radio-inline" ng-repeat="option in lookups.outerBackOptions">
                            <label>
                                <input type="radio" ng-value="option" 
                                ng-model="job.WiroInfo.OuterBack" ng-required="true"
                                ng-checked="$first" name="outerBack">{{::option}}
                            </label>
                        </span> -->
                </div>
            </div>

            <!-- Wire Color -->
            <div class="form-group ng-class:{'has-error' : !job.WiroInfo.WireColor}"
            ng-if="vis.wiro">
                <label class="col-xs-3 control-label" for="wireColor">Coil Wire Color</label>
                <div class="col-xs-7">
                    <!-- <select id="wireColor" name="wireColor" class="form-control input"
                        ng-model="job.WiroInfo.WireColor">
                        <option value="Black">Black</option>
                        <option value="White">White</option>
                    </select> -->

                        <span class="radio-inline" ng-repeat="option in lookups.wireColorOptions">
                            <label>
                                <input ng-model="job.WiroInfo.WireColor"   ng-required="!job.WiroInfo.WireColor" 
                                 type="radio" ng-value="option" name="wireColor">{{::option}}
                            </label>
                        </span>
                </div>
            </div>
        </fieldset>

        <fieldset ng-disabled="!job.ReOrderSourceJobId  && !(job.Visibility.optionPnlEnabled)  ">
            
      
            


            <!-- Quantity   -->
            <div class="form-group ng-class:{'has-error' : (!job.Quantity || job.Quantity==0)}" ng-show="job.Stock.Id">
                <label class="col-xs-3 control-label" for="quantity" title="{{slider.options.stepsArray}}">
                    {{labeldata["quantity"]}} <!--= {{job.Quantity}}-->
                </label>

                <div class="col-xs-7">

                    <!-- Quantity slider -->
                    <div ng-show="{{(!vis.qtyList && $root.globals.IsStaff) || showQuantitySlider}}">
                        <div>

                            <rzslider rz-slider-model="jobQuantity" rz-slider-options="slider.options"
                                rz-slider-disabled="!job.Visibility.optionPnlEnabled">
                            </rzslider>
                        </div>
                    </div>

                    <!--<a ng-click="setjobQty(3000)"> 3000</a>
                <a ng-click="setjobQty(5000)"> 5000</a>
                <a ng-click="setjobQty(9000)"> 9000</a>
                ng-model-options="{ allowInvalid: false, updateOn: 'keyup blur' }"
                -->
                    <!-- Quantity  input box   debounce: { 'keyup': 10000, 'blur': 0 }-->
                    <div ng-show="vis.qty">
                        <input id="quantity" name="quantity" type="number" class="form-control input"
                            ng-model="job.Quantity" ng-keypress="filterOnlyDigits($event)"
                            ng-model-options="{allowInvalid: false, updateOn: 'change blur'}" update-on-enter
                            ng-required="true" maxlength="6" ng-min="1" style="-webkit-appearance: none; margin: 0;" />

                        <span class="help-block"
                            ng-show="{{minValue && job.Status == 0  && ( $root.globals.IsStaff ||showQuantitySlider )}}">
                            Minimum order of {{minValue}} when {{job.PrintType | printtype}} printing.
                            <!--<br />--> Maximum order of {{maxQty}}.
                        </span>
                    </div>

                    <!-- qty List, only for certain types -->
                    <div class="radio" ng-show="vis.qtyList">
                        <span class="radio-inline" ng-repeat="q in qtylistValues[job.Template.Id]">
                            <label>
                                <input ng-model="job.Quantity" ng-value="q*1"
                                    ng-checked="(job.Quantity == (q*1) || qtylistValues[job.Template.Id].length == 1 )"
                                    name="qtyList" type="radio">{{::q}}
                            </label>
                        </span>
                    </div>
                </div>

                <div class="form-group  " ng-show="vis.NCRNumbered">
                    <label class="col-xs-3 control-label">NCR Numbered</label>
                    <div class="col-xs-7 ">
                        <span class="radio-inline" style="margin-right:10px;">
                            <label>
                                <input type="radio" name="NCRNumbered" ng-model="job.NCRNumbered" ng-value="true"
                                    ng-required="true">Yes</label>
                        </span>
                        <span class="radio-inline" ng-show="vis.landscape" style="margin-left:0;">
                            <label>
                                <input type="radio" name="NCRNumbered" ng-model="job.NCRNumbered" ng-value="false"
                                    ng-required="true">No</label>
                        </span>
                    </div>
                </div>

                <div class="form-group" ng-show="vis.NCRNumbered && job.NCRNumbered">
                    <div>
                        <label class="col-xs-3 control-label" for="NCRStartingNumber">NCR Starting number</label>
                        <div class="col-xs-7">
                            <input id="NCRStartingNumber" name="NCRStartingNumber" type="text"
                                class="form-control input" ng-model="job.NCRStartingNumber"
                                ng-required="!priceCalc && job.NCRNumbered" maxlength="80" />

                        </div>

                    </div>

                    <div style="padding-left: 12px;  color: #d9534f; clear: both" class="col-xs-offset-3">Please
                        indicate position for numbering on your artwork in red. </div>

                </div>
            </div>

        </fieldset>

        <fieldset ng-disabled="!(job.Visibility.optionPnlEnabled)">

            <!-- PrintType -->
            <div ng-if="$root.globals.IsStaff == true && vis.printType">
                <div class="form-group ng-class:{'has-error' : (job.PrintType=='B' || !job.PrintType) }">
                    <label class="col-xs-3 control-label">Print type </label>
                    <div class="col-xs-7  radio">

                        <label class=""
                            ng-show="printTypesAvaiable=='B' || printTypesAvaiable=='O' || job.PrintType == 'O'">
                            <input type="radio" name="optradioPrintType" ng-model="job.PrintType" value="O"
                                ng-required="printTypesAvaiable && printTypesAvaiable != 'N'"> Offset&nbsp;&nbsp;
                        </label>

                        <label class=""
                            ng-show="printTypesAvaiable=='B' || printTypesAvaiable=='D' || job.PrintType == 'D'">
                            <input type="radio" name="optradioPrintType" ng-model="job.PrintType" value="D"
                                ng-required="printTypesAvaiable && printTypesAvaiable != 'N'"> Digital&nbsp;&nbsp;
                        </label>

                        <label class="" ng-show="printTypesAvaiable=='W'">
                            <input type="radio" name="optradioPrintType" ng-model="job.PrintType" value="W"
                                ng-required="printTypesAvaiable && printTypesAvaiable != 'N'"> Wide Format&nbsp;&nbsp;
                        </label>

                        <label class="" ng-show="printTypesAvaiable=='N'">
                            <input type="radio" name="optradioPrintType" ng-model="job.PrintType" value="N"
                                ng-required="printTypesAvaiable && printTypesAvaiable == 'N'"> N/A&nbsp;&nbsp;
                        </label>
                        <div class="help-block  pull-right" ng-if="!priceCalc ">
                            <a ng-click="forcePrintType('O')">Force Offset</a><br />
                            <a ng-click="forcePrintType('D')">Force Digital</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Round Corners -->
            <div class="form-group " ng-show="vis.round || job.RoundOption != 0">
                <label class="col-xs-3 control-label" for="roundOptionLst">
                    Round Cornering / Custom Die Cutting
                </label>
                <div class="col-xs-7">
                    <select class="form-control input" name="roundOptionLst" id="roundOptionLst"
                        ng-options="k*1 as $root.enums.ValueDesc.RoundOption[k.toString()] for k in roundListValues"
                        ng-model="job.RoundOption" lep-required-id="vis.round || job.RoundOption != 0"
                        allow-zero="true">
                        <option value="{}">select...</option>
                    </select>
                </div>
            </div>

            <!--Round detail-->
            <div class="form-group " ng-show="vis.roundDetail">
                <label class="col-xs-3 control-label" for="roundDetailOptionLst">Round detail</label>
                <div class="col-xs-7">
                    <select class="form-control input" name="roundDetailOptionLst" id="roundDetailOptionLst"
                        ng-options="k*1 as $root.enums.ValueDesc.RoundDetailOption[k.toString()] for k in roundAndRoundDetails[job.RoundOption]"
                        ng-model="job.RoundDetailOption" lep-required-id="vis.roundDetail">
                        <option value="">select...</option>
                    </select>
                </div>
            </div>

            <!-- Custom die cut -->
            <div class="form-group ng-class:{'has-error' : (!job.CustomDieCut || job.CustomDieCut=='')}"
                ng-show="vis.round && job.RoundOption == 3">
                <label class="col-xs-3 control-label" for="CustomDieCut">Custom DieCut instructions</label>
                <div class="col-xs-7">
                    <textarea id="CustomDieCut" name="CustomDieCut" class="form-control input"
                        ng-model="job.CustomDieCut" rows="5" placeholder="enter desired custom DieCut instructions"
                        ng-required="vis.round && job.RoundOption == 3"></textarea>
                </div>
            </div>

            <!-- Rounding corner widget -->
            <div class="form-group" ng-show="vis.roundDetail">
                <label class="col-xs-3 control-label"></label>
                <div class="col-xs-7">
                    <div rounding-box job="job"></div>
                </div>
            </div>

            <!-- Hole Drilling -->
            <div class="form-group " ng-show="vis.hole">
                <label class="col-xs-3 control-label" for="HoleDrilling">Hole Drilling </label>
                <div class="col-xs-7">
                    <select id="HoleDrilling" name="HoleDrilling" class="form-control input"
                        ng-options="k*1 as v for (k,v) in holeListValues" ng-model="job.HoleDrilling"
                        lep-required-id="vis.hole" allow-zero="true">
                        <option value="{}">select...</option>
                    </select>
                </div>
            </div>

            <!-- Number of holes -->
            <div class="form-group ng-class:{'has-error' : (job.NumberOfHoles==null)}"
                ng-show="vis.hole && job.HoleDrilling">
                <label class="col-xs-3 control-label" for="HoleDrilling">Number of holes</label>
                <div class="col-xs-7">

                    <select id="NumberOfHoles" name="NumberOfHoles" class="form-control input" ng-hide=""
                        ng-model="job.NumberOfHoles" ng-required="job.HoleDrilling && job.HoleDrilling>0">

                        <option value="">Select...</option>
                        <option ng-value="1*1">1 hole</option>
                        <option ng-value="2*1">2 holes</option>
                        <option ng-value="3*1">3 holes</option>
                        <option ng-value="4*1">4 holes</option>
                    </select>
                </div>
            </div>

            <!-- FrontColour  radio  -->
            <div class="form-group  ng-class:{'has-error' : (job.FrontPrinting==null)}"
                ng-if="lookups.stock.FrontPrintOptions.length">
                <label class="col-xs-3 control-label">{{labeldata["frontprint"]}}</label>
                <div class="col-xs-8">
                    <span class="radio-inline" ng-repeat="s0 in (lookups.stock.FrontPrintOptions || [])">
                        <label>
                            <input ng-model="job.FrontPrinting" ng-value="s0*1" name="frontPrintOption1" type="radio"
                                ng-required="true">
                            {{$root.enums.ValueDesc.JobPrintOptions[s0]}}
                        </label>
                    </span>
                </div>
            </div>

            <!-- BackColour  radio"-->
            <div class="form-group   ng-class:{'has-error' : (job.BackPrinting==null)}"
                ng-if="lookups.stock.BackPrintOptions.length">
                <label class="col-xs-3 control-label">{{labeldata["backprint"]}}</label>
                <div class="col-xs-8">
                    <span class="radio-inline" ng-repeat="s1 in (lookups.stock.BackPrintOptions || [])">
                        <label>
                            <input ng-model="job.BackPrinting" ng-value="s1*1" name="backPrintOption1" type="radio"
                                ng-required="true">
                            {{$root.enums.ValueDesc.JobPrintOptions[s1]}}
                        </label>
                    </span>
                </div>
            </div>







            <!-- ************************** LORD 1189 ************************* -->
            <div ng-if="job.Category == 'NCR Books'">
                <!-- Paper Colors -->
                <div class="form-group">
                    <label class="col-xs-3 control-label">Paper Colors</label>
                    <div class="col-xs-7 ">
                        <span ng-repeat="x in [].constructor( (job.Template.Id-49)) track by $index" class="ot">
                            Sheet {{ $index+1 }}
                            <select ng-options="o as o for o in lookups.NCR.paperColors[$index]" id="c{{$index+1}}"
                                class="form-control-sm" ng-model="job.NCRInfo.Sheets[$index+1].PaperColor"
                                ng-required="true"
                                ng-init="job.NCRInfo.Sheets[$index+1].PaperColor = job.NCRInfo.Sheets[$index+1].PaperColor || (lookups.NCR.paperColors[$index] && lookups.NCR.paperColors[$index][0])"
                                name="PaperColor{{ $index+1 }}">
                            </select>
                        </span>
                    </div>
                </div>

                <!-- Perforated -->
                <div class="form-group">
                    <label class="col-xs-3 control-label">Perforated</label>
                    <div class="col-xs-7 ">
                        <span ng-repeat="x in [].constructor( (job.Template.Id-49)) track by $index" class="ot">
                            <label>Sheet {{ $index+1 }}
                                <input type="checkbox" ng-model="job.NCRInfo.Sheets[$index+1].Perforated" />
                            </label>
                        </span>
                    </div>
                </div>

                <!-- Printed on Back -->
                <div class="form-group">
                    <label class="col-xs-3 control-label">Printed on Back</label>
                    <div class="col-xs-7 ">
                        <span ng-repeat="x in [].constructor( (job.Template.Id-49)) track by $index" class="ot">
                            <label>Sheet {{ $index+1 }}
                                <input type="checkbox" ng-model="job.NCRInfo.Sheets[$index+1].PrintedOnBack" />
                            </label>
                        </span>
                    </div>
                </div>

                <!-- Cover Stock -->
                <div class="form-group ng-class:{'has-error' : (!job.NCRInfo.Cover)}">
                    <label class="col-xs-3 control-label">Cover</label>
                    <div class="col-xs-7 ">
                        <select id="coverLst" name="coverLst" class="form-control input"
                            ng-options="s as s for s in lookups.NCR.stocksForCover" ng-model="job.NCRInfo.Cover"
                            ng-required="true">
                            <option value="">select...</option>
                        </select>
                    </div>
                </div>

                <!-- Binding Tape -->
                <div class="form-group ng-class:{'has-error' : (!job.NCRInfo.BindingTape)}">
                    <label class="col-xs-3 control-label">Binding Tape</label>
                    <div class="col-xs-7 ">
                        <select id="bindingTapeLst" name="bindingTapeLst" class="form-control input"
                            ng-options="s as s for s in lookups.NCR.bindingTapes" ng-model="job.NCRInfo.BindingTape"
                            ng-required="true">
                            <option value="">select...</option>
                        </select>

                    </div>
                </div>

                <!-- Front Ink Color -->
                <!--
                <div class="form-group ng-class:{'has-error' : (!job.NCRInfo.FrontInkColor)}" ng-show="job.FrontPrinting==5">
                    <label class="col-xs-3 control-label">Front Ink Color</label>
                    <div class="col-xs-7 ">
                        <input id="FrontInkColor" name="FrontInkColor" type="text" class="form-control input"
                        ng-model="job.NCRInfo.FrontInkColor" />
                    </div>
                </div>
				-->
                <!-- Back Ink Color -->
                <!--
                <div class="form-group ng-class:{'has-error' : (!job.NCRInfo.BackInkColor)}"  ng-show="job.BackPrinting==5">
                    <label class="col-xs-3 control-label">Back Ink Color</label>
                    <div class="col-xs-7 ">
                        <input id="BackInkColor" name="BackInkColor" type="text" class="form-control input"
                        ng-model="job.NCRInfo.BackInkColor" />
                    </div>
                </div>
                -->

            </div>


            <!-- FinishedBy Options -->
            <div class="form-group   ng-class:{'has-error' : (job.PadDirection==null)}" ng-show="vis.finishedBy">
                <label class="col-xs-3 control-label" for="finishedBy">Finished by</label>
                <div class="col-xs-7">
                    <select id="finishedBy" name="finishedBy" class="form-control input"
                        ng-options="k*1 as v for (k,v) in $root.enums.ValueDesc.PadDirection"
                        ng-model="job.PadDirection" lep-required-id="{{vis.finishedBy}}" allow-zero="{{false}}">
                        <option value="">select...</option>
                    </select>
                </div>
            </div>

            <!-- Folding FoldedSize  && lookups.foldOptions.length > 1 && vis.fold-->
            <div class="form-group " ng-show="(lookups.stock.FoldOptions.length > 0) && vis.fold">
                <label class="col-xs-3 control-label" for="FoldedSize">{{labeldata["fold"] }}</label>
                <div class="col-xs-7">
                    <select id="FoldedSize" name="FoldedSize" class="form-control input"
                        ng-options="f  as f.Name for f in lookups.stock.FoldOptions track by f.Id"
                        ng-model="job.FoldedSize.PaperSize"
                        lep-required-id="(lookups.stock.FoldOptions.length > 0) && vis.fold" allow-zero="{{true}}">
                        <!--<option value="">select...</option>-->
                        <option value="">{{labeldata["nofold"] }}</option>
                    </select>
                </div>
            </div>

            <!-- Width and height -->
            <div class="form-group " ng-show="job.FoldedSize.PaperSize && job.FoldedSize.PaperSize.Id != 0">
                <div class="col-xs-3 control-label"></div>
                <div class="col-xs-7">
                    <div class="input-group">
                        <span class="input-group-addon">Width</span>
                        <input class="form-control" ng-model="job.FoldedSize.Width"
                            ng-disabled="job.FoldedSize.PaperSize.Name != 'Custom'" value-needs-to-pass="gt30JobFolded"
                            name="foldedSizeW" maxlength="5" />
                        <span class="input-group-addon">Height</span>
                        <input class="form-control" ng-model="job.FoldedSize.Height"
                            ng-disabled="job.FoldedSize.PaperSize.Name != 'Custom'" value-needs-to-pass="gt30JobFolded"
                            name="foldedSizeH" maxlength="5" />
                    </div>
                </div>
            </div>

            <!-- Bound on -->
            <div id="boundEdgeDiv0"
                class="form-group ng-class:{'has-error' : (job.BoundEdge==undefined || job.BoundEdge== 0 )}"
                ng-show="vis.boundEdge">
                <label class="col-xs-3 control-label">Bound Edge </label>
                <div class="col-xs-3 ">

                    <span class="radio-inline" ng-show="vis.boundEdgeSide" style="margin-right:10px;">
                        <label>
                            <input type="radio" name="BoundOn" ng-model="job.BoundEdge" ng-value="1 * 1"
                                ng-required="vis.boundEdge">Side
                        </label>
                    </span>
                    <span class="radio-inline" ng-show="vis.boundEdgeTop" style="margin-left:0;">
                        <label>
                            <input type="radio" name="BoundOn" ng-model="job.BoundEdge" ng-value="1 * 2"
                                ng-required="vis.boundEdge">Top
                        </label>
                    </span>
                </div>
         
                
                <div ng-show="job.oneSideBindingMsg" class="col-xs-6 help-block error-redish">
                    {{job.oneSideBindingMsg}}
                </div>
                

            </div>

            <!-- Spine width-->
            <div class="form-group "
                ng-show="vis.binding && job.spineWidth  && ((!job.BindingOption) || (job.BindingOption && job.BindingOption.Id != 1))">
                <label class="col-xs-3 control-label" for="binding">Spine width</label>
                <div class="col-xs-7 form-control-static">
                    {{job.spineWidth}}mm
                </div>
            </div>

            <!-- Binding Options -->
            <div class="form-group " ng-show="vis.binding">
                <label class="col-xs-3 control-label" for="binding">Binding</label>
                <div class="col-xs-7">
                    <select id="binding" name="binding" class="form-control input"
                        ng-options="s as s.Name for s in lookups.bindingOptions track by s.Id"
                        ng-model="job.BindingOption" lep-required-id="vis.binding">
                        <option value="">select...</option>
                    </select>
                </div>
            </div>

            <!-- Artwork requirements, based on Magazine Binding -->
            <div class="form-group " ng-show="[3,4,5].indexOf(job.BindingOption.Id)>-1">
                <label class="col-xs-3 control-label" for="binding">Artwork requirements</label>
                <div class="col-xs-7 form-control-static">
                    <a style="color: #d9534f;" href="/images/pdfs/A4Magazine%20-%20Burst%20Bound.pdf" target="_blank">
                        Refer attached guide for assistance
                    </a>
                </div>
            </div>

            <!-- Cello options  &&  lookups.stock.CelloOptions2.length > 0 -->
            <div class="form-group
             ng-class:{'HasSpotFoil' : ($root.globals.IsStaff && (job.FrontCelloglaze == 5 || job.FrontCelloglaze == 6 || job.FrontCelloglaze == 7 || job.FrontCelloglaze == 10))  }"
                ng-show="vis.cello">
                <label class="col-xs-3 control-label" for="Celloglaze">
                    {{labeldata["cello"]}}
                </label>
                <div class="col-xs-7">
                    <select id="Celloglaze" name="Celloglaze" class="form-control input"
                        ng-options="k.Key as k.Value for  k in lookups.celloOptions" ng-model="selectedCello"
                        ng-change="changeCello(selectedCello)" va3lue-n3eeds-to-pass="validCelloFn"
                        lep-required-id="vis.cello" allow-zero="false">
                        <option value="">select...</option>
                    </select>
                    <a ng-if="$root.globals.IsStaff" title="Override Cello" ng-click="overrideCello('selectedCello')"
                        style="position: relative;top: -25px;left: 102%; color:gray;">
                        <i class="glyphicon glyphicon-wrench"></i>
                    </a>
                    <span ng-if="$root.globals.IsStaff == true"><br />
                        {{$root.enums.ValueDesc.JobCelloglazeOptions[job.FrontCelloglaze]}} -
                        {{$root.enums.ValueDesc.JobCelloglazeOptions[job.BackCelloglaze]}}
                    </span>
                </div>
            </div>


            
            <!-- Cello Override -->
            <div class="form-group" ng-if="vis.cello && (job.FrontCelloglazeOverride || job.BackCelloglazeOverride)">
                <label class="col-xs-3 control-label" for="Stock"> {{labeldata["cello"]}} Override </label>
                <div class="col-xs-7 form-control-static bold">
                    {{$root.enums.ValueDesc.JobCelloglazeOptions[job.FrontCelloglazeOverride]}} -
                    {{$root.enums.ValueDesc.JobCelloglazeOptions[job.BackCelloglazeOverride]}}
                </div>
                <a ng-if="$root.globals.IsStaff && job.StockOverride.Id" ng-click="job.StockOverride = null"
                    title="Undo Stock Override" style="position: relative; top:9px; left:-9px;color:gray;">
                    <i class="glyphicon glyphicon-remove"></i>
                </a>
            </div>

            <!-- Foil color if foil selected in cello-->
            <div class="form-group ng-class:{'has-error' : (job.FrontCelloglaze == 5 || job.FrontCelloglaze == 7) && !job.FoilColour }"
                ng-show="job.FrontCelloglaze == 5 || job.FrontCelloglaze == 7">
                <label class="col-xs-3 control-label" for="FoilColour">
                    Foil Colour
                </label>
                <div class="col-xs-7">
                    <span ng-if="($root.globals.IsStaff == true && !priceCalc) &&
                          (job.FoilColour == 'Clear Foil') ">
                        {{job.FoilColour }}
                    </span>
                    <select id="FoilColour" name="FoilColour" class="form-control input "
                        ng-options="k as k for k in foilColours" ng-model="job.FoilColour"
                        ng-required="(job.FrontCelloglaze == 5 || job.FrontCelloglaze == 7) && !job.FoilColour">
                        <option value="">select...</option>
                    </select>
                </div>
            </div>

            <!-- Envelope-->
            <div class="form-group  ng-class:{'has-error' :  !job.Envelope }" ng-show="vis.envelope">
                <label class="col-xs-3 control-label" for="Envelope">
                    Envelope
                </label>
                <div class="col-xs-7">
                    <select id="Envelope" name="Envelope" class="form-control input"
                        ng-options="k as k for k in envelopes" ng-model="job.Envelope" ng-required="vis.envelope">
                        <option value="">select...</option>
                    </select>
                </div>
            </div>

            <!-- PresentationFolder DieCut options -->
            <div class="form-group " ng-show="vis.diecut">
                <label class="col-xs-3 control-label" for="PresentationFolderDiecut">Die cutting</label>
                <div class="col-xs-7">
                    <select id="PresentationFolderDiecut" name="PresentationFolderDiecut" class="form-control input"
                        ng-model="job.DieCutType"
                        ng-options="k*1 as  $root.enums.ValueDesc.CutOptions[k] for k in cutOptions[job.Template.Id]"
                        lep-required-id="vis.diecut">
                        <option value="">select...</option>
                    </select>
                    <span class="help-block">
                        <a href="/images/pdfs/Presentation%20Folder%20DIE%20CATALOGUE.pdf" target="_blank"
                            class=" pull-right">Die Line Catalogue</a>
                    </span>
                </div>
            </div>

            <!-- job.DieCutType Tent Calendar DieCut options -->
            <div class="form-group " ng-show="vis.diecutTentCalendars">
                <label class="col-xs-3 control-label" for="TentCalendarFolderDiecut">Die cutting</label>
                <div class="col-xs-7">
                    <select id="TentCalendarFolderDiecut" name="TentCalendarFolderDiecut" class="form-control input"
                        ng-model="job.DieCutType"
                        ng-options="k*1 as  $root.enums.ValueDesc.CutOptions[k] for k in cutOptions[job.Template.Id]"
                        lep-required-id="vis.diecutTentCalendars">
                        <option value="">select...</option>
                    </select>
                    <span class="help-block" ng-show="job.DieCutType!=3">
                        <a href="/images/pdfs/LEP19_TENT-CALENDAR.pdf" target="_blank" class=" pull-right">Die Line
                            Catalogue</a>
                    </span>
                </div>
            </div>

            <!-- Perforate  -->
            <div class="form-group" ng-show="vis.perforate">
                <label class="col-xs-3 control-label" for="perforate">{{ labeldata["perforate"]}}</label>
                <div class="col-xs-7">
                    <label class="control-label"><input id="perforate" type="checkbox" ng-model="job.Perforating" />
                    </label>
                </div>
            </div>

            <!-- Scoring -->
            <div class="form-group" ng-show="vis.score">
                <label class="col-xs-3 control-label" for="score">{{labeldata["score"]}}</label>
                <div class="col-xs-7">
                    <label class="control-label"><input id="score" type="checkbox" ng-model="job.Scoring" /> </label>
                    <div class="help-block" ng-show="vis.scoreReqd">
                        Scoring is included when folding this stock.
                    </div>
                </div>
            </div>
            <!-- Magnet -->
            <div class="form-group" ng-show="vis.magnet">
                <label class="col-xs-3 control-label" for="magnet">Magnet</label>
                <div class="col-xs-7">
                    <!-- <label class="control-label">
                        <input id="magnet" type="checkbox" ng-model="job.Magnet" /> A magnet will be attached to each item

                    </label> -->

                    <select id="magnet" name="magnet" class="form-control input" ng-model="job.NumberOfMagnets"
                        ng-options="n for n in [] | range:lookups.stock.MinMagnet:5">
                        <option value="">No magnet required</option>
                    </select>

                    <div class="help-block" ng-show="vis.magnet && lookups.stock.MinMagnet > 0">
                        a minimum of {{lookups.stock.MinMagnet}} magnet is required for this if you choose to have
                        mangets.
                    </div>
                </div>
            </div>
        </fieldset>

        <!-- Send Samples -->
        <div class="form-group"
            ng-show="(job.Visibility.sendSamplesCheckVisible && !priceCalc && vis.sendSamples) && !isWhiteLabel">
            <label class="col-xs-3 control-label" for="sendSamplesCheck">Send samples</label>
            <div class="col-xs-7">
                <label class="control-label">
                    <input type="checkbox" ng-model="job.SendSamples" id="sendSamplesCheck" name="sendSamplesCheck"
                        ng-disabled="!job.Visibility.sendSamplesCheckEnabled" />
                    <span ng-show="$root.globals.IsCustomer">Send copy of completed job to me</span>
                </label>
            </div>
        </div>
        <!--BrochureDistPackInfo -->
        <!--
            1	IVE (Salmat)	* Multiples of 50* Max 500 per bundle* Same number of flyers per bundle* Max 16kg per carton* Plastic band uniform bundles with single strap
            2	Local Direct Network (LDN)	* Multiples of 50, Max 500 per bundle* Same number of flyers per bundle* Max 16kg per carton
            3	Ovoto/PMP Distribution	* Max 500 per bundle* Same number of flyers per bundle* Turn in 50s or 100s per bundle - turn height <100mm* Plastic cross strap banding* Max 10kg per carton
            4	Gold Coast Leaflet Distribution	* Multiples of 50, Max 500 per bundle* Same number of flyers per bundle* Max 16kg per carton
            5	Fuji Xerox	* Bundling in 100's* Paper Banding only* Same number of flyers per bundle* Max 16kg per carton
            6	Australia Post	* Bundling in 50 or 100* Secure with two vertical bands* Maximum 16kg per carton
            -->
        <fieldset ng-disabled="!(job.Visibility.optionPnlEnabled)">
            <div ng-show="(job.Template.Id == 7) || (job.Template.Id == 8) || (job.Template.Id == 14) "
                class="extra-info" data-title="{{job.BrochureDistPackInfo.MailHouse.Instructions}}">
                <!-- MailhouseInstructions -->
                <div class="form-group">
                    <label class="col-xs-3 control-label" for="mailhouseInstructions">Mailhouse</label>
                    <div class="col-xs-7">
                        <select id="mailhouseInstructions" name="mailhouseInstructions" class="form-control input"
                            ng-options="s as s.Name for s in brohureMailHouses track by s.Id"
                            ng-model="job.BrochureDistPackInfo.MailHouse">
                            <option value="">N/A</option>
                        </select>
                    </div>
                </div>
                <div ng-hide="job.BrochureDistPackInfo.MailHouse != null  && job.BrochureDistPackInfo.MailHouse.Id > 0">
                    <!-- If a Customer has chosen Mailhouse Distribution Option, then Packing Instructions Option should be disabled -->
                    <!-- Packing Instructions -->
                    <div class="form-group">
                        <label class="col-xs-3 control-label" for="packingInstructions">Packing Instructions</label>
                        <div class="col-xs-7">
                            <select id="packingInstructions" name="packingInstructions" class="form-control input"
                                ng-options="k*1 as v for (k,v) in $root.enums.ValueDesc.PackingInstruction"
                                ng-model="job.BrochureDistPackInfo.PackingInstruction"></select>
                        </div>
                    </div>
                    <!--Bundles-->

                    <div class="form-group  ng-class:{'has-error' : (!job.BrochureDistPackInfo.BundlesOf)} "
                        ng-show="currentBundlesOf != null">
                        <label class="col-xs-3 control-label" for="bundles">Bundles of approx</label>
                        <div class="col-xs-7">

                            <input name="BundlesOf" type="number" ng-model="job.BrochureDistPackInfo.BundlesOf"
                                ng-max="{{((80 - (80 % job.ThicknessOfSingleJob)) / job.ThicknessOfSingleJob)}}"
                                ng-min="{{(job.BrochureDistPackInfo.PackingInstruction == 2? 100: 50)}}" />
                            <div class="error-redish bold" ng-show="JobDetailsForm.BundlesOf.$error.max">reduce bundle
                                size</div>
                            <div class="error-redish bold" ng-show="JobDetailsForm.BundlesOf.$error.min">min bundle size
                                is {{(job.BrochureDistPackInfo.PackingInstruction == 2? 100: 50)}}</div>
                        </div>
                    </div>
                </div>

                <!--Extra fields for  Salmat/Local Delivery Network-->
                <div
                    ng-show="job.BrochureDistPackInfo.MailHouse != null  && (job.BrochureDistPackInfo.MailHouse.Id == 1 ||job.BrochureDistPackInfo.MailHouse.Id == 2 ||job.BrochureDistPackInfo.MailHouse.Id == 3 ||job.BrochureDistPackInfo.MailHouse.Id == 5)">
                    <div class="form-group">
                        <label class="col-xs-3 control-label" for="BookingIdNumber">Booking ID Number</label>
                        <div class="col-xs-7">
                            <input id="BookingIdNumber" name="BookingIdNumber" class="form-control input"
                                ng-model="job.BrochureDistPackInfo.BookingIdNumber" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-xs-3 control-label" for="CampaignName">Campaign Name</label>
                        <div class="col-xs-7">
                            <input id="CampaignName" name="CampaignName" class="form-control input"
                                ng-model="job.BrochureDistPackInfo.CampaignName" />
                        </div>
                    </div>
                </div>
                <!--Extra fields for Fuji Xerox-->
                <div ng-show="job.BrochureDistPackInfo.MailHouse != null  && job.BrochureDistPackInfo.MailHouse.Id ==5">
                    <div class="form-group">
                        <label class="col-xs-3 control-label" for="QtyPerBox">Qty per box</label>
                        <div class="col-xs-7">
                            <input id="QtyPerBox" name="QtyPerBox" class="form-control input" type="text"
                                ng-model="job.BrochureDistPackInfo.QtyPerBox" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-xs-3 control-label" for="MHDatePacked">Date packed</label>
                        <div class="col-xs-7">
                            <input id="MHDatePacked" name="MHDatePacked" class="form-control input"
                                ng-model="job.BrochureDistPackInfo.DatePacked" />
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>

        <!-- show CustomerRequestedPrice , readonly, only for IsStaff -->
        <div class="form-group amberAlert" ng-if="$root.globals.IsStaff && job.CustomerRequestedPrice">
            <label class="col-xs-5 control-label" style="margin-top: 4px;">Customer Requested Price</label>
            <div class="col-xs-7 form-control-static bold number">{{job.CustomerRequestedPrice | currency}}</div>
        </div>


        <!-- price list  -->
        <div id="price-list" ng-if="$root.globals.IsCustomer && job.Price">
            <div>
                <table>
                    <tr>
                        <td>Quantity</td>
                        <td>Price</td>
                    </tr>
                    <tr ng-repeat="pr in priceResults ">
                        <td>
                            <span ng-click="job.Quantity=pr.Quantity" class="aa">
                                {{pr.Quantity | number}}</span>
                        </td>
                        <td>{{pr.Price | currency}}</td>
                    </tr>
                </table>
            </div>
        </div>
        <div style=" clear: both;"></div>

        <!-- Price display  -->
        <div class="form-group" ng-if="showPrice">
            
            
            <label class="col-xs-3  control-label">Price to print</label>


            <div class="col-xs-7 ">
                <div class="form-control-static">
                    <div ng-if="$root.globals.IsCustomer == true">

                        <div class="number" animate-model-change model="{{job.Price}}" timeout="200">

                            <span ng-if="(vnp || priceCalc) && job.Price">Buy: {{job.Price | currency }}</span>
                        </div>

                        <div class="number" animate-model-change model="{{job.PriceWL}}" timeout="200"
                            style="margin-left:15px">

                            <span ng-if="vpp && job.IsWhiteLabel && job.PriceWL">Sell: {{job.PriceWL | currency
                                }}</span>
                        </div>

                        <button ng-if="job.Status == 0 && job.Visibility.priceButton && !job.IsQuotePrice"
                            class="btn btn-default" style="top: 0px; position: absolute; right: 15px;"
                            ng-click="getPrice2()">
                            <i class="glyphicon glyphicon-refresh" title="refresh price"></i>
                        </button>

                        <div ng-if="job.Price  || job.PriceWL" class="help-block">
                            Indicative price does not include freight or GST.
                        </div>
                    </div>

                    <div ng-if="$root.globals.IsSubCustomer == true">

                        <div class="number" animate-model-change model="{{job.PriceWL}}" timeout="200"
                            style="margin-left:15px">
                            <span ng-if="vpp && job.IsWhiteLabel && job.PriceWL">{{job.PriceWL | currency }}</span>
                        </div>

                        <button ng-if="job.Status == 0 && job.Visibility.priceButton && !job.IsQuotePrice"
                            class="btn btn-default" style="top: 0px; position: absolute; right: 15px;"
                            ng-click="getPrice2()">
                            <i class="glyphicon glyphicon-refresh" title="refresh price"></i>
                        </button>

                        <div ng-if="job.Price  || job.PriceWL" class="help-block">
                            Indicative price does not include freight or GST.
                        </div>
                    </div>

                    <div ng-if="$root.globals.IsStaff == true" class="">
                        <div class="input-group extra-info" data-title="">
                            <span class="input-group-addon">
                                <i class="glyphicon glyphicon-usd"></i>
                            </span>
                            <span class="col-xs-7 lpad0 rpad0">
                                <input type="text" class="form-control" ng-model="job.Price" />
                                <span class="input-group-btn">
                                    <button class="btn btn-default" style="top: 0px; position: absolute; right: 0"
                                        ng-click="getPrice2(true)">
                                        <i class="glyphicon glyphicon-refresh" title="refresh price"></i>
                                    </button>

                                </span>
                            </span>
                            <a href="#" class="tip">
                                <i class="glyphicon glyphicon-comment"></i>
                                <span>{{priceResult.Log}}</span>
                            </a>
                        </div>

                    </div>


                </div>
            </div>

        </div>

        <!--<pre>
    Id                  {{job.Id}}
    OrderId             {{job.OrderId}}
    OrderCustomerId     {{job.OrderCustomerId}}
    OrderWLCustomerId   {{job.OrderWLCustomerId}}
    </pre>
    <pre>{{priceResult.Log}}</pre>-->
        <!-- Quote is approved  -->
        <div class="form-group " ng-if="($root.globals.IsStaff == true && !priceCalc)">
            <label class="col-xs-3  control-label"> </label>
            <div class="col-xs-7 checkbox">
                <label class="checkbox">
                    <input name="perforate" type="checkbox" ng-model="job.QuoteNeedApprove" ng-true-value="{{false}}"
                        ng-false-value="{{true}}" />

                    Quote is approved
                    <!---->
                </label>
            </div>
        </div>



    </div>
</div>

<a style="position: fixed; top:10%; left: 10%" ng-if="$root.test" ng-click="fillJob()">fill with test job</a>

<pre style="
    display: none;
    visibility: hidden;
    position: fixed;
    font-size: x-small;
    overflow: auto;
    right: 10%;
    top: 15%;
    height: 400px;
">
{{job.Stock |json}}

{{job.WiroInfo | json}}


{{jo0b | json}}



</pre>
