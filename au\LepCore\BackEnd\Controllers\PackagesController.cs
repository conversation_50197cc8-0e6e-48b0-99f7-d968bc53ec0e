using LepCore.Dto;

namespace LepCore.src.Controllers
{
	#region

	using AutoMapper;
	using lep.freight;
	using lep.job;
	using lep.order;
	using lep.freight.impl;
	using lep.user;

	using Serilog;
	using Microsoft.AspNetCore.Mvc;
	using Microsoft.AspNetCore.Mvc.Filters;
	using Newtonsoft.Json;
	using System;
	using System.ComponentModel.DataAnnotations;
	using System.Linq;
	using System.Net;
	using System.Reflection;

	#endregion

	namespace LepCore.Controllers
	{
		[Produces("application/json")]
		[Route("api/[controller]")]
		[ApiExplorerSettings(IgnoreApi = true)]
		public partial class PackagesController : Controller
		{
			// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
			private readonly IFreightApplication _freightApplication;

			private readonly IOrderApplication _orderApp;
			private readonly IUserApplication _userApplication;
			private bool _currentUserIsCust;
			private bool _currentUserIsStaff;
			private IUser _currentUser;
			private IMapper _mapper;

			public PackagesController(IUserApplication userApplication, IOrderApplication orderApp, IFreightApplication freightApplication, IMapper mapper)
			{
				_orderApp = orderApp;
				_freightApplication = freightApplication;
				_userApplication = userApplication;
				_mapper = mapper;
			}

			[ApiExplorerSettings(IgnoreApi = true)]
			public override void OnActionExecuting(ActionExecutingContext context)
			{
				var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
				_currentUser = _userApplication.GetUser(userId);
				_currentUserIsCust = !_currentUser.IsStaff;
				_currentUserIsStaff = _currentUser.IsStaff;
			}

			[HttpGet("All")]
			public IActionResult List([FromServices] CartonFinder cartonFinder, [FromRoute] int? level = null)
			{
				return new OkObjectResult(cartonFinder.ListCarton(level));
			}

			[HttpGet("Order/{orderId:int}/FreightPackDetails")]
			[Produces(typeof(OrderFreightPackDetailsViewDto))]
			public IActionResult GetFreightPackDetails([FromRoute] int orderId)
			{
				Log.Information($"{_currentUser.Username}  getting  Order {orderId} freights");
				var order = _orderApp.GetOrder(orderId);
				var dto = _mapper.Map<OrderFreightPackDetailsViewDto>(order);

				if (dto.PackDetail.FGPackageCount > 0)
				{
					dto.PackDetail.FGWeight = dto.PackDetail.FGPackageJson.TotalWeight();
				}
				if (dto.PackDetail.PMPackageCount > 0)
				{
					dto.PackDetail.PMWeight = dto.PackDetail.PMPackageJson.TotalWeight();
				}

				return new OkObjectResult(dto);
			}

			[HttpPost("Order/{orderId:int}/FreightPackDetails")]
			public IActionResult SetFreightPackDetails([FromRoute] int orderId,
				[FromBody] [Required] OrderFreightPackDetailsUpdateDto request)
			{
				Log.Information($"{_currentUser.Username} getting Order {orderId} freights");
				try
				{
					var order = _orderApp.GetOrder(orderId);
					//var freightLog = _freightApplication.GetFreightLog(order.Id);

					order = _mapper.Map(request, order);

					order.SetDispatchLabels();
					_orderApp.BaseSave(order);
				//	_freightApplication.Save(freightLog);
				}
				catch (Exception ex)
				{
					return new ContentResult { StatusCode = (int)HttpStatusCode.BadRequest, Content = ex.Message };
				}
				return new ContentResult
				{
					StatusCode = (int)HttpStatusCode.Accepted,
					Content = "The Order freight has been updated"
				};
			}

			[HttpGet("Order/{orderId:int}/{facility}/packing")]
			public IActionResult GetPacking([FromRoute] int orderId, [FromRoute] Facility facility)
			{
				Log.Information($"{_currentUser.Username}  getting  Order {orderId} freights");
				var order = _orderApp.GetOrder(orderId);

				var r = order.PackDetail.GetPackages(facility).ToString();
				return new OkObjectResult(r);
			}
		}
	}
}
