﻿<a class="btn" ng-click="downloadPriceCsv()"><i class="glyphicon glyphicon-cloud-download"></i> Download job price csv </a>

<a class="btn" ng-click="uploadPriceCsv()"><i class="glyphicon  glyphicon-cloud-upload  "></i> Upload job price csv</a>

<a class="btn" ng-click="importJobOption()"><i class="glyphicon  glyphicon-cloud-upload  "></i> import job options</a>
<!--<a class="btn" ng-click="uploadCartonCsv()"><i class="glyphicon  glyphicon-cloud-upload  "></i> Upload carton csv</a>-->

<div class="col-xs-12">
	<h4>Setup Job Price</h4>
</div>

<div class="row">
	<div class="col-xs-6">
		<div class="row1">
			<div class="col-xs-12">
				<div class="row ">
					<div class="form-horizontal">
						<div class="form-group ">
							<label class="col-xs-4  control-label" for="templateId">Job Type</label>
							<div class="col-xs-8 ">
								<select id="templateId" name="templateId" class="form-control input"
										ng-options="t.Id as t.Name for t in templates | filter:{Name:'!Custom'}" ng-model="job.Template.Id" ng-required="true"></select>
							</div>
						</div>

						<!-- Trim Size -->
						<div class="form-group ">
							<label class="col-xs-4  control-label" for="sizeLst">Size</label>
							<div class="col-xs-8 ">
								<select id="sizeLst" name="size" class="form-control input" ng-options="s.Id as s.Name for s in jobTemplateSizeOptions  | filter:{Name:'!Custom'}"
										ng-model="job.FinishedSize.PaperSize.Id" ng-required="true" ng-change="ps=s"></select>
							</div>
						</div>

						<!-- Stock -->
						<div class="form-group ">
							<label class="col-xs-4  control-label" for="stockLst">Stock</label>
							<div class="col-xs-8 ">
								<select id="stockLst" name="stocks" class="form-control input"
										ng-options="so.Id as so.Name for so in stockOptions"
										ng-model="job.Stock.Id" ng-required="true"></select>
							</div>
						</div>

						<!-- PrintType -->
						<div class="form-group ">
							<label class="col-xs-4  control-label">Print type</label>
							<div class="col-xs-8 ">
								<label class="radio-inline" ng-show="printTypesAvaiable=='B' || printTypesAvaiable=='O'">
									<input type="radio" name="optradioPrintType" ng-model="job.PrintType" value="O" ng-required="printTypesAvaiable && printTypesAvaiable != 'N'"> Offset&nbsp;&nbsp;
								</label>

								<label class="radio-inline" ng-show="printTypesAvaiable=='B' || printTypesAvaiable=='D'">
									<input type="radio" name="optradioPrintType" ng-model="job.PrintType" value="D" ng-required="printTypesAvaiable && printTypesAvaiable != 'N'"> Digital&nbsp;&nbsp;
								</label>

								<label class="radio-inline" ng-show="printTypesAvaiable=='W'">
									<input type="radio" name="optradioPrintType" ng-model="job.PrintType" value="W" ng-required="printTypesAvaiable && printTypesAvaiable != 'N'"> Wide Format
								</label>

								<label class="radio-inline" ng-show="printTypesAvaiable=='N'">
									<input type="radio" name="optradioPrintType" ng-model="job.PrintType" value="N" ng-required="printTypesAvaiable && printTypesAvaiable =='N'"> N/A
								</label>

							</div>
						</div>

						<!-- FrontColour -->
						<div class="form-group ">
							<label class="col-xs-4  control-label" for="FrontColour">Colour pages</label>
							<div class="col-xs-8 ">
								<select id="FrontColour" class="form-control input"
										ng-options="k*1 as  $root.enums.ValueDesc.ColourSides[k.toString()] for k in stock.colourOptions "
										ng-model="job.ColorSides"></select>
							</div>
						</div>

						<div class="form-group " ng-show="vis.page">
							<label class="col-xs-4  control-label" for="pagesList">{{labeldata.pages}}</label>
							<div class="col-xs-8 ">
								<select class="form-control input" ng-model="job.Pages" ng-options="x*1 as x for x in numberOfPages[job.Template.Id]"></select>
							</div>
						</div>


						<div class="form-group ">
							<label class="col-xs-4  control-label" for="celloLst">Cello</label>
							<div class="col-xs-8 ">
								<select id="celloLst" class="form-control input"
										ng-options="k.Key as k.Value for   k in stock.CelloOptions2"
										ng-model="selectedCello"
										ng-change="changeCello(selectedCello)"></select>
							</div>
						</div>
						<div class="form-group ">
							<label class="col-xs-4  control-label"> </label>
							<div class="col-xs-8  readonly-text">
								<button class="btn btn-default" ng-click="showOldPrices()">Show old prices</button>
							</div>
						</div>
					</div>
				</div>

				<div class="row form-horizontal">

					<div class="form-group ">
						<label class="col-xs-4  control-label">Minimum</label>
						<div class="col-xs-8 readonly-text"> {{stock.QuantityOption.Minium}} </div>
					</div>
					<div class="form-group ">
						<label class="col-xs-4  control-label">Step1</label>
						<div class="col-xs-8  readonly-text">{{stock.QuantityOption.Step1}} </div>
					</div>

					<div class="form-group ">
						<label class="col-xs-4  control-label">Change Point</label>
						<div class="col-xs-8  readonly-text">{{stock.QuantityOption.Change}} </div>
					</div>


					<div class="form-group ">
						<label class="col-xs-4  control-label">Step2</label>
						<div class="col-xs-8  readonly-text">{{stock.QuantityOption.Step2}} </div>
					</div>
					<div class="form-group ">
						<label class="col-xs-4  control-label">Change Point 2</label>
						<div class="col-xs-8  readonly-text">{{stock.QuantityOption.Change2}}</div>
					</div>

					<div class="form-group ">
						<label class="col-xs-4  control-label">Quantities allowed</label>
						<div class="col-xs-8  readonly-text">{{stock.QuantityOptions.join(", ")   }}</div>
					</div>





				</div>

			</div>
		</div>
	</div>

	<!-- right column with Qty Price MYOB -->
	<div class="col-xs-6" ng-if="result1">
		<div class="row">
			<div class="col-xs-12">
				<div ui-grid="gridOptions" ui-grid-edit ui-grid-cellnav class="grid"></div>

				<a class="btn" ng-click="addBlankRow()"><i class="glyphicon glyphicon-plus"></i>Add new price point</a>
			</div>
			<div class="col-xs-12">

				<div class="help-block">
					Last Changed on {{result1.lastChanged}} by {{result1.lastChangedBy}}<br />
				</div>


				<div class="help-block"  style="height: 80px;overflow: scroll;">
					Must specify price for the quantities {{result1.ReqdQtys.join(", ")}} <br />
				</div>

				<div ng-if="missing.length > 0"   style="height: 80px;overflow: scroll;">
					prices are misssing for quntities {{missing.join(", ")}}
					<a class="" ng-click="addMissingQty()">add missing quantities</a>
				</div>

			</div>
			<!-- <div class="col-xs-12">
				<div class="form-group">
					<label class="col-xs-4 control-label">MYOB Other</label>
					<div class="col-xs-8"><input class="form-control " id="myob1Txt" ng-model="result1.productMYOB"></div>

				</div>
			</div> -->
			<div class="col-xs-12">
				<div class="form-group">

					<button class="btn btn-info pull-right" ng-click="savePrices()">Save</button>
				</div>
			</div>

		</div>
	</div>
</div>


<br><br><br>
<pre>{{result}}</pre>

