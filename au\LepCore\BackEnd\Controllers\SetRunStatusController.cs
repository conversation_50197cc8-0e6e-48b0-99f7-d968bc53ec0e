using lep;
using lep.configuration;
using lep.job;
using lep.run;
using LepCore.Dto;

using Serilog;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;

namespace LepCore.src.Controllers
{
	[AllowAnonymous]
	public class SetRunStatusController : Controller
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		public SetRunStatusController(
			IRunApplication runApp,
			FileDetector fileDetector
		)
		{
			this._runApp = runApp;
			this._fileDetector = fileDetector;
		}

		#region services

		private IRunApplication _runApp;
		//private IConfigurationApplication _configApp;
		private FileDetector _fileDetector;

		#endregion services

		//private IList<string> _permitAddress;
		private IRun _run;

		[HttpGet]
		[HttpPost]
		[Route("setrunstatus.aspx")]
		public IActionResult Index([FromQuery] int id, [FromQuery] string status)
		{
			return Page_Load(id, status);
		}
 
		protected IActionResult Page_Load(int runid, string status)
		{
			if (!IsValidAddress())
			{
				return GenerateErrorMsg("invalid address");
			}

			_run = _runApp.GetRun(runid);

			if (_run == null)
			{
				return GenerateErrorMsg("run not found");
			}

			var file = new FileInfo(string.Format((string)"{0}/success.txt", (object)LepGlobal.Instance.ArtworkDirectory(_run).FullName));
			if (file.Exists)
			{
				file.Delete();
			}
			file = new FileInfo(string.Format((string)"{0}/error.txt", (object)LepGlobal.Instance.ArtworkDirectory(_run).FullName));
			if (file.Exists)
			{
				file.Delete();
			}
			try
			{
				SetRunStatus(status);
			}
			catch (Exception ex)
			{
				return GenerateErrorMsg(ex.Message + ex.StackTrace);
			}

			return GenerateSuccessMsg();
		}

		private IActionResult GenerateErrorMsg(string error)
		{
			var errorFile = new FileInfo(string.Format((string)"{0}/error.txt", (object)LepGlobal.Instance.ArtworkDirectory(_run).FullName));
			var writer = errorFile.CreateText();
			writer.Write(error);
			writer.Close();

			return new ObjectResult(new { success = false, message = error });
			//Response.Write(string.Format(@"{{""success"":false,""message"":""{0}""}}", error));
			//Response.End();
		}

		private IActionResult GenerateSuccessMsg()
		{
			var successFile = new FileInfo(string.Format((string)"{0}/success.txt", (object)LepGlobal.Instance.ArtworkDirectory(_run).FullName));
			var writer = successFile.CreateText();
			writer.Write("run status changed");
			writer.Close();

			return new ObjectResult(new { success = true });
			// Response.Write(@"{""success"":true}");
			// Response.End();
		}

		private bool IsValidAddress()
		{
			return true;

			//foreach (string permit in permitAddress) {
			//    if ((new Regex(permit)).IsMatch(
			//        HttpContext.Connection.RemoteIpAddress.MapToIPv4().ToString()
			//    )) {
			//        return true;
			//    }
			//}
			//return false;
		}

		private void SetRunStatus(string status)
		{
			if (string.IsNullOrEmpty(status))
			{
				throw new Exception("status required");
			}

			Log.Information("Start set run status: " + _run.RunNr);

			var runDir = LepGlobal.Instance.ArtworkDirectory(_run);
			bool fileExist = false;
			foreach (FileInfo f in runDir.GetFiles())
			{
				var sr = f.OpenRead();
				var fileType = _fileDetector.GetFileType(sr);
				Log.Information($"File: {f.Name} {fileType.ToString()}");
				if (fileType == ArtworkTypeOptions.Quark || fileType == ArtworkTypeOptions.InDesign)
				{
					fileExist = true;
					break;
				}
				sr.Close();
			}

			Log.Information("End set run status: " + _run.RunNr);

			if (!fileExist)
			{
				throw new Exception("layout file required");
			}

			RunStatusOptions runStatus = RunStatusOptions.Filling;
			try
			{
				runStatus = (RunStatusOptions)Enum.Parse(typeof(RunStatusOptions), status, true);
			}
			catch
			{
				throw new Exception("invalid status");
			}

			if (runStatus != RunStatusOptions.Filling && _run.Jobs.Count == 0)
			{
				throw new Exception("run doesn't contain any job");
			}

			_runApp.SetLayout(_run, runStatus);
		}
	}
}
