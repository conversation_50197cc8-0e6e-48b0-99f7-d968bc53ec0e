window.apiBase = '/api/';
(function (window, angular, undefined) {
    'use strict';

    angular.module('app')
        .service('lepApi2',
            ['$http', '$q', 'cfpLoadingBar', '$rootScope', '$localStorage',
                function ($http, $q, cfpLoadingBar, $rootScope, $localStorage) {
                    var apiBase = window.apiBase;
                    // if you pass in a hash it will get converted to
                    // query params by $http
                    function get(url, params, cache, canceller) {
                        var d = $q.defer();
                        var config = {};
                        
                        config.url = apiBase + url;
                        if (typeof params !== "undefined" && params !== null) {
                            config.params = params;
                        }
                        cache = false;
                        if (typeof cache !== "undefined" && cache === true) {
                            config.cache = true;
                        }

                        if (canceller && canceller.promise) {
                            config.timeout = canceller.promise;
                        }
                        

                        $http(config).then(
                            function (response) {
                                d.resolve(response.data);
                                // note: using a defferred promise just as we are resolving on .data...
                            },
                            function (response) {
                                d.reject(response);
                            }
                        );
                        return d.promise;
                    }

                    function post(url, postData, params) {
                        return postFn(url, postData, params, 'POST');
                    }

                    function put(url, postData, params) {
                        return postFn(url, postData, params, 'PUT');
                    }

                    function postFn(url, postData, params, method) {
                        var d = $q.defer();
                        var p = {};
                        angular.extend(p, params || {});
                        angular.extend(p, {
                            url: apiBase + url,
                            method: method,
                            data: postData ? postData : '',

                            headers: {
                                'Content-Type': 'application/json, text/html'
                            },
                        });

                        $http(p).then(function (response, status, headers, config) {
                            if (typeof response !== 'undefined' && response !== null) {
                                d.resolve( response.data);
                            }
                        }, function (error) {
                                d.reject(error.data);
                        });
                        return d.promise;
                    }

                    // as ajax post wont return downloaded content inline
                    // make a post call in an iframe to download a file
                    // assumption: the passed in JSON object 'value' does not contain nested objects
                    function download(url, values) {
                        var $, form, hiddenIframe, i, iframeContainer, l, property, value;
                        values = values || {};

                        $ = angular.element;
                        form = $('<form>', {
                            action: url,
                            method: 'POST',
                            enctype: 'application/x-www-form-urlencoded'
                        });

                        for (property in values) {
                            if (values.hasOwnProperty(property)) {
                                value = values[property];
                                if (value instanceof Array) {
                                    i = 0;
                                    l = value.length;
                                    while (i < l) {
                                        form.append($('<input />', {type: 'hidden', name: property, value: value[i]}));
                                        i++;
                                    }
                                } else {
                                    form.append($('<input />', {type: 'hidden',name: property, value: value }));
                                }
                            }
                        }

                        form.append($('<input />', {type: 'hidden', name: 'token', value: $localStorage.lepToken}));

                        hiddenIframe = $('<iframe  style="display:none;"/>').append(form);
                        iframeContainer = $('.iframeContainer');
                        iframeContainer.append(hiddenIframe);
                        form.submit();
                    }

                    function downloadJson(json, name) {
                        var data = "text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(json, 0, 4));
                        var a = document.createElement('a');
                        a.href = 'data:' + data;
                        a.download = name + '.json';
                        a.innerHTML = 'download JSON';
                        var container = $('.iframeContainer');
                        container.append(a);
                        a.click();
                        a.remove();
                    }

                    function upload(f, onSuccessCb, onErrorCb) {
                        var fd = new FormData();
                        fd.append("uploadedFile", f);
                        fd.append("name", f.name);
                        fd.append("mime", f.type);
                        $rootScope.$broadcast("percentage", 1);
                        $http({
                            method: 'POST',
                            url: '/api/file/Upload',
                            headers: {
                                'Content-Type': void 0
                            },
                            ignoreLoadingBar: true,
                            uploadEventHandlers: {
                                progress: function (e) {
                                    if (e.lengthComputable) {
                                        var px = e.loaded / e.total;
                                        cfpLoadingBar.set(px);
                                        $rootScope.$broadcast('percentage', px);
                                    }
                                }
                            },
                            data: fd,
                            transformRequest: angular.identity
                        }).then(onSuccessCb, onErrorCb);
                    }

                    function documentsPost(files, profile) {
                        var fd = new FormData();
                        var url = '/api/document/post';
                        if (profile) {
                            url += '?profile=' + profile;
                        }

                        angular.forEach(files, function (value) {
                            fd.append("files[]", value);
                        });
                        $rootScope.$broadcast("percentage", 1);
                        return $http({
                            method: 'POST',
                            url: url,
                            headers: {
                                'Content-Type': void 0
                            },
                            eventHandlers: {
                                readystatechange: function (event) {
                                    if (event.currentTarget.readyState === 4) {
                                        window.toastr.remove();
                                    }
                                },
                            },
                            uploadEventHandlers: {
                                progress: function (e) {
                                    if (e.lengthComputable) {
                                        var px = (e.loaded / e.total);
                                        cfpLoadingBar.set(px);
                                        $rootScope.$broadcast('percentage', Math.floor(px * 100));
                                        if (px == 1) {
                                            var msg = "Good news!    Your Artwork has been uploaded.\nPlease wait whilst we perform some checks on your Artwork.\nThis should only take a few more seconds to complete.";
                                            if( typeof(window.ignoreErrorAndAttachAnyways) !== 'undefined' &&  window.ignoreErrorAndAttachAnyways){
                                                msg = "Good news!    Your Artwork has been uploaded";
                                            }
                                            window.toastr.info(
                                                msg,
                                                "Please wait....",
                                                {
                                                    "closeButton": false,
                                                    "progressBar": true,
                                                    "preventDuplicates": false,
                                                    "onclick": null,
                                                    "timeOut": "60000",
                                                    "extendedTimeOut": "1000",
                                                });
                                        }
                                    }
                                }
                            },
                            data: fd,
                            transformRequest: angular.identity
                        });
                    }

                    function downloadReport(Id) {
                        download('/api/document/report/' + Id + '/download');
                    }

                    function downloadPostAac(Id) {
                        download('/api/document/postaac/' + Id + '/download');
                    }

                    function downloadExtraFile(jobId, fileName) {
                        download('/api/Document/job/' + jobId + '/extrafiles/download/' + fileName);
                    }

                    function downloadOrderExtraFile(orderId, fileName) {
                        download('/api/Document/order/' + orderId + '/extrafiles/download/' + fileName);
                    }

                    function downloadCustomerExtraFile(customerId, fileName) {
                        download('/api/Document/customer/' + customerId + '/extrafiles/download/' + fileName);
                    }

                    function addCommentAac(jobId, comment, aacNotPerformed) {
                        var m = {
                            Id: 0,
                            CommentText: comment,
                            LepOnly: false,
                            AACNotPerformed: aacNotPerformed
                        };

                        post('Orders/job/' + jobId + '/commentsAac', m);
                    }
                    function downloadQuotePdf(orderId) {
                        download('/api/orders/order/' + orderId + '/quotePdf');
                    }
                    return {
                        get: get,
                        post: post,
                        download: download,
                        upload: upload,
                        put: put,
                        documentsPost: documentsPost,
                        downloadReport: downloadReport,
                        downloadPostAac: downloadPostAac,
                        downloadExtraFile: downloadExtraFile,
                        downloadOrderExtraFile: downloadOrderExtraFile,
                        addCommentAac: addCommentAac,
                        downloadJson: downloadJson,
                        downloadCustomerExtraFile: downloadCustomerExtraFile
                    };
                }
            ]);
})(window, window.angular);
