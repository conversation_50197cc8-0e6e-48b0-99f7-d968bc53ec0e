﻿<style>

	input[type=checkbox] {
		cursor: pointer
	}
</style>

<script type="text/ng-template" id="processingTemplate">
	<div id="processing">
		<div><strong>Your request is being processed.</strong></div>
		<div><asp:image id="Image1" imageurl="~/app_themes/lep/uploading.gif" width="64" height="64" runat="server" /></div>
	</div>
</script>

<script type="text/ng-template" id="warningTemplate">
	<div align="left" ng-repeat="m in messages">{{ m}}</div>
	<div class="buttons-large border-end" style="text-align: center;">
		<input type="button" class="inputSearchButton" value="Close" ng-click="closeThisDialog()" />
	</div>
</script>

<div leppane="Runs">

	<!--
	{{searchcriteria | json}}
	   <pre>
			  runs = {{runs|json}}
			  jobs = {{jobs|json}}
	   </pre>
		-->



	<form>

		<div class="row form-horizontal">
			<div class="col-sm-4">

				<div class="form-group form-group-sm">
					<label class="control-label col-xs-4" for="customer">Production facility</label>
					<div class="col-xs-8">
						<select name="facility" ng-options="k*1 as v for (k,v)  in ::enums.ValueDesc.Facility" ng-model="searchcriteria.Facility" class="form-control"
								ng-change="Search(null,null)"></select>
					</div>

				</div>

				<div class="form-group form-group-sm">
					<label class="control-label col-xs-4" for="customer">Customer</label>
					<div class="col-xs-8"><input name="customerText" type="text" maxlength="20" id="customerText" tabindex="1" ng-model="searchcriteria.Customer" class="form-control col-xs-8" autocomplete="off"></div>

				</div>
				<div class="form-group form-group-sm">
					<label class="control-label col-xs-4" for="quote-number">Order #</label>
					<div class="col-xs-8"><input name="orderText" type="text" maxlength="20" id="orderText" tabindex="2" ng-model="searchcriteria.OrderNr" class="form-control col-xs-8"></div>

				</div>
				<div class="form-group form-group-sm">
					<label class="control-label col-xs-4" for="job">Job #</label>
					<div class="col-xs-8"><input name="jobText" type="text" maxlength="20" id="jobText" tabindex="3" ng-model="searchcriteria.JobNr" class="form-control col-xs-8"></div>

				</div>
				<div class="form-group form-group-sm">
					<label class="control-label col-xs-4" for="job">Run #</label>
					<div class="col-xs-8"><input name="runText" type="text" maxlength="20" id="runText" tabindex="3" ng-model="searchcriteria.RunNr" class="form-control col-xs-8"></div>
				</div>
				<div class="form-group form-group-sm">

					<div class="col-xs-8 col-md-offset-4">
						<label class="control-label"><input type="checkbox" id="urgentChk" tabindex="4" ng-model="searchcriteria.Urgent" class=""> Urgent Only</label>
					</div>
				</div>
				<div class="form-group form-group-sm">
					<div class="col-xs-8  col-md-offset-4 ">
						<label class="control-label"><input name="onholdChk" type="checkbox" id="onholdChk" tabindex="4" ng-model="searchcriteria.IsOnHold" class=""> On Hold Only </label>
					</div>
				</div>
			</div>

			<div class="col-sm-4 runs-print-characteristics">
				<div class="form-group form-group-sm ">
					<label class="col-xs-4 control-label" for="job-type">Category</label>
					<div class="col-xs-8">
						<select ng-model="searchcriteria.JobTypes" id="job-types" class="form-control input "
								ng-options="templateTree.Ids(tv) as t for (t, tv)  in templateTree.topdown.Category"
								ng-change="Search(null, null)">
							<option value="">Any</option>
						</select>
					</div>
				</div>

				<div class="form-group form-group-sm">
					<label class="col-xs-4 control-label" for="job-type">Template</label>
					<div class="col-xs-8">
						<select ng-model="searchcriteria.JobType" id="job-type" class="form-control input"
								ng-options="t.Id as t.Name for t in templates"
								ng-change="Search(null, null)">
							<option selected="selected" value="">-- Any --</option>
						</select>
					</div>
				</div>

				<!--Size-->
				<div class="form-group form-group-sm">
					<label class="control-label col-xs-4" for="size">Size</label>
					<div class="col-xs-8">
						<select name="sizeLst" id="sizeLst" tabindex="5" ng-model="searchcriteria.Size" class="form-control col-xs-8"
								ng-options="s.Id as s.Name for s in allSizes">
							<option value="">-- Any --</option>
						</select>
					</div>
				</div>

				<!-- Celloglaze  JOB  -->
				<div class="form-group form-group-sm">
					<label class="control-label col-xs-4" for="celloglaze-front">Celloglaze</label>
					<div class="col-xs-8">
						<select name="celloLst" id="celloLst" tabindex="6" ng-model="searchcriteria.Cello" class="form-control col-xs-8"
								ng-options="k*1 as v for (k,v) in  runCelloOptions">
							<option value="">-- Any --</option>

						</select>
					</div>
				</div>

				<!-- Sides -->
				<div class="form-group form-group-sm">
					<label class="control-label col-xs-4" for="celloglaze-back">Sides</label>
					<div class="col-xs-8">
						<select name="sides" id="sides" tabindex="7" ng-model="searchcriteria.Side" class="form-control col-xs-8">
							<option value="">-- Any --</option>
							<option value="1">1</option>
							<option value="2">2</option>
						</select>
					</div>
				</div>

				<!--Stock-->
				<div class="form-group form-group-sm">
					<label class="control-label col-xs-4" for="stock">Stock</label>
					<div class="col-xs-8">
						<select name="stockLst" id="stockLst" tabindex="8" ng-model="searchcriteria.Stock" class="form-control col-xs-8"
								ng-options="s.Id as  s.Name for s in allStocks">
							<option value="">-- Any --</option>
						</select>
					</div>
				</div>

				<!--Stock Type -->
				<div class="form-group form-group-sm">
					<label class="control-label col-xs-4" for="stock">Stock Type</label>
					<div class="col-xs-8">
						<select name="stockLst" id="stockKindLst" tabindex="8" ng-model="searchcriteria.StockKind" class="form-control col-xs-8"
								ng-options="s for s in allStocksKind">
							<option value="">-- Any --</option>
						</select>
					</div>
				</div>

				<!--Run Types-->
				<div class="form-group form-group-sm">
					<label class="control-label col-xs-4" for="stock">Run Type</label>
					<div class="col-xs-8">
						<select name="runTypeLst" id="runTypeLst" tabindex="9" ng-model="searchcriteria.RunSearchOption" ng-change="Search(null, null)" class="form-control col-xs-8"
								ng-options="k*1 as v for (k,v) in  runSearchOptions"></select>
					</div>
				</div>
			</div>

			<div class="col-sm-2">
				<div class="form-group-sm">
					<div ng-repeat="(k,v) in ::enums.ValueDesc.RunStatusOptions">
						<label class="control-label">
							<input type="checkbox" checkbox-model="searchcriteria.RunStatus"
								   checkbox-value="k*1" multiple>  {{::v}}
						</label>
					</div>
				</div>
			</div>

			<div class="col-sm-2">
				<table class="table table-striped table-hover">
					<tr ng-repeat="fs in favRunSearches" class="fav-search">
						<td><a ng-click="setAsCurrentSearch(fs)" >{{fs.Name}}</a></td>
						<td><a class="r"  ng-click="removeFavSearch(fs)"  > <i class="glyphicon glyphicon-remove"></i>   </a> </td>
					</tr>
				</table>
			</div>

		</div>
		<div class="row">
			<div class="col-md-12" style="text-align: right;">
				<button type="button" class="btn" ng-click="clear()"><i class="glyphicon glyphicon-erase"></i>  Clear </button>
				<button type="submit" class="btn" value="Search" ng-click="Search(null, null);"><i class="glyphicon glyphicon-search"></i> Search</button>

				<button type="button" class="btn" ng-click="saveAsFavSearch()">Save <i class="glyphicon glyphicon-star"></i></button>

			</div>
		</div>


	</form>



</div>


<div ng-if="!AutoRunAllocationEnabled" >
 	<a ng-click="RAEnable()" class="btn-success">Start BC AutoFill</a>
</div>
<div ng-if="AutoRunAllocationEnabled" >
	<a ng-click="RADisable()" class="btn-warning">Stop BC AutoFill</a>
</div>





<div leppane="Jobs not allocated to runs">


	<table border="0" cellspacing="0" cellpadding="0" class="table">
		<thead>
			<tr>
				<th>&nbsp;</th>
				<th>
					<a ng-click="Search('Submitted', null)">Submitted date</a>
				</th>
				<th>
					<a ng-click="Search('RequiredBy', null)">Required by</a>
				</th>
				<th>
					<a ng-click="Search('HD', null)">HrsDispatch</a>
				</th>
				<th>
					<a ng-click="Search('JobId', null)">Job#</a>
				</th>
				<th></th>
				<th>
					<a ng-click="Search('Customer', null)">Customer</a>
				</th>

				<th>
					<a ng-click="Search('JobName', null)">Job name</a>
				</th>
				<th>  Slots</th>
				<th> <a ng-click="Search('Template', null)">Template</a></th>
				<th> <a ng-click="Search('Stock', null)">Stock</a></th>
				<th>  <a ng-click="Search('Size', null)">Size</a></th>
				<th> <a ng-click="Search('Qty', null)">Qty</a></th>
				<th>Sides</th>
				<th>Cello</th>
			</tr>
		</thead>
		<tbody id="jobtable">
			<tr ng-repeat="job in jobs" class="{{::job.rowCss}}">
				<td><input type="checkbox" ng-model="job.selected" /></td>
				<td class="Jobs nowrap" nowrap="">{{::job.Date }}</td>
				<td>
					{{::job.RequiredDate}}

					<span style="color:red; font-weight:bold" ng-if="job.ProofStatus==1">On Hold</span>
				</td>
				<td class="bold nowrap">
						{{::job.TimeTillDispatch}}
				</td>
				<td class="bold nowrap">
					<a class="td180 id-link" ui-sref="staff.order.job({orderId: job.OrderId, jobId: job.Id})">
						{{::job.JobNr}}
					</a>



				</td>
				<td class=" nowrap">
					<a href="javascript:void(0)" class="tip"
						title="Nominate this job for DPC production." ng-click="jobToDPC(job.Id)">DPC</a>

					<a href="javascript:void(0)" class="tip"
						title="Send this job to IMP." ng-click="jobToIMP(job.Id)">IMP</a>

					<a href="javascript:void(0)" class="tip" ng-if="::job.SpecialInstructions">
						<i class="glyphicon glyphicon-info-sign"></i>
						<span><strong>Special Instructions</strong><br/>{{::job.SpecialInstructions}}</span>
					</a>

					<a href="javascript:void(0)" class="tip" ng-if="::job.ProductionInstructions">
						<i class="glyphicon glyphicon-tasks"></i>
						<span><strong>Production Instructions</strong><br/>{{::job.ProductionInstructions}}</span>
					</a>
				</td>
				<td class="td180">{{::job.Customer}}</td>
				<td class="">

					<span style="color:red; font-weight:bold" ng-if="job.IsRestart">Restart </span>
					{{::job.Job}}
				</td>
				<td>{{::job.Slots}}</td>
				<td>{{::job.Template}}</td>
				<td class="td180">{{::job.Stock}}</td>
				<td class="nowrap">{{::job.Size}}</td>
				<td>{{::job.Quantity}}</td>
				<td>{{::job.Sides}}</td>
				<td>{{::job.Cello}}</td>
			</tr>
		</tbody>
	</table>

	<div class="buttons-large border-end">
		<!-- ngIf: allowOperation --><a id="addToRunLnk" class="search ng-scope" ng-if="allowOperation" ng-click="CreateNewRun()">Add jobs to a new run</a><!-- end ngIf: allowOperation -->
	</div>
</div>


<div leppane="Runs">
	<table border="0" cellspacing="0" cellpadding="0" class="table" id="runtable">
		<thead>
			<tr>
				<th>&nbsp;</th>
				<th>&nbsp;</th>
				<th>&nbsp;</th>
				<th><a ng-click="Search(null, 'SubmittedDate')" class="nowrap">Submitted date</a></th>
				<th class="nowrap">Required by</th>
				<th><a ng-click="Search(null, 'HD')">HrsDispatch</a></th>
				<th>Status</th>
				<th class="nowraps">InUseBy</th>
				<th><a ng-click="Search(null, 'Id')">Run/Job#</a></th>
				<th>Job</th>
				<th><a ng-click="Search(null, 'Slot')">Slots</a></th>
				<th><a ng-click="Search(null, 'Stock')">Stock</a></th>
				<th>Size</th>
				<th>Qty</th>
				<th><a ng-click="Search(null, 'Cmyk')">Sides</a></th>
				<th><a ng-click="Search(null, 'Celloglaze')">Cello</a></th>
				<th>Manual</th>
			</tr>
		</thead>
		<tbody ng-repeat="run in runs">
			<tr class="{{::run.uneffectedCss}} {{::run.Health}}">
				<td class="widthSmall {{::run.showjobCss}}">
					<a ng-click="OpenRun(run)">
						<i class="glyphicon glyphicon-folder-open" ng-if="run.Open"></i>
						<i class="glyphicon glyphicon-folder-close" ng-if="!run.Open"></i>
					</a>
				</td>
				<td ng-if="! (run.Enable && allowOperation)">&nbsp;</td>
				<td ng-if="run.Enable && allowOperation" nowrap>
					<a ng-click="MoveToRun(run)" class="addRemoveButton">
						<i class="glyphicon glyphicon-plus"></i>
					</a>
					&nbsp;<a ng-click="RemoveRun(run)" class="addRemoveButton">
						<i class="glyphicon glyphicon-minus"></i>
					</a>
				</td>
				<td ng-if="run.Enable && allowOperation">
					<input type="checkbox" ng-model="run.selected" ng-click="toggleJobSelected(run)" />
				</td>
				<td ng-if="!(run.Enable && allowOperation)">&nbsp;</td>
				<td class="nowrap">{{::run.Date}}</td>
				<td>{{::run.MinRequiredByDate}} </td>

				<td class="bold nowrap">{{::run.TimeTillDispatch}}</td>
				<td class="nowrap">{{::run.Status}} </td>
				<td class="nowrap">{{::run.UseBy}}</td>
				<td class="bold nowrap">
					<a ui-sref="staff.run-edit({runId: run.Id})" class="id-link">{{::run.Run}}</a>&nbsp;&nbsp;
					<span run-scan id="run.Id" print-type="run.PrintType"></span>&nbsp;&nbsp;
					<a href="javascript:void(0)" class="tip"
						title="Send this run to IMP." ng-click="runToIMP(run.Id)">IMP</a>

					<a href="javascript:void(0)" class="tip" ng-if="ifAnyJobHasSpecialInstructions(run)">
						<i class="glyphicon glyphicon-comment"></i>
						<span>
							<strong>Special Instructions</strong><br/>
							<div ng-repeat="job in run.Jobs | filter:{SpecialInstructions: '!!'} track by job.Id">
								<strong>Job {{job.Id}}:</strong> {{job.SpecialInstructions}}
							</div>
						</span>
					</a>

					 <a href="javascript:void(0)" class="tip" ng-if="ifAnyJobHasProductionInstructions(run)">
						<i class="glyphicon glyphicon-tasks"></i>
						<span>
							<strong>Production Instructions</strong><br/>
							<div ng-repeat="job in run.Jobs | filter:{ProductionInstructions: '!!'} track by job.Id">
								<strong>Job {{job.Id}}:</strong> {{job.ProductionInstructions}}
							</div>
						</span>
					</a>

				</td>
				<td title="{{::run.jobnr}}">{{::run.Jobs.length}}{{::run.IsHalfBC}}</td>
				<td>{{::run.Slots}}</td>
				<td class="nowrap">{{::run.Stock}}</td>
				<td>{{::run.Size}}</td>
				<td>-</td>
				<td>{{::run.Sides}}</td>
				<td>{{::run.Cello}}</td>
				<td style="border-right:0px;">{{::run.Manual}}</td>
			</tr>
			<tr ng-if="run.Open" ng-repeat="job in run.Jobs" class="{{::job.searchedCss}} {{::job.uneffectedCss}} {{::job.Health}}">
				<td>&nbsp;</td>
				<td>&nbsp;</td>
				<td ng-if="run.Enable"><input type="checkbox" ng-model="job.selected" ng-click="SetRunJobCtrlClick(run, $index)" /></td>
				<td ng-if="!run.Enable">&nbsp;</td>
				<td class="nowrap">{{::job.Date}}</td>
				<td class="nowrap">{{::job.RequiredDate}}  {{::(job.Template.search('NDD') != -1 ? "NDD": "") }} {{::(job.Template.search('SDD') != -1 ? "SDD": "") }}</td>
				<td class="bold nowrap">{{::job.TimeTillDispatch}}</td>
				<td class="nowrap">
					<span ng-style="{color: job.StatusCss}">{{::job.Status}}</span>
				</td>
				<td class="nowrap"></td>
				<td class="bold nowrap">
					<a class="td180 nowrap id-link" ui-sref="staff.order.job({orderId: job.OrderId, jobId: job.Id})" target="_blank">
						{{::job.JobNr}}</a>


						<a href="javascript:void(0)" class="tip"
						title="Nominate this job for DPC production."   ng-click="jobToDPC(job.Id)">DPC</a>

						<a href="javascript:void(0)" class="tip"
						title="Send this job to IMP."   ng-click="jobToIMP(job.Id)">IMP</a>

						<a href="javascript:void(0)" class="tip" ng-if="::job.SpecialInstructions">
							<i class="glyphicon glyphicon-comment"></i>
							<span><strong>Special Instructions</strong><br/>{{::job.SpecialInstructions}}</span>
						</a>
						<a href="javascript:void(0)" class="tip"  ng-if="::job.ProductionInstructions">
							<i class="glyphicon glyphicon-tasks"></i>
							<span><strong>Production Instructions</strong><br/>{{::job.ProductionInstructions}}</span>
						</a>
					
				</td>
				<td class="td180">
					<span style="color:red; font-weight:bold" ng-if="job.IsRestart">(Restart)</span> {{::job.Job}}
				</td>
				<td>{{::job.Slots}}</td>
				<td class="nowrap">{{::job.Stock}}</td>
				<td class="nowrap">
					{{::job.Size}}
				</td>
				<td>{{::job.Quantity}}</td>
				<td>{{::job.Sides}}</td>
				<td class="nowrap">{{::job.Cello}}</td>
				<!--<td style="border-right:0px;width:2px">&nbsp;</td>-->
			</tr>
		</tbody>
	</table><br />
	<div class="buttons-large border-end">
		<a class="search" ng-if="allowOperation" ng-click="MoveToNewRun()">Add jobs to a new run</a>
	</div>


</div>
