
appStaff = angular.module('app.staff')

aborter = {promise : {}}

appStaff.factory 'Customers', ['$resource', ($resource) ->
    url = 'api/Staff/Customers/:Id'
    $resource url, { Id: '@Id' },
        'update': method: 'PUT'
        'query':
            method: 'GET'
            isArray: false
            cancellable: true
        'patch':
            method: 'PATCH'
            headers: {'Content-Type': 'application/json-patch+json'}
]

appStaff.controller 'StaffCustomersListController', [
    '$scope','lepApi2',	 'Customers', '$state', 'Utils', '$http', '$sessionStorage', '$q', '$timeout'
    ($scope, lepApi2,    Customers,   $state,    Utils,   $http,   $sessionStorage, $q, $timeout ) ->

        lepApi2.get("staff/Customers/CustomerLookups", null, true).then (r) ->
            $scope.CustomerProductPricing = r.CustomerProductPricing
            $scope.CustomerFreightPricing = r.CustomerFreightPricing
            $scope.CustomerStatus         = r.CustomerStatus
            $scope.Franchise              = r.Franchise
            $scope.SalesConsultant        = r.SalesConsultant
            $scope.BusinessTypes          = r.BusinessTypes
            $scope.RegionLep              = r.RegionLep



        cs = "staffCustomersList"
        $sessionStorage.search        = $sessionStorage.search        || {SystemAccess: 'true'}
        $sessionStorage.searchresults = $sessionStorage.searchresults || {}

        $scope.vm = angular.copy($sessionStorage.search[cs]) || {Page: 1}
        $scope.r       = $sessionStorage.searchresults[cs] || {}


        $scope.clear = () -> $scope.vm = {Page:1, SystemAccess: 'true'}
        cc = null

        $scope.search = () ->
            if cc then cc.$cancelRequest()
            $sessionStorage.search[cs] = angular.copy  $scope.vm
            cc = Customers.query $scope.vm, (r) ->
                $scope.r = r
                $sessionStorage.searchresults[cs] = r

        $scope.searchDownload = () ->
            #$scope.search()
            lepApi2.download('/api/Staff/Customers/csv', $scope.vm)

        $scope.toggleSort = (sort) ->
            $scope.vm.SortDir =  sort == $scope.vm.SortField and !$scope.vm.SortDir
            $scope.vm.SortField = sort

        $scope.goPage = (p) ->
            $scope.vm.Page = p

        $scope.$watch 'vm', (x,y) ->
            if angular.equals(x,y)
                return
            $scope.search()
        , true

        $scope.newOrder = (custId) ->
            lepApi2.post("Orders/NewOrder", custId).then (d) ->
                $state.go('staff.order.job', {orderId: d.Id, jobId: 0})

        $scope.OnHoldExport = () ->
            lepApi2.download("/api/Staff/Customers/OnHoldExport")

        $scope.OnHoldImport = () ->
            Utils.browseForFile().then (file) ->
                if !file then return
                formData = new FormData
                formData.append 'csv', file.file

                $http(
                    method          : 'POST'
                    url             : "/api/staff/customers/onhold"
                    headers         : {'Content-Type': undefined}
                    transformRequest: angular.identity
                    data: formData
                ).then((data, status, headers, config) ->
                        toastr.info('success!')
                    , (data, status, headers, config) ->
                        alert 'failed!'
                )
        return @
    ]



appStaff.controller 'StaffCustomersViewController', [
    '$scope','lepApi2', '$stateParams', 'Customers', 'Utils', '$http', '$state', 'templateTree', 'JobService', 'ngDialog','cfpLoadingBar', '$rootScope',
    ($scope, lepApi2,    $stateParams,   Customers,   Utils,   $http,   $state,   templateTree, JobService, ngDialog, cfpLoadingBar,  $rootScope) ->
        $scope.vis = {}
        $scope.vm = {} #important
        $scope.logo = ""

        id = $stateParams.id
        url = 'staff/Customers'


        lepApi2.get("#{url}/CustomerLookups", null, true).then (r) ->
            $scope.CustomerProductPricing = r.CustomerProductPricing
            $scope.CustomerFreightPricing = r.CustomerFreightPricing
            $scope.CustomerStatus         = r.CustomerStatus
            $scope.Franchise              = r.Franchise
            $scope.SalesConsultant        = r.SalesConsultant
            $scope.BusinessTypes          = r.BusinessTypes


        #lepApi2.get("#{url}/CustomerProductPricing", null, true).then (r) -> $scope.CustomerProductPricing = r
        #lepApi2.get("#{url}/CustomerFreightPricing", null, true).then (r) -> $scope.CustomerFreightPricing = r
        #lepApi2.get("#{url}/CustomerStatus"        , null, true).then (r) -> $scope.CustomerStatus         = r
        #lepApi2.get("#{url}/Franchise"             , null, true).then (r) -> $scope.Franchise              = r
        #lepApi2.get("#{url}/SalesConsultant",        null, true).then (r) -> $scope.SalesConsultant        = r
        #lepApi2.get("#{url}/BusinessTypes",          null, true).then (r) -> $scope.BusinessTypes          = r


        $scope.allTemplatesName = []
        JobService.getTemplates().then (d) ->
            $scope.allTemplatesName = d

        $scope.load = (id) ->
            lepApi2.get( "#{url}/#{id}").then (r) ->
                $scope.vm  = r

                if r.Id == 0
                    r.IsEnabled = true

                $scope.logo = "/api/staff/customers/#{id}/logo?r=" + (+new Date())
                lepApi2.get( "#{url}/#{id}/notes").then (r2) ->
                    $scope.CustomerNotes = r2.notes
                    $scope.Last10Orders  = r2.last10Orders

                    #if window.location.href.indexOf('notes=true') > -1
                    #    angular.element('#tab7').click()


        $scope.load(id)

        $scope.save = () ->
            Customers.update({Id: id}, $scope.vm, (value, responseHeadersFn) ->
                id = parseInt(responseHeadersFn('Id'), 10)
                toastr.info( "Success")
                $state.go('staff.customers-view', {id: id},   {reload: true})
            )

        $scope.resetPassword = () ->
            url = "/api/Users/<USER>/password/reset"
            newpassword =  $scope.vm.Password1
            $http.post(url, JSON.stringify(newpassword)).then ()->
                if newpassword && newpassword.length > 0
                    toastr.info("Customer's password has been set to the new password.")
                else
                    toastr.info("Customer's password has been reset to a randomly generated password and the new password has been emailed to client.")


        $scope.uploadCustomerLogo = () ->
            Utils.browseForFile().then (file) ->
                if !file then return
                formData = new FormData
                formData.append 'csv', file.file

                $http(
                    method          : 'POST'
                    url             : "/api/staff/customers/#{id}/logo"
                    headers         : {'Content-Type': undefined}
                    transformRequest: angular.identity
                    data: formData
                ).then((data, status, headers, config) ->
                        toastr.info('success!')
                        $state.reload()
                    , (data, status, headers, config) ->
                        alert 'failed!'
                )


        $scope.makePostalSameAsBilling = (b) ->
            if b
                $scope.vm.PostalAddress = angular.copy( $scope.vm.BillingAddress )

        $scope.mergeCustomer = (id, toCust) ->
            lepApi2.post("staff/customers/#{id}/merge",JSON.stringify(toCust)).then (r) ->
                $state.go('staff.customers-view', {id: r.Id})

        myobAccounts = [
                    {Id: '8-1050', Name: 'Freight recovered'}
                    {Id: '4-1010', Name: 'Discounts allowed'}
                    {Id: '8-1100', Name: 'Other Income'}
                    ]

        $scope.createInvoice = () ->
            dialog =  ngDialog.open {
            template   : 'staff/customers/inv-dlg.html'
            controller : [ '$scope', ($scope) ->
                    $scope.cn = {
                        DateCreated: new Date() # Set default date to today
                    }
                    $scope.myobAccounts = myobAccounts
                    $scope.returnOrderCredit = () ->
                        price = parseFloat($scope.cn.Amount)
                        $scope.cn.Amount = price
                        $scope.cn.GST = price * 0.10
                        $scope.cn.Total = price + price * 0.10
                        # Ensure DateCreated is properly formatted
                        if $scope.cn.DateCreated
                            $scope.cn.DateCreated = new Date($scope.cn.DateCreated)
                        $scope.closeThisDialog($scope.cn)
                ]

            }
            dialog.closePromise.then (r) ->
                rv = r.value
                if !rv then return
                url = "orders/Customer/#{id}/credit"
                lepApi2.post(url, rv).then (r) ->
                        toastr.info "Refund created"
            return

        $scope.downloadCustomerExtraFile = lepApi2.downloadCustomerExtraFile

        $scope.emailExtraFile = (customerId, fileName ) ->
            x = "document/customer/#{customerId}/extrafiles/Email/#{fileName}"
            lepApi2.post(x).then (r) ->
                toastr.info "Email Sent"


        $scope.addCustomerNote = (note, nid) ->
            payload = {}
            payload.NoteText = note
            payload.Id = nid || 0
            formData = new FormData
            formData.append 'request',  angular.toJson(payload, true)

            if $scope.noteAttachment
                formData.append 'na', $scope.noteAttachment.file

            url = "/api/staff/customers/#{id}/notes"
            $http(
                    method          : 'POST'
                    url             : url
                    headers         : {'Content-Type': undefined}
                    transformRequest: angular.identity
                    data            : formData
                    uploadEventHandlers:
                        progress: (e) ->
                            if (e.lengthComputable)
                                px = (e.loaded / e.total)
                                cfpLoadingBar.set(px)
                                $rootScope.$broadcast("percentage",  Math.floor(px * 100))

                ).then((result, status, headers, config) ->
                    r2 = result.data
                    $scope.vm.NoteText = ""
                    $scope.noteAttachment = null
                    $scope.CustomerNotes = r2
                    return
                ,(data, status, headers, config) ->
                    $rootScope.$broadcast("percentage",  -1)
                    return
                )


        $scope.editNote = (nid, ntext) ->

            bootbox.prompt({
                title: "Edit note",
                inputType: 'textarea',
                value: ntext
                callback: (result) ->
                    $scope.addCustomerNote(result, nid)
            })


        $scope.downloadNoteDcoment = (noteId) ->
            lepApi2.download("api/staff/Customers/noteattachment/#{noteId}",{})

        $scope.pathToRecord = (note) ->
            "/recordings/" + moment(note.CreatedOn).format("YYYY-MMM") + "/" + note.PhoneRecordId + ".mp3"

        return
    ]



appStaff.controller 'StaffCustomersViewWLController', [
    '$scope','lepApi2',	'enums', '$stateParams', 'Customers', 'Utils', '$http', '$state', 'customer',
    ($scope, lepApi2,   enums,    $stateParams,   Customers,   Utils,   $http,   $state,   customer) ->
        $scope.vis = {}
        $scope.vm = angular.copy(customer) #important
        id = $stateParams.id

        $scope.save = () ->
            Customers.update({Id: id}, $scope.vm, (value, responseHeadersFn) ->
                id = parseInt(responseHeadersFn('Id'), 10)
                toastr.info( "Success")
                $state.reload()
            )



        return
    ]

