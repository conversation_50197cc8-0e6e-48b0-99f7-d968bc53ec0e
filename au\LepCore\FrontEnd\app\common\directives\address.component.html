

<div class="row form-horizontal  form-group-sm " ng-form="addressForm">
	<fieldset ng-disabled="noteditable">
		<!--<pre>{{vm|json}}</pre>-->

		<div class="col-xs-12">

			<!--<div class="form-group ">

				<div class="col-xs-12   ">
					<gogleaddress ng-model="fullAddress"
											   zip="vm.Postcode"
											   country="vm.Country"
											   state="vm.State" city="vm.City"
											   address1="vm.Address1">
					</gogleaddress>
				</div>
			</div>-->

			<div class="form-group">
				<label class="col-xs-3 control-label" for="country">Country</label>
				<div class="col-xs-7">
					<select  name="country1" class="form-control input"
							ng-model="vm.Country" ng-required="!noteditable" mvs>

						<option value="">-- Select --</option>
						<option value="AU">Australia</option>
						<option value="NZ" ng-if="$root.globals.IsStaff">New Zealand</option>
					</select>
				</div>
			</div>


			<!--<div class="form-group   ng-class:{'has-error': addressForm.country.$invalid}">
				<label class="col-xs-3 control-label" for="country">Country</label>
				<div class="col-xs-7   ">
					<select name="country" class="form-control input"
							ng-model="vm.Country" ng-required="!noteditable">

						<option value="">-- Select --</option>
						<option value="AU">Australia</option>
						<option value="NZ">New Zealand</option>
					</select>
				</div>
			</div>-->


			<div class="form-group   ng-class:{
				 'pristine': addressForm.postCode.$pristine,
				 'ng-invalid ng-invalid-required': addressForm.postCode.$error.required
				 }">
				<label class="col-xs-3 control-label" for="postcode">Postcode</label>
				<div class="col-xs-7   ">


					<div angucomplete-alt type="text" id="postcode" input-name="postCode" input-class="form-control input nospinner" placeholder="Search postcode"
						 pause="400" disable-input="noteditable"
						 selected-object="Postcode" remote-url="/api/postcode/getsuburbs?postcode=" remote-url-data-field="Data" title-field="PostCode"
						 description-field="Suburb" field-required="!noteditable" field-required-class="required" min-length="3" match-class="highlight" initial-value="vm.Postcode"
						 ng-paste="dontPastePostCode($event);"  input-changed="inputChanged"></div>
				</div>
			</div>

			<div class="form-group ">
				<label class="col-xs-3 control-label" for="town">Suburb</label>
				<div class="col-xs-7    readonly-text ">
					<!--<input name="town" type="text" class="form-control input" ng-model="vm.City" ng-required="true"  ng-disabled="true"
					   title="Enter post code and this will be automatically filled in" />-->
					{{vm.City}}
				</div>
			</div>

			<div class="form-group ">
				<label class="col-xs-3 control-label" for="state">State</label>
				<div class="col-xs-7  readonly-text">

					<!--<input name="state" type="text" class="form-control input" ng-model="vm.State" ng-required="true"  ng-disabled="true"
					   title="Enter post code and this will be automatically filled in"  />-->
					{{vm.State}}
				</div>
			</div>

			<div class="form-group">
				<label class="col-xs-3 control-label" for="line-1">Line 1</label>
				<div class="col-xs-7   ">
					<input name="line1" type="text" class="form-control input" ng-model="vm.Address1"
						   ng-required="!noteditable" ng-maxlength="30" mvs />
					<div class="redAlert bold" ng-if="addressForm.line1.$error.maxlength">* max 30 charecters</div>

				</div>
			</div>


			<div class="form-group ">
				<label class="col-xs-3 control-label" for="line-2">Line 2</label>
				<div class="col-xs-7   ">
					<input name="line2" type="text" class="form-control input" ng-model="vm.Address2" ng-maxlength="30" />
					<div class="redAlert bold"  ng-if="addressForm.line2.$error.maxlength">* max 30 charecters</div>
				</div>
			</div>


			<!-- <div class="form-group ">
				<label class="col-xs-3 control-label" for="line-3">Line 3 </label>
				<div class="col-xs-7   ">
					<input name="line3" type="text" class="form-control input" ng-model="vm.Address3" maxlength="80"  readonly />
					<div class="help-block  redAlert">Line 3 is being removed, please use Lines 1 & 2 only</div>
				</div>
			</div> -->
		</div>
	</fieldset>
</div>
<!--{{vm | json }}
<pre>{{addressForm | json}}</pre>


	-->
