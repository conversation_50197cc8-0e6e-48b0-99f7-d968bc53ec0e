﻿<div class="container your-settings">
	<form class="form-horizontal">
		<div leppane="Basic Info" visible="true">
			<div class="form-group form-group-lg">
				<label class="control-label col-xs-3">Customer * </label>
				<div class="col-xs-7 form-control-static">
					{{vm.Name}}

				</div>
			</div>

		
			<div class="form-group">
				<label class="control-label col-xs-3">Main email</label>
				<div class="col-xs-7">

					<input class="form-control " type="text" ng-model="vm.Email" ng-required="true" />
				</div>
			</div>


		</div>
		 

		<div leppane="Contacts List" visible="true">
			<div lep-contact-list lep-contact-list-details-sm contacts="vm.Contacts"></div>
		</div>

		<div leppane="Address" visible="true">
			<div class="row">
				<div class="col-sm-6">
					<h5>Billing address *</h5>

					<div class="form-group">
						<div class="col-xs-4 control-label" for="order-no"></div>
						<div class="col-xs-8">
							<label class="checkbox">
								&nbsp;
							</label>
						</div>
					</div>
					<div address-details address="vm.BillingAddress"></div>
				</div>



				<div class="col-sm-6">
					<h5>Delivery address</h5>
					<div class="form-group">
						<div class="col-xs-offset-3 col-xs-8">
							<label class="checkbox">
								<input name="checkbox" type="checkbox" ng-model="vm.PostalIsBilling" ng-change="makePostalSameAsBilling(vm.PostalIsBilling)" />
								Delivery address is same as billing address
							</label>
						</div>
					</div>
					<div>
						<div address-details address="vm.PostalAddress" noteditable="vm.PostalIsBilling"></div>
					</div>
				</div>


			</div>
		</div>

		<div leppane="Login" visible="true">
			<div class="form-horizontal">
				<div class="form-group">
					<label class="control-label col-xs-3">User name</label>
					<div class="col-xs-7">
						<input class="form-control" type="text" ng-model="vm.Username" ng-disabled="true" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label col-xs-3">Password</label>
					<div class="col-xs-7">
						<input class="form-control" type="text" ng-model="vm.Password" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label col-xs-3">Confirm Password</label>
					<div class="col-xs-7">
						<input class="form-control" type="text" ng-model="vm.Password1" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label col-xs-4"></label>
					<div class="col-xs-5">
						<button class="btn btn-default" type="button" ng-click="updatePassword(vm.Password)" ng-disabled="!vm.Password || !(vm.Password == vm.Password1)">Update password</button>
					</div>
				</div>
			</div>
		</div>


	
		<div class="row" style="margin-top:10px;">
			<div class="col-xs-12">

				<div class="row">
					<div class="form-actions pull-right">
						<button type="submit" class="btn btn-default" ng-click="save()"> <i class="glyphicon glyphicon-floppy-save"></i> Save my details</button>
					</div>
				</div>
			</div>
		</div>
	</form>
</div>
