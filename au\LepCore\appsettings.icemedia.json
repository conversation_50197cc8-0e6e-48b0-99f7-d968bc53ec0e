{
  "IMP": {
    "HotfolderPath": "\\\\ppp01\\HotFolderRoot\\MyLEP"
  },
  "TestBox": true,
  "ApplicationInsights": {
    "InstrumentationKey": ""
  },
  "HolidayMode": false,
  "Logging": {
    "IncludeScopes": false,
    "LogLevel": {
      "Default": "Error",
      "System": "Error",
      "Microsoft": "Error",
      "Microsoft.AspNetCore.Mvc": "Error",
      "Microsoft.AspNetCore.Authentication": "Error",
      "Microsoft.AspNetCore.Routing.RouteBase": "Error",
      "Microsoft.AspNetCore.Server.Kestrel": "Error",
      "NHibernate": "Warn",
      "NHibernate.SQL": "DEBUG",
      "Microsoft.AspNetCore.SignalR": "None",
      "Microsoft.AspNetCore.Hosting": "None",
      "Microsoft.AspNetCore.StaticFiles": "None"
    }
  },
  "jobboard": {
    "messagequeue": ".\\Private$\\leponline.jobboardqueue"
  },

  "CustomerLogoDirectory": "C:\\lepdata\\customerlogos",
  "DataDirectory": "C:\\lepdata",
  "OldDataDirectory": "c:\\lepdata\\oldorders",
  "DataDirectoryPC": "C:\\lepdata",
  "DataDirectoryMac": "C:\\lepdata",

  //"AbsolutePathURL": "http://lepold.localtest.me",

  "lepcrm.webservice.url": "http://winston.internal.lepcolourprinters.com.au:1000/MSCRMService.asmx",
  "compdata.webservice.url": "http://harry:8089/iFreightChargeEnquiryService.svc",

  "AbsolutePathURL": "http://icemedia",
  "job.option.csv.folder": "C:\\inetpub\\LORDTEST4\\~\\app_data\\job",

    "reports": {
        "LepQuote":  "C:\\LepData\\t1.frx"
    },

  "email": {
    "sendMail": false,
    "server"  : "smtp.office365.com",
    "port"    : "587",
    "username": "<EMAIL>",
    "password": "11_Sandalwood",
  },
  "print.messagequeue": ".\\Private$\\leponline.messageprintqueue",
  "jobboard.messagequeue": ".\\Private$\\leponline.jobboardqueue",

  "Nhibernate": {
    //"Con": "Data Source=srv03; user id=sa; password=*************; Initial Catalog=PRD_AU;MultipleActiveResultSets=true"
 	//"Con": "Data Source=newman; user id=sa; password=*************; Initial Catalog=LEP_DEV_20190617;MultipleActiveResultSets=true"
	"Con": "Data Source=newman; user id=sa; password=*************; Initial Catalog=PRD_AU_2020_03_30;MultipleActiveResultSets=true"

 },

  "SupplyMaster": {
    "Con": "Data Source=*************; user id=sa; password=*************; Initial Catalog=SupplyMaster;MultipleActiveResultSets=true"
  }
  ,

  "Seq": {
	"ServerUrl": "http://localhost:5341",
	"MinimumLevel": "Trace",
	"LevelOverride": {
		"Microsoft": "Warning"
	}
  },

  "FreightProvider": "SmartFreight", // or  CompData "current"

    "Labels":{
        "LogoLabel"      : "d:\\LEPDATA\\Labels\\LogoLabel.frx",
        "PayMeLabel"     : "d:\\LEPDATA\\Labels\\PayMeLabel.frx",
        "FillingLabel"   : "d:\\LEPDATA\\Labels\\FillingLabel.frx",
        "SampleLabel"    : "d:\\LEPDATA\\Labels\\SampleLabel.frx",
        "AddressA4Label" : "d:\\LEPDATA\\Labels\\AddressA4Label.frx",
        "PickupLabel"    : "d:\\LEPDATA\\Labels\\PickupLabel.frx",
    },
  "AutomatedArtworkCheck": {
	  "Enabled": false,
	  "Timeout": "00:00:40",
	  // "Method": "DoPitStopViaCommandLine",
	  "Method":   "DoPitStopViaHotFolder",

	  "DoPitStopViaCommandLine": {
		  "path": "C:\\Program Files\\Enfocus\\Enfocus PitStop Server 13\\PitStopServerCLI.exe",
		  "mutator": "C:\\LEPDATA\\LEP Check 2013-1.ppp"
	  },

	  "DoPitStopViaHotFolder": {
		  "input":  "\\\\Henry\\hotfolder\\Input Folder" ,
		  "output": "\\\\Henry\\hotfolder\\Output Folder"
	  }
  },

    "Dispatchers": [
        {
            "Name": "FG-DISTRIB-02",
            "DispatchFacility": "FG"
        },
        {
            "Name": "FG-DISTRIB-03",
            "DispatchFacility": "FG"
        },
        {
            "Name": "PM-DISTRIB-01",
            "DispatchFacility": "PM"
        },
        {
            "Name": "PM-BINDERY-05",
            "DispatchFacility": "PM"
        }
    ],

    "SmartFreight": {

        "Url" : {
            "SFOv1": "http://api-r1.smartfreight.com/api/soap/classic",
            "DeliveryOptions": "http://api-r1.smartfreight.com/api/soap/deliveryoptions"
        },

        "Senders": {
            "LEPQLD": {
                "Id": "NWM",
                "Key": "RqB0fJOapwP79AVDdzF5Pgg7fESwF4GxHQ"
            },
            "TESTQLD": {
                "Id": "RqB0fJOapwP79AVDdzF5Pgg7fESwF4GxHQ",
                "Key": ""
            },

            "LEPVIC": {
                "Id": "NGU",
                "Key": "JoIJ2bXndxCk0V3ZfkM6VxKuzv1qgEAiA0"
            },

            "TESTVIC": {
                "Id": "NGU",
                "Key": "JoIJ2bXndxCk0V3ZfkM6VxKuzv1qgEAiA0"
            }
        },


    },
}
