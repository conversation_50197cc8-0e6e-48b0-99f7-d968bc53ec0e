﻿

<div leppane="NCR {{rrLabel}}  Job">


    <div class="row">
        <div class="col-sm-6">
			<div class="form-horizontal">

				<div class="form-group">
					<label class="col-xs-4 control-label">Original Job #</label>
					<div class="col-xs-8  form-control-static">
						{{job.Id}}
					</div>
				</div>

				<div class="form-group">
					<label class="col-xs-4 control-label" for="priceLbl">Original Price</label>
					<div class="col-xs-8  form-control-static">
						{{job.Price | currency}}
					</div>
				</div>


				<div class="form-group">
					<label class="col-xs-4 control-label" for="reprintCostTextBox">{{rrLabel}}  Cost</label>
					<div class="col-xs-8">
						<input class="form-control" id="reprintCostTextBox" maxlength="6" ng-model="reprintReqVm.ReprintCost" rg-required="true" />
					</div>
				</div>

				<div class="form-group">
					<label class="col-xs-4 control-label" for="invoicePrice">Invoice Price</label>
					<div class="col-xs-8">
						<input class="form-control" id="invoicePrice" ng-model="reprintReqVm.InvoicePrice" rg-required="true" />
					</div>
				</div>



				<div class="form-group">
					<label class="col-xs-4 control-label" for="quantity">Quantity</label>
					<div class="col-xs-8">
						<input class="form-control" id="quantity" type="text" ng-model="reprintReqVm.Quantity" rg-required="true" />
					</div>
				</div>




				<div class="form-group">
					<label class="col-xs-4 control-label" for="ncrNo">NCR No</label>
					<div class="col-xs-8">
						<input class="form-control" id="ncrNo" type="text" ng-model="reprintReqVm.NcrNo" rg-required="true" />
					</div>
				</div>


				<div class="form-group">
					<label class="col-xs-4 control-label" for="copyChk"> Copy Preflight Artwork</label>
					<div class="col-xs-8">
						<input class="checkbox" type="checkbox" id="copyChk" checked="checked" ng-model="reprintReqVm.CopyPreflight" rg-required="true" />
					</div>
				</div>

				<!--<div class="form-group">
					<label class="col-xs-4 control-label" for="dispatchChk">
						Original was dispatched
					</label>
					<div class="col-xs-8">
						<input class="checkbox" type="checkbox" id="dispatchChk" checked="checked" ng-model="reprintReqVm.HasDispatch" rg-required="true" />
					</div>
				</div>-->
			</div>
        </div>

        <div class="col-sm-6">
            <div class="form-horizontal0">


                <div class="form-group">
                    <label class="col-xs-12 control-label" for="resultTxt"> {{rrLabel}}  Special Instructions</label>
                    <div class="col-xs-12">
                        <textarea id="resultTxt" class="form-control" cols="45" rows="4" ng-model="reprintReqVm.Result"> </textarea>
                    </div>

                </div>


                <div class="form-group">
                    <label for="predefinedReasonSelect" class="col-xs-12 control-label">
                        {{rrLabel}} Production Instructions
                    </label>

                    <div class="col-xs-12">
                        <select id="predefinedReasonSelect" class="form-control" ng-model="reprintReqVm.PredefinedReason" rg-required="true">
                            <option value="" label="" />
                            <option value="1.1 Imposition error" label="1.1 Imposition error" />
                            <option value="1.2 Pre-flight error" label="1.2 Pre-flight error" />
                            <option value="1.3 Wrong Placement" label="1.3 Wrong Placement" />
                            <option value="1.4 Business Process" label="1.4 Business Process" />
                            <option value="1.5 Prepress Other (including software issues)" label="1.5 Prepress Other (including software issues)" />
                            <option value="2.1 Set Off" label="2.1 Set Off" />
                            <option value="2.2 Colour Variation" label="2.2 Colour Variation" />
                            <option value="2.3 Hickey/Scratched plate" label="2.3 Hickey/Scratched plate" />
                            <option value="2.4 Business Process" label="2.4 Business Process" />
                            <option value="2.5 Printing Other" label="2.5 Printing Other" />
                            <option value="3.1 Set Off/Not Dry" label="3.1 Set Off/Not Dry" />
                            <option value="3.2 Folding" label="3.2 Folding" />
                            <option value="3.3 Cutting" label="3.3 Cutting" />
                            <option value="3.4 Cello Issue" label="3.4 Cello Issue" />
                            <option value="3.5 Letterpress Issue" label="3.5 Letterpress Issue" />
                            <option value="3.6 Business Process" label="3.6 Business Process" />
                            <option value="3.7 Bindery Other" label="3.7 Bindery Other" />
                            <option value="4.1 Round Corner/Drill Issue" label="4.1 Round Corner/Drill Issue" />
                            <option value="4.2 Insufficient Packing" label="4.2 Insufficient Packing" />
                            <option value="4.3 Not Packed to instructions/SOP" label="4.3 Not Packed to instructions/SOP" />
                            <option value="4.4 Business Process" label="4.4 Business Process" />
                            <option value="4.5 Packing Other" label="4.5 Packing Other" />
                            <option value="5.1 Sent to wrong customer/address" label="5.1 Sent to wrong customer/address" />
                            <option value="5.2 Business Process" label="5.2 Business Process" />
                            <option value="5.3 Despatch Other" label="5.3 Despatch Other" />
                            <option value="6.1 Damaged Goods" label="6.1 Damaged Goods" />
                            <option value="6.2 Lost Goods" label="6.2 Lost Goods" />
                            <option value="6.3 Business Process" label="6.3 Business Process" />
                            <option value="6.4  Transport Other" label="6.4  Transport Other" />
                            <option value="7.1 Incorrect/no instructions given for Job Bags" label="7.1 Incorrect/no instructions given for Job Bags" />
                            <option value="7.2  Business Process" label="7.2  Business Process" />
                            <option value="7.3 Office Other" label="7.3 Office Other" />
                            <option value="8  Other" label="8  Other" />
                            <option value="9.1 Digital - Printing" label="9.1 Digital - Printing" />
                            <option value="9.2 Digital - Cutting" label="9.2 Digital - Cutting" />
                            <option value="9.3 Digital - Folding" label="9.3 Digital - Folding" />
                            <option value="9.4 Digital - Collating" label="9.4 Digital - Collating" />
                            <option value="9.5 Digital - Stitching" label="9.5 Digital - Stitching" />
                            <option value="9.6 Digital - Business Process" label="9.6 Digital - Business Process" />
                            <option value="9.7 Digital - Other" label="9.7 Digital - Other" />
                        </select>
                    </div>
                </div>



                <div class="form-group">
                    <div class="col-xs-12 ">
                        <textarea class="form-control" id="reasonTxt" cols="45" rows="4" ng-model="reprintReqVm.Reason"></textarea>
                    </div>
                </div>

            </div>
        </div>


    </div>

    <div class="row">
        <br /><br /><br />
        <div class="col-xs-12">
            <div class="pull-right">
                <a class="btn"   ui-sref="staff.order.job({orderId:job.OrderId, jobId: job.Id})"> <i class="glyphicon glyphicon-chevron-left"></i> back to Job</a >
                <button class="btn  btn-default"  ng-show="rrLabel == 'Reprint'" ng-click="reprintRestartJob('reprint')" >Reprint job </button>
				<button class="btn btn-default"   ng-show="rrLabel == 'Restart'"      ng-click="reprintRestartJob('restart')" > Restart job </button>
            </div>


        </div>

    </div>


</div>
