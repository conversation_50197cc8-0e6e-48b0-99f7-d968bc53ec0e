const { src, dest, series, parallel, watch } = require('gulp');
const del = require('del');
const browserSync = require('browser-sync').create();
const reload = browserSync.reload;
const gulpSass = require('gulp-sass');
const sourcemaps = require('gulp-sourcemaps');
const concat = require('gulp-concat');
const uglify = require('gulp-uglify');
const rename = require('gulp-rename');
const order = require('gulp-order');
const mainBowerFiles = require('main-bower-files');
const jshint = require('gulp-jshint');
const htmlhint = require('gulp-htmlhint');
const htmlmin = require('gulp-htmlmin');
const ngHtml2Js = require('gulp-ng-html2js');
const nodemon = require('gulp-nodemon');

// Define paths
const paths = {
  coffee_scripts: 'app/**/*.coffee',
  coffee_scripts_e2e: 'e2e/**/*.coffee',
  scripts: 'app/**/*.js',
  styles: ['./app/css/*.css', './app/css/*.scss'],
  images: './images/**/*',
  index: ['./app/index.html', './app/index_.html', './app/index2.html', './app/index_r1.html', './app/staff.html', './app/index_wl.html'],
  partials: ['app/**/*.html', '!app/index*.html', '!./app/staff.html', '!./app/index_wl.html'],
  appfonts: './fonts/*',
  distDev: './../wwwroot',
  distProd: './../wwwroot',
  distScriptsProd: './../wwwroot/scripts',
  scriptsDevServer: 'devServer/**/*.js'
};

// Task to clean the development directory
function cleanDev() {
  return del([paths.distDev]);
}

// Task to clean the production directory
function cleanProd() {
  return del([paths.distProd]);
}

// Task to validate html partials
function validatePartials() {
  return src(paths.partials)
    .pipe(htmlhint())
    .pipe(htmlhint.failOnError());
}

// Task to validate index.html
function validateIndex() {
  return src(paths.index)
    .pipe(htmlhint())
    .pipe(htmlhint.failOnError());
}

// Task to build partials for development
function buildPartialsDev() {
  return validatePartials()
    .pipe(dest(paths.distDev));
}

// Task to convert partials to JavaScript using html2js for development
function convertPartialsToJsDev() {
  return validatePartials()
    .pipe(htmlmin({ collapseWhitespace: true, removeComments: true }))
    .pipe(ngHtml2Js({ moduleName: "app" }))
    .pipe(concat('templates.min.js'))
    .pipe(dest(paths.distDev));
}

// Task to validate dev server scripts
function validateDevServerScripts() {
  return src(paths.scriptsDevServer)
    .pipe(jshint())
    .pipe(jshint.reporter('jshint-stylish'));
}

// Task to validate app scripts
function validateAppScripts() {
  return src(paths.scripts)
    .pipe(jshint())
    .pipe(jshint.reporter('jshint-stylish'));
}

// Task to build app scripts for development
function buildAppScriptsDev() {
  return validateAppScripts()
    .pipe(dest(paths.distDev));
}

// Task to build vendor scripts for development
function buildVendorScriptsDev() {
  return src(mainBowerFiles({ filter: jsFileFilter }))
    .pipe(order(defaultOrder))
    .pipe(concat('vendor.min.js'))
    .pipe(dest(paths.distDev + '/libs'));
}

// Task to build styles for development
function buildStylesDev() {
  return src(paths.styles)
    .pipe(sourcemaps.init())
    .pipe(gulpSass().on('error', gulpSass.logError))
    .pipe(sourcemaps.write('./maps'))
    .pipe(dest(paths.distDev + '/css'));
}

// Task to process images for development
function processImagesDev() {
  return src(paths.images)
    .pipe(dest(paths.distDev + '/images/'));
}

// Task to validate and inject sources into index.html for development
function buildIndexDev() {
  const orderedVendorScripts = buildVendorScriptsDev()
    .pipe(order(defaultOrder));

  const orderedAppScripts = buildAppScriptsDev()
    .pipe(order(defaultOrder));

  const scriptedPartialsDev = convertPartialsToJsDev();

  const appStyles = buildStylesDev();

  return validateIndex()
    .pipe(dest(paths.distDev))
    .pipe(inject(orderedVendorScripts, { relative: true, name: 'bower' }))
    .pipe(inject(scriptedPartialsDev, { relative: true, name: 'templates' }))
    .pipe(inject(orderedAppScripts, { relative: true }))
    .pipe(inject(appStyles, { relative: true }))
    .pipe(dest(paths.distDev))
    .pipe(reload({ stream: true }));
}

// Task to build a complete development environment
const buildAppDev = series(
  cleanDev,
  parallel(
    buildPartialsDev,
    convertPartialsToJsDev,
    validateDevServerScripts,
    validateAppScripts,
    buildAppScriptsDev,
    buildVendorScriptsDev,
    buildStylesDev,
    processImagesDev
  ),
  buildIndexDev
);

// Task to watch live changes to the development environment
function watchDev() {
  // Start nodemon to auto-reload the dev server
  nodemon({ script: 'server.js', ext: 'js', watch: ['devServer/'], env: { NODE_ENV: 'development' } });

  // Start browser-sync server
  browserSync.init({
    server: {
      baseDir: paths.distDev
    }
  });

  // Watch index
  watch(paths.index, buildIndexDev);

  // Watch app coffee scripts
  watch(paths.coffee_scripts, buildAppScriptsDev);

  // Watch app scripts
  watch(paths.scripts, buildAppScriptsDev);

  // Watch html partials
  watch(paths.partials, convertPartialsToJsDev);

  // Watch styles
  watch(paths.styles, buildStylesDev);
  watch("./app/css/**/*.*", buildStylesDev);

  // Watch images
  watch(paths.images, processImagesDev);
}

exports.cleanDev = cleanDev;
exports.cleanProd = cleanProd;
exports.validatePartials = validatePartials;
exports.validateIndex = validateIndex;
exports.buildPartialsDev = buildPartialsDev;
exports.convertPartialsToJsDev = convertPartialsToJsDev;
exports.validateDevServerScripts = validateDevServerScripts;
exports.validateAppScripts = validateAppScripts;
exports.buildAppScriptsDev = buildAppScriptsDev;
exports.buildVendorScriptsDev = buildVendorScriptsDev;
exports.buildStylesDev = buildStylesDev;
exports.processImagesDev = processImagesDev;
exports.buildIndexDev = buildIndexDev;
exports.buildAppDev = buildAppDev;
exports.watchDev = watchDev;

// Default task
exports.default = watchDev;
