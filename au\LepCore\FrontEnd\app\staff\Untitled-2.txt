
192.168.1.16     PC-RWLYFA
192.168.1.39     PCRGUXDA
192.168.1.72     PCGMAHXC




Update Job set Status = 'Dispatched' where Orderid in 
(SELECT        ConNote.OrderId
FROM            ConNote INNER JOIN
                         [Order] AS o ON ConNote.OrderId = o.Id
WHERE       
  o.Status <> 'Dispatched');


Update [Order] set Status = 'Dispatched' where id in 
(SELECT        ConNote.OrderId
FROM            ConNote INNER JOIN
                         [Order] AS o ON ConNote.OrderId = o.Id
WHERE       
  o.Status <> 'Dispatched')