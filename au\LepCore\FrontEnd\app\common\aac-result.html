﻿<style>
	.alert { margin-bottom: 2px; }
</style>
<div class="row" style="font-size: 16px;">
	<div class="col-md-12">
		<div class="modal-header">
			<h4 class="modal-title">Automated Artwork Check (AAC):</h4>
		</div>
		<div class="modal-body">

			<div class="row" ng-repeat="r in uploadResults">
				<div class="col-md-12">
					<div ng-if="r.AAC">
						The file <b>{{r.Name}}</b> you just uploaded has
						<div ng-if="r.AAC.errors.length" class="alert alert-danger bold">
							<div>Errors:</div>
							<ol class="error-reddish"><li ng-repeat="x in r.AAC.errors  track by $index">{{::x}}</li></ol>
							<u>Please fix these issues and upload the file again.</u>
						</div>

						<div ng-if="r.AAC.warnings.length" class="alert alert-warning" role="alert">
							<div>Warnings:</div>
							<ol class="warning"><li ng-repeat="x in r.AAC.warnings track by $index">{{::x}}</li></ol>

						</div>



						<div ng-if="r.AAC.fixes.length" class="alert alert-info" role="alert">
							<div class="help-block">
								Uploaded file had fixes applied to it. Download and verify fixes.
							</div>
							<div>Fixes:</div>
							<ol class="info"><li ng-repeat="x in r.AAC.fixes track by $index">{{::x}}</li></ol>
						</div>

						<button ng-click="downloadReport(r.Id, r.Name)" ng-hide="isWL"> Download report </button>

						<button ng-click="downloadPostAac(r.Id, r.Name)" ng-hide="isWL || r.AAC.fixes.length == 0">
							Download Fixed file
						</button>


					</div>

				</div>

			</div>
		</div>
		<div class="modal-footer">
			<div>

				<!--Cancel User will re upload-->
				<button class="btn" ng-click="closeThisDialog(false)">Cancel</button>
				&nbsp;&nbsp;
				<!--Proceed with fixed art-->
				<button class="btn" ng-click="closeThisDialog(true)"
						ng-if="(hasWarnings || hasFixes) && !hasErrors">
					<i class="glyphicon glyphicon-floppy-save"></i> Proceed with <span ng-if="hasFixes">using fixed</span> supplied artwork
				</button>
				&nbsp;&nbsp;

				<!--Proceed with whatever was uploaded-->
				<button class="btn" ng-click="closeThisDialog(2)" ng-if="!hasErrors && hasFixes">
					<i class="glyphicon glyphicon-floppy-save"></i> Don’t fix my artwork. Proceed with original supplied artwork
				</button>

			</div>

			<div class="st" style="text-align: left; font-size: 13px; line-height: 1; margin-top: 10px; ">


			</div>

		</div>

	</div>

</div>
