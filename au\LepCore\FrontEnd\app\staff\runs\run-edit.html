<!--<div ng-show="run.Visibility.overloadDiv" id="overloadDiv" class="alert alert-danger">
    Run contains {{run.UsedBC}}" slot business card.
</div>
<div ng-show="run.Visibility.LayoutInst" id="LayoutInst" class="alert alert-warning">
    Upload layout file/s.
</div>
<div ng-show="run.Visibility.FillingInst" id="FillingInst" class="alert alert-success">
    No action required - run is filling
</div>
<div ng-show="run.Visibility.ProgressInst" id="ProgressInst" class="alert alert-success">
    No action required - run is in progress.
</div>
<div ng-show="run.Visibility.CompleteInst" id="CompleteInst" class="alert alert-success">
    No action required - run is complete.
</div>-->



<div leppane="Run details">

  <div class="row form-horizontal">


      <div class="col-sm-4">
          <div class="form-group">
              <label class="control-label col-xs-3">Run #</label>


              <div class="col-sm-4  form-control-static">{{::run.Id}}</div>

          </div>


          <!-- PrintType -->

              <div class="form-group ng-class:{'has-error' : (run.PrintType=='B' || !run.PrintType) }">
                  <label class="col-xs-3 control-label">Print type  </label>
                  <div class="col-xs-7  radio">

                      <label class="" >
                          <input type="radio" name="optradioPrintType" ng-model="run.PrintType" value="O"  > Offset&nbsp;&nbsp;
                      </label>

                      <label class="" >
                          <input type="radio" name="optradioPrintType" ng-model="run.PrintType" value="D" > Digital&nbsp;&nbsp;
                      </label>


                  </div>
              </div>



          <div class="form-group">
              <label class="control-label col-xs-3">Celloglaze</label>
              <div class="col-sm-4  form-control-static">{{ enums.ValueDesc.RunCelloglazeOptions[run.Celloglaze] }}</div>
          </div>

          <div class="form-group">
              <label class="control-label col-xs-3">CMYK</label>
              <div class="col-sm-4  form-control-static">{{::(run.Cmyk?"Yes":"No")}}</div>
          </div>
          <div class="form-group">


          </div>
          <div class="form-group" ng-show="run.Visibility.printRunSheet">
              <label class="control-label col-xs-4"># of press sheets</label>
              <div class="col-sm-4 ">
                  <input type="text" class="form-control" ng-model="run.NumOfPressSheets" />
              </div>
          </div>
<!--
          <div class="form-group" ng-show="run.Visibility.printRunSheet">
            <label class="control-label col-xs-4">Run Length</label>
            <div class="col-sm-4 ">
                <input type="text" class="form-control" ng-model="run.RunLength" />
            </div>
          </div>

          <div class="form-group" ng-show="run.Visibility.printRunSheet">
            <label class="control-label col-xs-4">Overs</label>
            <div class="col-sm-4 ">
                <input type="text" class="form-control" ng-model="run.Overs" />
            </div>
          </div>
 -->


      </div>

      <div class="col-sm-4">
        <div class="form-group" ng-if="run.IsBusinessCard == true">
            <h4>Slot Machine</h4>
            <table class="table table-stripped table-sm">
              <tr>
                <th>Count</th>
                <th>Quantity</th>
                <th>How Many Up</th>
                <th>Total Slots</th>
              </tr>
              <tr ng-repeat="(q,xx) in ql">
                <td>{{xx.Count}}</td>
                <td>{{q}}</td>
                <td>
                  <select class="form-control" ng-model="xx.Ups" ng-options="n for n in [] | range:1:43" ng-change="updSum()">

                  </select>
                </td>
                <td>
                  {{ (xx.Ts)}}
                </td>
              </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td style="font-size: larger; font-weight: bold;" ng-class="{'error-redish': (TSlots > maxSlots) }">
                  {{TSlots}} </td>
              </tr>


            </table>
            <!-- <pre>{{run.ups | json}}

              {{ql | json}}
            </pre> -->
        </div>
      </div>

    <div class="col-sm-4">
      <div class="form-group">
        <label class="control-label col-xs-3">
          Status
        </label>

        <div class="col-sm-6">
          <select name="statusLst" id="statusLst" class="form-control"
                  ng-model="run.Status"
                  ng-options="k*1 as v for (k,v) in runStatusOptions[run.PrintType]"></select>
        </div>
      </div>

      <div class="form-group">
        <label class="control-label col-xs-3">Stock</label>
        <div class="col-sm-6">
          <select name="stockLst" id="stockLst" class="form-control"
                  ng-model="run.StockId"
                  ng-options="stock.Id as stock.Name for stock in stocks"></select>
          <span class="help-block" ng-if="run.StockId != originalStockId">Stock has been changed from original</span>
        </div>
      </div>

      <div class="form-group">
        <label class="control-label col-xs-3">Started</label>
        <div class="col-sm-6  form-control-static">{{::run.DateCreated | date:"dd/MM/yyyy HH:mm"}}</div>
      </div>


      <div class="form-group">
        <div class="col-sm-6 col-xs-offset-3 checkbox">
          <label class="checkbox">
            <input type="checkbox" ng-model="run.ManuallyManage"
                   class="checkbox "> Manually manage this run
          </label>
        </div>
        <div class="col-sm-6 col-xs-offset-3 checkbox">
          <label class="checkbox">
            <input type="checkbox" id="urgentChk" tabindex="4" ng-model="run.Urgent"
                   class="checkbox ">Urgent
          </label>
        </div>
        <div class="col-sm-6 col-xs-offset-3 ">
          <button   class="resetdisable btn btn-default pull-right" ng-click="saveRun()">Update Run</button>
        </div>
      </div>


    </div>
  </div>
</div>


<div leppane="Layout">


  <div class="form-group" ng-init="upload_artwork=false">
    <span class="item">
      <a   id="artwork-link"  target="_self" >Open artwork folder</a>
    </span>
  </div>

  <div class="form-group" id="existing_artwork" ng-show="run.LayoutFiles.length">

    <div ng-repeat="a in run.LayoutFiles">

      {{:: enums.ValueDesc.LayoutType[a.Type] }} :  {{:: a.FileName}}

    </div>

  </div>
  <a ng-click="upload_artwork = !upload_artwork">Upload artwork</a>

  <div class="form-group" ng-show="upload_artwork">

    <div ng-repeat="p in requiredPositions">
      <div style="margin-bottom: 15px;">
        {{p}} : <span>{{files[$index].name}}</span>
        <input type="file" ng-file-model="files[$index]" name="{{p}}"
               accept=".txt,.tiff,.jpg,.pdf,.eps,.cdr,.zip" />

        <div style="float: right;">
          <span>{{files[$index].size | bytes}}</span>
          <a ng-click="btn_remove(file)" title="Remove from list to be uploaded">
            <i class="icon-remove"></i>
          </a>
        </div>
        <progress style="width: 400px;" value="{{files[$index].loaded}}" max="{{files[$index].size}}"></progress>
      </div>
    </div>
    <div>
      <div style="float: left;">
        <div class="btn btn-sm" ng-click="btn_upload()">Upload</div>
        <div class="btn btn-sm" type="reset">Remove all</div>
      </div>
    </div>
  </div>





</div>

<div leppane="Jobs">
  <div ng-if="run.IsBusinessCard == true">Free Slots : {{freeSlots}}</div>
  <table class="table table-bordered table-striped">
    <tbody>
      <tr>
        <th width="100">
          <a ng-click="toggleSort('SlotX')">Slot</a>
        </th>
    		<th><a ng-click="toggleSort('Id')">Job #</a></th>
        <th>Status</th>
        <th>Job Name</th>
        <th>Qty</th>
        <th>Size</th><th></th>
        <th> <a ng-click="toggleSort('OrderCustomerName')">Customer </a>     </th>
        <th>Contact</th>
        <th>Phone</th>
        <th class="actions">Action</th>
      </tr>

      <tr ng-repeat="job in run.Jobs | orderBy:SortField:SortDir " class="{{::job.css}}">
        <td>
          <div ng-if="(run.IsBusinessCard && ( run.Status == 2 || run.Status == 1))" class="">
            <!--  == {{job.NumberOfSlots}} - {{job.NumberOfSlots2}}==  {{ job.Slots | json }} -->

            <div ng-repeat="i in [].constructor(job.NumberOfSlots) track by $index">
			        <input ng-model="job.Slots[$index]" type="number" class="form-control nospinner" maxlength="2" style="padding: 2px 1px;" />
            </div>
          </div>
          <div ng-if="(run.IsBusinessCard && !( run.Status == 2 || run.Status == 1))" class="">
            {{job.Slots.join(', ')}}
          </div>
        </td>

	      <td width="50" class="{{::job.HealthCss}}">
		      <a ui-sref="staff.order.job({orderId:job.OrderId, jobId: job.Id})" class="bold nowrap" target="job">{{::job.Id}}</a>
	      </td>
        <td class="{{::job.HealthCss}}">
          {{::$root.enums.ValueDesc.JobStatusOptions[job.Status]}}
        </td>
        <td class="{{::job.HealthCss}}">
          <span style="color:red; font-weight:bold" ng-if="job.IsRestart">(Restart)</span> {{::job.Name}}
        </td>
        <td>
          {{::job.Quantity}}
        </td>
        <td class="nowrap">
            {{::job.FinishedSizeStr}}
            <!--<div ng-show="::job.RoundOption" title=" Round Option">
               {{::enums.ValueDesc.RoundOption[job.RoundOption]}}
            </div>-->

            <!--<div ng-show="::job.RoundDetailOption" title="Round Detail">
               {{::enums.ValueDesc.RoundDetailOption[job.RoundDetailOption]}}
            </div>-->


            <div ng-show="job.FoldedSize != null">
                FOLDING: {{::job.FoldedSize.Width}} x {{::job.FoldedSize.Height}}
            </div>


            <div ng-show="::job.CustomDieCut">CUSTOM DIECUT: {{::job.CustomDieCut}}</div>
            <div ng-show="::job.HoleDrilling">DRILLED: {{::enums.ValueDesc.HoleDrilling[job.HoleDrilling]}}</div>
            <div ng-show="::job.FrontCelloglaze == 4">EMBOSSED</div>
            <div ng-show="::job.FrontCelloglaze == 5">FOILED, {{::job.FoilColour}}</div>


            <div ng-show="::job.Scoring">Scoring ticked</div>
            <div ng-show="::job.ScoringInstructions">Scoring instuctions: {{::job.ScoringInstructions}}</div>
        </td>
  	  	<td class="nowrap"> {{::(job.TemplateName.search('next day') != -1 ? "NDD": "") }}</td>
        <td  class="nowrap">
          {{::job.OrderCustomerName}}

        </td>
        <td  class="nowrap">
          {{::job.OrderCustomerContact1Name}}
        </td>
        <td  class="nowrap">
          {{::job.OrderCustomerContact1Phone}}
        </td>
        <td class="links actions">
          <a ui-sref="staff.run-edit-jobspec({runId: run.Id, jobId:job.Id})" tabindex="99999">View</a>
        </td>
      </tr>
    </tbody>
  </table>
</div>



<div class="content">
  <div class="content-left">
    <div id="regularContent" class="entry">
        <div class="pull-right">
            <a id="cancelButton" class="resetdisable  btn  " ng-click="cancel()">
                <i class="glyphicon glyphicon-chevron-left"></i>
                back to runs
            </a>


            <a id="printFurtherProcessingList" class="print-order btn" ng-sho00w="run.Visibility.printRunSheet"
               ng-click="printFurtherProcessingList()">Print FPList</a>


            <a id="printJobSheetsButton" class="print-order btn" ng-click="printJobSheets()">Print job sheets</a>

            <a id="printRunSheet" class="print-order btn" ui-sref="staff.run-print-bc-run-sheet({runId: run.Id})" ng-show="run.Visibility.printRunSheet"
               ng-click="print('runSheet')">Print run sheets</a>




            <!-- Single button -->
            <div class="btn-group" ng-show="run.IsBusinessCard">
              <button type="button" class=" dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                BC Reference Sheet <span class="caret"></span>
              </button>
              <ul class="dropdown-menu">
                <li><a ng-click="downloadBCRef()">Download</a></li>
                <li><a ng-click="printBCRefSheet('FG')" >Print in FG</a></li>
                <li><a ng-click="printBCRefSheet('PM')" >Print in PM</a></li>

              </ul>
            </div>


            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <button id="saveButton" class="resetdisable btn btn-default" ng-click="saveRun()">Update Run</button>
        </div>
    </div>
  </div>
</div>



<!--<json-explorer data="run"></json-explorer><pre>
{{run | json}}
</pre>-->
