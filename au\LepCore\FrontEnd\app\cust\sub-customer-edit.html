<form name="form">

    <div leppane="Customer Details" visible="true">
        <div class="form-horizontal">
            <div class="row">
                <div class="col-sm-6">

                    <div class="form-group
						  ng-class:{'unique' : form.Name.$error.unique,
						     		'required': form.Name.$error.required,
									'maxlength': form.Name.$error.maxlength}">
                        <label class="col-sm-4 control-label" for="bus-name">Business Name</label>
                        <div class="col-sm-8">
                            <input name="Name" type="text" class="form-control input" ng-maxlength="200"
                                   ng-model="vm.Name" ng-model-options="{allowInvalid:true, debounce : 1000}"
                                   ng-required="true"
                                   lep-unique-businessname
                                   user-id="vm.Id"/>
                        </div>
                    </div>


                    <div class="form-group ng-class:{'unique' : form.Name.$error.unique,
                                    'required': form.Name.$error.required,
                                    'minlength': form.Name.$error.minlength,
                                    'maxlength': form.Name.$error.maxlength}">
                        <label class="col-sm-4 control-label" for="username">Username</label>
                        <div class="col-sm-8">
                            <input name="UserName" type="text" ng-model="vm.Username"
                                   ng-model-options="{allowInvalid:true}" class="form-control input" ng-required="true"
                                   ng-minlength="5" ng-maxlength="40" lep-unique-username user-id="vm.Id">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-4 control-label" for="order-no">Payment terms </label>
                        <div class="col-sm-8">

                            <div ng-if="ppv >= 2">
                                <label>
                                    <input name="PaymentTerms" type="radio" ng-model="vm.PaymentTerms"
                                           ng-value="0 * 1"/>On Account</label>
                            </div>
                            <div>
                                <label>
                                    <input name="PaymentTerms" type="radio" ng-model="vm.PaymentTerms"
                                           ng-value="1 * 1"/>Cash Before Dispatch (COD)</label>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                </div>
            </div>
        </div>

    </div>

    <!--<div class="row">
        <div class="col-sm-12">
            <div class="form-horizontal col-sm-12">
                <div class="row">
                    <div class="form-actions pull-right">
                        <a ui-sref="staff.customers" class="btn">
                        <i class="glyphicon glyphicon-chevron-left"></i> Back </a>

                        <button type="submit" class="btn" ng-click="save()">
                        <i class="glyphicon glyphicon-floppy-save"></i> Save Customer</button>


                    </div>
                </div>
            </div>
        </div>
    </div>-->

    <div leppane="Email">
        <div class="form-horizontal ">
            <div class="form-group">
                <label class="col-sm-4 control-label" for="job-notifiications">Send Job Status Notifications to
                    *</label>
                <div class="col-sm-6">
                    <input id="job-notifiications" type="text" class="form-control input" ng-model="vm.Email"
                           ng-required="true"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-4 control-label" for="notif-type">Notification Type</label>
                <div class="col-sm-6">
                    <select ng-model="vm.NotificationType" ng-required="true" id="notif-type" class="form-control input"
                            ng-options="k*1 as v for (k,v) in $root.enums.ValueDesc.NotificationType"></select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-4 control-label" for="send-account">Send Accounts to </label>
                <div class="col-sm-6">
                    <input id="send-account" type="text" class="form-control input" ng-model="vm.AccountEmail"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-4 control-label" for="other-materials">Send all other materials including
                    promotions to </label>
                <div class="col-sm-6">
                    <input id="other-materials" type="text" class="form-control input" ng-model="vm.OtherEmail"/>
                </div>
            </div>

        </div>
    </div>

    <div leppane="Contacts List" visible="true">
        <div lep-contact-list lep-contact-list-details contacts="vm.Contacts" receive-marketing-emails="true"></div>
    </div>

    <div leppane="Address details">
        <div class="form-horizontal">
            <div class="row">

                <div class="col-sm-6">
                    <div class="form-group">
                        <div class="col-sm-9">
                            <h6>Billing address</h6>
                        </div>
                    </div>

                    <div address-details address="vm.BillingAddress"></div>
                    <!--<pre>{{vm.BillingAddress|json}}</pre>-->
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <div class="col-sm-9">
                            <h6>Delivery address</h6>
                        </div>
                    </div>

                    <div>
                        <div address-details address="vm.PostalAddress" noteditable="vm.PostalIsBilling"></div>
                    </div>
                    <div class="form-group">

                        <div class="col-sm-10 col-sm-offset-2">
                            <label class="checkbox">
                                <input name="checkbox" type="checkbox" ng-model="vm.PostalIsBilling"
                                       ng-change="makePostalSameAsBilling(vm.PostalIsBilling)"/>
                                Customer delivery address is the same as billing address
                            </label>
                        </div>
                    </div>
                    <!--<pre>{{vm.PostalAddress|json}}</pre>-->
                </div>
            </div>
        </div>

    </div>


    <div leppane="Password" visible="true">
        <div class="row">
            <div class="col-sm-6">
                <div class="form-horizontal">
                    <!-- <legend>Login</legend> -->
                    <div class="form-group">
                        <label class="control-label col-sm-4">Password</label>
                        <div class="col-sm-8">
                            <input class="form-control" type="text" ng-model="vm.Password"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-4">Confirm Password</label>
                        <div class="col-sm-8">
                            <input class="form-control" type="text" ng-model="vm.Password1"/>
                        </div>
                    </div>


                    <div class="form-group" ng-if="vm.Id != 0">
                        <label class="control-label col-sm-4"></label>
                        <div class="col-sm-5">
                            <button class="btn btn-default" type="button" ng-click="updatePassword(vm.Password)"
                                    ng-disabled="!vm.Password || !(vm.Password == vm.Password1)">Update password
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>


    <div leppane="Products" visible="true">
        <div class="row">
            <div class="col-sm-12">
                <div class="form-group">
                    <label class="control-label col-xs-2">Denied Templates</label>
                    <div class="col-xs-10">
                        <ui-select multiple ng-model="vm.PrintPortalSettings.DeniedTemplates" theme="bootstrap" close-on-select="false" style="width: 100%; "
                                title="Choose job templates not available on your print portal">
                            <ui-select-match placeholder="Select templates...">
                                {{$item.Name}}
                            </ui-select-match>

                            <ui-select-choices repeat="c.Id as c in allTemplatesName | filter:$select.search">
                                {{c.Name}}
                            </ui-select-choices>
                        </ui-select>
                        <div class="help-block">
                            If you want any job categories not orderable by your web users, please select them here.
                        </div>
                    </div>
                </div>


                <div class="form-group" ng-click="updateVm()" sddtyle="max-height: 400px; overflow-y:scroll">
                    <label class="control-label col-xs-2">Allowed Products</label>
                    <div class="col-xs-10" style="font-size: smaller;">
                        <div class="help-block">
                            Please select product comibations that you want offer in your site.
                        </div>



                        <label class="checkbox"><input type="checkbox" ng-click="toggleAllTemplates($event)" /> All templates</label>

                        <div class="row template-matrix-row" ng-repeat="t in d.templatesL | filter:filterOutDeniedTemplates ">
                            <div class="col-sm-12">
                                <label>
                                    <input type="checkbox" ng-click="toggleAllUnderTemplate($event,t.Id)" /> &nbsp;&nbsp;<h5 style="display: inline">{{::t.Name}}</h5>
                                </label>
                            </div>
                            <div class="col-md-1 ">
                
                
                
                            </div>
                            <div class="col-sm-3">
                                <label class="checkbox alltoggle"><input type="checkbox" ng-click="toggleSizesUnderTemplate($event,t.Id)" /> All sizes</label>
                                <div ng-repeat="k in d.matrix[t.Id].Sizes">
                                    <label class="checkbox"><input type="checkbox" checkbox-model="d.matrix2[t.Id].Sizes" checkbox-value="k*1" multiple>{{:: d.sizesD[k]}}</label>
                                </div>
                            </div>
                
                            <div class="col-sm-8">
                                <div class="row ">
                                    <div class="col-sm-12">
                                        <label class="checkbox alltoggle"><input type="checkbox" ng-click="toggleStocksUnderTemplate($event,t.Id)" /> All stocks</label>
                                    </div>
                                </div>
                
                                <div class="row ">
                                    <div ng-repeat="k in d.matrix[t.Id].Stocks" class="col-md-5">
                                        <label class="checkbox"><input type="checkbox" checkbox-model="d.matrix2[t.Id].Stocks" checkbox-value="k*1" multiple>{{:: d.stocksD[k]}}</label>
                                    </div>
                                </div>
                            </div>
                
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div leppane="Pricing for this customer" visible="true">
        <div class="row">
            <div class="form-group">
                <label class="col-xs-2 control-label" for="order-no">Pricing method</label>
                <div class="col-xs-10">
                    <div>
                        <div class="radio">
                            <label class="ng-class:{'bold': vm.PricingModel == null}">
                                <input name="PricingModel" type="radio" ng-model="vm.PrintPortalSettings.PricingModel" ng-value="null" />
                                Apply global settings from my print portal
                            </label>
                            <span class="help-block">I choose to  not override any pricing for this customer.</span>
                        </div>


                        <div class="radio">
                            <label class="ng-class:{'bold': vm.PricingModel == 1}">
                                <input name="PricingModel" type="radio" ng-model="vm.PrintPortalSettings.PricingModel" ng-value="1*1" />
                                Category Markup %
                            </label>
                            <span class="help-block">Specify product type / catergory specific markups based on quantity only for this customer</span>
                        </div>

                        <div class="radio">
                            <label class="ng-class:{'bold': vm.PricingModel == 2}">
                                <input name="PricingModel" type="radio" ng-model="vm.PrintPortalSettings.PricingModel" ng-value="1*2" />
                                Price Range Markup %
                            </label>
                            <span class="help-block"> specify price range/band specific markup only for this customer</span>
                        </div>
                    </div>
                    <div class="help-block">
                    </div>
                </div>
            </div>

            <div class="form-group" ng-if="(vm.PrintPortalSettings.PricingModel == 1) || (vm.PrintPortalSettings.PricingModel == 2) ">
                <div class="form-group ng-class:{'has-error' : (!vm.PrintPortalSettings.WhiteLabelGlobalMarkup)}">
                    <label class="control-label col-xs-2"> General (%)</label>
                    <div class="col-xs-2">
                        <input class="form-control" type="number" ng-model="vm.PrintPortalSettings.WhiteLabelGlobalMarkup"
                               ng-required="true" ng-min="0" />
                    </div>
                </div>
                <div class="clear"></div>
                <div class="form-group">
                    <label class="control-label col-xs-2"></label>

                    <div class="help-block col-xs-10">
                        A catch all markup % that gets applied to all jobs, if a Category or Price range specific markup is not specified below.
                    </div>
                </div>
            </div>


            <div class="row" ng-if="vm.PrintPortalSettings.PricingModel == 1">


                <div lep-white-label-setup-pricing-1 vm="vm"></div>

            </div>

            <div class="row" ng-if="vm.PrintPortalSettings.PricingModel == 2">

                <div lep-white-label-setup-pricing-2 vm="vm"></div>


            </div>


        </div>

    </div>



    <div class="row">
        <div class="col-sm-12">
            <div class="form-horizontal col-sm-12">
                <div class="row">

                    <div class="form-actions pull-left">
                        <button ng-click="delete(vm.Id)" class="btn">
                            <i class="glyphicon glyphicon-trash"></i>
                            Delete
                        </button>

                        <button ng-click="archive(vm.Id)" class="btn" ng-show="!vm.Archived">
                            <i class="glyphicon glyphicon-book"></i>
                            Archive
                        </button>
                        <button ng-click="unarchive(vm.Id)" class="btn" ng-show="vm.Archived">
                            <i class="glyphicon glyphicon-book"></i>
                            Un-Archive
                        </button>
                    </div>
  
                    <div class="form-actions pull-right">
                        <a ui-sref="cust.sub-customers" class="btn">
                            <i class="glyphicon glyphicon-chevron-left"></i>
                            Back
                        </a>

                        <button type="submit" class="btn btn-default" ng-click="save()">
                            <i class="glyphicon glyphicon-floppy-save"></i> Save
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

