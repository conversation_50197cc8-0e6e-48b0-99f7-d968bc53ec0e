﻿namespace LepCore
{
	using Microsoft.AspNetCore.Http;
	using System;
	using System.Diagnostics;
	using System.IO;
	using System.IO.Compression;
	using System.Threading.Tasks;

	namespace Middlewares
	{

		public class ProcessingTimeMiddleware
		{
			private readonly RequestDelegate _next;

			public ProcessingTimeMiddleware(RequestDelegate next)
			{
				_next = next;
			}

			public async Task Invoke(HttpContext context)
			{
				var watch = new Stopwatch();
				context.Response.OnStarting(state =>
				{
					var httpContext = (HttpContext)state;
					httpContext.Response.Headers.Add("X-ElapsedTime", new[] { watch.ElapsedMilliseconds.ToString() });
					return Task.FromResult(0);
				}, context);

				watch.Start();
				await _next(context);
				watch.Stop();
			}
		}


		/// <summary>
		/// Experimental compression middleware for self-hosted web app
		/// </summary>
		public class CompressionMiddleware
		{
			private readonly RequestDelegate _next;
			private const long MinimumLength = 2700;

			public CompressionMiddleware(RequestDelegate next)
			{
				_next = next;
			}

			public async Task Invoke(HttpContext context)
			{
				var acceptEncoding = context.Request.Headers["Accept-Encoding"];
				if (acceptEncoding.ToString().IndexOf("gzip", StringComparison.CurrentCultureIgnoreCase) < 0)
				{
					await _next(context);
					return;
				}

				using (var buffer = new MemoryStream())
				{
					var body = context.Response.Body;
					context.Response.Body = buffer;
					try
					{
						await _next(context);

						if (buffer.Length >= MinimumLength)
						{
							using (var compressed = new MemoryStream())
							{
								using (var gzip = new GZipStream(compressed, CompressionLevel.Optimal, leaveOpen: true))
								{
									buffer.Seek(0, SeekOrigin.Begin);
									await buffer.CopyToAsync(gzip);
								}

								if (compressed.Length < buffer.Length)
								{
									// write compressed data to response
									context.Response.Headers.Add("Content-Encoding", new[] { "gzip" });
									if (context.Response.Headers["Content-Length"].Count > 0)
									{
										context.Response.Headers["Content-Length"] = compressed.Length.ToString();
									}

									compressed.Seek(0, SeekOrigin.Begin);
									await compressed.CopyToAsync(body);
									return;
								}
							}
						}

						// write uncompressed data to response
						buffer.Seek(0, SeekOrigin.Begin);
						await buffer.CopyToAsync(body);
					}
					finally
					{
						context.Response.Body = body;
					}
				}
			}
		}
	}
}
