.strong {
  font-weight: bold; }

.hidden {
  display: none; }

.clear {
  clear: both; }

.align-center {
  text-align: center; }

.align-right {
  text-align: right; }

.align-left {
  text-align: left; }

.formTable, .table {
  width: 100%; }

.table {
  border: 1px solid #ccc;
  background-color: #F8F8F8; }

.table th {
  font: 12px Arial,sans-serif;
  background-color: #f8f8f8;
  color: #2f2f2f;
  padding: 4px 0 4px 8px;
  font-weight: bold;
  border: none;
  text-align: left; }

.table td {
  font: 12px Arial,sans-serif;
  color: #555;
  background-color: #fff;
  padding: 4px 6px 4px 6px;
  border: none; }

.table .total {

  font-weight: bold;
  }

.small-width {
  width: 10%; }

.formCell {
  width: 20%; }

.field-row {
  white-space: nowrap; }

.field-row label {
  font: 12px Arial,sans-serif;
  color: #373737;
  font-weight: bold;
  text-align: left;
  padding: 4px 0 0 0px;
  line-height: 100%;
  margin: 0 5px 0 0;
  width: 110px;
  float: left; }

.field-row-nopadding label {
  font: 12px Arial,sans-serif;
  color: #373737;
  font-weight: bold;
  text-align: left;
  padding: 0px 0 0 0px;
  line-height: 100%;
  margin: 0 5px 8px 0;
  width: 110px;
  float: left; }

.field-row .label {
  float: left;
  width: 110px; }

.field-row select {
  width: 240px; }

.field-row .item {
  float: left; }

.field-row-radio label {
  font: 12px Arial,sans-serif;
  color: #373737;
  font-weight: bold;
  text-align: left;
  padding: 4px 0 0 0px;
  line-height: 100%;
  margin: 0 5px 0 0;
  float: left; }

.field-row-radio input {
  margin-right: 10px; }

.field-row-radio .block {
  float: left;
  margin-left: 34px;
  margin-bottom: 5px;
  width: 300px; }

.field-row-radio-price .block {
  float: left;
  margin-left: 0px;
  margin-bottom: 5px; }

.field-row-radio-price input {
  float: left;
  margin-bottom: 3px; }

.field-row-radio-price label {
  width: 200px;
  margin-left: 5px; }

.field-row-radio .block label {
  font-weight: normal; }

.field-row-radio table td {
  width: 200px; }

.field-row-radio table td input {
  float: left; }

.field-row-radio table td label {
  width: 160px;
  float: left; }

.field-row-checkbox {
  font-weight: bold; }

.field-row-checkbox input {
  margin-right: 10px; }

.field-row-checkbox-track {
  font-weight: bold; }

.field-row-checkbox-track .label {
  float: left;
  display: block;
  height: 20px;
  width: 100px; }

.field-row-checkbox-track .box {
  height: 20px;
  float: left;
  display: block;
  margin-left: 29px;
  width: 250px; }

.field-row-checkbox-track input {
  margin-right: 3px; }

.form-field-width-widthheight {
  width: 40px; }

.form-field-width-long {
  width: 235px; }

.form-field-width-short {
  width: 93px; }

.form-field-width-pricecode {
  width: 45px; }

.form-field-width-phone-areacode {
  width: 24px; }

.form-field-width-phone {
  width: 66px; }

.form-field-width-mobile {
  width: 93px; }

.form-field-width-postcode {
  width: 36px; }

.form-field-width-credit-limit {
  width: 60px; }

.form-field-width-qty-price {
  width: 80px; }

.form-field-width-myob {
  width: 130px; }

.formCell {
  padding: 0 0 8px 0; }

.formCell input {
  font: 11px Arial,sans-serif;
  color: #555;
  line-height: 100%; }

.formCell select, .buttons select {
  font: 12px Arial,sans-serif;
  color: #555;
  width: 150px; }

.formCell .checkbox {
  font: 11px Arial,sans-serif;
  color: #555;
  line-height: 100%;
  margin: 0 10px 0 0; }

/* Error validation styling */
.error {
  font: 13px Arial,sans-serif;
  color: #333333;
  padding: 1px 6px 1px 6px;
  margin: 0 0 10px 0;
  border: solid 2px #df3939;
  background: #FAEDED;
  font-weight: bold;
  line-height: 160%; }

.field-row span.error {
  border: none !important;
  background-color: transparent !important; }

.updateMessage {
  font: 13px Arial,sans-serif;
  color: #333333;
  padding: 1px 6px 1px 6px;
  margin: 0 0 10px 0;
  border: solid 2px #df3939;
  background: #94c83f;
  font-weight: bold;
  line-height: 160%; }

.instructionMessage {
  background-color: #bee5e4;
  border: 2px solid #9fd9da;
  font: 13px Arial,sans-serif;
  color: #333333;
  padding: 1px 6px 1px 6px;
  margin: 0 0 10px 0;
  font-weight: bold;
  line-height: 160%; }

.requiredField {
  color: red;
  font-weight: bold;
  font: 12px Verdana, Arial, Helvetica, sans-serif;
  padding-left: 4px; }

.error p {
  font: 12px Arial,sans-serif;
  color: #df3939 !important;
  margin: 0 0 5px 0; }

.error-row {
  color: #df3939; }

.error-row label {
  color: #df3939 !important;
  font-weight: bold; }

.error ul {
  margin: 0 0 0 20px; }

.error li {
  line-height: 16px; }

.error-required {
  font: 10px Arial,sans-serif;
  color: #df3939; }

.form-item {
  margin: 5px 0 5px 0; }

/* Form elements */
.column-header {
  text-align: right;
  width: 33%;
  float: left;
  font: 12px Arial,sans-serif;
  color: #373737;
  padding: 0 0 10px 0 !important;
  font-weight: bold; }

.join-row {
  float: left;
  display: block;
  margin: 10px 0 0 20px; }

.join-row label {
  width: auto; }

.join-row .field-row {
  display: block;
  float: left;
  margin: 0 6px 10px 10px;
  width: 100px; }

.field-row .item {
  font: 12px Arial,sans-serif;
  float: left;
  display: block;
  margin: 0px 10px 5px 0px; }

.field-row .stock-width-item {
  width: 118px; }

form .field-row .radio-exception {
  float: none; }

.field-row .radio-exception-first {
  margin: 0 0 0 90px; }

.radio-row, .checkbox-row {
  margin: 5px 0 5px 0;
  /* display: table-row; */ }

.checkbox-row input, .radio-row input {
  font: 11px Arial,sans-serif;
  color: #555;
  line-height: 100%;
  margin: 0 0 5px 0;
  float: left;
  /* display: table-cell;*/ }

.checkbox-row label, .radio-row label {
  text-align: left;
  padding: 0px 0 4px 5px;
  line-height: 100%;
  margin: 0 5px 0 0;
  /* width:25%; */
  float: left;
  font: 12px Arial,sans-serif;
  color: #373737;
  font-weight: bold; }

.radio-row-price input {
  float: left; }

.radio-row-price label {
  text-align: left;
  float: left;
  font-weight: bold;
  margin-left: 20px;
  width: 200px; }

.checkbox-row label, .radio-row .text {
  text-align: left; }

.text-row {
  margin: 10px 0 0 22px; }

.text-row .help, .entry .change-order {
  font: 13px Arial,sans-serif;
  background: url("images/btn/help.gif") left 2px no-repeat;
  padding: 0 0 0 14px; }

.text-row .help:link, .text-row .help:visited, .entry .change-order:link, .entry .change-order:visited {
  text-decoration: none;
  color: #2369bf; }

.text-row .help:active, form .text-row .help:hover, .entry .change-order:active, .entry .change-order:hover {
  text-decoration: underline;
  color: #2369bf; }

fieldset ul {
  margin: 0 0 0 20px; }

fieldset li {
  padding: 5px 0 0 0;
  line-height: 16px; }

form fieldset .form-section-expand h3 {
  padding: 2px 0 0 23px;
  background: url("images/btn/fieldset-legend.png") left top no-repeat;
  font: 16px Arial,sans-serif;
  color: #4a4a42;
  font-weight: bold;
  margin: 1px 0 0 5px;
  display: inline; }

form fieldset h4 {
  padding: 2px 0 0 2px;
  font: 14px Arial,sans-serif;
  color: #4a4a42;
  font-weight: bold;
  margin: 10px 0 20px -1px;
  display: block; }

form fieldset.closed div {
  display: none; }

form fieldset.closed h3 {
  background: url("images/btn/fieldset-legend-open.png") left top no-repeat; }

form fieldset h3 {
  background: url("images/btn/fieldset-legend.png") left top no-repeat; }

form fieldset.closed div.form-section-expand {
  display: block; }

.content a:link, .content a:visited {
  text-decoration: none;
  border-bottom: 1px dotted #2369bf;
  color: #2369bf; }

.content a:active, .content a:hover {
  text-decoration: none;
  border-bottom: 2px solid #2369bf;
  color: #2369bf; }

.artwork-columntwo-linkspacing {
  margin-left: 110px; }

.artwork-columnone-linkspacing {
  margin-left: 140px; }

form fieldset .form-data {
  font: 12px Arial,sans-serif;
  color: #333;
  margin: 10px 0 0 0;
  text-align: left; }

form fieldset .field-row {
  margin: 0 0 10px 0; }

form fieldset .size-indent {
  padding: 0 0 0 90px; }

form fieldset .field-row .text {
  margin: 0 0 0 40px;
  font: 11px Arial,sans-serif;
  color: #555555; }

.quote-data .column-three .label, .quote-data .column-two .label, .quote-data .column-one .label {
  width: auto;
  margin-bottom: 10px; }

.price-row .buttons {
  padding: 2px 0;
  background: url("images/btn/button-gradient-customerprice.gif") left bottom repeat-x;
  background-color: #f36e22;
  border: 1px solid #333333;
  color: #ffffff;
  font: 13px Arial,sans-serif;
  text-align: center;
  font-weight: bold;
  width: 100px; }

.price-row .buttons a {
  margin: 0; }

.price-row {
  font: 13px Arial,sans-serif;
  font-weight: bold;
  color: #373737;
  /* margin:0 0 10px 0; */ }

form fieldset .no-margin {
  margin: 0 0 5px 0 !important; }

form fieldset .form-section-content {
  margin: 0 0 10px 0px; }

form fieldset .form-section-content p {
  margin: 0 0 0px 8px; }

.order-now .float-left {
  margin: 0 10px 0 10px; }

.fix-orders {
  padding: 0 0 30px 0; }

.fix-orders h3 {
  padding: 30px 0 0 20px;
  display: block;
  font: 16px Arial,sans-serif;
  font-weight: bold;
  color: #2f2f2f;
  margin: 0; }

.fix-orders .full-width {
  margin: 0 20px 0 20px; }

/* Artwork */
.artwork-box {
  /*border-left:1px solid #ccc;
		border-bottom:1px solid #666;
		border-top:1px solid #ccc;
		border-right:1px solid #666;
		background-color:#fff;
		padding:10px;
		width:90%;*/ }

/* Table */
.table .order-decal {
  background: url("images/art/order-decal.png") left center no-repeat;
  padding: 0 0 0 15px; }

.table .status {
  width: 200px; }

.table .status .stage {
  width: 100px;
  display: block;
  float: left; }

.table .status .condition {
  float: right;
  width: 80px;
  color: #666;
  padding: 2px;
  text-align: center; }

.table th span {
  display: none; }

.table .buttons {
  width: 100px;
  text-align: center; }

.table .buttons a {
  background: url("images/btn/button-gradient-table.gif") left bottom repeat-x #f0f8ff !important; }

.table .buttons .view-job {
  margin: 0 4px 0 0 !important; }

.table .buttons .delete-job {
  font-weight: bold; }

.total-row .total-quote, .total-row .total-cost {
  background-color: #e0f0fe;
  font-weight: bold; }

.total-row .total-quote {
  text-align: right; }

.total-row .total-cost {
  text-align: left; }

.LeftAlignedLabel {
  text-align: left;
  float: right;
  white-space: nowrap; }

.rightAlignedNuemeric {
  width: 100px !important;
  text-align: right;
  float: left; }

.three-column-box {
  overflow: auto; }

.three-column-box .column-one {
  width: 32%;
  float: left;
  margin-left: 1%; }

.three-column-box .column-two {
  width: 32%;
  float: left; }

.three-column-box .column-three {
  width: 32%;
  float: left; }

.payment-box {
  margin: 20px 0 20px 0; }

.two-column-box {
  overflow: auto; }

.two-column-box .column-one, .column-one {
  float: left;
  width: 49%;
  margin-left: 1%; }

.two-column-box .column-two, .column-two {
  float: right;
  width: 45%; }

.wide-column-one .label {
  width: 160px !important;
  padding-right: 20px; }

.two-column-box .column-two .notice {
  width: 295px;
  background: url("images/bg/notice-tile.gif") left repeat-y; }

.two-column-box .column-two .notice h3 {
  background: url("images/bg/notice-top.gif") left top no-repeat;
  color: #db0404;
  padding: 12px 0 0 16px;
  margin: 0 0 8px 0;
  font: 14px Arial,sans-serif;
  font-weight: bold; }

.two-column-box .column-two .notice {
  margin: 0 0 0 0px;
  font: 12px Arial,sans-serif;
  color: #555; }

.two-column-box .column-two .notice ul {
  margin: 5px 0 8px 18px; }

.two-column-box .column-two .notice li {
  margin: 0 0 0px 16px; }

.two-column-box .column-two .notice p {
  margin: 0 16px 0 16px; }

.two-column-box .column-two .notice .field-row-checkbox {
  background: url("images/bg/notice-bottom.gif") left bottom no-repeat;
  padding: 0 0 0px 0;
  margin: 0; }

.two-column-box .column-two .notice .checkbox-row label {
  font: 11px Arial,sans-serif;
  color: #277202;
  font-weight: bold; }

.two-column-box .column-two #business-card-bleed {
  margin: 0 0 20px 0; }

.textarea-row label {
  font: 12px Arial,sans-serif;
  color: #555;
  font-weight: bold;
  display: block;
  margin: 0 10px 5px 0; }

.two-column-box .column-two .text {
  font: 13px Arial,sans-serif;
  color: #555;
  font-weight: bold;
  display: block;
  margin: 0 0 10px 0; }

.textarea-row textarea {
  border-left: 2px solid #ccc;
  border-top: 2px solid #ccc;
  border-bottom: 1px solid #666;
  border-right: 1px solid #666;
  font: 13px Arial,sans-serif;
  line-height: 16px;
  color: #555;
  padding: 1px 0 0 3px; }

.full-column {
  width: 100%; }

.two-column-box .comment-entry {
  border-top: 1px dotted #5f9fbf;
  background-color: #e0f0fe;
  padding: 8px;
  margin: 0 0 2px 0;
  font: 12px Arial,sans-serif;
  color: #555555; }

.two-column-box .comment-entry .comment-left {
  display: block;
  width: 100px;
  float: left; }

.two-column-box .comment-entry .name {
  display: block;
  float: left; }

.two-column-box .comment-entry .date {
  font-weight: bold;
  display: block; }

.two-column-box .comment-entry .text {
  line-height: 18px; }

/* Page */
.page {
  width: 100%;
  min-width: 988px;
  text-align: left;
  margin: auto;
  background-color: #FFFFFF; }

.page #header .content-top {
  margin: 0 0 0 0;
  height: 30px;
  width: 100%;
  background-color: #DBDBDB; }

.page .content {
  clear: both;
  margin: 15px; }

.page .content .entry {
  clear: both; }

.page .content .entry-content {
  margin: 0 0 10px 0; }

.page .content .entry .float-left {
  float: left;
  width: 490px; }

.page .content .entry .float-left .page-content {
  margin: 0 0 0 5px; }

.page .content .entry .float-right {
  float: left;
  width: 200px;
  font: 12px Arial,sans-serif;
  color: #555;
  line-height: 20px; }

.page .content .entry .float-right h3 {
  font: 16px Arial,sans-serif;
  font-weight: bold;
  color: #2f2f2f;
  margin: 0 0 15px 0; }

.page .content .entry .float-right li {
  background: url("images/art/dot.png") left center no-repeat;
  list-style-type: none;
  padding: 0 0 0 12px; }

.page .content .entry .float-right li a {
  color: #2369bf; }

.page .content .entry .float-right p {
  line-height: 15px; }

.page .content .entry .float-right .request-quote {
  margin: 90px 0 0 0;
  display: block; }

.page .content .entry .float-right .request-quote img {
  border: none; }

.page .content .order {
  padding: 15px 0 0 10px; }

.page .content .order .order-row .order-item-product {
  float: left;
  clear: both;
  display: block;
  width: 223px;
  color: #555;
  line-height: 40px;
  height: 40px;
  margin: 0 0 2px 0;
  background: url("images/bg/order-row-left.png") left top no-repeat; }

.page .content .order .order-row .order-item-product .product-name {
  font: px Arial,sans-serif;
  margin: 0 0 0 10px; }

.page .content .order .order-row .order-item-product .order-item-product-image {
  float: left;
  display: block;
  width: 81px;
  height: 40px;
  text-align: center;
  padding: 7px 0 0 0; }

.page .content .order .order-row .order-item-download {
  float: left;
  display: block;
  width: 243px;
  height: 40px;
  margin: 0 0 2px 0;
  background: url("images/bg/order-row-right.png") right top no-repeat; }

.page .content .order .order-row .order-item-download a {
  padding: 0 0 0 20px;
  margin: 0 0 0 34px;
  font: 12px Arial,sans-serif;
  line-height: 40px;
  color: #2369bf;
  text-decoration: none; }

.page .content .order .order-row .order-item-download .pdf-download {
  background: url("images/art/pdf.png") left center no-repeat; }

.page .content .entry .progress {
  float: right; }

.page .content .entry .progress span {
  font: 13px "Arial Narrow",Arial,sans-serif;
  color: #555555;
  text-align: center; }

.page .content .entry .progress strong {
  display: none; }

.page .content .entry .progress .button1 {
  background: url("images/btn/progress-left-active.png") left top no-repeat;
  float: left;
  display: block;
  width: 111px;
  height: 68px; }

.page .content .entry .progress .button2 {
  background: url("images/btn/progress-middle.png") left top no-repeat;
  float: left;
  width: 107px;
  height: 68px; }

.page .content .entry .progress .button3 {
  background: url("images/btn/progress-right.png") left top no-repeat;
  float: left;
  width: 110px;
  height: 68px; }

.page .content h2 {
  font: 20px Arial,sans-serif;
  color: #2f2f2f;
  font-weight: bold;
  margin: 0 0 10px 0; }

.page .content p {
  font: 12px Arial,sans-serif;
  color: #555; }

.page .order-number {
  font: 12px Arial,sans-serif;
  color: #58b005;
  font-weight: bold;
  margin: 5px 0 5px 0; }

.page_links {
  margin: 5px; }

.page_summary {
  margin: 0 0 5px 0;
  font: 11px Arial,sans-serif;
  color: #929292; }

.page_navigation {
  padding: 10px 0 0 0; }

.page_pages {
  margin: 0 5px 0 5px; }

.page_pages a:link, .page_pages a:visited {
  text-decoration: none;
  color: #2369bf;
  padding: 5px 10px 5px 10px;
  border: 1px solid #929292;
  background-color: #fff; }

.page_pages a:active, .page_pages a:hover {
  text-decoration: none;
  color: #2369bf;
  border: 1px solid #929292; }

.page_pages .Current {
  color: #666;
  padding: 5px 10px 5px 10px;
  border: 1px solid #929292;
  background-color: #e0f0fe; }

/* Header */
/* Secondary navigation */
.page #header .secondary-navigation {
  font: 10px Arial,sans-serif; }

.page #header .secondary-navigation .floating {
  float: left;
  width: 100%;
  background-color: #DBDBDB; }

.page #header .secondary-navigation li {
  list-style-type: none;
  display: inline;
  margin: 0 0px 0 3px;
  padding: 0px 0 0 0px;
  background-color: #DBDBDB; }

.page #header .secondary-navigation .last a {
  border: 0;
  border-left: 1px solid #699278;
  padding-left: 4px; }

.page #header .secondary-navigation a {
  padding: 0 0 0 0;
  background-color: #DBDBDB; }

.page #header .secondary-navigation a:link, .page #header .secondary-navigation a:visited {
  text-decoration: none;
  color: #2369bf; }

.page #header .secondary-navigation a:active, .page #header .secondary-navigation a:hover {
  text-decoration: underline; }

/* Main navigation */
.page #header .content-top .primary-navigation {
  font: 14px Arial,sans-serif;
  font-weight: bold;
  float: left; }

.page #header .content-top .primary-navigation ul {
  width: 825px; }

.page #header .content-top .primary-navigation li {
  float: left;
  list-style-type: none;
  display: block; }

.page #header .content-top .primary-navigation a {
  line-height: 30px;
  height: 30px;
  text-align: center;
  display: block; }

.page #header .content-top .primary-navigation .nav-first a {
  width: 116px;
  background-color: #efefef;
  margin-right: 2px;
  font-weight: normal;
  font-style: italic; }

.page #header .content-top .primary-navigation .nav-first .nav-active {
  width: 116px;
  background-color: #ffffff;
  font-weight: bold;
  font-style: normal; }

.page #header .content-top .primary-navigation .nav-second a {
  width: 116px;
  background-color: #efefef;
  margin-right: 2px;
  font-weight: normal;
  font-style: italic; }

.page #header .content-top .primary-navigation .nav-second .nav-active {
  width: 116px;
  background-color: #ffffff;
  font-weight: bold;
  font-style: normal; }

.page #header .content-top .primary-navigation .nav-third a {
  width: 116px;
  background-color: #efefef;
  margin-right: 2px;
  font-weight: normal;
  font-style: italic; }

.page #header .content-top .primary-navigation .nav-third .nav-active {
  width: 116px;
  background-color: #ffffff;
  font-weight: bold;
  font-style: normal; }

.page #header .content-top .primary-navigation .nav-fourth a {
  width: 116px;
  background-color: #efefef;
  margin-right: 2px;
  font-weight: normal;
  font-style: italic; }

.page #header .content-top .primary-navigation .nav-fourth .nav-active {
  width: 116px;
  background-color: #ffffff;
  font-weight: bold;
  font-style: normal; }

.page #header .content-top .primary-navigation .nav-fifth a {
  width: 116px;
  background-color: #efefef;
  margin-right: 2px;
  font-weight: normal;
  font-style: italic; }

.page #header .content-top .primary-navigation .nav-fifth .nav-active {
  width: 116px;
  background-color: #ffffff;
  font-weight: bold;
  font-style: normal; }

.page #header .content-top .primary-navigation .nav-sixth a {
  width: 116px;
  background-color: #efefef;
  font-weight: normal;
  font-style: italic; }

.page #header .content-top .primary-navigation .nav-sixth .nav-active {
  width: 116px;
  background-color: #ffffff;
  font-weight: bold;
  font-style: normal; }

.page #header .content-top .primary-navigation a:link, .page #header .content-top .primary-navigation a:visited {
  text-decoration: none;
  color: #333; }

.page #header .content-top .primary-navigation a:active, .page #header .content-top .primary-navigation a:hover {
  text-decoration: none;
  color: #333; }

/* Content body */
/* Form buttons */
.buttons {
  text-align: center;
  margin-bottom: 20px;
  margin-Left: 20px;
  margin-right: 0px;
  margin-top: 20px;
  border: 1px solid #3079ed;
  color: white;
  text-shadow: 0 1px rgba(0, 0, 0, 0.1);
  background-color: #4d90fe;
  border-radius: 3px;
  width: 125px;
  float: right; }

.buttons a, .buttons .genericBtn {
  text-align: center;
  margin-bottom: 20px;
  margin-Left: 20px;
  margin-right: 0px;
  margin-top: 20px;
  border: 1px solid #3079ed;
  color: white;
  text-shadow: 0 1px rgba(0, 0, 0, 0.1);
  background-color: #4d90fe;
  border-radius: 3px;
  width: 125px;
  float: right; }

input.inputSearchButton {
  text-align: center;
  margin-bottom: 20px;
  margin-Left: 20px;
  margin-right: 0px;
  margin-top: 20px;
  border: 1px solid #3079ed;
  color: white;
  text-shadow: 0 1px rgba(0, 0, 0, 0.1);
  background-color: #4d90fe;
  border-radius: 3px;
  width: 125px;
  float: right; }

input.inputSearchButton, .buttons .genericBtn, .buttons a:link, .buttons a:visited, .buttons-large a:link, .buttons-large a:visited {
  text-align: center;
  margin-bottom: 20px;
  margin-Left: 20px;
  margin-right: 0px;
  margin-top: 20px;
  border: 1px solid #3079ed;
  color: white;
  text-shadow: 0 1px rgba(0, 0, 0, 0.1);
  background-color: #4d90fe;
  border-radius: 3px;
  width: 125px;
  float: right; }

input.inputSearchButton, .buttons .genericBtn, .buttons a:active, .buttons a:hover, .buttons-large a:active, .buttons-large a:hover {
  text-align: center;
  margin-bottom: 20px;
  margin-Left: 20px;
  margin-right: 0px;
  margin-top: 20px;
  border: 1px solid #3079ed;
  color: white;
  text-shadow: 0 1px rgba(0, 0, 0, 0.1);
  background-color: #4d90fe;
  border-radius: 3px;
  width: 125px; }

.buttons .genericBtn {
  text-align: center;
  margin-bottom: 20px;
  margin-Left: 20px;
  margin-right: 0px;
  margin-top: 20px;
  border: 1px solid #3079ed;
  color: white;
  text-shadow: 0 1px rgba(0, 0, 0, 0.1);
  background-color: #4d90fe;
  border-radius: 3px;
  width: 125px; }

.buttons-large {
  text-align: right; }

.buttons-large input, .buttons-large a {
  text-align: center;
  padding: 4px 10px;
  border: 1px solid #3079ed;
  color: white;
  text-shadow: 0 1px rgba(0, 0, 0, 0.1);
  background-color: #4d90fe;
  border-radius: 3px; }

.border-end {
  margin: 0 0 10px 0; }

.buttons-large.strong {
  font-weight: bold; }

.buttons-large a:link, .buttons-large a:visited {
  text-align: center;
  margin-bottom: 20px;
  margin-Left: 20px;
  margin-right: 0px;
  margin-top: 20px;
  border: 1px solid #3079ed;
  color: white;
  text-shadow: 0 1px rgba(0, 0, 0, 0.1);
  background-color: #4d90fe;
  border-radius: 3px;
  width: 125px; }

.links a {
  color: #2369bf;
  margin: 0 5px 0 0; }

.links a:link, .links a:visited {
  text-decoration: underline; }

.links a:active, .links a:hover {
  text-decoration: underline; }

/* Footer */
.page .footer {
  clear: both; }

/* Continue styles */
.continue-holder {
  text-align: center;
  padding: 100px 0; }

.continue {
  border-left: 1px solid #ccc;
  border-bottom: 1px solid #666;
  border-top: 1px solid #ccc;
  border-right: 1px solid #666;
  background-color: #f8f8f8;
  padding: 10px 15px 10px 10px;
  margin: 0 auto;
  text-align: left;
  width: 45%; }

.continue h2 {
  font: 14px Arial,sans-serif !important;
  font-weight: bold !important;
  margin: 0 0 20px 0; }

.continue .field-row label {
  display: block;
  text-align: left;
  margin: 0 0 5px 0; }

.continue .field-row {
  margin: 0 0 5px 0; }

.continue .field-row input {
  width: 97%;
  padding: 5px;
  font: 14px Arial,sans-serif; }

.continue .checkbox-row input {
  margin: 0 5px 0 0; }

.continue .buttons-large {
  text-align: right;
  margin: 0 0 10px 0; }

.continue .entry-content p {
  line-height: 18px; }

.press-box .press-row {
  clear: both;
  padding: 0; }

.press-box .press-row .label {
  padding: 4px 0 0 0;
  line-height: 100%;
  padding: 0 0 4px 0; }

.press-box .press-row .field {
  float: left;
  width: 80px;
  display: block;
  margin: 0 0 7px 0; }

.A2-checkbox {
  margin-left: 5px; }

.A1-checkbox {
  margin-left: 30px; }

#job-details-order .form-data .column-two {
  width: 47%; }

#job-details-order .form-data .column-two label, #job-details-order .form-data .column-two .label {
  width: 100px; }

#job-details-order .form-data .column-two .join-row label {
  width: auto; }

#job-details-order .form-data .column-three {
  width: 20%; }

#packing-dispatch .column-one {
  width: 40%; }

#packing-dispatch .column-two {
  width: 60%; }

#packing-dispatch .label {
  width: 100px; }

#finishing .column-one {
  width: 65%; }

#finishing .column-one label, #finishing .column-one .label {
  width: 100px; }

#finishing .column-one .radio-row label {
  width: auto; }

#finishing .column-two {
  width: 35%; }

#sysconfig .column-one label, #sysconfig .column-one .label {
  width: 160px; }

.job-number-box .big-text {
  margin: 10px 0 0 0;
  text-align: center;
  font-size: 32px;
  letter-spacing: 6px; }

.job-number-box .small-text {
  text-align: center;
  font-size: 14px;
  font-weight: bold; }

#proofs-sent .item {
  padding: 0 0 0 10px; }

#proofs-sent input {
  float: left; }

.widthHeight {
  font-weight: normal !important; }

.column-three .radio-row label {
  text-align: left;
  width: 265px;
  float: right; }

.column-three .radio-row .checkbox input {
  width: 20px;
  float: left;
  clear: both; }

.runs-checkboxes .radio-row .checkbox-spacing label {
  padding: 0; }

.column-three .radio-row .checkbox-spacing {
  margin: 0px;
  padding: 0px;
  width: 300px; }

.staffOverviewPage {
  padding-bottom: 200px; }

.staffOverviewPage dl {
  margin: 0;
  padding: 0;
  font-size: 12px; }

.staffOverviewPage dt {
  font-weight: bold;
  margin-top: 1em;
  font-size: 14px; }

.staffOverviewPage dd {
  padding-left: 20px;
  padding-top: 6px;
  background: #ffffff url(images/btn/help.gif) no-repeat 3px 8px; }

.urgentSpacing {
  padding-bottom: 15px;
  display: block; }

.priceCheckBox {
  margin-right: 5px; }

.priceCheckBox input {
  clear: both;
  float: left;
  display: inline; }

.priceCheckBox label {
  float: left;
  width: 80%;
  display: inline;
  padding-left: 3px; }

.priceContentPrint {
  padding-top: 2px;
  margin-left: 0px; }

.version {
  float: left;
  width: 100%;
  text-align: right;
  background-color: #DBDBDB; }

.portraitlandscape {
  padding-top: 7px !important;
  display: block; }

.portraitlandscape label {
  float: none; }

.portraitlandscape input {
  margin-right: 10px !important;
  margin-left: 118px; }

.resetaddresspadding {
  padding: 1px 8px; }

#sizemsg {
  margin: 2px 0 8px 115px;
  width: 200px; }

.attachmagnet input {
  float: left;
  width: 20px;
  margin-top: 3px; }

.attachmagnet label {
  float: left;
  width: 228px; }

.importanttext {
  color: #ff2222;
  font-weight: bold; }

.roundedcorners {
  background: url(images/businesscardnocorners.gif) no-repeat left top;
  width: 239px;
  height: 101px;
  margin-left: 26%;
  position: relative; }

.topleft {
  position: absolute;
  z-index: 300;
  top: -1px;
  left: 0;
  width: 10px;
  height: 11px; }

.topright {
  position: absolute;
  z-index: 300;
  top: -1px;
  right: 0;
  width: 10px;
  height: 11px; }

.botleft {
  position: absolute;
  z-index: 300;
  bottom: 0;
  left: 0;
  width: 10px;
  height: 12px; }

.botright {
  position: absolute;
  z-index: 300;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 12px; }

#reporttabwrapper {
  border-bottom: 1px solid #E0F0FE;
  height: 32px; }

#reporttabwrapper a {
  border: none;
  display: block;
  text-decoration: none !important;
  background-color: #f8f8f8;
  padding: 8px 40px;
  font-size: 81%;
  font-weight: bold;
  color: #333;
  float: left; }

#reporttabwrapper a.active {
  background-color: #e0f0fe;
  text-decoration: none; }

#reporttabwrapper a:hover {
  background-color: #e0f0fe;
  text-decoration: none !important; }

#reporttabwrapper .mrsize {
  width: 130px;
  margin-right: 2px; }

#reporttabwrapper .rrsize {
  width: 120px; }

fieldset.table-box {
  background-color: #fff;
  border: 0px solid #e0f0fe; }

.formgrouping {
  background-color: #e0f0fe;
  margin-right: 20px;
  padding: 10px; }

.groupheading {
  font-weight: bold;
  font-size: 110%; }

.field-label-top {
  margin-top: 11px; }

.field-label-top label {
  font-weight: bold;
  display: block; }

.inputstock {
  width: 90%; }

.inputsect {
  width: 60%; }

.inputsize {
  width: 60%; }

.inputmethod {
  width: 60%; }

.inputqty {
  width: 60%; }

.warning {
  color: #FF0000; }

#DialogContent {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: url(images/semitile.png); }

#DialogContent .contentHolder {
  text-align: left;
  min-width: 150px;
  width: 640px;
  height: 60%;
  min-height: 200px;
  max-height: 345px;
  overflow: auto;
  margin-top: 10%;
  margin-left: auto;
  margin-right: auto;
  background: white;
  padding: 15px;
  border: solid 1px gray;
  background-color: #F8F8F8; }

.dialogTitle {
  color: #333;
  margin: 5px 0 20px 0 !important;
  padding: 0 !important;
  font-size: 16px !important; }

#DialogContent .contentHolder textarea {
  width: 98%;
  height: 180px;
  margin: 0;
  padding: 5px; }

.explanation {
  display: block;
  font-style: italic; }

.DigitalStyle {
  font-weight: bold !important;
  font-style: italic !important; }

.gray {
  background-color: #ebebe4;
  border: 1px solid #CCC;
  padding: 2px 2px; }

li.ui-menu-item {
  list-style-type: none;
  font-size: 13px; }

select.monospace, select.monospace option {
  font-family: monospace;
  white-space: pre; }




.tableContainer table thead th.widthSmallJobs,
.tableContainer table td.widthSmallJobs	{
	width: 4% !important; /*Firefox needs this*/
}

.tableContainer table thead th.widthMedJobs,
.tableContainer table td.widthMedJobs	{
	width: 12% !important; /*Firefox needs this*/
}

.tableContainer table thead th.widthLargeJobs,
.tableContainer table td.widthLargeJobs	{
	width: 16% !important; /*Firefox needs this*/
}

.tableContainer table thead th.widthSmall,
.tableContainer table tbody td.widthSmall	{
	width: 4% !important; /*Firefox needs this*/
}

.tableContainer table thead th.widthMed,
.tableContainer table tbody td.widthMed	{
	width: 8% !important; /*Firefox needs this*/
}

.tableContainer table thead th.widthLarge,
.tableContainer table tbody td.widthLarge	{
	width: 12% !important; /*Firefox needs this*/
}




#processing div {
	text-align: center;
	padding-bottom: 14px;
}

td.hidejob {
 	background:url('<%= AppThemesPath %>/lep/images/close.gif') 3px 8px no-repeat;
}

td.showjob {
 	background:url('<%= AppThemesPath %>/lep/images/open.gif') 3px 8px no-repeat;
}

.searched td {
	background-color:#f8f7d2;
}

.uneffected td {
	background-color:#fee3d2;
}


.altjobrow td {
	background-color: #e4fef0;
}

.jobrow td {

}

.h3 {color:#4a4a42;font-size:100%;font-weight:bold; }

td.widthSmall a.addRemoveButton {border-bottom:none;}

#runCheckboxes {margin-left:20px;font-weight: bold;}
#runCheckboxes input {margin-left:7px;}
#runCheckboxes label {margin-left:4px;}
