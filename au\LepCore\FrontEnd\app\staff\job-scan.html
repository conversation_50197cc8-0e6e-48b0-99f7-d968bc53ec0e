<span class="dropdown" ng-if="::jobStatusesToScan.length">
    <span class="dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
        <span class="glyphicon glyphicon-barcode hand" ></span>
    </span>
    <div class="dropdown-menu" style="width: 500px !important; font-size: 10pt!important;">
        <span ng-repeat="s in ::jobStatusesToScan" class="col-xs-4">
            <a ng-click="scanJob(s)">

                {{::$root.enums.ValueDesc.JobStatusOptions[s]}}
            </a>
        </span>
    </div>
</span>
