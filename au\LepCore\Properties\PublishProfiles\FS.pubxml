﻿<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit http://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>FileSystem</WebPublishMethod>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>x64</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish />
    <LaunchSiteAfterPublish>true</LaunchSiteAfterPublish>
    <ExcludeApp_Data>false</ExcludeApp_Data>
    <PublishFramework>net461</PublishFramework>
    <UsePowerShell>True</UsePowerShell>
    <publishUrl>C:\FS\LEPCoreBuilds</publishUrl>
    <DeleteExistingFiles>true</DeleteExistingFiles>
    <TargetFramework>net8.0-windows7.0</TargetFramework>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <ProjectGuid>6ca134b9-ed76-40c0-85ef-4292bb63c243</ProjectGuid>
    <PublishReadyToRun>true</PublishReadyToRun>
    <SelfContained>false</SelfContained>
  </PropertyGroup>

  <!-- Import MSBuild Community Tasks if needed -->
  <PropertyGroup>
    <FrontEndDir>$(ProjectDir)FrontEnd</FrontEndDir>
    <FrontEndDistDir>$(FrontEndDir)\dist</FrontEndDistDir>
    <PublishWwwrootDir>$(PublishUrl)\wwwroot</PublishWwwrootDir>
  </PropertyGroup>

  <!-- Custom targets to run before and after publish -->
  <Target Name="RunGulpRelease" BeforeTargets="BeforeBuild">
    <Message Text="Running gulp release for frontend..." Importance="high" />
    <!-- Use a PowerShell script instead of direct command -->
    <Exec Command="powershell -Command &quot;&amp; { Set-Location '$(FrontEndDir)'; gulp release }&quot;" />
    <Message Text="Gulp release completed successfully." Importance="high" />
  </Target>

  <Target Name="CopyFrontEndOutput" AfterTargets="CopyAllFilesToSingleFolderForPackage">
    <Message Text="Copying frontend output to publish directory..." Importance="high" />
    <!-- Use MSBuild's Copy task instead of xcopy -->
    <ItemGroup>
      <FrontEndFiles Include="$(FrontEndDistDir)\**\*.*" />
    </ItemGroup>
    <Copy
      SourceFiles="@(FrontEndFiles)"
      DestinationFiles="@(FrontEndFiles->'$(PublishWwwrootDir)\%(RecursiveDir)%(Filename)%(Extension)')"
      SkipUnchangedFiles="true" />
    <Message Text="Frontend files copied successfully." Importance="high" />
  </Target>
</Project>
