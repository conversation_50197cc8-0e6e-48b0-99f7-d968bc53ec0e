﻿<a class="pull-right" ui-sref="staff.customers-view-wl({id: vm.Id})">Print Portal setup</a>
<form name="form">
    <div leppane="Customer Details" visible="true">

        <div class="form-horizontal">
            <div class="row">
                <div class="col-sm-6">

                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="bus-name">Business Name *</label>
                        <div class="col-sm-8">
                            <input name="Name" type="text" class="form-control input" ng-maxlength="200"
                                   ng-model="vm.Name" ng-model-options="{allowInvalid:true}" ng-required="true" lep-unique-businessname
                                   user-id="vm.Id" />
                            <div role="alert" class="help-block">
                                <div ng-show="form.Name.$error.required">required</div>

                                <div ng-show="form.Name.$error.maxlength">too long</div>
                                <div ng-show="form.Name.$error.unique">already taken</div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="username">Username *</label>
                        <div class="col-sm-8 readonly-text">
                            <input name="UserName" type="text" ng-model="vm.Username" ng-model-options="{allowInvalid:true}" ng-disabled="vm.Id != 0" class="form-control input" ng-required="true" ng-minlength="5" ng-maxlength="40" lep-unique-username user-id="vm.Id">
                            <div role="alert" class="help-block">
                                <div ng-show="form.UserName.$error.required">required</div>
                                <div ng-show="form.UserName.$error.minlength">too short</div>
                                <div ng-show="form.UserName.$error.maxlength">too long</div>
                                <div ng-show="form.UserName.$error.unique">already taken</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="order-no">Payment terms </label>
                        <div class="col-sm-8">
                            <div>
                                <label>
                                    <input name="PaymentTerms" type="radio" ng-model="vm.PaymentTerms" ng-value="0 * 1" />
                                    On Account
                                </label>
                            </div>
                            <div>
                                <label>
                                    <input name="PaymentTerms" type="radio" ng-model="vm.PaymentTerms" ng-value="1 * 1" />
                                    Cash Before Dispatch (COD)
                                </label>
                            </div>
                            <div>
                                <label>
                                    <input name="PaymentTerms" type="radio" ng-model="vm.PaymentTerms" ng-value="2 * 1" />
                                    Pre-pay
                                </label>
                            </div>

                            <div>
                                <label>
                                    <input name="PaymentTerms" type="radio" ng-model="vm.PaymentTerms" ng-value="3 * 1" />
                                    On Hold
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label">Credit limit</label>
                        <div class="col-sm-8  form-control-static">
                            <p>{{::vm.CreditLimit | currency}}</p>
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label">MYOB balance</label>
                        <div class="col-sm-8  form-control-static">
                            <p>{{::vm.MYOBBalance | currency}}</p>
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label">LEPOnline balance</label>
                        <div class="col-sm-8  form-control-static">
                            <p>{{::vm.LEPOnlineBalance | currency}}</p>
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label">Available balance</label>
                        <div class="col-sm-8  form-control-static">
                            <p>
                                {{::vm.AvaiableBalance| currency}}
                                <span class="text-muted">
                                    ({{::vm.CreditLimit | currency}} - ({{::vm.MYOBBalance | currency}}+{{::vm.LEPOnlineBalance | currency}}))
                                </span>
                            </p>
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="order-no">Charge GST</label>
                        <div class="col-sm-8">

                            <label class="">
                                <input name="checkbox" type="checkbox" ng-model="vm.IsChargedGST" />
                            </label>
                        </div>
                    </div>
                    <!--<div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="order-no">Enrolled in IVR</label>
                        <div class="col-sm-8">

                            <label class="">
                                <input name="checkbox" type="checkbox" ng-model="vm.IsEnrolledInIVR" />
                            </label>
                        </div>
                    </div>-->
                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="notes">Customer Logo</label>
                        <div class="col-sm-8">
                            <img ng-src="{{logo}}" style="max-width: 50%;" no-image="images/no-image.png" />

                            <div ng-if="vm.Id != 0">
                                <a class="btn" ng-click="uploadCustomerLogo()"> <i class="glyphicon  glyphicon-cloud-upload  "></i> Upload Customers logo</a>
                            </div>
                        </div>
                    </div>

                    <!--
                    <div class="form-group form-group-sm">
                      <label class="control-label col-sm-4">Logo File</label>
                         <div class="col-md-5">
                             <img ng-src="{{logo}}" style="width: 100%;" />
                             <input type="file" ng-file-model="file" accept=".png,.jpg" />
                             <progress style="width: 100%;" value="{{file.loaded}}" max="{{file.size}}"></progress>
                             <button ng-click="uploadLogo(file)" class="btn btn-sm" ng-show="file.file"> Upload logo</button>
                         </div>
                     </div>
                    -->
                </div>

                <div class="col-sm-6">

                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label">Customer #</label>
                        <div class="col-sm-8 readonly-text bold">
                            {{::vm.CustomerNr}}
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="MYOB">MYOB  </label>
                        <div class="col-sm-8">
                            <!--<input name="MYOB" type="text" class="form-control input" ng-model="vm.MYOB" ng-model-options="{allowInvalid:true}" lep-unique-myob user-id="vm.Id" />-->
                            <input name="MYOB" type="text" class="form-control input" ng-model="vm.MYOB" ng-model-options="{allowInvalid:true}"
                                   lep-unique-myob user-id="vm.Id" />
                            <div ng-shows="form.MYOB.$error" role="alert" class="help-block">
                                <div ng-show="unique">already taken</div>
                            </div>
                        </div>
                    </div>


                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="prod-price-code">Business type</label>
                        <div class="col-sm-8">
                            <select class="form-control input" ng-model="vm.BusinessType"
                                    ng-options="k  as k for k in BusinessTypes"></select>
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="abn">ABN *</label>
                        <div class="col-sm-8">
                            <input name="abn" type="text" class="form-control input" ng-model="vm.ABN" abn />
                            <div role="alert" class="help-block">
                                <div ng-show="form.abn.$error.required">required</div>
                                <div ng-show="form.abn.$error.abn">not a valid ABN</div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="order-no">System Access</label>
                        <div class="col-sm-8">
                            <div>
                                <label>
                                    <input name="SystemAccess" type="radio" ng-model="vm.IsEnabled" ng-value="true" />
                                    Allowed
                                </label>
                            </div>
                            <div>
                                <label>
                                    <input name="SystemAccess" type="radio" ng-model="vm.IsEnabled" ng-value="false" />
                                    Denied
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="site-loc">Site Location</label>
                        <div class="col-sm-8">
                            <select ng-model="vm.SiteLocation" id="site-loc" class="form-control input" ng-options="k*1 as v for (k,v) in $root.enums.ValueDesc.SiteLocation"></select>
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="cour-pref">Preferred Courier</label>
                        <div class="col-sm-8">
                            <select ng-model="vm.Size" id="cour-pref" class="form-control input">
                                <option selected="selected" value="">-- Select --</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group form-group-sm" title="Toggles visibility of the 'Send samples' tick box in customers job screen">
                        <label class="col-sm-4 control-label" for="AllowedSamples">Allow samples</label>
                        <div class="col-sm-8">
                            <div class="checkbox">
                                <label class="">
                                    <input id="AllowedSamples" type="checkbox" ng-model="vm.AllowedSamples" />
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group form-group-sm" title="if ticked, on a new job, 'Send samples' will be ticked by default">
                        <label class="col-sm-4 control-label" for="SendSamples">Send samples</label>
                        <div class="col-sm-8">
                            <div class="checkbox">
                                <label class="">
                                    <input id="SendSamples" type="checkbox" ng-model="vm.SendSamples" />
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="prod-price-code">Product Price Code*</label>
                        <div class="col-sm-8">
                            <select class="form-control input" ng-model="vm.ProductPriceCode"
                                    ng-options="k  as (k +' - ' + v) for (k,v) in CustomerProductPricing"></select>
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="freight-price-code">Freight Price Code*</label>
                        <div class="col-sm-8">
                            <select class="form-control input" ng-model="vm.FreightPriceCode"
                                    ng-options="k  as (k +' - ' + v) for (k,v) in CustomerFreightPricing"></select>
                        </div>
                    </div>

                    <div class="form-group form-group-sm" title="">
                        <label class="col-sm-4 control-label" for="QTP">QTP</label>
                        <div class="col-sm-8">
                            <div class="checkbox">
                                <label class="">
                                    <input id="QTP" type="checkbox" ng-model="vm.QTP" />
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        <label class="col-xs-4 control-label">First order date</label>
                        <div class="col-xs-4">

                            <input type="text" size="10" class="form-control" ng-model="vm.FirstOrderDate" data-date-format="dd-MMM-yy" data-autoclose="1" placeholder="Date" bs-datepicker>
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        <label class="col-xs-4 control-label">Returned lapse date</label>
                        <div class="col-xs-4">
                            <input type="text" size="10" class="form-control" ng-model="vm.ReturnedLapseDate" data-date-format="dd-MMM-yy" data-autoclose="1" placeholder="Date" bs-datepicker>
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="">Customer status</label>
                        <div class="col-sm-8">
                            <select class="form-control input" ng-model="vm.CustomerStatus"
                                    ng-options="k for k in CustomerStatus">
                                <option value="">-- Select --</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="">Franchise Code</label>
                        <div class="col-sm-8">
                            <select class="form-control input" ng-model="vm.FranchiseCode"
                                    ng-options="k.code  as k.name for k in Franchise"
                                    ng-disabled="vm.Id != 0 && !$root.globals.IsA">
                                <option value="">-- Select --</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="">Sales consultant</label>
                        <div class="col-sm-8">
                            <select class="form-control input" ng-model="vm.SalesConsultant"
                                    ng-options="k  as k for k in SalesConsultant"
                                    ng-disabled="vm.Id != 0 && !$root.globals.IsA">
                                <option value="">-- Select --</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        <label class="col-sm-4 control-label" for="">Customer Potential</label>
                        <div class="col-sm-8">
                            <select class="form-control input" ng-model="vm.Potential"
                                    ng-options="k  as k for k in ['A','B', 'C', 'D']">
                                <option value="">-- Select --</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        <label class="control-label" for="notes">Notes</label><br />
                        <div class="col-sm-12 lpad0">
                            <textarea id="notes" rows="6" class="form-control input" ng-model="vm.Notes" auto-grow></textarea>
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        <label class="control-label" for="prod-notes">Production Notes</label><br />
                        <div class="col-sm-12 lpad0">
                            <textarea id="prod-notes" rows="6" class="form-control input" ng-model="vm.ProductionNotes" auto-grow></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-sm-12">
            <div class="form-horizontal col-sm-12">
                <div class="row">
                    <div class="form-actions pull-right">
                        <a ui-sref="staff.customers" class="btn"> <i class="glyphicon glyphicon-chevron-left"></i> Back </a>

                        <button type="submit" class="btn btn-default" ng-click="save()"> <i class="glyphicon glyphicon-floppy-save"></i> Save Customer</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div leppane="Email">
        <div class="form-horizontal ">
            <div class="form-group form-group-sm">
                <label class="col-sm-4 control-label" for="job-notifiications">Send Job Status Notifications to *</label>
                <div class="col-sm-6">
                    <input id="job-notifiications" type="text" class="form-control input" ng-model="vm.Email" ng-required="true" />
                </div>
            </div>
            <div class="form-group form-group-sm">
                <label class="col-sm-4 control-label" for="notif-type">Notification Type</label>
                <div class="col-sm-6">
                    <select ng-model="vm.NotificationType" ng-required="true" id="notif-type" class="form-control input" ng-options="k*1 as v for (k,v) in $root.enums.ValueDesc.NotificationType"></select>
                </div>
            </div>
            <div class="form-group form-group-sm">
                <label class="col-sm-4 control-label" for="send-account">Send Accounts to </label>
                <div class="col-sm-6">
                    <input id="send-account" type="text" class="form-control input" ng-model="vm.AccountEmail" />
                </div>
            </div>
            <div class="form-group form-group-sm">
                <label class="col-sm-4 control-label" for="other-materials">Send all other materials including promotions to  </label>
                <div class="col-sm-6">
                    <input id="other-materials" type="text" class="form-control input" ng-model="vm.OtherEmail" />
                </div>
            </div>
        </div>
    </div>

    <div leppane="Contacts List" visible="true">
        <div lep-contact-list lep-contact-list-details contacts="vm.Contacts" receive-marketing-emails="true"></div>
    </div>

    <div leppane="Address details">
        <div class="form-horizontal">
            <div class="row">

                <div class="col-sm-6">
                    <div class="form-group form-group-sm">
                        <div class="col-sm-9">
                            <h6>Billing address</h6>
                        </div>
                    </div>

                    <div address-details address="vm.BillingAddress"></div>
                    <!--<pre>{{vm.BillingAddress|json}}</pre>-->
                </div>
                <div class="col-sm-6">
                    <div class="form-group form-group-sm">
                        <div class="col-sm-9">
                            <h6>Delivery address</h6>
                        </div>
                    </div>

                    <div>
                        <div address-details address="vm.PostalAddress" noteditable="vm.PostalIsBilling"></div>
                    </div>
                    <div class="form-group form-group-sm">

                        <div class="col-sm-10 col-sm-offset-2">
                            <label class="checkbox">
                                <input name="checkbox" type="checkbox" ng-model="vm.PostalIsBilling" ng-change="makePostalSameAsBilling(vm.PostalIsBilling)" />
                                Customer delivery address is the same as billing address
                            </label>
                        </div>
                    </div>
                    <!--<pre>{{vm.PostalAddress|json}}</pre>-->
                </div>
            </div>
        </div>

    </div>




    <div ng-if="vm.Id != 0" leppane="Merge customer">
        <div class="form-horizontal">
            <div class="row">
                <div class="col-sm-6">
                    <fieldset>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-4 control-label" for="username">Merge to Username</label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <input type="text" class="form-control input" ng-model="toCust" />
                                    <span class="input-group-btn">
                                        <button ng-click="mergeCustomer(vm.Id, toCust)" class="btn">Merge</button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>
        </div>
    </div>

    <div leppane="Password" visible="true">

        <div class="row">
            <div class="col-sm-6">

                <div class="form-horizontal">
                    <!-- <legend>Login</legend> -->

                    <div class="form-group form-group-sm">
                        <label class="control-label col-sm-4">Password</label>
                        <div class="col-sm-8">
                            <input class="form-control" type="text" ng-model="vm.Password" />
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="control-label col-sm-4">Confirm Password</label>
                        <div class="col-sm-8">
                            <input class="form-control" type="text" ng-model="vm.Password1" />
                        </div>
                    </div>
                    <!--<div class="form-group form-group-sm">
                        <label class="control-label col-sm-4"></label>
                        <div class="col-sm-5">
                            <button class="btn btn-default" type="button" ng-click="updatePassword(vm.Password)" ng-disabled="!vm.Password || !(vm.Password == vm.Password1)">Update password</button>
                        </div>
                    </div>-->
                </div>
            </div>
        </div>
    </div>

    <div leppane="Other" visible="true">
        <div class="row">
            <div class="form-group form-group-sm">
                <label class="control-label col-xs-2">Templates denied for this customer </label>
                <div class="col-xs-10">
                    <ui-select multiple ng-model="vm.DeniedTemplates" theme="bootstrap"
                               close-on-select="false" style="width: 100%;height: 100px;" title="Choose a category to deny">
                        <ui-select-match placeholder="Select templates...">
                            {{$item.Name}}
                        </ui-select-match>

                        <ui-select-choices repeat="c.Id as c in allTemplatesName | filter:$select.search">
                            {{c.Name}}
                        </ui-select-choices>
                    </ui-select>
                </div>
            </div>

            <div class="form-group form-group-sm">
                <label class="control-label col-xs-2">
                </label>
            </div>
        </div>
    </div>


    <div leppane="Files" visible="true">
        <div class="row">
            <div class="form-group form-group-sm">
                <label class="control-label col-xs-2"></label>
                <div class="col-xs-10"> 
                    <a ng-click="createInvoice()">create Invoice  </a>
                </div>
            </div>

            <div class="form-group form-group-sm">
                <label class="control-label col-xs-2">
                </label>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-sm-12">
            <div class="form-horizontal col-sm-12">
                <div class="row">
                    <div class="form-actions pull-right">
                        <a ui-sref="staff.customers" class="btn"> <i class="glyphicon glyphicon-chevron-left"></i> Back </a>
                        <!--<button type="submit" class="btn">Remove Customer</button>
                        <button type="submit" class="btn">New order</button>-->
                        <button class="btn" ng-click="resetPassword()">

                            <span ng-if="vm.Password  && vm.Password == vm.Password1">Set password </span>
                            <span ng-if="!vm.Password || vm.Password != vm.Password1">Reset password</span>
                        </button>

                        <button type="submit" class="btn btn-default" ng-click="save()"> <i class="glyphicon glyphicon-floppy-save"></i> Save Customer</button>

                        <!--<button type="submit" class="btn" ng-click="patch()">Patch Customer</button>-->
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<!--<pre>
{{vm.Username}}
{{vm.Name}}

{{vm | json}}</pre>
-->
