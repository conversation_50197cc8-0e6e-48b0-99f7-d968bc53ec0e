using lep.address.impl;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Linq;
namespace LepCore.Controllers
{
	[Produces("application/json")]
	[Route("api/[controller]")]
	public class PostCodeController : Controller
	{
		public PostCodeController()
		{
		}

		[HttpGet("GetSuburbs")]
		public dynamic GetSuburbs([FromQuery] string postcode, [FromServices] NHibernate.ISession session)
		{
			//var auspostSuburbs = AusPostSuburb.Search(postcode);
			//return new { Data = auspostSuburbs };
			try
			{
				var auspostSuburbs = session
						.CreateSQLQuery($"Select postcode,locality,state from PostCode2 where postcode = {postcode} order by 2")
						.List<object[]>()
						.Select(x => new AusPostSuburb { PostCode = x[0].ToString(), Suburb = (string)x[1], State = (string)x[2] });

				return new { Data = auspostSuburbs };
			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
				return null;
			}

		}
	}
}
