﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using System.Diagnostics;
using System.Threading.Tasks;

namespace LepCore.Middlewares
{
	public class TimingMiddleware
	{
		private readonly RequestDelegate _next;

		public TimingMiddleware(RequestDelegate next)
		{
			_next = next;
		}

		public async Task Invoke(HttpContext httpContext)
		{



			var stopWatch = new Stopwatch();
			stopWatch.Start();

			await _next(httpContext);

			stopWatch.Stop();
			// Not yet supported in any browser dev tools
			httpContext.Response.Headers.Add("Server-Timing", $"app;dur={stopWatch.ElapsedMilliseconds}.0");

		}
	}

	// Extension method used to add the middleware to the HTTP request pipeline.
	public static class TimingMiddlewareExtensions
	{
		public static IApplicationBuilder UseTimingMiddleware(this IApplicationBuilder builder)
		{
			return builder.UseMiddleware<TimingMiddleware>();
		}
	}
}
