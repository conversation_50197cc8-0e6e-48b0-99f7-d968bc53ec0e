﻿<div>
	<style type="text/css">
		.rjspec td {
			font-family: arial,helvetica,verdana;
			padding-left: 5px;
			padding-top: 4px;
			padding-bottom: 4px;
		}

			.rjspec td td {
				font-size: 100%;
				padding-top: 2px;
				padding-bottom: 2px;
			}

		.rjspec tr.dotted td {
			border-right: 1px dotted #cccccc;
			border-bottom: 1px dotted #cccccc;
			font-size: 90%;
		}

		.rjspec .border-bottom {
			border-bottom: 1px dotted #cccccc;
		}

		.rjspec .notify-state {
			background-color: #eeeeee;
		}

		.rjspec .a1 {
			border: solid thin black;
		}


		.rjspec .a2 {
			border-left: solid thin black;
			border-bottom: solid thin black;
		}

		table.rjspec {
			border: solid thin black;
			border-collapse: collapse;
			background-color: white !important;
		}
	</style>

	<table class="rjspec" border="0" cellpadding="0" cellspacing="0" style="">
		<tr>
			<td colspan="7" style="border-right: solid thin black; border-bottom: solid thin black;">
				<table width="100%" border="0" cellpadding="2" cellspacing="0">
					<tr>
						<td style="border-left: none; border-bottom: solid thin black; font-size: 16px;">
							<strong>Business Card Run Sheet: </strong> {{run.Id}}
							<br />
							<strong>Date:</strong>  {{ now | datex:'dd/MM/YYYY HH:mm:ss'  }} <br />
							<strong>Stock:</strong> {{ run.Stock.Name}} 							<br />
							<strong>Cello:</strong> {{ enums.ValueDesc.RunCelloglazeOptions[run.Celloglaze] }}

						</td>
						<td width="14" style="border-bottom: solid thin black;">
							<b style="font-size: 20px">
								{{run.NumOfPressSheets}}

							</b>
						</td>
					</tr>
					<tr>
						<td colspan="2" style="border-left: none;">
							<strong>Imposition By: </strong>
							{{ globals.User.FirstName}} {{ globals.User.LastName}}
						</td>
					</tr>
				</table>
			</td>
			<td colspan="8" style="border-bottom: solid thin black; text-align: center; vertical-align: middle"
				height="80">
				<div class="barcode">R{{::run.Id}}</div>



				<label id="bcBarcodelabel">R{{::run.Id}}</label>
			</td>
		</tr>
		<tr>
			<td width="100" class="a1">
				<a ng-click="toggleSort('SlotX')">Slot</a>
			</td>
			<td class="a1">
				<a ng-click="toggleSort('Id')">Job #</a>
			</td>
			<td class="a1">
				<strong><a ng-click="toggleSort('OrderCustomerName')">Customer </a></strong>
			</td>
			<td class="a2">
				<strong>Job Name</strong>
			</td>
			<td class="a2">
				<strong>500 Qty</strong>
			</td>
			<td class="a2">
				<strong>Send Sam.</strong>
			</td>
			<td width="60" class="a1">
				<strong> Size</strong>
			</td>

			<td width="60" class="a1">
				<strong>Cello</strong>
			</td>
		

			<td width="60" class="a1">
				<strong>Score</strong>
			</td>
			<td width="45" class="a1">
				<strong>DieCut</strong>
			</td>
			<td width="45" class="a1">
				<strong>Corner</strong>
			</td>
			<td class="a1">
				<strong>Hole Drilling</strong>
			</td>
			<td class="a1">
				<strong>Special Comments</strong>
			</td>
		</tr>
		<tr ng-repeat="j in run.Jobs | orderBy:SortField:SortDir" class="dotted">
			<td>
				{{::j.Slots.join(',')}}
			</td>
			<td>
				{{::j.Id}}
			</td>
			<td>
				{{  j.OrderCustomerName }}
			</td>
			<td>
				
				<span style="color:red; font-weight:bold" ng-if="j.IsRestart">(Restart)</span> {{ j.Name }} 
				
				
			</td>
			<td>
				{{ getHalfQty(run.IsHalfBC,j.Quantity) }}
			</td>
			<td>
				{{(j.SendSamples? "Yes" :"")}}
			</td>
			<td class="pre">
				{{j.FinishedSizeStr}}
			</td>
			<td>
				<span ng-if="[5,7,8,9].indexOf(j.FrontCelloglaze) > -1">
					{{$root.enums.ValueDesc.JobCelloglazeOptions[j.FrontCelloglaze]}} ({{::j.FoilColour}})
				</span>
			
			</td>
			 

			<td>
				{{ ( j.FoldedSize == null? "" 
				: (j.FoldedSize.PaperSize.Name != "Custom") ? "Yes" :
				  ( j.FoldedSize.Width + "x" + j.FoldedSize.Height)
				   )
				}}
			</td>
			<td>
				{{j.CustomDieCut}}
			</td>
			<td>
				<div ng-if="j.Corners.length">
					<img src="/images/corners/corners_{{j.Corners}}.gif" />
				</div>


			</td>
			<td>
				{{j.HoleDrilling!=0?  enums.ValueDesc.HoleDrilling[j.HoleDrilling] : '' }}
				<span ng-if="j.Magnet==true"> <br/>Magnet: Yes</span>
			</td>
			<td class="nowrap">{{ j.Comment }}
			</td>
		</tr>
	</table>

</div>
