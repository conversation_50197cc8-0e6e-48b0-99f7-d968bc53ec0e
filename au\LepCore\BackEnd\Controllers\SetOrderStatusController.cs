using lep;
using lep.configuration;
using lep.despatch.impl;
using lep.job;
using lep.order;
using lep.user;
using LepCore.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;

namespace LepCore.src.Controllers
{
	[AllowAnonymous]
	public class SetOrderStatusController : Controller
	{
		public SetOrderStatusController(
			PrintEngine printEngine,
			IJobApplication jobApplication,
			LabelPrinterApplication labelPrintApp,
			IOrderApplication orderApp
			, IUserApplication userApp)
		{
			_printEngine = printEngine;

			_jobApp = jobApplication;
			_orderApp = orderApp;
			_userApp = userApp;
			_labelPrintApp = labelPrintApp;
		}

		private LabelPrinterApplication _labelPrintApp;
		protected IOrderApplication _orderApp;
		protected IConfigurationApplication _configApp;
		protected IUserApplication _userApp;
		protected IJobApplication _jobApp;
		protected PrintEngine _printEngine;
		//private IList<string> _permitAddress;
		private IOrder order;

		[HttpGet]
		[HttpPost]
		[Route("setorderstatus.aspx")]
		public IActionResult Index([FromQuery] int Id, [FromQuery] string Status)
		{
			return Page_Load(Id, Status);
		}
		protected IActionResult Page_Load(int orderid, string status)
		{
			try
			{
				if (!IsValidAddress())
				{
					return GenerateErrorMsg("invalid address");
				}

				order = _orderApp.GetOrder(orderid);
				if (order == null)
				{
					return GenerateErrorMsg("order not found");
				}

				var file = new FileInfo(string.Format("{0}/success.txt", LepGlobal.Instance.ArtworkDirectory(order).FullName));
				if (file.Exists)
				{
					file.Delete();
				}
				file = new FileInfo(string.Format("{0}/error.txt", LepGlobal.Instance.ArtworkDirectory(order).FullName));
				if (file.Exists)
				{
					file.Delete();
				}

				SetOrderStatus();
			}
			catch (Exception ex)
			{
				return GenerateErrorMsg(ex.Message);//+ ex.StackTrace
			}

			return Ok();
		}

		private IActionResult GenerateErrorMsg(string error)
		{
			var errorFile = new FileInfo(string.Format("{0}/error.txt", LepGlobal.Instance.ArtworkDirectory(order).FullName));
			var writer = errorFile.CreateText();
			writer.Write(error);
			writer.Close();
			return new ObjectResult(new { success = false, message = error });
		}

		private bool IsValidAddress()
		{
			return true;
			//foreach (string permit in permitAddress) {
			//    if ((new Regex(permit)).IsMatch(Request.UserHostAddress)) {
			//        return true;
			//    }
			//}
			//return false;
		}

		private void SetOrderStatus()
		{
			//var IPAddress = HttpContext.Connection.RemoteIpAddress.MapToIPv4().ToString();
			var IPAddress = Request.HttpContext.Connection.RemoteIpAddress.ToString();
			var staff = _userApp.GetStaffByIP(IPAddress);
			_orderApp.SubmitReady(order, staff);

			var successFile = new FileInfo(string.Format("{0}/success.txt", LepGlobal.Instance.ArtworkDirectory(order).FullName));
			var writer = successFile.CreateText();
			writer.Write("order is ready");
			writer.Close();
		}
	}
}
