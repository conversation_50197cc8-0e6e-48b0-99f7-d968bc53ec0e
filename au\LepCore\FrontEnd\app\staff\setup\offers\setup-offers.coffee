appStaff = angular.module('app.staff')

appStaff.controller 'StaffSetupOffersController', [
	'$scope'
	'lepApi2'
	'enums'
	'JobService'
	'OrderService',  'Utils', '$http', '$stateParams'
	($scope, lepApi2,  enums, JobService, OrderService, Utils, $http,  $stateParams) ->


		$scope.vm  = {}
		$scope.filters =
			Promotion  : null
			Customers   : []


		$scope.clear = () -> 
			$scope.filters =
				Promotion  : null
				Customers   : []

		$scope.offers = { List : []}
		$scope.promos = []
		$scope.customers = []

		# prefill the top promos drop down
		lepApi2.get("offers/getPromotions").then (r) ->
			$scope.promos = r

		# search offers by promo(P) or customer Q
		$scope.search = () ->
			n = $scope.filters
			p = n.Promotion?.Id || 0
			c =  _.map(n.Customers,'Id')
			if p is 0  and c.length is 0
				toastr.info 'Please use the search filters to narrow down the offer listing'
				return
			searchFilters = {p,c}
			lepApi2.get("offers/search", searchFilters).then (d) ->
				for dx in d
					dx.DateOffered = new Date(dx.DateOffered)
					dx.DateOfferEnds = new Date(dx.DateOfferEnds)
					#dx.DateTakenUp = new Date(dx.DateTakenUp)
				$scope.offers = { List: d }

		$scope.$watch('filters', (n)->
			$scope.search()
		, true)


		$scope.addOffer = () ->

			d = angular.copy($scope.filters)
			d.Id = 0
			d.DateOffered   = $scope.vm.DateOffered
			d.DateOfferEnds = $scope.vm.DateOfferEnds
			d.AllowReuse    = $scope.vm.AllowReuse

			lepApi2.post("offers/Create2", d).then (r)->
				toastr.info('Offer(s) added')
				$scope.search()

		$scope.searchCust = (s) ->
			lepApi2.get("offers/getCustomers", {search: s}, true).then (list) ->
				$scope.customers = list


		$scope.deleteOffer = (id) ->
			lepApi2.post("offers/#{id}/delete").then (r) ->
				$scope.search()

		$scope.updateOffer = (o) ->
			lepApi2.post("offers/#{o.Id}/update",o).then (r) ->
				$scope.search()
	]
