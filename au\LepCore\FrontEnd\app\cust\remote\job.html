﻿<br />
<form name="jobDetailsForm">
    <div class="alert alert-danger"
         ng-show="job.FoldedSize.PaperSize.Id == 12 && !job.SpecialInstructions && job.Visibility.saveButton">
        Please also supply special instruction
    </div>

    <div class="row">
        <div class="col-md-9">

            <div class="form-horizontal">
                <div class="form-group ng-class:{'has-error': !job.Name}">
                    <label class="col-xs-3 control-label" for="JobName">Job Name</label>
                    <div class="col-xs-7">
                        <input id="JobName" name="JobName" type="text" class="form-control input"
                               ng-model="job.Name" ng-required="!priceCalc" maxlength="80" />
                    </div>
                </div>
            </div>

            <div lep-job-details job="job" price-calc="false"></div>

            <div class="form-horizontal">
                <!--Special instructions-->
                <div class="form-group " ng-if="!priceCalc">

                    <label class="col-xs-3  control-label">Special instructions</label>
                    <div class="col-xs-7 form-control-static">
                        <div class="bold" style="color: blue; white-space: pre-wrap; overflow-wrap: break-word;">{{job.SpecialInstructions}}</div>
                        <a ng-show="job.Visibility.saveButton && !job.Visibility.approvalButton && job.ReOrderSourceJobId==0  " ng-click="addSpecialInstructions()">
                            <i class="glyphicon glyphicon-pencil"></i> Add instructions
                        </a>
                        <div ng-show="job.Visibility.saveButton " class="help-block">Note: Quoting will require review by our print team</div>
                    </div>
                </div>
            </div>

            <div lep-job-artwork-uploader job="job"></div>
        </div>
    </div>
</form>

<!--
<div visual-diff n="job" o="jobVmo"></div>

<pre>
{{job | json}}
</pre>

<br />	<ul>
    <li ng-repeat="(key, errors) in jobDetailsForm.$error track by $index">
        <ul>
            <li ng-repeat="e in errors">{{ e.$name }} has an error: <strong>{{ key }}</strong>.</li>
        </ul>
    </li>
</ul>
-->
