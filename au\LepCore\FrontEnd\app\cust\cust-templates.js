(function (angular) {
    'use strict';

    var app = angular.module('app');

    function removePropsByVal(obj, vals) {
        if (obj instanceof Array) {
            obj.forEach(function (item) {
                removePropsByVal(item, vals);
            });
        }
        else if (typeof obj === 'object') {
            var own = Object.getOwnPropertyNames(obj);

            own.forEach(function (key) {

                if (vals.indexOf(obj[key]) !== -1) {
                    delete obj[key];
                }
                else {
                    removePropsByVal(obj[key], vals);
                }
            });
        }
    }

    function clean(el) {
        function internalClean(el) {
            return _.transform(el, function (result, value, key) {
                var isCollection = _.isObject(value);
                var cleaned = isCollection ? internalClean(value) : value;

                if (isCollection && _.isEmpty(cleaned)) {
                    return;
                }

                _.isArray(result) ? result.push(cleaned) : (result[key] = cleaned);
            });
        }

        return _.isObject(el) ? internalClean(el) : el;
    }


    app.controller('TemplatesController', ['$scope', 'JobService', '$location', '$stateParams', '$state','templateTree', 'dcatsCust',
        function ($scope, JobService, $location, $stateParams, $state, templateTree, dcatsCust) {
            //console.debug('TemplatesController', $stateParams);
            $scope.msg = 'This is templates page showing a list of templates';

            $scope.orderId = $stateParams.orderId || 0;
            $scope.jobId = $stateParams.jobId || 0;



            var t  = angular.copy(templateTree);

            delete t.topdown.Category['Other Products'];


            // var wideFormatTemplateIds = [32,33,34,36,37,38,41,42,43,44]
            // removePropsByVal(t.topdown, wideFormatTemplateIds );
            // t.topdown = clean(t.topdown);
            
            // if (dcatsCust && dcatsCust.length) {
            //     removePropsByVal(t.topdown, dcatsCust);
            //     t.topdown = clean(t.topdown);
            // }

            $scope.templateTree = t;


            $scope.chooseTemplate2 = function (category) {
                var orderId = $stateParams.orderId || 0;
                var jobId = $stateParams.jobId || 0;

                if ($scope.globals.IsCustomer) {
                    $state.go('cust.order.job', { orderId: orderId, jobId: jobId, category: category }, { reload: true });
                } else {
                    $state.go('staff.order.job', { orderId: orderId, jobId: jobId, category: category }, { reload: true });
                }

            };


            $scope.chooseTemplate = function (templateId) {

                var orderId = $stateParams.orderId || 0;
                var jobId = $stateParams.jobId || 0;

                if ($scope.globals.IsCustomer) {
                    $state.go('cust.order.job', { orderId: orderId, jobId: jobId, templateId: templateId }, { reload: true });
                } else {
                    $state.go('staff.order.job', { orderId: orderId, jobId: jobId, templateId: templateId }, { reload: true });
                }

            };

        }]);


})(window.angular);
