using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using System;

namespace LepCore.Controllers
{
	public class CustomAuthorizeAttribute : AuthorizeAttribute
	{


	}

	public class ReturnBadRequestOnModelError : ActionFilterAttribute
	{
		public ReturnBadRequestOnModelError()
		{
			Order = 2;
		}

		public override void OnActionExecuting(ActionExecutingContext context)
		{
			if (!context.ModelState.IsValid)
			{
				context.Result = new BadRequestObjectResult(context.ModelState);
			}
		}
	}


	public class ReturnJsonErrorOnExceptionAttribute : ExceptionFilterAttribute
	{
		private readonly ILogger<ReturnJsonErrorOnExceptionAttribute> logger;

		public ReturnJsonErrorOnExceptionAttribute(ILogger<ReturnJsonErrorOnExceptionAttribute> logger)
		{
			this.logger = logger;
		}

		public override void OnException(ExceptionContext context)
		{
			var exception = context.Exception;

			logger.LogError(
				$"An unhandled exception was thrown by the application.{Environment.NewLine}{exception.ToString()}");

			context.HttpContext.Response.StatusCode = 500;

			context.Result = new JsonResult(
				new
				{
					errors = new[]
					{
						new
						{
							message = exception.Message,
							details = exception.ToString()
						}
					}
				});
		}
	}


	//public class ValidateActionParametersAttribute : ActionFilterAttribute
	//{
	//    public override void OnActionExecuting (ActionExecutingContext context)
	//    {
	//        var descriptor = context.ActionDescriptor as ControllerActionDescriptor;

	//        if (descriptor != null) {
	//            var parameters = descriptor.MethodInfo.GetParameters();

	//            foreach (var parameter in parameters) {
	//                var argument = context.ActionArguments[parameter.Name];
	//                EvaluateValidationAttributes(parameter, argument, context.ModelState);
	//            }
	//        }
	//        base.OnActionExecuting(context);
	//    }

	//    private void EvaluateValidationAttributes (ParameterInfo parameter, object argument,
	//        ModelStateDictionary modelState)
	//    {
	//        var validationAttributes = parameter.CustomAttributes;

	//        foreach (var attributeData in validationAttributes) {
	//            var attributeInstance = CustomAttributeExtensions.GetCustomAttribute(parameter, attributeData.AttributeType);

	//            var validationAttribute = attributeInstance as ValidationAttribute;

	//            if (validationAttribute != null) {
	//                var isValid = validationAttribute.IsValid(argument);
	//                if (!isValid) {
	//                    modelState.AddModelError(parameter.Name, validationAttribute.FormatErrorMessage(parameter.Name));
	//                }
	//            }
	//        }
	//    }
	//}
}
