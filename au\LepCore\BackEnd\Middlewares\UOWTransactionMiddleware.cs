using Serilog;
using Microsoft.AspNetCore.Http;
using System;
using System.Reflection;
using System.Runtime.ExceptionServices;
using System.Threading.Tasks;

namespace LepCore.Middleware
{
	public class UOWTransactionMiddleware
	{
		private readonly RequestDelegate _next;
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		public UOWTransactionMiddleware(RequestDelegate next)
		{
			_next = next;
		}

		public async Task Invoke(HttpContext context, NHibernate.ISession session)
		{
			bool transactionReqd =
				!context.Request.Method.Equals("GET", StringComparison.OrdinalIgnoreCase)
				|| context.Request.Path.ToString().Contains("status.aspx")

				;

			//bool transactionReqd = true;
			if (!transactionReqd)
			{
				await _next.Invoke(context);
			}
			else
			{
				using (var transaction = session.BeginTransaction())
				{
					try
					{
						await _next.Invoke(context);
						transaction.Commit();
					}
					catch (Exception ex)
					{
						transaction.Rollback();
						//log.Error(ex.Message, ex);
						ExceptionDispatchInfo.Capture(ex).Throw();
					}
					finally
					{
						if (session.IsOpen && session.IsDirty())
						{
							Log.Warning("hibernate - flushing uncommitted changes");
							session.Clear();
						}
						session.Close();
					}
				}
			}
		}
	}
}
