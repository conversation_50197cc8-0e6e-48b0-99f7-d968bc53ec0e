.template-card {
	background: #fff;
	border: 1px solid #f1f1f1;
	border-radius: 0;
	margin-bottom: 5px;
	margin-top: 5px;
	cursor: pointer;
	vertical-align: middle;
	font-size: 12px;
	.flipper {
		position: relative;
		height: 160px;
		padding: 5px;
		overflow: hidden;

		.front, .back {
			-moz-backface-visibility: hidden;
			-webkit-backface-visibility: hidden;
			backface-visibility: hidden;
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 130px;
			vertical-align: bottom;
		}

		.front {
			text-align: center;
			z-index: 2;

			.tick {
				float: left;
				top: 5px;
				left: 5px;
				position: absolute;

				&:after {
					clear: both;
				}
			}

			img {
				vertical-align: middle;
				height: 68%;
				width: 68%;
				margin: 25px 0 10px;
				transition: 0.1s;

				&:hover {
				}
			}
		}

		.back {
			padding: 0 20px;

			ul {
				padding: 0;

				li {
					list-style: none;
					text-align: left;

					&:first-of-type {
						text-align: center;
					}
				}
			}


			a {
				text-align: left;
				color: #fff !important;
				font-size: 12px;

				&:before {
					content: '- ';
				}

				&.name {
					font-size: 18px !important;
					padding-bottom: 15px;
					text-align: center;
					display: block;
					transition: 0;

					&:before {
						content: '';
					}
				}
			}
		}

		.bottom-txt {

			ul {
				display: none;
			}
		}
	}

	.flipper.flipped {
		.bottom-txt {
			height: 160px;
			background: rgba(255, 158, 27, 0.9);
			padding-top: 5px;

			> a.name {
				color: #fff !important;
				font-size: 16px !important;
			}

			ul {
				display: inline-block;
				position: absolute;
				top: 44%;
    			left: 12%;
				
				li {
					text-align: left;

					&:before {
						content: '';
						color: #fff;
					}
				}

				a {
					color: #fff;
				}
			}

			.btn {
				display: block;
				font-size: 15px !important;
				position: absolute;
				top: 64%;
    			left: 24%;	
			}
		}
	}

	.bottom-txt {
		display: inline-block;
		width: 100%;
		height: 50px;
		padding: 0px;
		vertical-align: bottom;
		z-index: 10;
		color: #4a5157;
		text-align: center;
		position: absolute;
		bottom: 0;
		left: 0;

		a.name {
			font-family: 'Conv_EnzoOT-Medi';
			color: #666;
			font-size: 18px !important;
			padding: 15px;
			padding-top: 5px;
			text-decoration: none;
		}

		.btn {
			display: none;
			width: 50%;
			margin: 10px auto;
			background: rgba(255, 255, 255, 0.52);
		}
	}
}
