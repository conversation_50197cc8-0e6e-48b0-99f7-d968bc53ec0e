using Microsoft.AspNetCore.Mvc;
using System;
using System.CodeDom;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.IO;
using Microsoft.Extensions.Configuration;
using LepCore.Dto;
using Microsoft.AspNetCore.Http;
using System.Xml.Linq;
using System.Xml.XPath;
using lep.configuration;
using lep.job;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;
using lep.order;
using lep;
using System.Net.Mail;
using lep.user;
using System.Drawing;
#pragma warning disable 1998

namespace LepCore
{
	[Route("api/[controller]")]
	[ApiExplorerSettings(IgnoreApi = true)]
	public class DocumentController : Controller
	{
		//private const string PitstopErrors = "//PreflightResultEntry[@type='Check'and @level='error']//Message";
		//private const string PitstopWarnings = "//PreflightResultEntry[@type='Check'and @level='warning']//Message";
		//private const string PitstopFixes = "//PreflightResultEntry[@type='Fix']//Message";

		private const string PitstopErrors = "//PreflightReport/Errors//Message";
		private const string PitstopWarnings = "//PreflightReport/Warnings//Message";
		private const string PitstopFixes = "//PreflightReport/Fixes//Message";
		private const string PDF = "application/pdf";
		private IConfigurationRoot _config;
		private ILogger<DocumentController> _logger;
		private bool _aacEnabled = false;
		private string _aacMethod = "";
		private TimeSpan _aacTimeout = TimeSpan.FromMinutes(0);

		public DocumentController([FromServices] IConfigurationRoot config, ILogger<DocumentController> logger)
		{
			_config = config;
			_logger = logger;
			_aacEnabled = bool.Parse(_config["AutomatedArtworkCheck:Enabled"]);
			_aacTimeout = TimeSpan.Parse(_config["AutomatedArtworkCheck:Timeout"]);
			_aacMethod = _config["AutomatedArtworkCheck:Method"];
		}

		[HttpPost("post")]
		[DisableRequestSizeLimit]
		public async Task<IActionResult> Post([FromQuery] string profile = "default")
		{
			var baseFolder = _config["DataDirectory"];
			var files = Request.Form.Files;
			var list = new List<Attachments>();
			foreach (var uploadedFile in files)
			{
				var uploadedFileName = Path.GetFileName(uploadedFile.FileName);
				var position = uploadedFile.Name;

				var uploadedFilePath = Path.Combine(baseFolder, "TempUploads", uploadedFileName);

				using (var fs = System.IO.File.Create(uploadedFilePath))
				{
					await uploadedFile.CopyToAsync(fs);
					fs.Close();
					_logger.LogInformation($"Uploaded {uploadedFilePath}");
				}

				// do PitStop on source file here
				// copy to hot folder
				// wait for report
				// parse  report
				// if error return  error message, with report   disble client side save button
				dynamic reportResult = null;
				if (_aacEnabled  )
				{
					var sw = Stopwatch.StartNew();
					switch (_aacMethod)
					{
						case "DoPitStopViaHotFolder":
							reportResult = await DoPitStopViaHotFolder(uploadedFilePath);
							break;

						case "DoPitStopViaCommandLine":
							reportResult = await DoPitStopViaCommandLine(uploadedFilePath, profile);
							break;
					}
					sw.Stop();
					var size = (((uploadedFile.Length / 1024f) / 1024f));
					var time = sw.Elapsed.TotalSeconds;
					var thp = size / (int)time;
					var msg = reportResult == null
						? $"Couldnt finish in {_aacTimeout.TotalSeconds:0.##}s"
						: $"Finished in {time:0.##}s";
					_logger.LogInformation($"{_aacMethod}: {size:0.##}MB/{time:0.##}s = {thp:0.##} MB/s ({msg}) {uploadedFileName}");
				}

				// just for testing put good|timeout|bad|fix in filename
				if ( false && bool.Parse(_config["TestBox"]) && reportResult == null)
				{
					var empty = new List<string> { };
					if (uploadedFileName.Contains("good"))
					{
						reportResult = new { errors = empty, warnings = empty, fixes = empty, hasErrors = false };
					}
					else if (uploadedFileName.Contains("timeout"))
					{
						reportResult = null;
					}
					else if (uploadedFileName.Contains("bad"))
					{
						reportResult = new { errors = new[] { "e 1", "e 2" }, warnings = new[] { "w1 1", "w2 2" }, fixes = new[] { "f 1", "f 2" }, hasErrors = true };
					}
					else if (uploadedFileName.Contains("fix"))
					{
						reportResult = new { errors = empty, warnings = empty, fixes = new[] { "f 1", "f 2" }, hasErrors = false }; ;
					}



					var destFileName2 = Path.Combine(baseFolder, "TempUploads", System.IO.Path.GetFileNameWithoutExtension(uploadedFile.FileName) + ".aac.pdf");

					using (var fs = System.IO.File.Create(destFileName2))
					{
						await uploadedFile.CopyToAsync(fs);
						fs.Close();
					}
				}

				var attachment = new Attachments
				{
					Name = System.IO.Path.GetFileName(uploadedFile.FileName),
					NameAac = System.IO.Path.GetFileNameWithoutExtension(uploadedFile.FileName) + ".aac.pdf",
					Id = uploadedFileName,
					Position = position,
					Pages = 0,
					AAC = reportResult
				};
				list.Add(attachment);
			}
			return Ok(list.ToArray());
		}

		/// pit stop: by copying to input hotfolder and watching out put hot folder
		private async Task<dynamic> DoPitStopViaHotFolder(string destFileName)
		{
			var hotfolderIn = _config["AutomatedArtworkCheck:DoPitStopViaHotFolder:input"];
			var hotfolderOut = _config["AutomatedArtworkCheck:DoPitStopViaHotFolder:output"];

			// do pit stop
			var input = destFileName;
			var fileName = Path.GetFileName(input);
			var fileNameWoExt = Path.GetFileNameWithoutExtension(input);
			await Task.Run(() =>
			{
				var dfn = $@"{hotfolderIn}\{fileName}";
				if (System.IO.File.Exists(dfn))
				{
					System.IO.File.Delete(dfn);
				}

				// delete old files by the same name if it exists
				var hotfolderOutDir = new DirectoryInfo(hotfolderOut);
				foreach (var file in hotfolderOutDir.EnumerateFiles($"{fileNameWoExt}*"))
				{
					file.Delete();
				}

				// copy the just uploaded file to pitstop input folder
				System.IO.File.Copy(input, dfn);
			}
			);

			// waits up to a 5 minute for the 3 file in hot out put folder
			var finishedInTime = await Task.Run(async () =>
			{
				var found3Files = false;
				var sw = Stopwatch.StartNew();
				while (!found3Files)
				{
					await Task.Delay(1000);
					var needToWait = Directory.GetFiles(hotfolderOut, $"{fileNameWoExt}*").Length < 3;
					if (needToWait)
					{
						if (sw.Elapsed > _aacTimeout) // have we tried enough times
							return false; // yes then no point in waiting more.
						continue; // maybe we should wait a bit more...
					}
					found3Files = true; // expectedFiles.lenght atleast 3
				}
				sw.Stop();
				return true;
			});

			var reportPDF = $@"{hotfolderOut}\{fileNameWoExt}_log.pdf";
			var reportXml = $@"{hotfolderOut}\{fileNameWoExt}_log.xml";
			dynamic reportResult = null;
			if (finishedInTime)
			{
				reportResult = ExtractReportData(reportXml, "");
			}
			return reportResult;
		}

		// call pit stop Cli directly and give back report
		//private async Task<dynamic> DoPitStopViaCommandLine(string inputFilePath, string profile)
		//{
		//	// do pit stop
		//	var workFolder = Path.GetDirectoryName(inputFilePath);
		//	var inputFileName = Path.GetFileNameWithoutExtension(inputFilePath);

		//	var cliPath = _config["AutomatedArtworkCheck:DoPitStopViaCommandLine:path"];  //  @"D:\Enfocus\PitStopServer12\PitStopServerCLI.exe";
		//																				  //var mutator = _config[$"AutomatedArtworkCheck:DoPitStopViaCommandLine:mutator"]; //  @"D:\LEPDATA\CMYK v3.0.ppp";
		//	var mutator = _config[$"AutomatedArtworkCheck:profiles:{profile}"]; //  @"D:\LEPDATA\CMYK v3.0.ppp";

		//	//var reportPDFTmpl = @"D:\Enfocus\PitStopServer12\Resources\Report Templates\Default\ServerReportDefault.pdf";
		//	var output = $@"{workFolder}\{inputFileName}.aac.pdf";
		//	var reportPdfFn = $@"{workFolder}\{inputFileName}.report.pdf";
		//	var reportXmlFn = $@"{workFolder}\{inputFileName}.report.xml";
		//	var taskReportFn = $@"{workFolder}\{inputFileName}.taskReport.xml";

		//	//-reportPDFTmpl  ""{reportPDFTmpl}""
		//	//-output         ""{output}""

		//	var strCommandParameters = $@"-input      ""{inputFilePath}""   -mutator    ""{mutator}""   -reportPDF  ""{reportPdfFn}""  -reportXML  ""{reportXmlFn}""  -taskReport ""{taskReportFn}""   -output ""{output}""   -CertifiedPDFAction MakeCertified";

		//	//strCommandParameters = strCommandParameters.Replace("\n", " ").Replace("	", " ");

		//	var cmd = $"\"{cliPath}\" {strCommandParameters}";
		//	_logger.LogInformation(cmd);

		//	var finishedInTime = await Task.Run(() =>
		//	{
		//		var p = new System.Diagnostics.Process();
		//		p.StartInfo = new ProcessStartInfo()
		//		{
		//			FileName = cliPath,
		//			WorkingDirectory = Path.GetDirectoryName(cliPath),
		//			Arguments = strCommandParameters,
		//			UseShellExecute = true,
		//			CreateNoWindow = true,
		//			RedirectStandardOutput = false,

		//		};
		//		p.Start();
		//		return p.WaitForExit((int)_aacTimeout.TotalMilliseconds);
		//	});

		//	dynamic reportResult = null;
		//	if (finishedInTime)
		//	{
		//		reportResult = ExtractReportData(reportXmlFn, taskReportFn);

		//		//if(reportResult != null) {
		//		//	if (System.IO.File.Exists(output)) {
		//		//		System.IO.File.Delete(destFileName);
		//		//		System.IO.File.Move(output, destFileName);
		//		//	}
		//		//}
		//		if (reportResult != null)
		//		{
		//		}
		//	}

		//	return reportResult;
		//}



		private async Task<dynamic> DoPitStopViaCommandLine(string inputFilePath, string profile)
		{
			// Get work folder and file names
			var workFolder = Path.GetDirectoryName(inputFilePath);
			var inputFileName = Path.GetFileNameWithoutExtension(inputFilePath);

			// Path to the CLI executable and profile mutator
			var cliPath = _config["AutomatedArtworkCheck:DoPitStopViaCommandLine:path"];
			var mutator = _config[$"AutomatedArtworkCheck:profiles:{profile}"];

			// Output file paths
			var output = $@"{workFolder}\{inputFileName}.aac.pdf";
			var reportPdfFn = $@"{workFolder}\{inputFileName}.report.pdf";
			var reportXmlFn = $@"{workFolder}\{inputFileName}.report.xml";
			var taskReportFn = $@"{workFolder}\{inputFileName}.taskReport.xml";
		
			// Generate the XML configuration dynamically
			var configXmlPath = $@"{workFolder}\{inputFileName}_config.xml";
			var configXml = GeneratePitStopConfigXml(inputFilePath, mutator, output, reportPdfFn, reportXmlFn, taskReportFn);

			// Save the XML configuration to a file
			System.IO.File.WriteAllText(configXmlPath, configXml);

			// Create the command-line arguments with -config
			var strCommandParameters = $@"-config ""{configXmlPath}""";

			var cmd = $"\"{cliPath}\" {strCommandParameters}";
			_logger.LogInformation(cmd);

			// Execute the PitStop command
			var finishedInTime = await Task.Run(() =>
			{
				var p = new System.Diagnostics.Process();
				p.StartInfo = new ProcessStartInfo()
				{
					FileName = cliPath,
					WorkingDirectory = Path.GetDirectoryName(cliPath),
					Arguments = strCommandParameters,
					UseShellExecute = false, // Set to false to allow output redirection
					CreateNoWindow = false,  // Show the console window
					RedirectStandardOutput = true, // Redirect output to capture progress
					RedirectStandardError = true   // Redirect error output
				};
				p.Start();
				return p.WaitForExit((int)_aacTimeout.TotalMilliseconds);
			});

			dynamic reportResult = null;
			if (finishedInTime)
			{
				reportResult = ExtractReportData(reportXmlFn, taskReportFn);
			}

			return reportResult;
		}
		private string GeneratePitStopConfigXml(string inputFilePath, string mutator, string outputPdf, string reportPdf, string reportXml, string taskReport)
		{

			// Get the directory and file name without extension from the input file
			var workFolder = Path.GetDirectoryName(inputFilePath);
			var inputFileName = Path.GetFileNameWithoutExtension(inputFilePath);

			// Define the thumbnail output path, using the same folder as the input file
			var thumbnailPath = $@"{workFolder}\thumbnail_{inputFileName}.%1.png"; // This will save thumbnails as thumb_filename.1.png, thumb_2.png

			var mutatorPath = Path.GetDirectoryName(mutator);
			var firstTwoPagesActionPath = Path.Combine( mutatorPath, "select-page-1-2.eal");

			// Ensure that the XML includes the required Versioning tag
			var xmlContent = $@"<?xml version=""1.0"" encoding=""UTF-8""?>
				<cf:Configuration xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance""
					xmlns:cf=""http://www.enfocus.com/PitStop/24.07/PitStopServerCLI_Configuration.xsd"">
        
					<!-- Required Versioning element -->
					<cf:Versioning>
						<cf:Version>14</cf:Version>
						<cf:VersioningStrategy>BestEffort</cf:VersioningStrategy>
					</cf:Versioning> 

					<cf:Process>
						<cf:InputPDF>
							<cf:InputPath>{inputFilePath}</cf:InputPath>
						</cf:InputPDF>
						<cf:OutputPDF>
							<cf:OutputPath>{outputPdf}</cf:OutputPath>
						</cf:OutputPDF>

						<cf:Mutators>
							<cf:PreflightProfile>{mutator}</cf:PreflightProfile>
						</cf:Mutators>

						<cf:Reports>
							<cf:ReportPDF>
								<cf:ReportPath>{reportPdf}</cf:ReportPath>
							</cf:ReportPDF>
							<cf:ReportXML>
								<cf:ReportPath>{reportXml}</cf:ReportPath>
							</cf:ReportXML>
						</cf:Reports>


						<!-- Force Certified PDF Re-preflighting -->
						<cf:CertifiedPDF>
							<cf:CertifiedPDFAction>MakeCertified</cf:CertifiedPDFAction> <!-- Force certification -->
							<cf:KeepOriginalProfile>false</cf:KeepOriginalProfile> <!-- Discard the original certification profile -->
                
							<!-- Add your company info -->
							<cf:UserInfo>
								<cf:Name>LEP</cf:Name>
								<cf:Company>LEP Colour Printers</cf:Company>
								<cf:Street>Your Street Address</cf:Street>
								<cf:City>Your City</cf:City>
								<cf:State>Your State</cf:State>
								<cf:Country>Your Country</cf:Country>
								<cf:PostalCode>Your Postal Code</cf:PostalCode>
								<cf:Phone>Your Phone Number</cf:Phone>
								<cf:Email>Your Email Address</cf:Email>
							</cf:UserInfo>
							<cf:SessionComment>Re-preflighted with company details</cf:SessionComment>
						</cf:CertifiedPDF>

					</cf:Process>

					
						<cf:ExportImage>
							<cf:InputPDF>
								<cf:InputPath>{inputFilePath}</cf:InputPath>
							</cf:InputPDF>

							<cf:OutputImages>
								<cf:OutputPath>{thumbnailPath}</cf:OutputPath>
								<cf:SaveOptions FileType=""PNG"">
									<cf:Interlaced>true</cf:Interlaced>
							   </cf:SaveOptions>
							</cf:OutputImages>
							
							<cf:RenderOptions>
								<cf:RenderType>Composite</cf:RenderType>
								<cf:OutputColorSpace>DeviceRGB</cf:OutputColorSpace>
								<cf:ImageDimensions>
									<cf:Resolution>150</cf:Resolution>
								</cf:ImageDimensions>
								<cf:RenderArea>TrimBox</cf:RenderArea>
								<cf:BackgroundColor>
									<cf:Red>255</cf:Red>
									<cf:Green>255</cf:Green>
									<cf:Blue>255</cf:Blue>
								</cf:BackgroundColor>
								<cf:AntiAliasing>true</cf:AntiAliasing>
							</cf:RenderOptions>
							<cf:ActionLists>
								<cf:RestrictingActionList>{firstTwoPagesActionPath}</cf:RestrictingActionList>
							</cf:ActionLists>
						</cf:ExportImage>

					<cf:TaskReport>
						<cf:TaskReportPath>{taskReport}</cf:TaskReportPath>
						<cf:LogExportImageResults>true</cf:LogExportImageResults>
					</cf:TaskReport>
				</cf:Configuration>";

			return xmlContent.Trim(); // Ensure no leading/trailing whitespace
		}

		private static dynamic ExtractReportData(string reportXml, string taskReport)
		{
			if (!System.IO.File.Exists(reportXml))
				return null;
			var r = XDocument.Load(reportXml);
			var errors = r.XPathSelectElements(PitstopErrors).Select(_ => _.Value).ToList();
			var warnings = r.XPathSelectElements(PitstopWarnings).Select(_ => _.Value).ToList();
			var fixes = r.XPathSelectElements(PitstopFixes).Select(_ => _.Value).ToList();

			var hasErrors = errors.Any();

			dynamic reportResult = new { errors, warnings, fixes, hasErrors };
			return reportResult;
		}

		[HttpGet("Report/{baseName}/download")]
		[HttpPost("Report/{baseName}/download")]
		[Produces(typeof(FileContentResult))]
		[AllowAnonymous]
		public IActionResult DownloadAACReport([FromRoute]string baseName)
		{
			string baseFolder, reportPdf, dnldName, suggestedName;
			suggestedName = "";
			baseName = Path.GetFileNameWithoutExtension(baseName);
			suggestedName = baseName + ".report.pdf";

			if (_aacMethod == "DoPitStopViaCommandLine")
			{
				// try locating report in temp uploads folder if we used cli
				baseFolder = _config["DataDirectory"];
				reportPdf = $@"{baseFolder}\TempUploads\{baseName}.report.pdf";
				if (System.IO.File.Exists(reportPdf))
				{
					var fileStream = new FileStream(reportPdf, FileMode.Open, FileAccess.Read);
					return File(fileStream, PDF, suggestedName);
				}
			}
			else if (_aacMethod == "DoPitStopViaHotFolder")
			{
				// try locating report in pitstop hotfolder if we used hotfolder
				baseFolder = _config["AutomatedArtworkCheck:DoPitStopViaHotFolder:output"];
				reportPdf = $@"{baseFolder}\{baseName}_log.pdf";
				if (System.IO.File.Exists(reportPdf))
				{
					var fileStream = new FileStream(reportPdf, FileMode.Open, FileAccess.Read);
					return File(fileStream, PDF, suggestedName);
				}
			}

			return new ContentResult { StatusCode = StatusCodes.Status400BadRequest, Content = $"File does not exist" };
		}

		[HttpGet("PostAAC/{baseName}/download")]
		[HttpPost("PostAAC/{baseName}/download")]
		[Produces(typeof(FileContentResult))]
		[AllowAnonymous]
		public IActionResult DownloadPostAACFile([FromRoute]string baseName)
		{
			string baseFolder, reportPdf, dnldName, suggestedName;
			suggestedName = "";
			baseName = Path.GetFileNameWithoutExtension(baseName);
			suggestedName = baseName + ".pdf";

			if (_aacMethod == "DoPitStopViaCommandLine")
			{
				// try locating report in temp uploads folder if we used cli
				baseFolder = _config["DataDirectory"];
				reportPdf = $@"{baseFolder}\TempUploads\{baseName}.aac.pdf";
				if (System.IO.File.Exists(reportPdf))
				{
					var fileStream = new FileStream(reportPdf, FileMode.Open, FileAccess.Read);
					return File(fileStream, PDF, suggestedName);
				}
			}
			else if (_aacMethod == "DoPitStopViaHotFolder")
			{
				// try locating report in pitstop hotfolder if we used hotfolder
				baseFolder = _config["AutomatedArtworkCheck:DoPitStopViaHotFolder:output"];
				reportPdf = $@"{baseFolder}\{baseName}_log.pdf";
				if (System.IO.File.Exists(reportPdf))
				{
					var fileStream = new FileStream(reportPdf, FileMode.Open, FileAccess.Read);
					return File(fileStream, PDF, suggestedName);
				}
			}

			return new ContentResult { StatusCode = StatusCodes.Status400BadRequest, Content = $"File does not exist" };
		}

		[HttpPost("job/{jobId:int}/extrafiles")]
		[DisableRequestSizeLimit]
		public async Task<IActionResult> SaveExtraFiles(int jobId, [FromServices] IConfigurationApplication configApp, [FromServices]  IJobApplication jobApp)
		{
			var baseFolder = _config["DataDirectory"];
			var j = jobApp.GetJob(jobId);
			var dir = LepGlobal.Instance.ArtworkDirectory(j, false);
			var extraFilesDir = dir + "\\ExtraFiles";
			if (!Directory.Exists(extraFilesDir))
			{
				Directory.CreateDirectory(extraFilesDir);
			}

			var files = Request.Form.Files;
			if (files != null && files.Count > 0)
				foreach (var sourceFile in files)
				{
					var fileId = Path.GetFileName(sourceFile.FileName);
					var destFileName = Path.Combine(extraFilesDir, fileId.ToString());

					using (var fs = System.IO.File.Create(destFileName))
					{
						await sourceFile.CopyToAsync(fs);
					}
				}

			var extraFilesDirInfo = new DirectoryInfo(extraFilesDir);

			var result = extraFilesDirInfo.GetFiles()
				.OrderByDescending(f => f.CreationTime)
				.Select(f => new { f.Name, f.CreationTime });

			return Ok(result);
		}


		[HttpPost("order/{orderId:int}/SavePOD")]
		[DisableRequestSizeLimit]
		public async Task<IActionResult> SavePOD(int orderId, [FromServices] IConfigurationApplication configApp
			, [FromServices] IOrderApplication orderApp, [FromBody] string podImage)
		{
			var baseFolder = _config["DataDirectory"];
			var o = orderApp.GetOrder(orderId);
			o.DateModified = DateTime.Now;
			orderApp.BaseSave(o);
			var dir = LepGlobal.Instance.GetOrderExtraFilesFolder(o).FullName;
			var x = DateTime.Now.ToString("yyyy-MMM-dd_HH_mm");
			await System.IO.File.WriteAllBytesAsync( Path.Combine(dir, $"POD-{x}.png") , Convert.FromBase64String(podImage));
			return Ok();
		}

		[HttpGet("job/{jobId:int}/extrafiles")]
		[DisableRequestSizeLimit]
		public async Task<IActionResult> GetExtraFiles(int jobId, [FromServices] IConfigurationApplication configApp, [FromServices]  IJobApplication jobApp)
		{
			var baseFolder = _config["DataDirectory"];
			var j = jobApp.GetJob(jobId);
			var dir = LepGlobal.Instance.ArtworkDirectory(j, false);
			var extraFilesDir = dir + "\\ExtraFiles";
			if (!Directory.Exists(extraFilesDir))
			{
				// if it doesn't exist then just return empty, keep folders clean
				return Ok(new object[0]);
			}

			var extraFilesDirInfo = new DirectoryInfo(extraFilesDir);

			var result = extraFilesDirInfo.GetFiles()
				.OrderByDescending(f => f.CreationTime)
				.Select(f => new { f.Name, f.CreationTime });

			return Ok(result);
		}
		/*

		[HttpGet("order/{orderId:int}/extrafiles")]
		[DisableRequestSizeLimit]
		public async Task<IActionResult> GetOrderExtraFiles(int orderId, [FromServices] IConfigurationApplication configApp, [FromServices] IOrderApplication orderApp)
		{
			var baseFolder = _config["DataDirectory"];
			var o = orderApp.GetOrder(orderId);
			var dir = configApp.GetOrderExtraFilesFolder(o);
			var extraFilesDir = dir + "\\ExtraFiles";
			if (!Directory.Exists(extraFilesDir))
			{
				// if it doesn't exist then just return empty, keep folders clean
				return Ok(new object[0]);
			}
			GetOrderExtraFiles(orderId, )

			var extraFilesDirInfo = new DirectoryInfo(extraFilesDir);

			var result = extraFilesDirInfo.GetFiles()
				.OrderByDescending(f => f.CreationTime)
				.Select(f => new { f.Name, f.CreationTime });

			return Ok(result);
		}
		*/

		[HttpPost("order/{orderId:int}/extrafiles/download/{fileName}")]
		[HttpGet("order/{orderId:int}/extrafiles/download/{fileName}")]
		[DisableRequestSizeLimit]
		[AllowAnonymous]
		public async Task<IActionResult> downloadOrderExtraFiles(int orderId, string fileName, [FromServices] IConfigurationApplication configApp, [FromServices] IOrderApplication orderApp)
		{
			var baseFolder = _config["DataDirectory"];
			var order = orderApp.GetOrder(orderId);

			var dir = LepGlobal.Instance.GetOrderExtraFilesFolder(order);
			var fullPath = Path.Combine(dir.FullName,fileName);

			if (System.IO.File.Exists(fullPath))
			{
				var fileStream = new FileStream(fullPath, FileMode.Open, FileAccess.Read);
				return File(fileStream, PDF, fileName);
			}
			return NotFound();
		}


		[HttpPost("order/{orderId:int}/extrafiles/Email/{fileName}")]
		[DisableRequestSizeLimit]
		[AllowAnonymous]
		public async Task<IActionResult> EmailOrderExtraFiles(int orderId, string fileName, [FromServices] lep.email.IEmailApplication emailApp, [FromServices] IOrderApplication orderApp)
		{
			var baseFolder = _config["DataDirectory"];
			var order = orderApp.GetOrder(orderId);

			var dir = LepGlobal.Instance.GetOrderExtraFilesFolder(order);
			var extraFileName = Path.Combine(dir.FullName, fileName);

			if (!System.IO.File.Exists(extraFileName))
				return NotFound();

			var address = order.Customer.AccountEmail;

			if (string.IsNullOrEmpty(address))
			{
				address = order.Customer.Email;
			}


			if (!string.IsNullOrEmpty(address))
			{
				var mail = new MailMessage(LepGlobal.Instance.SmtpUsername, address)
				{
					Subject = $"Proof of Delivery attached {fileName}",
					IsBodyHtml = true,
					Body = $@"Please find attached your Job Proof of Delivery {fileName} from LEP Colour Printers.<br/>
										 Contact <EMAIL> for any queries."
				};
				mail.Attachments.Add(new Attachment(extraFileName));
				emailApp.Send(mail);
			}
			return Ok();
		}



		[HttpPost("order/{orderId:int}/extrafiles/downloadT/{fileName}")]
		[HttpGet("order/{orderId:int}/extrafiles/downloadT/{fileName}")]
		[DisableRequestSizeLimit]
		[AllowAnonymous]
		public async Task<IActionResult> downloadOrderExtraFilesT(int orderId, string fileName,
			[FromServices] IConfigurationApplication configApp, [FromServices] IOrderApplication orderApp)
		{


			var baseFolder = _config["DataDirectory"];
			var order = orderApp.GetOrder(orderId);
			order.DateModified = DateTime.Now;
			orderApp.BaseSave(order);

			//if (_currentUserIsCust && order.Customer.Id != _currentUser.Id)
			//	return new StatusCodeResult(403);

			//if (_currentUserIsAnonymousWLCustomer && order.Customer.Id != (_currentUser as ICustomerUser).ParentCustomer.Id)
			//	return new StatusCodeResult((int)Forbidden);




			var dir = LepGlobal.Instance.GetOrderExtraFilesFolder(order);
			var extraFileToDownload = Path.Combine(dir.FullName,fileName);

			if (System.IO.File.Exists(extraFileToDownload))
			{

				 var stream = new FileStream(extraFileToDownload, FileMode.Open);
				 return new FileStreamResult(stream, PDF);   
 			}
			return NotFound();
		}


		[HttpPost("job/{jobId:int}/extrafiles/download/{fileName}")]
		[DisableRequestSizeLimit]
		[AllowAnonymous]
		public async Task<IActionResult> downloadExtraFiles(int jobId, string fileName, [FromServices] IConfigurationApplication configApp, [FromServices]  IJobApplication jobApp)
		{
			var baseFolder = _config["DataDirectory"];
			var j = jobApp.GetJob(jobId);
			var dir = LepGlobal.Instance.ArtworkDirectory(j, false);
			var fullPath = Path.Combine(dir.FullName, "ExtraFiles", fileName);

			if (System.IO.File.Exists(fullPath))
			{
				return File(new FileStream(fullPath,FileMode.Open ), PDF);
			}
			return NotFound();
		}

		[HttpPost("job/{jobId:int}/extrafiles/delete/{fileName}")]
		public async Task<IActionResult> deleteExtraFile([FromRoute] int jobId, [FromRoute] string fileName, [FromServices] IConfigurationApplication configApp, [FromServices]  IJobApplication jobApp)
		{
			var baseFolder = _config["DataDirectory"];
			var j = jobApp.GetJob(jobId);
			var dir = LepGlobal.Instance.ArtworkDirectory(j, false);
			var extraFileToDownload = Path.Combine(dir.FullName, "ExtraFiles", fileName);

			if (System.IO.File.Exists(extraFileToDownload))
			{
				System.IO.File.Delete(extraFileToDownload);
				return Ok();
			}
			return NotFound();
		}




		[HttpPost("Customer/{customerId:int}/extrafiles/download/{fileName}")]
		[HttpGet("Customer/{customerId:int}/extrafiles/download/{fileName}")]
		[DisableRequestSizeLimit]
		[AllowAnonymous]
		public async Task<IActionResult> downloadCustomerExtraFiles(int customerId, string fileName, [FromServices] IConfigurationApplication configApp, [FromServices] IUserApplication userApp)
		{
			var dir = LepGlobal.Instance.GetCustomerExtraFilesFolder(customerId);
			var fullPath = Path.Combine(dir, fileName);

			if (System.IO.File.Exists(fullPath))
			{
				var fileStream = new FileStream(fullPath, FileMode.Open, FileAccess.Read);
				return File(fileStream, PDF, fileName);
			}
			return NotFound();
		}


		[HttpPost("Customer/{customerId:int}/extrafiles/Email/{fileName}")]
		[DisableRequestSizeLimit]
		[AllowAnonymous]
		public async Task<IActionResult> EmailCustomerExtraFiles(int customerId, string fileName, [FromServices] lep.email.IEmailApplication emailApp, [FromServices] IUserApplication userApp)
		{
			var dir = LepGlobal.Instance.GetCustomerExtraFilesFolder(customerId);
			var extraFileName = Path.Combine(dir, fileName);

			if (!System.IO.File.Exists(extraFileName))
				return NotFound();

			var Customer = userApp.GetCustomerUser(customerId);
			var address = Customer.AccountEmail;

			if (string.IsNullOrEmpty(address))
			{
				address = Customer.Email;
			}

			if (!string.IsNullOrEmpty(address))
			{
				var mail = new MailMessage(LepGlobal.Instance.SmtpUsername, address)
				{
					Subject = $"Invoice Attached {fileName}",
					IsBodyHtml = true,
					Body = $@"Please find attached your invoice {fileName} from LEP Colour Printers.<br/>
                                    Contact <EMAIL> for any queries."
				};
				mail.Attachments.Add(new Attachment(extraFileName));
				emailApp.Send(mail);
			}
			return Ok();
		}
	}
}
#pragma warning restore 1998
