﻿using NHibernate.Event;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Text.Json.JsonDiffPatch;
using System.Dynamic;
using System.Collections.Generic;
using lep.security;
using Serilog;
using lep.freight.impl;

namespace LepCore
{
	[Serializable]
	public class NhAuditEventListener : IPostUpdateEventListener, IPostInsertEventListener, IPostDeleteEventListener
	{
		public NhAuditEventListener()
		{
		}

		static List<string> ignoreEntities = new List<string>() { "RunSearchCriteria", "RunSearchCriteria", "Operator" }; //"DateModified", "DateCreated",
		static List<string> ignoreProps = new List<string>() { "DateModified", "OpenRun", "RunSearchCriteria", "Freight", "OrderSearchCriteria", "RunSearchCriteria" , "Operator" }; //"DateModified", "DateCreated",
		public void OnPostUpdate(PostUpdateEvent e)
		{
			try
			{
				if (e.Persister.EntityName.Contains("Audit"))
					return;

				var entityName =  System.IO.Path.GetExtension(e.Persister.EntityName).Replace(".","");
				if (ignoreEntities.Contains(entityName)) return;
				var en = e.GetType().ToString();

				NHDIInterceptor interceptor = ((NHDIInterceptor)e.Session.Interceptor);
				var _securityApplication = interceptor.GetService<ISecurityApplication>();
				var user = _securityApplication.Identity;
				var isStaffB = (user?.IsStaff ?? false);
				var dirtyFieldIndexes = e.Persister.FindDirty(e.State, e.OldState, e.Entity, e.Session);
				if (dirtyFieldIndexes == null || dirtyFieldIndexes.Length == 0) return;
				var isStaff = (user?.IsStaff ?? false) ? 'Y' : 'N';
				var userName = user?.Username ?? "";
				var userId = user?.Id ?? -1;
				var eventTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");


				var x = new ExpandoObject() as IDictionary<string, Object>;
				var y = new ExpandoObject() as IDictionary<string, Object>;

				foreach (var dirtyFieldIndex in dirtyFieldIndexes)
				{
					var type = e.Persister.PropertyTypes[dirtyFieldIndex];
					string pn = e.Persister.PropertyNames[dirtyFieldIndex];
					if (ignoreProps.Contains(pn)) continue;
					var oldValue = e.OldState[dirtyFieldIndex];
					var newValue = e.State[dirtyFieldIndex];
					if (oldValue is Enum)
					{
						x.Add(pn, oldValue.ToString());
						y.Add(pn, newValue.ToString());
					}
					else if (type is NHibernate.Type.CustomType)
					{
						x.Add(pn, oldValue);
						y.Add(pn, newValue);
					} 
					//else if(pn  == "PackDetail")
					//{
					//	x.Add(pn, oldValue);
					//	y.Add(pn, newValue);
					//}
					else
					{
						x.Add(pn, oldValue);
						y.Add(pn, newValue);
					}
				}

				var diffX = JsonDiffPatcher.Diff(System.Text.Json.JsonSerializer.Serialize(x), System.Text.Json.JsonSerializer.Serialize(y)) ?? "-";

				e.Session.SaveOrUpdate(new lep.audit.Audit()
				{
					Entity = entityName,
					EntityId = (Int32)e.Id,
					UserId = userId,
					UserName = userName,
					Body = diffX.ToString(),
					EventDate = DateTime.Now,
					IsStaff = isStaffB
				});
			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
			}
		}


		public void OnPostInsert(PostInsertEvent e)
		{
			try
			{
				if (e.Persister.EntityName.Contains("Audit"))
					return;
				var entityName = System.IO.Path.GetExtension(e.Persister.EntityName).Replace(".", "");
				var en = e.GetType().ToString();
				NHDIInterceptor interceptor = ((NHDIInterceptor)e.Session.Interceptor);
				var _securityApplication = interceptor.GetService<ISecurityApplication>();
				var user = _securityApplication.Identity;
				//var isStaff = (user?.IsStaff ?? false) ? 'Y' : 'N';
				var isStaffB = (user?.IsStaff ?? false);
				var userName = user?.Username ?? "";
				var userId = user?.Id ?? -1;
				var eventTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");

				var x = new ExpandoObject() as IDictionary<string, Object>;

				var diffX = "Added";

				e.Session.SaveOrUpdate(new lep.audit.Audit()
				{
					Entity = entityName,
					EntityId = (Int32)e.Id,
					UserId = userId,
					UserName = userName,
					Body = diffX,
					EventDate = DateTime.Now,
					IsStaff = isStaffB
				});


			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
			}
		}



		public void OnPostDelete(PostDeleteEvent e)
		{
			try
			{
				var en = e.GetType().ToString();
				var entityName = System.IO.Path.GetExtension(e.Persister.EntityName).Replace(".", "");
				NHDIInterceptor interceptor = ((NHDIInterceptor)e.Session.Interceptor);
				var _securityApplication = interceptor.GetService<ISecurityApplication>();
				var user = _securityApplication.Identity;
				var userName = user?.Username ?? "";
				var userId = user?.Id ?? -1;
				var isStaffB = (user?.IsStaff ?? false);
				var eventTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
				//var x = new ExpandoObject() as IDictionary<string, Object>;
				var diffX = "Deleted";

				e.Session.SaveOrUpdate(new lep.audit.Audit()
				{
					Entity = entityName,
					EntityId = (Int32)e.Id,
					UserId = userId,
					UserName = userName,
					Body = diffX,
					EventDate = DateTime.Now,
					IsStaff = isStaffB
				});
			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
			}

		}
		public Task OnPostUpdateAsync(PostUpdateEvent @event, CancellationToken cancellationToken)
		{
			throw new NotImplementedException();
		}

		public Task OnPostInsertAsync(PostInsertEvent @event, CancellationToken cancellationToken)
		{
			throw new NotImplementedException();
		}

		public Task OnPostDeleteAsync(PostDeleteEvent @event, CancellationToken cancellationToken)
		{
			throw new NotImplementedException();
		}
	}
}
