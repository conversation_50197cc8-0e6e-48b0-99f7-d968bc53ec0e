//using HibernatingRhinos.Profiler.Appender.NHibernate;
//using lep;
//using lep.user;
//using Microsoft.Extensions.Configuration;
//using NHibernate;
//using NHibernate.Cfg;
//using NHibernate.Mapping;
//using System;
//using static NHibernate.Cfg.Environment;
//using Microsoft.AspNetCore.Hosting;
//using Microsoft.Extensions.DependencyInjection;
//using System.Text.Json;
//using System.Text.Json.Serialization;

//namespace LepCore
//{

//	public class NHToMicrosoftLoggerFactory : INHibernateLoggerFactory
//	{
//		private readonly Microsoft.Extensions.Logging.ILoggerFactory _loggerFactory;

//		public NHToMicrosoftLoggerFactory(Microsoft.Extensions.Logging.ILoggerFactory loggerFactory)
//		{
//			_loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
//		}

//		public INHibernateLogger LoggerFor(string keyName)
//		{
//			var msLogger = _loggerFactory.CreateLogger(keyName);
//			return new NHToMicrosoftLogger(msLogger);
//		}

//		public INHibernateLogger LoggerFor(System.Type type)
//		{
//			return LoggerFor(
//				Microsoft.Extensions.Logging.Abstractions.Internal.TypeNameHelper.GetTypeDisplayName(type));
//		}
//	}
//}
