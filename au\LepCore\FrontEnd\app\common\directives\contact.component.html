<div ng-form="contactDetails">
	<div class="form-horizontal" all-input-disabled="{{disabled}}">
		<div class="row">
			<div class="col-md-6">
		
				<div class="form-group">
					<label class="control-label  col-xs-3" for="name">Person</label>
					<div class="col-xs-7">
						<div class="input-group">
							<div class="input-group-addon"><i class="glyphicon glyphicon-user"></i></div>
							<input class="form-control" type="text" ng-model="vm.Name" name="name" 
								   ng-maxlength="50" size="50" ng-required="true" mvs/>
						</div>
					</div>
				</div>
				
				

			</div>

			<div class="col-md-6">
				<div class="form-group">
					<label class="control-label  col-xs-3" for="email">Email</label>
					<div class="col-xs-7">
						<div class="input-group">
							<span class="input-group-addon"><i class="glyphicon   glyphicon-envelope"></i></span>
							<input class="form-control" type="email" ng-model="vm.Email" name="email" ng-required="emailRequired" mvs />
						</div>
					</div>
				</div>
			</div>


			<div class="col-md-6">
				<div class="form-group ">
					<label class="control-label col-xs-3" for="phone">Phone</label>
					<div class="col-xs-7">
						<div class="input-group">
							<span class="input-group-addon">
								<i class="glyphicon glyphicon-phone-alt"></i>
							</span>

							<div class="col-sm-3 col-xs-3 lpad0 rpad0">
								<input class="form-control" type="text" ng-model="vm.AreaCode" maxlength="6" />
							</div>
							<div class="col-sm-9 col-xs-9 lpad0 rpad0">
								<input class="form-control" type="text" ng-model="vm.Phone" maxlength="12" />
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="col-md-6">
				<div class="form-group">
					<label class="control-label  col-xs-3" for="mobile">Mobile</label>
					<div class="col-xs-7">
						<div class="input-group">
							<span class="input-group-addon"><i class="glyphicon glyphicon-phone"></i></span>
							<input class="form-control" type="text" ng-model="vm.Mobile" id="mobile" name="mobile" ng-maxlength="12"  ng-required="mobileRequired" mvs />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


