<div class="col-xs-12">
	<h4>Production Timing</h4>
</div>
{{filters |json}}
<div class="row">
	<div class="col-xs-6">
		<div class="row1">
		 
			 
			<div class="form-group form-group-sm">
						<div class="form-group ">
							<label class="col-xs-4  control-label" for="templateId">Job Type</label>
							<div class="col-xs-8 ">
								<select id="templateId" name="templateId" class="form-control input"
										ng-options="t.Id as t.Name for t in templates | filter:{Name:'!Custom'}" ng-model="filters.TemplateId" ng-required="true"></select>
							</div>
						</div>

					
					</div>
			 

			 

	 
			
			
			<div class="form-group form-group-sm">
				<label class="col-xs-4 control-label" for="customer">Facility</label>
				<div class="col-xs-8">
				 
					<label class="control-label"><input type="radio" ng-model="filters.Facility" value="FG" /> Forest Glen</label>&nbsp;&nbsp;
					<label class="control-label"><input type="radio" ng-model="filters.Facility" value="PM" /> Melbourne</label>
				</div>
			</div>
					
			<div class="form-group form-group-sm">
				<label class="col-xs-4 control-label" for="customer">Folding</label>
				<div class="col-xs-8">
				 
					<label class="control-label"><input type="radio" ng-model="filters.Folding" value="true" /> Yes</label>&nbsp;&nbsp;
					<label class="control-label"><input type="radio" ng-model="filters.Folding" value="false" /> No</label>
				</div>
			</div>


		</div>
	</div>

	<!-- right column with Qty Price MYOB -->
	<div class="col-xs-6">
		<div class="row">
			<div class="col-xs-12">
				<div ui-grid="gridOptions" ui-grid-edit ui-grid-cellnav class="grid"></div>

				<a class="btn" ng-click="addBlankRow()"><i class="glyphicon glyphicon-plus"></i>Add new price point</a>
			</div>
			
			
			<div class="col-xs-12">
				<div class="form-group">

					<button class="btn btn-info pull-right" ng-click="savePrices()">Save</button>
				</div>
			</div>

		</div>
	</div>
</div>


<br><br><br>
<pre>{{result | json}}</pre>

