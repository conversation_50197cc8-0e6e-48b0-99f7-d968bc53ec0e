using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

//namespace LepCore.Hubs
//{
//	/*
//		public abstract class ApiControllerWithHub<THub> : Controller
//		  where THub : IHub
//		{
//			private readonly IConnectionManager connectionManager;

//			public ApiControllerWithHub(IConnectionManager connectionManager)
//			{
//				this.connectionManager = connectionManager;
//			}

//			Lazy<IHubContext> hub = new Lazy<IHubContext>(
//				() => connectionManager.GetHubContext<THub>()
//			);

//			protected IHubContext Hub
//			{
//				get { return hub.Value; }
//			}
//		}
//		*/




[Authorize]
public class LepHub : DynamicHub
{

	public override async Task OnConnectedAsync()
	{
		try
		{
			var u = Context.User;
			var userId = u.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
			var role = u.Claims.Where(c => c.Type == ClaimTypes.Role).Select(c => c.Value).FirstOrDefault();

			await Groups.AddToGroupAsync(Context.ConnectionId, role);
			await Clients.Caller.HubReceived("Welcome " + Context.User.Identity.Name + "!");
			await base.OnConnectedAsync();
		}
		catch (Exception ex)
		{
			var m = ex.Message;

			throw;
		}


	}



	public override async Task OnDisconnectedAsync(Exception exception)
	{
		await Groups.RemoveFromGroupAsync(Context.ConnectionId, "SignalR Users");
		await base.OnDisconnectedAsync(exception);
	}



}


//	//[HubName("runHub")]
//	//[Authorize(Roles = LepRoles.Staff)]
//	//public class RunHub : Hub
//	//{
//	//	public RunHub() { }

//	//	public string ConnectionID { get; set; }
//	//}

//	//[HubName("jobBoardHub")]
//	//[Authorize(Roles = LepRoles.Staff)]
//	//public class JobBoardHub : Hub
//	//{
//	//	public JobBoardHub() { }
//	//	public string ConnectionID { get; set; }
//	//}


//	[HubName("lepHub")]
//	[Authorize]
//	public class LepHub : Hub
//	{
//		private static int clientCounter = 0;
//		private static int staffCounter = 0;



//		public LepHub ()
//		{
//		}

//		public string ConnectionID { get; set; }

//		public void Update (string message)
//		{
//			Clients.All.update(message);
//		}

//		public void UpdateJobBoard ()
//		{

//		}




//		public void Send ()
//		{
//			string message = string.Format("{0} Customers and {1} staff online.  {2}", clientCounter, staffCounter, DateTime.Now.ToLongTimeString());
//			Clients.All.recalculateOnlineUsers(message);
//		}

//		public override Task OnConnected ()
//		{
//			ConnectionID = Context.ConnectionId;

//			if (Context.User.IsInRole(LepRoles.Staff)) {
//				staffCounter++;
//				Groups.Add(Context.ConnectionId, LepRoles.Staff);

//			} else {
//				clientCounter++;
//			}

//			Send();
//			return base.OnConnected();
//		}

//		public override Task OnDisconnected (bool stopCalled)
//		{
//			if (Context.User.IsInRole(LepRoles.Staff)) {
//				Groups.Remove(Context.ConnectionId, LepRoles.Staff);
//				staffCounter--;
//			} else
//				clientCounter--;
//			Send();
//			return base.OnDisconnected(stopCalled);
//		}
//	}





//	//[HubName("staffHub")]
//	//[Authorize(Roles = LepRoles.Staff)]
//	//public class StaffHub : Hub
//	//{
//	//}

//}

