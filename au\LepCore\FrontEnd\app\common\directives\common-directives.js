app = angular.module('app');
app.filter('trustThisUrl', ["$sce", function ($sce) {
    return function (val) {
        return $sce.trustAsResourceUrl(val);
    };
}]);

app.directive('selectDefaultValue', function() {
    return {
      require: 'ngModel',
      link: function(scope, element, attrs, ngModel) {
        var defaultValue = attrs.selectDefaultValue;
        scope.$watch(attrs.ngModel, function() {
          if (ngModel.$modelValue === null || ngModel.$modelValue === undefined) {
            debugger;
            ngModel.$setViewValue(defaultValue);
            ngModel.$render();
          }
        });
      }
    };
  });

app.directive('abn', function () {
    var reSpaces = /\s/g;
    var re11digits = /^\d{11}$/;
    return {
        restrict: 'A',
        require: 'ngModel',
        link: function (scope, elm, attrs, ctrl) {
            function isValidAbn(val) {
                if (!val || val === '') {
                    return true;
                }

                var isValid = false;
                val = val.replace(reSpaces, '');

                // 0. ABN must be 11 digits long
                if (val.match(re11digits)) {
                    isValid = true;
                    /*
                    var abn = [];
                    for (var i = 0; i < val.length; i++) {
                        abn.push(parseInt(val[i], 10));
                    }

                    // 1. Subtract 1 from the first (left) digit to give a new eleven digit number
                    abn[0]--;

                    // 2. Multiply each of the digits in this new number by its weighting factor
                    // 3. Sum the resulting 11 products
                    var weight = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19];
                    var weightedSum = 0;
                    for (var j = 0; j < abn.length; j++) {
                        var weightedDigit = abn[j] * weight[j];
                        weightedSum += weightedDigit;
                    }

                    // 4. Divide the total by 89, noting the remainder
                    // 5. If the remainder is zero the number is valid
                    var remainder = (weightedSum % 89);
                    isValid = (remainder === 0);
                    */
                }
                return isValid;
            }

            ctrl.$parsers.unshift(function (value) {
                value = value.replace(reSpaces, '');

                if (isValidAbn(value)) {
                    ctrl.$setValidity('abn', true);

                    return value;
                } else {
                    ctrl.$setValidity('abn', false);
                    return undefined;
                }
            });

            ctrl.$formatters.unshift(function (value) {
                ctrl.$setValidity('abn', isValidAbn(value));
                return value;
            });
        }
    };
});

app.directive('mvs', ['$log', function ($log) {
    //var r = /(ng-[valid|invalid]\-[a-z\-]*)/gm;
    var r = /(ng-[a-z\-]*)/gm;

    function link(scope, element, attrs, ctrl) {
        var p = $(element).closest('div.form-group');
        if (!p) {
            //cant find group
            return;
        }
        function mirrorClassesOnParent() {
            $(p).removeClass(function (index, className) {
                return (className.match(r) || []).join(' ');
            });
            var val = element.attr('class');
            var nx = val.match(r).join(" ");
            $(p).addClass(nx);
        }
        mirrorClassesOnParent();
        function w() { return element.attr('class'); }
        scope.$watch(w, function (aa) {
            setTimeout(mirrorClassesOnParent, 1);
        }, true);
    }

    return {
        //require: ['ngModel', '^form'],
        require: '?ngModel',
        link: link
    };
}]);

app.directive('paypalFastCheckout', [
    function () {
        return {
            restrict: 'E',
            template: '<div ng-show="config.show" id="paypal-button-{{config.id}}"></div>',
            scope: {
                callbacksuccess: '&',
                config: '=',
                show: '=',
                txn: '=',
            },
            link: function (scope, ele, att) {
                var loadPaypal, loaded;
                loaded = false;
                loadPaypal = function () {
                    paypal.Button.render({
                        env: scope.config.env,
                        client: {
                            sandbox: scope.config.sandbox,
                            production: scope.config.production
                        },
                        locale: 'en_US',
                        style: {
                            size: 'medium',
                            shape: 'rect',
                            color: 'blue'
                        },
                        payment: function () {
                            var client, env;
                            env = this.props.env;
                            client = this.props.client;
                            txn = scope.txn;
                            return paypal.rest.payment.create(env, client, txn);
                        },
                        commit: true,
                        onAuthorize: function (data, actions) {
                            return actions.payment.execute().then(function () {
                                scope.callbacksuccess()(data, actions);
                            });
                        }
                    }, '#paypal-button-' + scope.config.id);
                };
                scope.$watch('config', function (newv, oldv) {
                    if (!_.isUndefined(newv) && !loaded) {
                        loaded = true;
                        loadPaypal();
                    }
                });
            }
        };
    }
]);

app.filter('range', function() {
  return function(input, min, max) {
    min = parseInt(min); //Make string input int
    max = parseInt(max);
    for (var i=min; i<max; i++)
      input.push(i);
    return input;
  };
});

app.directive('require', function () {
    return {
        restrict: 'E',
        scope: { scriptSrc: '@' },
        template: '<script type="text/javascript" src="{{ scriptSrc }}"></script>',
        replace: true
    };
});

app.directive('uiSelectWrap', ['$document', 'uiGridEditConstants', function uiSelectWrap($document, uiGridEditConstants) {
    return function link($scope, $elm, $attr) {
        $document.on('click', docClick);

        function docClick(evt) {
            if ($(evt.target).closest('.ui-select-container').size() === 0) {
                $scope.$emit(uiGridEditConstants.events.END_CELL_EDIT);
                $document.off('click', docClick);
            }
        }
    };
}]);

app.filter('propsFilter', function () {
    return function (items, props) {
        var out = [];

        if (angular.isArray(items)) {
            var keys = Object.keys(props);

            items.forEach(function (item) {
                var itemMatches = false;

                for (var i = 0; i < keys.length; i++) {
                    var prop = keys[i];
                    var text = props[prop].toLowerCase();
                    if (item[prop].toString().toLowerCase().indexOf(text) !== -1) {
                        itemMatches = true;
                        break;
                    }
                }

                if (itemMatches) {
                    out.push(item);
                }
            });
        } else {
            // Let the output be the input untouched
            out = items;
        }

        return out;
    };
});

app.directive('rxx', function () {
    return {
        link: function link(scope, element) {
            element.bind('resize', function (event) {
                var m = { message: 'resize' + element.id, h: element.height, w: element.width };

                window.parent.postMessage(m, "*");
            });
        }
    };
});

app.directive('clickAndWait', function () {
    return {
        restrict: 'A',
        scope: {
            asyncAction: '&clickAndWait'
        },
        link: function link(scope, element) {
            element.bind('dblclick', function (event) {
                event.preventDefault();
                //throw "Double click not allowed!";
                return false;
            });

            element.bind('click', function () {
                element.addClass('state-waiting');
                element.prop('disabled', true);

                scope.$apply(function () {
                    scope.asyncAction().finally(function () {
                        element.prop('disabled', false);
                        element.removeClass('state-waiting');
                    });
                });
            });
        }
    };
});

app.directive('datepickerPopup', function () {
    return {
        restrict: 'EAC',
        require: 'ngModel',
        link: function (scope, element, attr, controller) {
            //remove the default formatter from the input directive to prevent conflict
            controller.$formatters.shift();
        }
    };
});
app.directive('gogleaddress', function () {
    return {
        restrict: "EA",
        scope: {
            ngModel:"=",
            details:"=",
           },
        link: function (scope, element, attrs) {
            var options = {
                types: [],
                componentRestrictions: {
                    country: "AU"
                }
            };
            var autocomplete = new google.maps.places.Autocomplete(element[0], options);
            google.maps.event.addListener(autocomplete, 'place_changed', function() {  
            var place = autocomplete.getPlace();  
             
            if (!scope.$$phase) scope.$apply(function(){
                    var componenents=place.address_components||[];
                    cd(componenents)
                    for(var i=0;i<componenents.length;i++){
                        var item=componenents[i];
                        if(item&&item.types){
                           if(/route/gi.test(item.types[0])){
                              scope.details.Address1 = componenents[0].short_name + " " + componenents[1].short_name;
                           }
                           if(/country/gi.test(item.types[0])){
                              scope.details.Country =item.long_name;
                           }
                           if(/street_number/gi.test(item.types[0])){
                              scope.details.streetNumber =item.long_name;
                           }
                           if(/postal_code/gi.test(item.types[0])){
                              scope.details.Postcode =item.long_name;
                           }
                           if(/locality/gi.test(item.types[0])){
                              scope.details.City =item.short_name;
                           }
                           if(/administrative_area_level_1/gi.test(item.types[0])){
                            scope.details.State =item.short_name;
                         }
                       }
                    }
            });
            })
        }
        }
    });

app.directive('updateOnEnter', function () {
    return {
        restrict: 'A',
        require: 'ngModel',
        link: function (scope, element, attrs, ctrl) {
            element.on("keyup", function (ev) {
                // Our input is set to update on keyup, so if it's not ENTER, we stop
                // propagation to prevent the event from triggering angular's build in updater.
                if (ev.keyCode !== 13) return ev.stopImmediatePropagation();
                ctrl.$commitViewValue();
                //scope.$apply(ctrl.$setTouched);
            });
        }
    };
});

app.directive('accessibleForm', function () {
    return {
        restrict: 'A',
        link: function (scope, elem) {
            // set up event handler on the form element
            elem.on('submit', function () {
                // find the first invalid element
                var firstInvalid = element.find('.ng-invalid:visible').first();

                // if we find one, set focus
                if (firstInvalid) {
                    firstInvalid.focus();
                }
            });
        }
    };
});

app.directive('numbersOnly', function () {
    return {
        require: 'ngModel',
        link: function (scope, element, attr, ngModelCtrl) {
            function fromUser(text) {
                if (text) {
                    var transformedInput = text.toString().replace(/[^0-9]/g, '');

                    if (transformedInput !== text) {
                        ngModelCtrl.$setViewValue(transformedInput);
                        ngModelCtrl.$render();
                    }
                    return transformedInput;
                }
                return undefined;
            }
            ngModelCtrl.$parsers.push(fromUser);
        }
    };
});

app.filter('reverse', function () {
    return function (items) {
        return items.slice().reverse();
    };
});

app.directive('visualDiff', ["dp", function (dp) {
    return {
        restrict: 'EA',
        scope: {
            n: "=n", o: "=o"
        },
        template: '<div ng-bind-html="changes"></div>',
        link: function link(scope, elem, attrs) {
            scope.$watch('n', function (n) {
                var delta = dp.diff(JSON.parse(angular.toJson(scope.o)), JSON.parse(angular.toJson(n)));
                window.jsondiffpatch.formatters.html.hideUnchanged();
                scope.changes = window.jsondiffpatch.formatters.html.format(delta, scope.o);
            }, true);
        }
    };
}
]);

app.directive('noImage', function () {
    return {
        restrict: 'A',
        link: function (scope, el, attr) {
            var setDefaultImage = function () {
                el.attr('src', attr.noImage);
            };
            scope.$watch(function () {
                return attr.ngSrc;
            }, function () {
                var src = attr.ngSrc;

                if (!src) {
                    setDefaultImage();
                }
            });

            el.bind('error', function () {
                setDefaultImage();
            });
        }
    };
});

app.directive('allInputDisabled', ["$compile", "$parse", "$timeout",
    function ($compile, $parse, $timeout) {
        return {
            restrict: 'A',
            link: function link(scope, elem, attrs, ctrl) {
                //cd (arguments)
                function toggleReadOnly(elem, disabled) {
                    if (disabled) {
                        elem.find('input,select,textarea').attr('disabled', 'disabled');
                        elem.find('label').addClass('text-muted');
                    } else {
                        elem.find('input,select,textarea').removeAttr('disabled');
                        elem.find('label').removeClass('text-muted');
                    }
                }

                attrs.$observe('allInputDisabled', function (n) {
                    toggleReadOnly(elem, n == 'true');
                });

                /*
                scope.$watch( function(){
                    return elem.children.length;
                }, function (n, o) {
                    toggleReadOnly(elem, attrs.allInputDisabled);
                })
               */
            }
        };
    }
]);

app.directive('selectOnClick', ['$window', function ($window) {
    return {
        restrict: 'A',
        link: function (scope, element, attrs) {
            element.on('focus', function () {
                if (!$window.getSelection().toString()) {
                    // Required for mobile Safari
                    this.setSelectionRange(0, this.value.length);
                }
            });
        }
    };
}]);

// some lookup Id fields are coming in as 0 in the View Models instead of coming as null.
// e.g.  ParentId of UnitVMs. When the form loads just using Required is not working as required
// as per design accpets 0 as valid value. this directive checks if the value is 0 and marks as
// invalid
app.directive('lepRequiredId', ['$parse', '$log', function ($parse, $log) {
    return {
        require: '?ngModel',
        link: function (scope, elm, attr, ctrl) {
            if (!ctrl) {
                return;
            }

            if (!attr.lepRequiredId) {
                attr.lepRequiredId = true; // force truthy in case we are on non input element
            }

            ctrl.$validators.lepRequiredId = function (modelValue, viewValue) {
                var valid = isValid(modelValue, viewValue);
                try {
                    if (!valid) {
                        //elm[0].parents('.form-group').addClass('has-error');
                        elm.parent().parent().addClass('has-error');
                    } else {
                        elm.parent().parent().removeClass('has-error');
                        //elm[0].parents('.form-group').removeClass('has-error');
                    }
                } catch (e) {
                }
                return valid;
            };

            var isValid = function (modelValue, viewValue) {   
                //if (elm[0].length <= 1) return true;
                var cvalue = typeof(attr.lepRequiredId) === "boolean" ? 
                                attr.lepRequiredId :
                                $parse(attr.lepRequiredId)(scope);
                // attr.lepRequiredId, cvalue
                if (cvalue === false || cvalue === undefined) {
                    return true;
                }

                var allowZero, allowNull;
                if (attr.allowZero === undefined) {
                    allowZero = false; // force truthy in case we are on non input element
                } else if (attr.allowZero === 'true' || attr.allowZero === true) {
                    allowZero = true; // force truthy in case we are on non input element
                } else {
                    allowZero = false;
                }

                var testVal;
                if (angular.isObject(viewValue)) {
                    testVal = (viewValue.Id) || allowZero || undefined;
                }
                else if (viewValue === null) {
                    testVal = (viewValue) || allowZero;
                }
                else {
                    testVal = (viewValue) || allowZero;
                }
                //elm.prop('title', JSON.stringify(  viewValue) );
                if (!!!testVal) {
                    //$log.debug(attr.ngModel + ' failed required id');
                    return false;
                } 
                //$log.debug(attr.ngModel + ' okay');

                return true;
            };

            attr.$observe('lepRequiredId', function (n, o) {
                ctrl.$validate();
            });

            scope.$watch(function () {
                return elm[0].length;
            }, function () {
                ctrl.$validate();
            });
        }
    };
}]);

app.directive("valueNeedsToBe", function () {
    return {
        restrict: "A",
        require: '?ngModel',
        link: function (scope, elm, attr, ctrl) {
            var validator;
            if (!ctrl) {
                return;
            }
            validator = function (value) {
                var result = value == attr.valueNeedsToBe;
                ctrl.$setValidity("valueNeedsToBe", result);
                return value;
            };
            ctrl.$formatters.push(validator);
            ctrl.$parsers.unshift(validator);
            attr.$observe("valueNeedsToBe", function () {
                validator(ctrl.$viewValue);
            });
        }
    };
});

// a custom validator, pass in a predicate that determines form validity
// for use case see member-email-list
app.directive("valueNeedsToPass", ['$log',
    function ($log) {
        return {
            priority: 0,
            restrict: "A",
            require: '?ngModel',
            link: function (scope, elm, attr, ctrl) {
                // return;
                var validator, predicateFn = attr.valueNeedsToPass;
                //if (!ctrl) {
                //    return;
                //}
                /*
                validator = function (value) {
                    var fn = scope.$eval(predicateFn);
                    var result = fn(value);
                    if (result) {
                        ctrl.$setValidity("valueNeedsToPass", true);
                    } else {
                        ctrl.$setValidity("valueNeedsToPass", false);
                    }
                    return value;
                };
                ctrl.$formatters.push(validator);
                ctrl.$parsers.unshift(validator);
                return validator(ctrl.$viewValue);
                */
                var msg = attr.ngModel;
                ctrl.$validators.valueNeedsToPass = function (mvalue, vvalue) {
                    var result = true;
                    var value = mvalue || vvalue;
                    var wh = elm.Id;

                    var fn = scope.$eval(predicateFn);
                    result = fn(value, attr.id);

                    if (!result && msg) {
                        //$Log.Debug(msg);
                    }

                    return result;
                };

                attr.$observe(attr.ngModel, function () {
                    ctrl.$validate();
                });
            }
        };
    }
]);

app.directive('staffOnly', ["$compile", "$parse",
    function ($compile, $parse) {
        return {
            restrict: 'A',
            link: function link(scope, elem, attrs, ctrl) {
                attrs.$observe('allInputDisabled', function (n, o) {
                    if (n == 'true') {
                        elem.find('input,select,textarea').attr('disabled', 'disabled');
                    } else {
                        elem.find('input,select,textarea').removeAttr('disabled');
                    }
                });
            }
        };
    }
]);

app.directive('integer', function () {
    return {
        require: 'ngModel',
        link: function (scope, ele, attr, ctrl) {
            ctrl.$parsers.unshift(function (viewValue) {
                return parseInt(viewValue, 10);
            });
        }
    };
});

app.filter("pre", ['$sce', function ($sce) { // register new filter
    return function (input) { // filter arguments
        var value = ['<pre>', input, '</pre>'].join("");
        var f = $sce.trustAsHtml(value); // implementation
        return f;
    };
}]);

app.filter('trust', ['$sce', function ($sce) {
    return function (value, type) {
        return $sce.trustAsHtml(value);
    };
}]);

app.filter("regexReplace", function () { // register new filter
    return function (input, searchRegex, replaceRegex) { // filter arguments
        return input.replace(RegExp(searchRegex), replaceRegex); // implementation
    };
});

app.filter("routingChain", function () { // register new filter
    return function (input) { // filter arguments
        return input.join("\r\n"); // implementation
    };
});

app.filter("eatlastdot", function () { // register new filter
    var r = /&amp;/g;
    return function (input) { // filter arguments
        if (!input) return input;
        input = input.replace('(next day dispatch)', ' NDD').replace('(same day dispatch)', ' SDD');//.replace(/Business Card/g, 'BC');

        return input;
        //return input.replace(r, '&').slice(0, -1).split(".").join('\n'); // implementation
    };
});

app.filter("datex", ['$filter', function ($filter) {
    return function (input, fmt, nullMsg) {
        if (input == '0001-01-01T00:00:00')
            return nullMsg;
        return $filter('date')(input, fmt, '+1000');
        //return $filter('date')(input, fmt );
    };
}]);

app.directive("checkboxModel", [
    "$compile", function ($compile) {
        return {
            restrict: "A",
            link: function (scope, ele, attrs) {
                // Defining updateSelection function on the parent scope
                if (!scope.$parent.updateSelections) {
                    // Using splice and push methods to make use of
                    // the same "selections" object passed by reference to the
                    // addOrRemove function as using "selections = []"
                    // creates a new object within the scope of the
                    // function which doesn't help in two way binding.
                    scope.$parent.updateSelections = function (selectedItems, item, isMultiple) {
                        var itemIndex = selectedItems.indexOf(item);
                        var isPresent = (itemIndex > -1);
                        if (isMultiple) {
                            if (isPresent) {
                                selectedItems.splice(itemIndex, 1);
                            } else {
                                selectedItems.push(item);
                            }
                        } else {
                            if (isPresent) {
                                selectedItems.splice(0, 1);
                            } else {
                                selectedItems.splice(0, 1, item);
                            }
                        }
                    };
                }

                // Adding or removing attributes
                ele.attr("ng-checked", attrs.checkboxModel + ".indexOf(" + attrs.checkboxValue + ") > -1");
                var multiple = attrs.multiple ? "true" : "false";
                ele.attr("ng-click",
                    "updateSelections(" + [attrs.checkboxModel, attrs.checkboxValue, multiple].join(",") + ")");

                // Removing the checkbox-model attribute,
                // it will avoid recompiling the element infinitly
                ele.removeAttr("checkbox-model");
                ele.removeAttr("checkbox-value");
                ele.removeAttr("multiple");

                $compile(ele)(scope);
            }
        };
    }
]);

//app.directive('animateSpin', ['$animate', function ($animate) {
//    return {
//        link: function (scope, elem, attrs) {
//            elem.on('click', function () {
//                var self = angular.element(this);
//                $animate.addClass(self, 'spin', function () {
//                    self.removeClass('spin');
//                });
//            });
//        }
//    };
//}]);

app.directive('ngMultiple', function () {
    return {
        restrict: 'A',
        scope: {
            ngMultiple: '='
        },
        link: function (scope, element) {
            var unwatch = scope.$watch('ngMultiple', function (newValue) {
                if (newValue) {
                    element.attr('multiple', 'multiple');
                } else {
                    element.removeAttr('multiple');
                }
            });
        }
    };
});

app.filter('bytes', function () {
    return function (bytes, precision) {
        if (isNaN(parseFloat(bytes)) || !isFinite(bytes)) return '-';
        if (typeof precision === 'undefined') precision = 1;
        var units = ['bytes', 'kB', 'MB', 'GB', 'TB', 'PB'],
            number = Math.floor(Math.log(bytes) / Math.log(1024));
        return (bytes / Math.pow(1024, Math.floor(number))).toFixed(precision) + ' ' + units[number];
    };
});

app.directive("fileInput", ['$q', function ($q) {
    return {
        scope: false,
        link: function (scope, element, attributes) {
            var $input = $(element),
                $label = $input.next('label'),
                labelVal = $label.html();

            element.bind("change", function (e) {
                var fileName = '';

                if (this.files && this.files.length > 1)
                    fileName = (this.getAttribute('data-multiple-caption') || '').replace('{count}', this.files.length);
                else if (e.target.value)
                    fileName = e.target.value.split('\\').pop();

                if (fileName)
                    $label.find('span').html(fileName);
                else
                    $label.html(labelVal);
            });
            element
                .on('focus', function () {
                    $input.addClass('has-focus');
                })
                .on('blur', function () {
                    $input.removeClass('has-focus');
                });
        }
    };
}]);

app.filter('printtype', function () {
    return function (input) {
        // return input ? '\u2713' : '\u2718';
        if (input === 'O' || input === 1) {
            return 'Offset';
        }
        else if (input === 'D' || input === 2) {
            return 'Digital';
        }
        else if (input === 'W' || input === 3) {
            return 'Wide format';
        }
        else if (input === 'N' || input === 4) {
            return 'N/A';
        }
        return '???';
    };
});

app.directive("postcode", ["AutoCompleteService", function (AutoCompleteService) {
    return {
        restrict: "A",
        link: function (scope, elem, attr, ctrl) {
            elem.autocomplete({
                source: function (searchTerm, response) {
                    AutoCompleteService.search(searchTerm.term).then(function (autocompleteResults) {
                        response($.map(autocompleteResults, function (autocompleteResult) {
                            return {
                                label: autocompleteResult.YourDisplayProperty,
                                value: autocompleteResult
                            };
                        }));
                    });
                },
                minLength: 3,
                select: function (event, selectedItem) {
                    // Do something with the selected item, e.g.
                    scope.yourObject = selectedItem.item.value;
                    scope.$apply();
                    event.preventDefault();
                }
            });
        }
    };
}]);
/*

// Workaround for bug #1404
// https://github.com/angular/angular.js/issues/1404
// Source: http://plnkr.co/edit/hSMzWC?p=preview
app.config(['$provide', function ($provide) {
    $provide.decorator('ngModelDirective', ['$delegate', function ($delegate) {
        var ngModel = $delegate[0], controller = ngModel.controller;
        ngModel.controller = ['$scope', '$element', '$attrs', '$injector', function (scope, element, attrs, $injector) {
            var $interpolate = $injector.get('$interpolate');
            attrs.$set('name', $interpolate(attrs.name || '')(scope));
            $injector.invoke(controller, this, {
                '$scope': scope,
                '$element': element,
                '$attrs': attrs
            });
        }];
        return $delegate;
    }]);
    $provide.decorator('formDirective', ['$delegate', function ($delegate) {
        var form = $delegate[0], controller = form.controller;
        form.controller = ['$scope', '$element', '$attrs', '$injector', function (scope, element, attrs, $injector) {
            var $interpolate = $injector.get('$interpolate');
            attrs.$set('name', $interpolate(attrs.name || attrs.ngForm || '')(scope));
            $injector.invoke(controller, this, {
                '$scope': scope,
                '$element': element,
                '$attrs': attrs
            });
        }];
        return $delegate;
    }]);
}]);

*/

app.directive("autoGrow", function () {
    return function (scope, element, attr) {
        var update = function () {
            element.css("height", "auto");
            element.css("height", (element[0].scrollHeight + 20) + "px");
        };
        scope.$watch(attr.ngModel, function () {
            update();
        });
        attr.$set("ngTrim", "false");
    };
});

angular.module('angular-jquery-autocomplete', [])
    .directive('autocomplete', function () {
        return {
            restrict: 'A',
            scope: {
                autocompleteconfig: '='
            },
            link: function (scope, elem, attr, ctrl) {
                scope.$watch('autocompleteconfig', function (value) {
                    elem.autocomplete(value);
                });
            }
        };
    });

/*
maxlines attribute directive, specify on a <textarea> to validate the number
of lines entered is less than the specified value.

Optional attributes:
   maxlines-prevent-enter: Specify as false to NOT block the pressing of the Enter
    key once the max number of lines has been reached.
*/

app.directive('maxlines', function () {
    return {
        restrict: 'A',
        require: 'ngModel',
        link: function (scope, elem, attrs, ngModel) {
            var maxLines = 1;
            attrs.$observe('maxlines', function (val) {
                maxLines = parseInt(val);
            });
            ngModel.$validators.maxlines = function (modelValue, viewValue) {
                var numLines = (modelValue || '').split("\n").length;
                return numLines <= maxLines;
            };
            attrs.$observe('maxlinesPreventEnter', function (preventEnter) {
                // if attribute value starts with 'f', treat as false. Everything else is true
                preventEnter = (preventEnter || '').toLocaleLowerCase().indexOf('f') !== 0;
                if (preventEnter) {
                    addKeypress();
                } else {
                    removeKeypress();
                }
            });

            function addKeypress() {
                elem.on('keypress', function (event) {
                    // test if adding a newline would cause the validator to fail
                    if (event.keyCode == 13 && !ngModel.$validators.maxlines(ngModel.$modelValue + '\n', ngModel.$viewValue + '\n')) {
                        event.preventDefault();
                    }
                });
            }

            function removeKeypress() {
                elem.off('.maxlines');
            }

            scope.$on('$destroy', removeKeypress);
        }
    };
});

app.directive('format', function ($filter) {
    'use strict';

    return {
        require: '?ngModel',
        link: function (scope, elem, attrs, ctrl) {
            if (!ctrl) {
                return;
            }

            ctrl.$formatters.unshift(function () {
                return $filter('number')(ctrl.$modelValue);
            });

            ctrl.$parsers.unshift(function (viewValue) {
                var plainNumber = viewValue.replace(/[\,\.]/g, ''),
                    b = $filter('number')(plainNumber);

                elem.val(b);
                return parseInt(plainNumber, 10);
            });
        }
    };
});


// I lazily load the images, when they come into view.
app.directive("bnLazySrc", 
    [ '$window', '$document',function( $window, $document ) 
    {
        // I manage all the images that are currently being
        // monitored on the page for lazy loading.
        var lazyLoader = (function() {

            // I maintain a list of images that lazy-loading
            // and have yet to be rendered.
            var images = [];

            // I define the render timer for the lazy loading
            // images to that the DOM-querying (for offsets)
            // is chunked in groups.
            var renderTimer = null;
            var renderDelay = 100;

            // I cache the window element as a jQuery reference.
            var win = $( $window );

            // I cache the document document height so that
            // we can respond to changes in the height due to
            // dynamic content.
            var doc = $document;
            var documentHeight = doc.height();
            var documentTimer = null;
            var documentDelay = 2000;

            // I determine if the window dimension events
            // (ie. resize, scroll) are currenlty being
            // monitored for changes.
            var isWatchingWindow = false;


            // ---
            // PUBLIC METHODS.
            // ---


            // I start monitoring the given image for visibility
            // and then render it when necessary.
            function addImage( image ) {
                images.push( image );
                if ( ! renderTimer ) {
                    startRenderTimer();
                }

                if ( ! isWatchingWindow ) {
                    startWatchingWindow();
                }

            }


            // I remove the given image from the render queue.
            function removeImage( image ) {
                // Remove the given image from the render queue.
                for ( var i = 0 ; i < images.length ; i++ ) {
                    if ( images[ i ] === image ) {
                        images.splice( i, 1 );
                        break;
                    }
                }

                // If removing the given image has cleared the
                // render queue, then we can stop monitoring
                // the window and the image queue.
                if ( ! images.length ) {
                    clearRenderTimer();
                    stopWatchingWindow();
                }
            }


            // ---
            // PRIVATE METHODS.
            // ---


            // I check the document height to see if it's changed.
            function checkDocumentHeight() {
                // If the render time is currently active, then
                // don't bother getting the document height -
                // it won't actually do anything.
                if ( renderTimer ) {
                    return;
                }

                var currentDocumentHeight = doc.height();

                // If the height has not changed, then ignore -
                // no more images could have come into view.
                if ( currentDocumentHeight === documentHeight ) {
                    return;
                }

                // Cache the new document height.
                documentHeight = currentDocumentHeight;
                startRenderTimer();
            }


            // I check the lazy-load images that have yet to
            // be rendered.
            function checkImages() {

                // Log here so we can see how often this
                // gets called during page activity.
       
                var visible = [];
                var hidden = [];

                // Determine the window dimensions.
                var windowHeight = win.height();
                var scrollTop = win.scrollTop();

                // Calculate the viewport offsets.
                var topFoldOffset = scrollTop;
                var bottomFoldOffset = ( topFoldOffset + windowHeight );

                // Query the DOM for layout and seperate the
                // images into two different categories: those
                // that are now in the viewport and those that
                // still remain hidden.
                var i;
                for ( i = 0 ; i < images.length ; i++ ) {
                    var image = images[ i ];
                    if ( image.isVisible( topFoldOffset, bottomFoldOffset ) ) {
                        visible.push( image );
                    } else {
                        hidden.push( image );
                    }
                }

                // Update the DOM with new image source values.
                for ( i = 0 ; i < visible.length ; i++ ) {
                    visible[ i ].render();
                }

                // Keep the still-hidden images as the new
                // image queue to be monitored.
                images = hidden;

                // Clear the render timer so that it can be set
                // again in response to window changes.
                clearRenderTimer();

                // If we've rendered all the images, then stop
                // monitoring the window for changes.
                if ( ! images.length ) {
                    stopWatchingWindow();
                }

            }


            // I clear the render timer so that we can easily
            // check to see if the timer is running.
            function clearRenderTimer() {
                clearTimeout( renderTimer );
                renderTimer = null;

            }


            // I start the render time, allowing more images to
            // be added to the images queue before the render
            // action is executed.
            function startRenderTimer() {
                renderTimer = setTimeout( checkImages, renderDelay );

            }


            // I start watching the window for changes in dimension.
            function startWatchingWindow() {
                isWatchingWindow = true;
                // Listen for window changes.
                win.on( "resize.bnLazySrc", windowChanged );
                win.on( "scroll.bnLazySrc", windowChanged );

                // Set up a timer to watch for document-height changes.
                documentTimer = setInterval( checkDocumentHeight, documentDelay );

            }


            // I stop watching the window for changes in dimension.
            function stopWatchingWindow() {
                isWatchingWindow = false;
                // Stop watching for window changes.
                win.off( "resize.bnLazySrc" );
                win.off( "scroll.bnLazySrc" );
                // Stop watching for document changes.
                clearInterval( documentTimer );
            }


            // I start the render time if the window changes.
            function windowChanged() {
                if ( ! renderTimer ) {
                    startRenderTimer();
                }
            }


            // Return the public API.
            return({
                addImage: addImage,
                removeImage: removeImage
            });

        })();


        // ------------------------------------------ //
        // ------------------------------------------ //


        // I represent a single lazy-load image.
        function LazyImage( element ) {

            // I am the interpolated LAZY SRC attribute of
            // the image as reported by AngularJS.
            var source = null;

            // I determine if the image has already been
            // rendered (ie, that it has been exposed to the
            // viewport and the source had been loaded).
            var isRendered = false;

            // I am the cached height of the element. We are
            // going to assume that the image doesn't change
            // height over time.
            var height = null;


            // ---
            // PUBLIC METHODS.
            // ---


            // I determine if the element is above the given
            // fold of the page.
            function isVisible( topFoldOffset, bottomFoldOffset ) {

                // If the element is not visible because it
                // is hidden, don't bother testing it.
                if ( ! element.is( ":visible" ) ) {

                    return( false );

                }

                // If the height has not yet been calculated,
                // the cache it for the duration of the page.
                if ( height === null ) {

                    height = element.height();

                }

                // Update the dimensions of the element.
                var top = element.offset().top;
                var bottom = ( top + height );

                // Return true if the element is:
                // 1. The top offset is in view.
                // 2. The bottom offset is in view.
                // 3. The element is overlapping the viewport.
                return(
                        (
                            ( top <= bottomFoldOffset ) &&
                            ( top >= topFoldOffset )
                        ) ||(
                            ( bottom <= bottomFoldOffset ) &&
                            ( bottom >= topFoldOffset )
                        )|| (
                            ( top <= topFoldOffset ) &&
                            ( bottom >= bottomFoldOffset )
                        )
                );

            }


            // I move the cached source into the live source.
            function render() {

                isRendered = true;

                renderSource();

            }


            // I set the interpolated source value reported
            // by the directive / AngularJS.
            function setSource( newSource ) {

                source = newSource;

                if ( isRendered ) {

                    renderSource();

                }

            }


            // ---
            // PRIVATE METHODS.
            // ---


            // I load the lazy source value into the actual
            // source value of the image element.
            function renderSource() {

                element[ 0 ].src = source;

            }


            // Return the public API.
            return({
                isVisible: isVisible,
                render: render,
                setSource: setSource
            });

        }


        // ------------------------------------------ //
        // ------------------------------------------ //


        // I bind the UI events to the scope.
        function link( $scope, element, attributes ) {

            var lazyImage = new LazyImage( element );

            // Start watching the image for changes in its
            // visibility.
            lazyLoader.addImage( lazyImage );


            // Since the lazy-src will likely need some sort
            // of string interpolation, we don't want to
            attributes.$observe(
                "bnLazySrc",
                function( newSource ) {

                    lazyImage.setSource( newSource );

                }
            );


            // When the scope is destroyed, we need to remove
            // the image from the render queue.
            $scope.$on(
                "$destroy",
                function() {

                    lazyLoader.removeImage( lazyImage );

                }
            );

        }


        // Return the directive configuration.
        return({
            link: link,
            restrict: "A"
        });

    }]
);


app.directive('checklistModel', ['$parse', '$compile', function($parse, $compile) {
    // contains
    function contains(arr, item, comparator) {
      if (angular.isArray(arr)) {
        for (var i = arr.length; i--;) {
          if (comparator(arr[i], item)) {
            return true;
          }
        }
      }
      return false;
    }
  
    // add
    function add(arr, item, comparator) {
      arr = angular.isArray(arr) ? arr : [];
        if(!contains(arr, item, comparator)) {
            arr.push(item);
        }
      return arr;
    }
  
    // remove
    function remove(arr, item, comparator) {
      if (angular.isArray(arr)) {
        for (var i = arr.length; i--;) {
          if (comparator(arr[i], item)) {
            arr.splice(i, 1);
            break;
          }
        }
      }
      return arr;
    }
  
    // http://stackoverflow.com/a/19228302/1458162
    function postLinkFn(scope, elem, attrs) {
       // exclude recursion, but still keep the model
      var checklistModel = attrs.checklistModel;
      attrs.$set("checklistModel", null);
      // compile with `ng-model` pointing to `checked`
      $compile(elem)(scope);
      attrs.$set("checklistModel", checklistModel);
  
      // getter for original model
      var checklistModelGetter = $parse(checklistModel);
      var checklistChange = $parse(attrs.checklistChange);
      var checklistBeforeChange = $parse(attrs.checklistBeforeChange);
      var ngModelGetter = $parse(attrs.ngModel);
  
  
  
      var comparator = function (a, b) {
        if(!isNaN(a) && !isNaN(b)) {
          return String(a) === String(b);
        } else {
          return angular.equals(a,b);
        }
      };
  
      if (attrs.hasOwnProperty('checklistComparator')){
        if (attrs.checklistComparator[0] == '.') {
          var comparatorExpression = attrs.checklistComparator.substring(1);
          comparator = function (a, b) {
            return a[comparatorExpression] === b[comparatorExpression];
          };
  
        } else {
          comparator = $parse(attrs.checklistComparator)(scope.$parent);
        }
      }
  
      // watch UI checked change
      var unbindModel = scope.$watch(attrs.ngModel, function(newValue, oldValue) {
        if (newValue === oldValue) {
          return;
        }
  
        if (checklistBeforeChange && (checklistBeforeChange(scope) === false)) {
          ngModelGetter.assign(scope, contains(checklistModelGetter(scope.$parent), getChecklistValue(), comparator));
          return;
        }
  
        setValueInChecklistModel(getChecklistValue(), newValue);
  
        if (checklistChange) {
          checklistChange(scope);
        }
      });
  
      // watches for value change of checklistValue
      var unbindCheckListValue = scope.$watch(getChecklistValue, function(newValue, oldValue) {
        if( newValue != oldValue && angular.isDefined(oldValue) && scope[attrs.ngModel] === true ) {
          var current = checklistModelGetter(scope.$parent);
          checklistModelGetter.assign(scope.$parent, remove(current, oldValue, comparator));
          checklistModelGetter.assign(scope.$parent, add(current, newValue, comparator));
        }
      }, true);
  
      var unbindDestroy = scope.$on('$destroy', destroy);
  
      function destroy() {
        unbindModel();
        unbindCheckListValue();
        unbindDestroy();
      }
  
      function getChecklistValue() {
        return attrs.checklistValue ? $parse(attrs.checklistValue)(scope.$parent) : attrs.value;
      }
  
      function setValueInChecklistModel(value, checked) {
        var current = checklistModelGetter(scope.$parent);
        if (angular.isFunction(checklistModelGetter.assign)) {
          if (checked === true) {
            checklistModelGetter.assign(scope.$parent, add(current, value, comparator));
          } else {
            checklistModelGetter.assign(scope.$parent, remove(current, value, comparator));
          }
        }
  
      }
  
      // declare one function to be used for both $watch functions
      function setChecked(newArr, oldArr) {
        if (checklistBeforeChange && (checklistBeforeChange(scope) === false)) {
          setValueInChecklistModel(getChecklistValue(), ngModelGetter(scope));
          return;
        }
        ngModelGetter.assign(scope, contains(newArr, getChecklistValue(), comparator));
      }
  
      // watch original model change
      // use the faster $watchCollection method if it's available
      if (angular.isFunction(scope.$parent.$watchCollection)) {
          scope.$parent.$watchCollection(checklistModel, setChecked);
      } else {
          scope.$parent.$watch(checklistModel, setChecked, true);
      }
    }
  
    return {
      restrict: 'A',
      priority: 1000,
      terminal: true,
      scope: true,
      compile: function(tElement, tAttrs) {
  
        if (!tAttrs.checklistValue && !tAttrs.value) {
          throw 'You should provide `value` or `checklist-value`.';
        }
  
        // by default ngModel is 'checked', so we set it if not specified
        if (!tAttrs.ngModel) {
          // local scope var storing individual checkbox model
          tAttrs.$set("ngModel", "checked");
        }
  
        return postLinkFn;
      }
    };
  }]);