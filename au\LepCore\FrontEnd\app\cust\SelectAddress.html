<div class="row" ng-init="idx=null">
	<style>
 

 
        /* Just common table stuff. Really. */
        table {
            border-collapse: collapse;
            width: 100%;
        }

        th, td {
            padding: 8px 16px;
        }

        th {
            background: #eee;
        }

     
        table.tableBodyScroll tbody {
            display: block;
            max-height: 250px;
            overflow-y: scroll;
        }

            table.tableBodyScroll thead, table.tableBodyScroll tbody tr {
                display: table;
                width: 100%;
                table-layout: fixed;
            }
	</style>
	<div class="col-md-12">
		<div class="modal-header">
			<h3 class="modal-title">Favourite delivery details</h3>
		</div>
		<div class="modal-body">
			<div class="row">
				<div class="col-md-12">
					<input type="text" ng-model="addrSeach" class="form-control  input-sm" placeholder="seach..."/>
                    <table class="table table-condensed table-bordered table-striped tableBodyScroll " id="addressList">
                        <colgroup>
                            <col style="width:30px">
                            <col style="">
                            <col style="">
                            <col style="width: 30px">
                        </colgroup>

                        <thead>
                            <tr>
                                <th style="width: 30px"></th>
                                <th>Recipient</th>
                                <th>Address</th>
                                <th style="width: 30px"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="fa in favAddresses | filter: addrSeach">
                                
                                <td  style="width: 30px">
                                    <a name="selectedAdress" ng-click="$parent.idx=fa; closeThisDialog(idx)" class="scale2" title="Use this address">
                                        <i class="glyphicon glyphicon-hand-right " style="font-size: 16pt"> </i>
                                    </a>
                                </td>
                                <td class="">{{fa.RecipientName}} {{fa.Phone}}</td>
                                <td class="">
                                    {{fa.Address.Address1}} {{fa.Address.Address2}} {{fa.Address.Address3}} <br/>
                                    {{fa.Address.City}},
                                    {{fa.Address.State}},
                                    {{fa.Address.Postcode}},
                                    {{fa.Address.Country}}
                                </td>

                                <td  style="width: 30px">
                                    <span ng-click="deleteFavouriteDeliveryDetails(fa)" title="delete this address">
                                        <i class="	glyphicon glyphicon-trash"></i>
                                    </span>
                                </td>
                               
                            </tr>
                        </tbody>

                    </table>
				</div>
				
			</div>
		</div>
		<div class="modal-footer">
			<div>
				<button class="btn" ng-click="closeThisDialog()">Cancel</button>
				<!--<button class="btn" ng-click="closeThisDialog(idx)"><i class="glyphicon glyphicon-floppy-save" ng-show="idx"></i> Use selected address</button>-->
			</div>
		</div>

	</div>

</div>
