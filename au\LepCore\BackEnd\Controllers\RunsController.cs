using AutoMapper;
using lep;
using lep.configuration;
using lep.despatch;
using lep.despatch.impl;
using lep.despatch.impl.label;
using lep.extensionmethods;
using lep.job;
using lep.job.impl;
using lep.jobmonitor.impl;
using lep.run;
using lep.user;
using LepCore.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;
using NHibernate.Criterion;
using Serilog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Microsoft.Extensions.Configuration;

namespace LepCore.Controllers
{
	[Produces("application/json")]
	[Route("api/[controller]")]
	[Authorize(Roles = LepRoles.Staff)]
	[ApiExplorerSettings(IgnoreApi = true)]
	public class RunsController : Controller
	{

		private readonly IJobApplication _jobApp;
		private readonly IRunApplication _runApp;
		private readonly IUserApplication _userApplication;
		private readonly IConfigurationApplication _configApp;
		private readonly JobBoardDTOHelper _jobBoardDTOHelper;
		private readonly LabelPrinterApplication _labelPrinterApplication;
		private IMapper _mapper;
		private IStaff _currentUser;

		private readonly string _impHotfolderPath;
		private string RUN_ERROR_CONFLICT = "{0}’s changes were handled before yours. Please retry";
		private string RUN_WARN_INVALID = "not meet run allocation rule";
		private string RUN_WARN_OVERSIZE = "run size over 42 slots";

		private PrintEngine _printEngine;
		private NHibernate.ISession _session;


		public RunsController(
			IUserApplication userApplication,
			IJobApplication jobApp,
			IRunApplication runApplication,
			IConfigurationApplication configurationApplication,
			IMapper mapper,
			LabelPrinterApplication labelPrinterApplication,
			PrintEngine printEngine,
			JobBoardDTOHelper jobBoardDTOHelper,
			NHibernate.ISession sf,
			IConfiguration configuration
		)
		{
			_jobApp = jobApp;
			_runApp = runApplication;
			_userApplication = userApplication;
			_mapper = mapper;
			_jobBoardDTOHelper = jobBoardDTOHelper;
			_labelPrinterApplication = labelPrinterApplication;
			_printEngine = printEngine;
			_session = sf;
			_configApp = configurationApplication;

			// Initialize the IMP hotfolder path from configuration
			_impHotfolderPath = configuration["IMP:HotfolderPath"] ?? @"\\ppp01\HotFolderRoot\MyLEP";
		}

		protected IStaff GetCurrentUser()
		{
			var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
			_currentUser = (IStaff)_userApplication.GetUser(userId);
			return _currentUser;
		}

		[HttpGet("RunsearchCriteria")]
		public IActionResult GeRunsearchCriteria()
		{
			return Ok(_mapper.Map<RunSearchCriteriaDto>(GetCurrentUser().RunSearchCriteria ?? new lep.user.impl.RunSearchCriteria()));
		}

		[HttpGet("{id:int}/print/{which}")]
		[Produces(typeof(IRun))]
		public IActionResult PrintJobRunSheet(int id, string which) //int? Id = null
		{
			var run = _runApp.GetRun(id);
			if (run == null) return NotFound();

			if (which == "jobsheet")
				_jobApp.PrintJobRunSheet(run);
			else if (which == "FurtherProcessingList")
				//todo: print run sheet
				_labelPrinterApplication.Print(LabelType.FurtherProcessingList, run);
			else if (which == "runsheet")
				//todo: print run sheet
				throw new NotImplementedException();

			_printEngine.ReceivePrintQueue();
			return Ok();
		}

		[HttpPost("{id:int}/ClearMeAsOperator")]
		[Produces(typeof(IRun))]
		public IActionResult ClearMeAsOperator(int id) //int? Id = null
		{
			var run = _runApp.GetRun(id);
			if (run == null) return NotFound();

			if (run.Operator == GetCurrentUser())
			{
				run.Operator = null;
				_runApp.Save(run);
			}
			return Ok();
		}

		[HttpGet("{id:int}")]
		[Produces(typeof(IRun))]
		public IActionResult Get(int id) //int? Id = null
		{
			var run = _runApp.GetRun(id);
			if (run == null) return NotFound();

			if (run.Operator == null && (run.Status == lep.RunStatusOptions.Filling || run.Status == lep.RunStatusOptions.LayoutRequired))
			{
				run.Operator = GetCurrentUser();
				_runApp.Save(run);
			}

			var runDto = _mapper.Map<RunViewDto>(run);
			runDto.HealthCss = _jobBoardDTOHelper.GetRunHealthCSS(run.Id);
			runDto.Visibility = GetFlagsRealtedToRun(run);

			foreach (var j in runDto.Jobs)
			{
				j.HealthCss = _jobBoardDTOHelper.GetJobHealthCSS(j.Id);
			}

			return new OkObjectResult(runDto);
		}

		[HttpPost("{runId:int}/artworks")]
		public IActionResult SaveJobArtworks([FromRoute] int runId)
		{
			var run = _runApp.GetRun(runId);
			if (run == null) return NotFound();

			var files = Request.Form.Files;
			var fileDetector = new FileDetector();
			Action<IFormFile, LayoutType, int> doFile = (x, layoutType, count) =>
			{
				if (x.Length == 0) return;
				ArtworkTypeOptions option = fileDetector.GetFileType(x.OpenReadStream());

				// todo:   uncomment this block
				//if (option != ArtworkTypeOptions.Quark && option != ArtworkTypeOptions.InDesign) {
				//    throw new ArgumentException("invalid file format");
				//}
				ILayout layout = _runApp.NewLayout(layoutType, count, x.FileName);
				run = _runApp.SaveLayout(run, layout, x.OpenReadStream(), Path.GetExtension(x.FileName), GetCurrentUser());
			};

			foreach (var file in files)
			{
				var filename = ContentDispositionHeaderValue.Parse(file.ContentDisposition).FileName.Trim().ToString();
				var position = file.Name;

				var layoutType = (LayoutType)Enum.Parse(typeof(LayoutType), position);

				doFile(file, layoutType, 0);
			}

			_runApp.Save(run);

			// todo: page files
			/*
                if (nonMagPanel.Visible) {
                    doFile(layoutFileBack, LayoutType.BackLayout, 0);
                    doFile(layoutFileFront, LayoutType.FrontLayout, 0);
                }

                if (magCoverPanel.Visible) {
                    doFile(layoutFileInsideBack, LayoutType.InsideBack, 0);
                    doFile(layoutFileInsideFront, LayoutType.InsideFront, 0);
                    doFile(layoutFileOutsideBack, LayoutType.OutsideBack, 0);
                    doFile(layoutFileOutsideFront, LayoutType.OutsideFront, 0);
                }

                if (magPageFile.Visible) {
                    int count = 1;

                    foreach (RepeaterItem item in layoutFilePagesRepeater.Items) {
                        FileUpload upload = item.FindControl("layoutFilePage") as FileUpload;
                        doFile(upload, LayoutType.Page, count);
                        count++;
                    }
                }
             */
			//string selectedUploadType = uploadReq.uploadtype;

			//var str = $"{files.Count} file(s) / {size} bytes uploaded successfully!";

			return Ok();
		}

		protected dynamic GetFlagsRealtedToRun(IRun run)
		{
			var currentUser = GetCurrentUser();
			Func<Role[], bool> AllowAccess = roles =>
			{
				if ((currentUser != null) && currentUser is IStaff)
					for (var i = 0; i < roles.Length; i++) if (((IStaff)currentUser).Role == roles[i]) return true;
				return false;
			};

			dynamic vis = new ExpandoObject();
			//statusLst.Enabled = run.ManuallyManage;
			vis.artworkMacButton = false;
			vis.artworkFolder = false;
			DirectoryInfo dir = LepGlobal.Instance.ArtworkDirectory(run);
			if (dir.Exists)
			{
				if (((string)HttpContext.Request.Headers["User-Agent"]).ToLower().IndexOf("mac") != -1)
				{
					vis.artworkFolderNavigateUrl = string.Format("lep:mount:{0}", LepGlobal.Instance.ArtworkURL(run, true)).Replace(" ", "%20");
					vis.artworkFolderTarget = "";
					vis.artworkFolderVisible = true;
				}
				else
				{
					// IE/win32 gets a file explorer link
					vis.artworkFolderNavigateUrl = LepGlobal.Instance.ArtworkURL(run, false);
					vis.artworkFolderTarget = "_artwork";
					vis.artworkFolderVisible = true;
				}
			}
			vis.statusLst = run.ManuallyManage;
			vis.printRunSheet = run.IsBusinessCard;
			vis.statusLstEnabled = run.ManuallyManage;
			vis.LayoutInst = run.Status == lep.RunStatusOptions.LayoutRequired;
			vis.FillingInst = run.Status == lep.RunStatusOptions.Filling;
			vis.CompleteInst = run.Status == lep.RunStatusOptions.PackingDone;
			vis.ProgressInst = vis.LayoutInst == false && vis.FillingInst == false && vis.FillingInst == false;
			vis.saveButton = (run.Operator == null || run.Operator == GetCurrentUser()) && AllowAccess(new[] { Role.SuperAdministrator, Role.Administrator, Role.Prepress });
			vis.workButton = run.Operator != null && run.Operator != GetCurrentUser() && AllowAccess(new Role[] { Role.SuperAdministrator, Role.Administrator, Role.Prepress });
			if (run.IsBusinessCard && run.CalculateUsedBC() > 42)
			{
				vis.overloadDiv = true;
				vis.overloadSlotLbl = run.CalculateUsedBC().ToString();
			}
			else
			{
				vis.overloadDiv = false;
			}
			if (run.Status != RunStatusOptions.LayoutDone && run.Status != RunStatusOptions.LayoutRequired)
			{
				vis.canEditSlot = false;
			}
			else
			{
				vis.canEditSlot = true;
			}
			vis.magCoverPanel = false;
			vis.magPageFile = false;
			foreach (IJob job in run.Jobs)
			{
				switch ((JobTypeOptions)Enum.ToObject(typeof(JobTypeOptions), job.Template.Id))
				{
					case JobTypeOptions.Magazine:
					case JobTypeOptions.A4CalendarSelfCover:
						vis.magPageFile = true;
						vis.nonMagPanel = false;
						break;

					case JobTypeOptions.MagazineSeparate:
					case JobTypeOptions.A4CalendarSeparateCover:
						vis.magCoverPanel = true;
						vis.magPageFile = true;
						vis.nonMagPanel = false;
						break;

					default:
						if (!vis.magCoverPanel && !vis.magPageFile)
						{
							vis.nonMagPanel = true;
						}
						break;
				}
			}
			return vis;
		}

		[HttpPost("{id:int}")]
		[ReturnBadRequestOnModelError]
		public IActionResult SaveRun([FromRoute] int id, [FromBody] RunUpdateDto dto, [FromServices] JobBoardDTOHelper jbh) //int? Id = null
		{
			var run = _runApp.GetRun(id);
			if (run == null) return NotFound();
			_currentUser = GetCurrentUser();
			run.SetStatus(dto.Status, _currentUser);
			run.ManuallyManage = dto.ManuallyManage;
			run.Status = dto.Status;
			run.Urgent = dto.Urgent;
			run.QtyList = dto.QtyList;
			run.TSlots = dto.TSlots;

			if (run.Status <= RunStatusOptions.PressDone)
			{
				run.PrintType = dto.PrintType;
				run.Jobs.ForEach(_ =>
				{
					if (_.PrintType != dto.PrintType)
					{
						_.PrintType = dto.PrintType;
						_.AddComment(_currentUser, $"PrintType changed to {_.PrintType} as included in {run.PrintType} Run {id}", true);
					}
				});
			}

			// Update the stock if provided
			if (dto.StockId.HasValue && dto.StockId.Value > 0)
			{
				var stock = _session.Get<lep.job.IStock>(dto.StockId.Value);
				if (stock != null && run.Stock.Id != stock.Id)
				{
					run.Stock = stock;
					run.AddComment(_currentUser, $"Stock changed to {stock.Name}", true);
				}
			}

			// bool cantChangeSlotNumberNow = (int)(run.Status) > (int)RunStatusOptions.LayoutDone;
			if (run.IsBusinessCard && dto.JobIdToSlots != null && dto.JobIdToSlots.Any())
			{
				run.Slots.Clear();
				foreach (var p in dto.JobIdToSlots)
				{
					IJob job = _jobApp.GetJob(p.JobId);
					run.AddSlot(job, p.Slot);
				}
			}

			if (run.IsBusinessCard && run.Status == RunStatusOptions.LayoutDone)
			{
				run.NumOfPressSheets = dto.NumOfPressSheets;
			}
			_runApp.Save(run);

			//jbh.CreateJobBoard();


			return Ok();
		}

		#region JOB SPEC

		/// <summary>
		/// Get Job  print spec. Finising details etc
		/// </summary>
		/// <param name="runId"></param>
		/// <param name="jobId"></param>
		/// <returns></returns>
		[HttpGet("{runId:int}/jobspec/{jobId}")]
		[Produces(typeof(JobPressDetailsViewDto))]
		public IActionResult GetJobPrintSpec(int runId, int jobId)
		{
			var run = _runApp.GetRun(runId);
			if (run == null) return NotFound();

			var job = _jobApp.GetJob(jobId);
			if (job == null) return NotFound();

			var jobSpecDto = _mapper.Map<JobPressDetailsViewDto>(job);

			return new OkObjectResult(jobSpecDto);
		}

		[HttpGet("{runId:int}/jobspec/{jobId:int}/from/{srcJobId:int}")]
		[Produces(typeof(JobPressDetailsViewDto))]
		public IActionResult GetJobPrintSpecWithCopyFromJob(int runId, int jobId, int srcJobId)
		{
			var run = _runApp.GetRun(runId);
			if (run == null) return NotFound();

			var job = _jobApp.GetJob(jobId);
			if (job == null) return NotFound();

			var jobSpecDto = _mapper.Map<JobPressDetailsViewDto>(job);
			jobSpecDto.PressDetails.Clear();

			if (srcJobId != 0)
			{
				var srcJob = _jobApp.GetJob(srcJobId);
				for (int i = 0; i < srcJob.PressDetails.Count; i++)
				{
					jobSpecDto.PressDetails.Add((lep.job.impl.PressDetail)srcJob.PressDetails[i]);
				}
			}
			return new OkObjectResult(jobSpecDto);
		}

		/// <summary>
		/// Save job print spec. Finising details etc
		/// </summary>
		/// <param name="runId"></param>
		/// <param name="jobId"></param>
		/// <param name="dto"></param>
		/// <returns></returns>
		[HttpPost("{runId:int}/jobspec/{jobId}")]
		[ReturnBadRequestOnModelError]
		public IActionResult SaveJobPrintSpec([FromRoute] int runId, [FromRoute] int jobId, [FromBody] JobPressDetailsUpdateDto dto)
		{
			var job = _jobApp.GetJob(jobId);
			if (job == null) return NotFound();

			try
			{
				job = _mapper.Map(dto, job);
			}
			catch (Exception ex)
			{
				var m = ex.Message;
			}

			job.FinishedBy = GetCurrentUser();
			_jobApp.Save(job);
			return Ok();
		}

		#endregion JOB SPEC

		/// <summary>
		/// The main handler for runs list / page
		/// </summary>
		/// <param name="sp"></param>
		/// <returns></returns>
		[HttpPost("process")]
		[ReturnBadRequestOnModelError]
		public IActionResult Process([FromBody] RunJobCommand sp)
		{
			IStaff currentUser = (IStaff)GetCurrentUser();

			if (sp.Action == "RAEnable")
			{
				_session.CreateSQLQuery("Update Configuration set DefaultValue = 'Y' where code = 'AutoRunAllocationEnabled'").ExecuteUpdate();
			}

			if (sp.Action == "RADisable")
			{
				_session.CreateSQLQuery("Update Configuration set DefaultValue = 'N' where code = 'AutoRunAllocationEnabled'").ExecuteUpdate();
			}

			if ((sp.Action == "search") && sp.Search != null)
			{
				if (currentUser.RunSearchCriteria == null)
				{
					currentUser.CreatRunSearchCriteria();
				}
				currentUser.RunSearchCriteria = _mapper.Map(sp.Search, currentUser.RunSearchCriteria);
				_userApplication.Save(currentUser);
			}

			var userRunSerch = currentUser.RunSearchCriteria;
			var rc = sp.Search;
			if (sp == null) sp = new RunJobCommand();

			dynamic response = new ExpandoObject();

			switch (sp.Action)
			{
				case "addnewrun":
					AddJobsToANewRun(response, sp.JobIds, sp.HasConfirm);
					break;

				case "movetorun":
					MoveToRun(response, sp.JobIdRunIds, sp.RunId, sp.HasConfirm);
					break;

				case "removerun":
					RemoveRun(response, sp.RunId, sp.JobIds);
					break;

				case "runopen":
				case "runclose":
					if (sp.RunOpenClose != 0)
					{
						if (sp.Action == "runopen")
						{
							currentUser.RunSearchCriteria.OpenRun.Add(sp.RunOpenClose);
						}
						else if (sp.Action == "runclose")
						{
							currentUser.RunSearchCriteria.OpenRun.Remove(sp.RunOpenClose);
						}
					}
					_userApplication.Save(currentUser);
					return new OkObjectResult(new { });
#pragma warning disable 162
					break;
#pragma warning restore 162
				default:
					break;
			}

			var openRun = currentUser.RunSearchCriteria.OpenRun;

			var searchorder = 0;
			var searchjob = 0;
			int.TryParse(userRunSerch.OrderNr, out searchorder);
			int.TryParse(userRunSerch.JobNr, out searchjob);

			Order orderJ = null;
			var ordering = currentUser.RunSearchCriteria.Ordering;
			var descJ = false;
			if (ordering.EndsWith("-"))
			{
				ordering = ordering.Remove(ordering.Length - 1);
				descJ = true;
			}

			switch (ordering)
			{
				case "Submitted": orderJ = new Order("o.SubmissionDate", descJ); break;
				case "RequiredBy": orderJ = new Order("j.StatusDate", descJ); break;
				case "Customer": orderJ = new Order("c.Name", descJ); break;
				case "Template": orderJ = new Order("t.Name", descJ); break;
				case "Stock": orderJ = new Order("s.Name", descJ); break;
				case "Size": orderJ = new Order("p.Name", descJ); break;
				case "JobId": orderJ = new Order("j.Id", descJ); break;
				case "JobName": orderJ = new Order("j.Name", descJ); break;
				case "Qty": orderJ = new Order("j.Quantity", descJ); break;
			}

			//response.jobs = _jobApp.FindReadyJobs(userRunSerch.Facility, userRunSerch, orderJ).Select(j => new RunJobDto(j)).ToList<RunJobDto>();
			var jobsCriteria = _jobApp.FindReadyJobs2(userRunSerch.Facility, userRunSerch, orderJ);

			// do a projection on jobs, 4seconds to .4 seconds
			var jobs = jobsCriteria
			   //.SetProjection(
			   //	   Projections.ProjectionList()
			   //		.Add(Projections.Property("j.ArtworkStatus"), "ArtworkStatus")
			   //		.Add(Projections.Property("j.BackCelloglaze"), "BackCelloglaze")
			   //		.Add(Projections.Property("j.BackCelloglaze"), "BackCelloglaze")
			   //		.Add(Projections.Property("j.BackCelloglaze"), "BackCelloglaze")
			   //		.Add(Projections.Property("j.BackPrinting"), "BackPrinting")
			   //		.Add(Projections.Property("j.Customer"), "Customer")
			   //		.Add(Projections.Property("j.Enable"), "Enable")
			   //		.Add(Projections.Property("j.FinishedSize"), "FinishedSize")
			   //		.Add(Projections.Property("j.FrontCelloglaze"), "FrontCelloglaze")
			   //		.Add(Projections.Property("j.FrontPrinting"), "FrontPrinting")
			   //		.Add(Projections.Property("j.FrontPrinting"), "FrontPrinting")
			   //		.Add(Projections.Property("j.Id"), "Id")
			   //		.Add(Projections.Property("j.Name"), "Name")
			   //		.Add(Projections.Property("j.Order"), "Order")
			   //		.Add(Projections.Property("j.ProofStatus"), "ProofStatus")
			   //		.Add(Projections.Property("j.Quantity"), "Quantity")
			   //		.Add(Projections.Property("j.Pages"), "Pages")
			   //		.Add(Projections.Property("j.ReadyArtworkApproval"), "ReadyArtworkApproval")
			   //		.Add(Projections.Property("j.RequiredByDate"), "RequiredByDate")
			   //		.Add(Projections.Property("j.Status"), "Status")
			   //		.Add(Projections.Property("j.Stock"), "Stock")
			   //		.Add(Projections.Property("j.SupplyArtworkApproval"), "SupplyArtworkApproval")
			   //		.Add(Projections.Property("j.Template"), "Template")
			   //		.Add(Projections.Property("j.Urgent"), "Urgent")
			   // )
			   //.SetResultTransformer(new NHibernate.Transform.AliasToBeanResultTransformer(typeof(Job)))
			   .List<Job>();

			var rjobs = jobs.Select(j => _mapper.Map<RunJobDto>(j));


			if (ordering == "HD")
			{
				if (descJ)
					rjobs = rjobs.OrderByDescending(j => j.HD).ToList();
				else
					rjobs = rjobs.OrderBy(j => j.HD).ToList();
			}

			response.jobs = rjobs;



			foreach (RunJobDto j in response.jobs)
			{
				j.ShowRed = (false);
				var searched = j.Id == searchjob || j.OrderId == searchorder;
				j.Searched = searched;
				j.Health = _jobBoardDTOHelper.GetJobHealthCSS(j.Id);
			}

			// runs array
			var status = currentUser.RunSearchCriteria.RunStatus.ToList();

			// Order orderR = null;
			var runOrdering = currentUser.RunSearchCriteria.RunOrdering;
			var descR = false;
			if (runOrdering.EndsWith("-"))
			{
				runOrdering = runOrdering.Remove(runOrdering.Length - 1);
				descR = true;
			}

			var runsCriteria = _runApp.FindCurrentRunsCriteria(userRunSerch.Facility, status, userRunSerch.RunNr,
				userRunSerch.OrderNr, userRunSerch.JobNr, (userRunSerch?.Stock ?? null),
				userRunSerch.RunSearchOption, userRunSerch.IsOnHold, userRunSerch.Cello);

			// Handle JobTypes filter (Category)
			if (userRunSerch.JobTypes != null && userRunSerch.JobTypes.Count > 0)
			{
				var jobTypesCriteria = DetachedCriteria.For(typeof(IJob), "j2")
					.CreateCriteria("j2.Template", "t2")
					.Add(Expression.In("t2.Id", userRunSerch.JobTypes.ToArray()))
					.SetProjection(Projections.Property("j2.Runs"));

				runsCriteria.Add(Subqueries.Exists(jobTypesCriteria));
			}

			// Handle JobType filter (Template)
			if (userRunSerch.JobType.HasValue)
			{
				var jobTypeCriteria = DetachedCriteria.For(typeof(IJob), "j3")
					.CreateCriteria("j3.Template", "t3")
					.Add(Expression.Eq("t3.Id", userRunSerch.JobType.Value))
					.SetProjection(Projections.Property("j3.Runs"));

				runsCriteria.Add(Subqueries.Exists(jobTypeCriteria));
			}



			if (userRunSerch.StockKind != null)
			{
				runsCriteria.Add(Expression.Like("s.Name", $"%{userRunSerch.StockKind}%"));
			}


			//criteria.SetResultTransformer(Transformers.AliasToBean(typeof(OrderSummaryDto)));

			var runs = runsCriteria.List<IRun>();

			#region run sort

			((List<IRun>)runs).Sort(delegate (IRun x, IRun y)
			{
				if (string.IsNullOrEmpty(ordering))
				{
					if (x.Urgent && x.Urgent != y.Urgent)
					{
						return -1;
					}
					else if (y.Urgent && x.Urgent != y.Urgent)
					{
						return 1;
					}
					else if (x.Status != y.Status)
					{
						return x.Status.CompareTo(y.Status);
					}
				}
				else
				{
					switch (runOrdering)
					{
						case "SubmittedDate":
							try
							{
								return descR
								? x.MinSubmitDate.Value.CompareTo(y.MinSubmitDate)
								: y.MinSubmitDate.Value.CompareTo(x.MinSubmitDate);
							}
							catch
							{
								return 0;
							}

						case "Id":
							return descR ? x.Id.CompareTo(y.Id) : y.Id.CompareTo(x.Id);

						case "Stock":
							return descR
								? x.StockString().CompareTo(y.StockString())
								: y.StockString().CompareTo(x.StockString());

						case "Slot":
							return descR
								? x.Slots.Count.CompareTo(y.Slots.Count)
								: y.Slots.Count.CompareTo(x.Slots.Count);

						case "Cmyk":
							return descR
								? x.SideString().CompareTo(y.SideString())
								: y.SideString().CompareTo(x.SideString());

						case "Celloglaze":
							return descR
								? x.CelloString().CompareTo(y.CelloString())
								: y.CelloString().CompareTo(x.CelloString());
					}
				}
				return x.DateCreated.CompareTo(y.DateCreated);
			});

			#endregion run sort

			//response.runs = runs.Select(r => new RunRunDto(r, currentUser)).ToList<RunRunDto>();

			var rruns = runs.Select(r => _mapper.Map<RunRunDto>(r)).ToList();
			if (runOrdering == "HD")
			{
				if (descR)
					rruns = rruns.OrderByDescending(r => r.HD).ToList();
				else
					rruns = rruns.OrderBy(r => r.HD).ToList();
			}

			response.runs = rruns;
			foreach (RunRunDto r in response.runs)
			{
				r.Open = openRun.Contains(r.Id);

				bool searched = r.Jobs.Any(j => j.Id == searchjob || j.OrderId == searchorder);
				r.Searched = searched;
				r.Health = _jobBoardDTOHelper.GetRunHealthCSS(r.Id);
				foreach (var j in r.Jobs)
				{
					j.ShowRed = (false);
					j.Searched = j.Id == searchjob || j.OrderId == searchorder;
					j.Health = _jobBoardDTOHelper.GetJobHealthCSS(j.Id);
				}
			}
			response.AutoRunAllocationEnabled = _session
				.CreateSQLQuery("select DefaultValue from Configuration where code = 'AutoRunAllocationEnabled'")
				.UniqueResult<string>() == "Y";

			return new OkObjectResult(response);
		}




		//[HttpPost("AddNewRun")]
		//public IActionResult AddNewRun ()
		//{
		//    return new OkObjectResult(null);
		//}

		//[HttpPost("RemoveRun")]
		//public IActionResult Removerun ( )
		//{
		//    return new OkObjectResult(null);
		//}

		//[HttpPost("MoveToRun")]
		//public IActionResult MoveToRun ()
		//{
		//    return new OkObjectResult(null);
		//}

		private void MoveToRun(dynamic response, List<JobIdRunId> JobIdRunIds, int runId, bool hasConfirm)
		{
			IStaff currentUser = GetCurrentUser();

			IRun r = null;
			if (runId != 0)
			{
				r = _runApp.GetRun(runId);
				if (r == null || (r.Operator != null && r.Operator != currentUser) ||
					r.Status != RunStatusOptions.Filling)
				{
					// ReturnError(jsonWriter, RUN_ERROR_CONFLICT, r);
					response.error = string.Format(RUN_ERROR_CONFLICT, "run " + r);
					return;
				}
			}

			IList<IRun> effectRuns = new List<IRun>();
			foreach (var jrId in JobIdRunIds)
			{
				var j = _jobApp.GetJob(jrId.JobId);
				var oldrunid = jrId.RunId;

				var currentrunid = j.Runs.Count > 0 ? j.Runs[0].Id : 0;
				if (oldrunid != currentrunid)
				{
					//ReturnError(jsonWriter, RUN_ERROR_CONFLICT, j);
					response.error = string.Format(RUN_ERROR_CONFLICT, "Job " + jrId.JobId);
					return;
				}

				var forceNeed = false;
				var oversize = false;
				var message = "";
				if (r == null)
				{
					r = _runApp.NewRun(j);
				}
				if (!r.CanAccept(j, out forceNeed, out oversize, out message))
				{
					// ReturnError(jsonWriter, RUN_ERROR_CONFLICT, r);
					response.error = string.Format(RUN_ERROR_CONFLICT, "Run " + r.Id);
					if (!string.IsNullOrEmpty(message))
						response.error = message;
					return;
				}
				if (!hasConfirm)
				{
					if (oversize)
					{
						//ReturnConfirm(jsonWriter, RUN_WARN_OVERSIZE);
						response.confirm = RUN_WARN_OVERSIZE;
						return;
					}

					if (forceNeed && r.Jobs.Count > 0 && !r.ManuallyManage)
					{
						//ReturnConfirm(jsonWriter, RUN_WARN_INVALID);
						response.confirm = RUN_WARN_INVALID;
						return;
					}
				}

				if (currentrunid > 0)
				{
					var effectRun = j.Runs[0];
					if (effectRuns.Contains(effectRun))
					{
						effectRun.RemoveJob(j, currentUser);
					}
					else
					{
						effectRun.RemoveJob(j, currentUser);
						effectRuns.Add(effectRun);
					}
				}

				r.AddJob(j, currentUser);
			}
			foreach (var effr in effectRuns)
			{
				_runApp.Save(effr);
			}
			_runApp.Save(r);
		}

		private void RemoveRun(dynamic response, int runId, List<int> jobIds)
		{
			var currentUser = GetCurrentUser();
			IRun r = null;
			if (runId != 0)
			{
				r = _runApp.GetRun((int)runId);
			}

			//else if (jobData != null) {
			//    r = _runApp.GetRun((int)runId);
			//}
			else
			{
				return;
			}

			if (r == null || (r.Operator != null && r.Operator != currentUser) || r.Status != RunStatusOptions.Filling)
			{
				// ReturnError(jsonWriter, RUN_ERROR_CONFLICT, r);
				response.error = string.Format(RUN_ERROR_CONFLICT, "run " + r);
				//context.Application["lastrunmodify"] = DateTime.Now;
				LepGlobal.Instance.LastRunModify = DateTime.Now;

				return;
			}

			if (runId != 0 && (jobIds == null || jobIds.Count == 0))
			{
				_runApp.Delete(r);
			}
			else
			{
				foreach (var jid in jobIds)
				{
					var j = _jobApp.GetJob(jid);
					if (!r.Jobs.Contains(j))
					{
						// ReturnError(jsonWriter, RUN_ERROR_CONFLICT, j);
						response.error = string.Format(RUN_ERROR_CONFLICT, "Job " + jid);
						return;
					}
					r.RemoveJob(j, currentUser);
				}
				_runApp.Save(r);
			}
		}

		private void AddJobsToANewRun(dynamic response, List<int> jobIds, bool hasConfirm)
		{
			IRun run = null;
			//  var username = "System";

			// jobIds --> jobs
			var jobs = jobIds.Select(jid => _jobApp.GetJob(jid)).ToList();

			foreach (var j in jobs)
			{
				if (j.Runs.Count > 0)
				{
					// ReturnError(jsonWriter, RUN_ERROR_CONFLICT, j);
					response.error = string.Format(RUN_ERROR_CONFLICT, "Job " + j.Id);
					return;
				}

				if (run == null)
				{
					run = _runApp.NewRun(j);
				}

				var forceNeed = false;
				var oversize = false;
				if (!run.CanAccept(j, out forceNeed, out oversize, out var message))
				{
					//ReturnError(jsonWriter, RUN_ERROR_CONFLICT, run);
					response.error = string.Format(RUN_ERROR_CONFLICT, "Run " + run.Id);

					if (!string.IsNullOrEmpty(message))
						response.error = message;

					return;
				}
				if (!hasConfirm)
				{
					if (oversize)
					{
						//  ReturnConfirm(jsonWriter, RUN_WARN_OVERSIZE);
						response.confirm = RUN_WARN_OVERSIZE;
						return;
					}

					if (forceNeed && run.Jobs.Count > 0 && !run.ManuallyManage)
					{
						//  ReturnConfirm(jsonWriter, RUN_WARN_INVALID);
						response.confirm = RUN_WARN_INVALID;
						return;
					}
				}
				run.AddJob(j, GetCurrentUser());
				run.PrintType = j.PrintType;
			}

			var printTypes = jobs.Select(j => j.PrintType).Distinct();
			if (printTypes.Count() == 1)
			{
				run.PrintType = printTypes.First();
			}
			else if (printTypes.Count() > 1)
			{
				run.PrintType = PrintType.B;
			}







			_runApp.Save(run);
		}

		[HttpGet("{runId:int}/Print/JobSheet")]
		public IActionResult PrintJobSheet(int runId)
		{
			var run = _runApp.GetRun(runId);
			if (run == null) return NotFound();
			_jobApp.PrintJobRunSheet(run);
			return Ok();
		}

		[HttpGet("{runId:int}/Print/BCRef")]
		[AllowAnonymous]
		public IActionResult PrintBCRef([FromRoute] int runId, [FromQuery] Facility facility,
			[FromServices] LabelPrinterApplication LabelPrinterApplication,
			[FromServices] PrintEngine PrintEngine
			)
		{
			var run = _runApp.GetRun(runId);
			if (run == null) return NotFound();

			var filename = Path.Combine(LepGlobal.Instance.DataDirectory.FullName,
				string.Format("{0}_{1}_{2}.xps", DateTime.Now.Ticks, "BC_REF", run.Id));

			var printerAndTray = _configApp.GetValue(facility == Facility.FG ? Configuration.FG_BCReferencePrinterName : Configuration.PM_BCReferencePrinterName);

			using (var printDocument = new BCReferenceLayout(run, _configApp, printerAndTray, filename))
			{
				printDocument.SetupPrintProperties();
				printDocument.Print();
			}

			return Ok();
		}



		[HttpGet("allocate")]
		public IActionResult allocate([FromServices] lep.run.impl.RunEngine runEngine)
		{
			runEngine.CronTask();
			return Ok();
		}

		[HttpPost("SendToImp/run/{id:int}")]
		public IActionResult SendToImpRun(int id)
		{
			try
			{
				// Make sure we have the current user
				if (_currentUser == null)
				{
					_currentUser = GetCurrentUser();
				}

				// Get the username from _currentUser
				string username = _currentUser?.Username ?? "unknown";

				// Get the full run object
				var run = _runApp.GetRun(id);
				if (run == null)
				{
					return NotFound($"Run with ID {id} not found");
				}

				// Map the run to a DTO for serialization
				var runDto = _mapper.Map<RunViewDto>(run);
				runDto.HealthCss = _jobBoardDTOHelper.GetRunHealthCSS(run.Id);
				runDto.Visibility = GetFlagsRealtedToRun(run);

				foreach (var j in runDto.Jobs)
				{
					j.HealthCss = _jobBoardDTOHelper.GetJobHealthCSS(j.Id);
				}

				// Create a dynamic object with both the basic info and the full run data
				var jsonObject = new
				{
					type = "run",
					id = id,
					user = username,
					run = runDto
				};

				// Use the MVC JSON serializer settings which are already configured to handle circular references
				string jsonContent = JsonConvert.SerializeObject(jsonObject, new JsonSerializerSettings
				{
					ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
					Formatting = Formatting.Indented
				});

				// Create a unique filename
				string fileName = $"imp_run_{id}_{DateTime.Now.ToString("yyyyMMdd_HHmmss")}.json";
				string filePath = Path.Combine(_impHotfolderPath, fileName);

				// Write the JSON content to the file
				System.IO.File.WriteAllText(filePath, jsonContent);

				return Ok(new { success = true, message = $"File created at {filePath}" });
			}
			catch (Exception ex)
			{
				Log.Error(ex, "Error in SendToImpRun");
				return StatusCode(500, new { success = false, message = ex.Message });
			}
		}

		[HttpPost("SendToImp/job/{id:int}")]
		public IActionResult SendToImpJob(int id)
		{
			try
			{
				// Make sure we have the current user
				if (_currentUser == null)
				{
					_currentUser = GetCurrentUser();
				}

				// Get the username from _currentUser
				string username = _currentUser?.Username ?? "unknown";

				// Get the full job object
				var job = _jobApp.GetJob(id);
				if (job == null)
				{
					return NotFound($"Job with ID {id} not found");
				}

				// Map the job to a DTO for serialization
				var jobDto = _mapper.Map<JobPressDetailsViewDto>(job);

				// Create a dynamic object with both the basic info and the full job data
				var jsonObject = new
				{
					type = "job",
					id = id,
					user = username,
					job = jobDto
				};

				// Use the MVC JSON serializer settings which are already configured to handle circular references
				string jsonContent = JsonConvert.SerializeObject(jsonObject, new JsonSerializerSettings
				{
					ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
					Formatting = Formatting.Indented
				});

				// Create a unique filename
				string fileName = $"imp_job_{id}_{DateTime.Now.ToString("yyyyMMdd_HHmmss")}.json";
				string filePath = Path.Combine(_impHotfolderPath, fileName);

				// Write the JSON content to the file
				System.IO.File.WriteAllText(filePath, jsonContent);

				return Ok(new { success = true, message = $"File created at {filePath}" });
			}
			catch (Exception ex)
			{
				Log.Error(ex, "Error in SendToImpJob");
				return StatusCode(500, new { success = false, message = ex.Message });
			}
		}


		[HttpPost("{runId:int}/Print/BCRefDownload")]
		[AllowAnonymous]
		public async Task<IActionResult> PrintBCRefDownload([FromRoute] int runId,
			[FromServices] LabelPrinterApplication LabelPrinterApplication,
			[FromServices] PrintEngine PrintEngine,
			[FromServices] IConfigurationApplication _configApp)
		{
			var run = _runApp.GetRun(runId);
			if (run == null) return NotFound();
			var folder = LepGlobal.Instance.DataDirectory.FullName;
			var filename = string.Format("{0}_{1}_{2}", DateTime.Now.Ticks, "BCRef", runId);
			var fullPath = Path.Combine(folder, filename);

			try
			{

				var printerAndTray = "Microsoft Print to PDF";
				using (var printDocument = new BCReferenceLayout(run, _configApp, printerAndTray, fullPath))
				{
					printDocument.DocumentName = filename;
					printDocument.SetupPrintProperties();
					printDocument.PrinterSettings.PrintToFile = true;
					printDocument.Print();
				}
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
			}

			int i = 0;
			while (!System.IO.File.Exists(fullPath))
			{
				await Task.Delay(2000);
				i++;

				if (i > 15)
					break;
			}
			await Task.Delay(4000);


			try
			{
				var executable = LepGlobal.Instance.GhostScriptPath;
				var dir = folder;

				var strCommandParameters = $@" -dBATCH -dSAFER -dNOPAUSE -sDEVICE=pdfwrite -dPDFSETTINGS=/prepress -dAutoRotatePages=/PageByPage -r600 -sOutputFile=""{fullPath}.pdf"" ""{fullPath}""";
				var p = new System.Diagnostics.Process();
				p.StartInfo = new ProcessStartInfo()
				{
					FileName = executable,
					WorkingDirectory = Path.GetDirectoryName(executable),
					Arguments = strCommandParameters,
					UseShellExecute = true,
					CreateNoWindow = true,
					RedirectStandardOutput = false,
				};
				p.Start();
				p.WaitForExit();
			}
			catch (Exception ex)
			{
				Log.Error("Error converting to pdf");
			}

			string fileName = fullPath + ".pdf";
			if (System.IO.File.Exists(fileName))
			{
				var fileStream = new FileStream(fileName, FileMode.Open, FileAccess.Read);
				return File(fileStream, "application/pdf", fileName);

			}
			return Ok();
		}
	}
}
