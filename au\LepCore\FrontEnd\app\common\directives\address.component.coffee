do (window, angular, toastr)->
    appCore = angular.module('app.core')

    appCore.directive 'addressDetails', () ->
        restrict    : 'EA'
        scope       : { vm : '=address', noteditable: '=?'}
        templateUrl : 'common/directives/address.component.html'
        controller  :  ['$scope', '$log', '$timeout', ($scope, $log, $timeout) ->

            $scope.noteditable  = $scope.noteditable || false

            $scope.dontPastePostCode = ($event)->
                $event.preventDefault()
                window.toastr.info('Please type in a Postcode  instead of Pasting.\nThis way you can nominate the correct suburb associated with the postcode')

            $scope.$watch 'vm.Postcode', (n,o) ->
                #if n is o then return
                #$log.debug(n,o)
                $scope.$broadcast('angucomplete-alt:changeInput', 'postcode', n)

            $scope.$watch 'Postcode', (n,o) ->
                if !n then return
                if !n && !n.originalObject then return
                $scope.vm.Postcode = n.originalObject?.PostCode
                $scope.vm.City = n.originalObject?.Suburb || ""
                $scope.vm.State = n.originalObject?.State || ""

            $scope.inputChanged = (n) ->
                if isNaN(parseInt(n))
                    window.toastr.error('Please enter postcode (numbers only)')
                    $scope.$broadcast('angucomplete-alt:clearInput')

            $timeout( ()->
                        x = angular.element('#postcode_value')
                        x.attr('type','number')
                        x.keyup (e) ->
                                this.value = this.value.replace(/[^0-9]/g, '')
                    , 10)
        ]
