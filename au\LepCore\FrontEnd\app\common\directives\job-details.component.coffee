### eslint-disable ###
### jshint ignore: start ###

appCore = angular.module('app.core')

appCore.directive 'lepJobDetails', () ->
    restrict: 'EA'
    scope:
        job: '='
        priceCalc: '='
        valid: '='
        dcats: '='
        vpp: '=' # view portal price
        vnp: '=' #view normal price
    templateUrl: 'common/directives/job-details.component.html'
    controller: 'JobDetailsDirectiveController'


appCore.controller 'JobDetailsDirectiveController', [
    '$scope', 'JobService', '$location', 'enums', 'Utils', 'ngDialog', '$timeout', '$log', '$state','$rootScope',
    ($scope, JobService, $location, enums, utils, ngDialog, $timeout, $log, $state, $rootScope ) ->
        $scope.showErrors = true
        $scope.showPrice = true

        $scope.vis = {}

        bypassWatches = false

        $scope.removeOtherProducts = $scope.priceCalc || $rootScope.globals.IsCustomer ||$rootScope.globals.IsSubCustomer

        $scope.isWhiteLabel =  $scope.job.IsWhiteLabel
        if window.IsBarneys
            $scope.IsBarneys = true
            $scope.showQuantitySlider = true
            $scope.showQuantityLabel = false

        if $state.current.name.indexOf('remote') > -1
            $scope.showPrice = false

        loading = true # used to prevent the watch from firing on load
        job = $scope.job

        t = enums.KeyVal.JobTypeOptions
        ro = enums.KeyVal.RoundOption
        rdo = enums.KeyVal.RoundDetailOption
        rot = enums.KeyVal.RotationOption

        $scope.jobQuantity = job.Quantity

        $scope.setjobQty = (x) ->
            if x == 0
                $scope.job.Price = ''
                $scope.job.PriceWL = ''
            $scope.jobQuantity = x
            $scope.$broadcast('rzSliderForceRender')

        $scope._ = "JobDetailsDirectiveController"
        #$scope.enums = enums

        numbersonly = (e, decimal) ->
            key = undefined
            keychar = undefined
            if window.event
                key = window.event.keyCode
            else if e
                key = e.which
            else
                return true
            keychar = String.fromCharCode(key)
            if key == null or key == 0 or key == 8 or key == 9 or key == 13 or key == 27
                true
            else if '0123456789,'.indexOf(keychar) > -1
                true
            else if decimal and keychar == '.'
                true
            else
                false

        $scope.filterOnlyDigits = (e) ->
            if !numbersonly(e, false)
                e.preventDefault()

        $scope.getPrice2 = () ->

        lookups = {}
        result = {}

        $scope.slider =
            options:
                id: 'qtySlider'
                #floor: 0
                #ceil: 10000
                stepsArray: [job.Quantity]
                interval: 10
                #readOnly :
                disabled: !(job.Visibility?.optionPnlEnabled || false)
                hideLimitLabels: false
                showTicks: false
                showTicksValues: false
                showSelectionBar: false
                translate: (q, id, l) ->
                    if !q then q = 0
                    r = "#{q}"
                    if l != 'model' then return r
                    p = parseFloat(job.Price)
                    if p
                        p = p.toFixed(2)
                        r = "#{q}" # ($#{p})
                    r
                onEnd: (sliderId, modelValue, highValue, pointerType) ->
                    $scope.job.Quantity = modelValue
                    return
                onChange: () ->
                    $scope.JobDetailsForm.$setDirty()

        $scope.init = () ->
            $scope.slider.options.stepsArray = []
            lookups = {}
            $scope.maxQty = 10000
            $scope.vis =
                magnet: false
                customer: !$scope.priceCalc
                jobname: !$scope.priceCalc
                perforate: true
                score: true
                fold: true
                round: true
                cello: true
                roundDetail: false
                customDieCut: false
                portrait: true
                landscape: true
            $scope.stockOptions = []
            $scope.stock = {}
            $scope.sizeOptions = {}
            $scope.roundListValues = []
            $scope.result = result
            $scope.lookups = lookups
            n = $scope.job?.Template?.Id || 0
            $scope.labeldata = JobService.getLabelsByJobTemplate(n, job)
            $scope.vis       = JobService.getVisibilityByTemplate(n, job)
            $scope.vis.boundEdgeTop = true
            $scope.vis.boundEdgeSide = true
            $scope.maxQty    = JobService.getMaximumQuantityFromTemplate(n)
            $scope.updateRoundDetails(n, job.FinishedSize?.PaperSize?.Id)
            JobService.getTemplates().then (d) ->
                $scope.templates = d
            JobService.getSizeOptions(n).then (d) ->
                $scope.sizeOptions = d
            # some job templates have limited choises in quantity
            $scope.qtylistValues = JobService.getFixedQuantityByTemplate()
            # set the available number of pages for magazine types
            $scope.numberOfPages = JobService.getNumberOfPagesByTemplate(job)
            $scope.cutOptions    = JobService.getCutOptionsByTemplate()
            $scope.foilColours   = JobService.getFoilColours()
            $scope.envelopes     = JobService.getEnvelopes()
            $scope.holeListValues= JobService.getHolesListByJobTemplate(n, job)

            if job.Template.Id in [7,8,14]
                JobService.getBrohureMailHouses(job.Template.Id).then (r)->
                    $scope.brohureMailHouses = r
            JobService.applyRules(job, $scope)


        #------------------------------------------------------------------------------------------
        # job template changed
        #------------------------------------------------------------------------------------------
        $scope.$watch 'job.Template.Id', (n, o) ->
            if bypassWatches then  return
            if n is o then return
            #$scope.jname = _.find($scope.templates, {Id: n})?.jname
            if !n
                job.Price = ''
                job.Quantity = 0
                $scope.jobQuantity = 0

            JobService.getSizeOptions(n).then (sizeOptions) ->
                $scope.sizeOptions = sizeOptions
                #$log.debug 'getting sizes', sizeOptions
                if sizeOptions.length == 1
                    job.FinishedSize.PaperSize = sizeOptions[0]

            $scope.vis = JobService.getVisibilityByTemplate(n, job)
            $scope.maxQty = JobService.getMaximumQuantityFromTemplate(n)
            $scope.labeldata = JobService.getLabelsByJobTemplate(n, job) # change labels
            $scope.updateRoundDetails(n, job.FinishedSize?.PaperSize?.Id) # update rounding details
            $scope.holeListValues= JobService.getHolesListByJobTemplate(n, job)

            if job.Template.Id in [7,8,14]
                JobService.getBrohureMailHouses(job.Template.Id ).then (r)->
                    $scope.brohureMailHouses = r
        # if Template is magazine then get and set stocks list for cover


        #------------------------------------------------------------------------------------------
        $scope.$watch 'job.EnvelopeType', (n, o) ->
            JobService.getSizeOptions(job?.Template?.Id || 0).then (d) ->
                if n is 'Plain Face/Self Seal'
                    d = _.reject(d, {Id: 75})
                if n is 'Window/Self Seal'
                    d = _.reject(d, {Id: 73})
                $scope.sizeOptions = d

        $scope.$watch 'slider', ->
            $scope.$broadcast('rzSliderForceRender')
        , true


        $scope.updateRoundDetails = (templateId, sizeId) ->
            n = templateId
            roundListValues = [ro.None]
            standardRoundValues = []
            diecutRoundValues = []
            #holeListValues = []
            $scope.roundAndRoundDetails = [
                [],                             # roundOption "None"       : 0
                [1, 2, 3, 4, 5, 6, 7, 8],       # roundOption "Standard"   : 1
                [9, 10, 11, 12, 13],            # roundOption "DieCut"     : 2
                [],                             # roundOption "Custom"     : 3
                [14, 15, 16, 17, 1, 2, 3, 4],   # roundOption "Round"      : 4
                [],                             # roundOption "RoundMagnet": 5
            ]

            if ([t.BusinessCard, t.BusinessCardNdd, t.BusinessCardSdd].indexOf(n) > -1)
                if [1,40].indexOf(sizeId) > -1
                    roundListValues.push(ro.DieCut)

            if ([t.FridgeMagnet].indexOf(n) > -1)
                roundListValues.push(ro.RoundMagnet)

            if ([t.BusinessCard, t.BusinessCardNdd, t.BusinessCardSdd, t.Postcard].indexOf(n) > -1)
                roundListValues.push(ro.Standard)
                if !$scope.isWhiteLabel
                    roundListValues.push(ro.Custom)
                #holeListValues = [0, 1, 2, 3, 4, 5] #to do use enums

            # if [2,20].indexOf(sizeId) > -1
            #     roundListValues = _.reject(roundListValues, (x)-> x is 2)

            $scope.roundListValues = roundListValues
            $scope.standardRoundValues = standardRoundValues
            $scope.diecutRoundValues = diecutRoundValues
            #$scope.holeListValues = holeListValues

            return


        # when RoundOption changes, change visibility of round detail and custom die cut
        $scope.$watch 'job.RoundOption', (n, o) ->
            if bypassWatches then  return
            #if !n then return
            if n == 0 then job.RoundDetailOption = 0
            $scope.vis.roundDetail = $scope.vis.round && ([ro.Standard, ro.DieCut, ro.Round].indexOf(n) > -1)
            $scope.vis.customDieCut = $scope.vis.round && ([ro.Custom].indexOf(n) > -1)

        # null out some fields when custom size selected
        $scope.$watch 'job.FinishedSize.PaperSize.Id', (n, o) ->
            if bypassWatches then  return
            #$log.debug("'job.FinishedSize.PaperSize.Id' " , n,o)
            if !n or n == 0 then return
            if n == 12 and n != o
                job.FinishedSize.Width = null
                job.FinishedSize.Height = null

        $scope.$watch 'job.FinishedSize.PaperSize', (n, o) ->
            if bypassWatches then  return
            $scope.changeRotationOrSize()
            $scope.labeldata = JobService.getLabelsByJobTemplate(job.Template.Id, job) # change labels
            $scope.updateRoundDetails(job.Template?.Id, job.FinishedSize?.PaperSize?.Id)
            # if !n then return
            # if job.FinishedSize.PaperSize.Name == 'Custom' then return
            # if job.Rotation == 0
            #   job.FinishedSize.Width = job.FinishedSize.PaperSize.Width
            #   job.FinishedSize.Height = job.FinishedSize.PaperSize.Height
            # if job.Rotation == 1
            #   job.FinishedSize.Width = job.FinishedSize.PaperSize.Height
            #   job.FinishedSize.Height = job.FinishedSize.PaperSize.Width
        , true


        #changeRotationOrSize
        $scope.changeRotationOrSize = () ->
            #$log.debug("changeRotationOrSize")
            #job = $scope.job
            r = job.Rotation
            psId = job.FinishedSize?.PaperSize?.Id
            if !psId or psId is 12  then return
            w = job.FinishedSize.PaperSize.Width
            h = job.FinishedSize.PaperSize.Height
            $log.debug(rot.Portrait  , rot.Landscape ,  r, w, h)
            job.FinishedSize.Width = w
            job.FinishedSize.Height = h
            if JobService.isBusinessCard(job)
                r = 1
            if r == rot.Portrait and w > h
                job.FinishedSize.Width = h
                job.FinishedSize.Height = w
            if r == rot.Landscape and h > w
                job.FinishedSize.Width = h
                job.FinishedSize.Height = w
            return

        # $scope.stockNotFoundInList = () ->
        #     if !job.Stock.Id
        #         return false
        #     !!! _.find($scope.stockOptions, {Id: job.Stock?.Id || 0})

        # $scope.stockForCoverNotFoundInList = () ->
        #     if !job.Stock.Id
        #         return false
        #     !!! _.find($scope.lookups.stocksForCover, {Id: job.StockForCover?.Id || 0})

        $scope.overrideStock = (field) ->
            JobService.getAllStocks().then (x) ->
                ngDialog.openConfirm({data: {stocks: x} , template: 'common/stocklist.html', closeByDocument: false, closeByEscape: false,  backdrop: 'static', disableAnimation :true}).then (y) ->
                    if !y then return
                    if field is 'job.Stock'
                        job.StockOverride = y
                    if field is 'job.StockForCover'
                        job.StockForCoverOverride = y


        $scope.overrideCello = (field) ->
            JobService.getAllCellosForOverride().then (x) ->
                ngDialog.openConfirm({data: {cellos: x} , template: 'common/cello-list.html', closeByDocument: false, closeByEscape: false,  backdrop: 'static', disableAnimation :true}).then (y) ->
                    if y.indexOf('/') > -1
                        cellos = y.split('/')
                        job.FrontCelloglazeOverride = parseInt(cellos[0]) || 0
                        job.BackCelloglazeOverride = parseInt(cellos[1]) || 0
                        return


        # when the size chanegs get the stocks
        $scope.$watchGroup ['job.Template.Id', 'job.Stock.Id', 'job.FinishedSize.PaperSize.Id',
                            'job.FinishedSize.Width', 'job.FinishedSize.Height'
                           ], (n, o) ->
            if bypassWatches then  return
            tId = job?.Template?.Id
            psId = job.FinishedSize?.PaperSize?.Id
            $scope.vis = JobService.getVisibilityByTemplate(tId, job)
            delete $scope.slider.options.stepsArray
            $scope.slider.options.showTicks = false
            JobService.getStockOptions(tId, psId).then (stockOptions) ->
                $scope.stock = null
                $scope.stockOptions = stockOptions

                # hack: if job is a poster and size bigger than 860 then just allow stock 60 for wide format
                if  job.Template?.Id is 10 and job.FinishedSize?.PaperSize?.Id is 12 and (job.FinishedSize.Width > 860 or job.FinishedSize.Height> 860)
                    $scope.stockOptions =  _.filter(stockOptions, {Id: 60})

                if stockOptions.length == 1
                    job.Stock = stockOptions[0]
            $scope.cutOptions    = JobService.getCutOptionsByTemplate(tId, job)

        #$log.debug 'getting stocks list', $scope.stockOptions
        #$scope.changeRotationOrSize()


        #Rotation
        $scope.$watch 'job.Rotation', () ->
            if bypassWatches then  return
            $scope.changeRotationOrSize()

        $scope.forcePrintType = (pt) ->
            bypassWatches = true
            job.PrintType = pt


        setPrintType = (available) ->
            #toastr.clear()
            #toastr.info('Both Offset and Digital printing is available for this quantity')
            $scope.printTypesAvaiable = available

            if job.Status > 0 or job.IsQuotePrice then return

            if $scope.$root.globals.IsStaff and  available == 'B' then return

            #if available == 'B' and (!job.PrintType or job.PrintType == 'B')  #and job.Quantity == 0
            if available == 'B' #and job.Quantity == 0
                job.PrintType = 'O'

            if  (available == 'O' or available == 'D') and (job.PrintType != available)
                job.PrintType = available
            if  (available == 'N')
                job.PrintType = available
            if  (available == 'W')
                job.PrintType = available


        $scope.changeCello = (selectedCello) ->
            if !selectedCello then  return #  selectedCello = '00'

            if selectedCello.length == 2
                job.FrontCelloglaze = parseInt(selectedCello[0]) || 0
                job.BackCelloglaze = parseInt(selectedCello[1]) || 0

            if selectedCello.length == 3
                job.FrontCelloglaze = parseInt(selectedCello.substring(0,2)) || 0
                job.BackCelloglaze = parseInt(selectedCello.substring(2)) || 0


        sortColors = (input) ->
            _.sortBy(input || [], (c) ->  [1, 2, 0].indexOf(c))

        $scope.applyCelloRules = () ->
            tid = job.Template?.Id
            bid = job.BindingOption?.Id
            return unless tid and bid
            celloOptionsTemp =  angular.copy($scope.lookups.celloOptions)
            #------------------------------------------------------------------------------------------------------------------
            # LORD-294
            # for magazines that are burst,purfect or PUR bound can it only show cello outside options
            if [9, 14].indexOf(tid) > -1  # if mag and mag separate Cover
                if [3, 4, 5].indexOf(bid) > -1  # if  burst , pur or perfect
                    celloOptionsTemp = _.reject(celloOptionsTemp, (c)-> c.Key == '11' or c.Key == '22')

            #------------------------------------------------------------------------------------------------------------------
            # LORD-954 : Digital Magazine Perfect Bound Must have Cello outside cover.
            # All digital magazines that require perfect binding must have a cello finish (gloss or matt) on the outside cover.
            # That means remove none
            if job.PrintType is 'D' and
                [8,14,22].indexOf(job.Template?.Id) > -1 and
                bid is 4 #Perfect Binding
                    celloOptionsTemp = _.reject celloOptionsTemp, (x)->x.Value is 'None'

            $scope.lookups.celloOptions = celloOptionsTemp
            return

        $scope.getStock = () ->
            tId = job.Template?.Id
            psId = job.FinishedSize?.PaperSize?.Id
            sId = job.Stock?.Id
            JobService.getStockOption(tId, psId, sId).then (d) ->
                r = d
                #$log.debug 'found stocks', r.length
                stock = _.find(r, PrintType: job.PrintType)
                if stock == undefined
                    if r.length == 0 then return
                    stock = r[0]

                minValue = _.min(stock.QuantityOptions)
                maxValue = _.max(stock.QuantityOptions)
                $scope.maxValue = maxValue
                $scope.minValue = minValue

                # set slider option
                stepsArray = if $scope.vis.qty
                    (stock.QuantityOptions || []).slice()
                else
                    ($scope.qtylistValues[job.Template.Id] || []).slice()

                #if stepsArray.indexOf(job.Quantity) == -1 and job.Quantity != 0  and  job.Quantity >= minValue and job.Quantity <= $scope.maxQty    then stepsArray.push(job.Quantity)
                #if stepsArray.indexOf($scope.maxQty ) == -1 then stepsArray.push($scope.maxQty )
                stepsArray.push(0)
                stepsArraySorted = stepsArray.sort((a, b)-> a - b)

                $scope.slider.options.stepsArray = stepsArraySorted
                $scope.jobQuantity = job.Quantity

                if  stepsArraySorted.length >= 2 and stepsArraySorted.length < 50
                    $scope.slider.options.showTicks = true
                else
                    $scope.slider.options.showTicks = false

                stepsArrayStr = stepsArraySorted.join(', ')

                #LORD-1068	Remove Black & White Back as a print option
                if job.FrontPrinting isnt 2
                    stock.FrontPrintOptions = _.reject(stock.FrontPrintOptions, (xx) -> xx is 2)
                if job.BackPrinting isnt  2
                    stock.BackPrintOptions = _.reject(stock.BackPrintOptions, (xx) -> xx is 2)

                # set fron options
                stock.FrontPrintOptions = sortColors(stock.FrontPrintOptions)
                stock.BackPrintOptions = sortColors(stock.BackPrintOptions)



                if [t.DuplicateNCRBooks, t.TriplicateNCRBooks, t.QuadruplicateNCRBooks].indexOf(job.Template?.Id) > -1
                    stock.FrontPrintOptions = [3,4]
                    stock.BackPrintOptions =  []


                lookups.stockOptions = r
                lookups.stock = stock

                #foldOptions =   _.map(stock.FoldOptions, (fo) ->  {Id: fo.Id, Name: fo.Name })
                #foldOptions.unshift({Id:0, Name: "Don't Fold"})
                #lookups.stock.FoldOptions = foldOptions

                # set default value where there arent many options
                if stock.CelloOptions2.length == 1
                    $scope.selectedCello = stock.CelloOptions2[0].Key
                    $scope.changeCello(stock.CelloOptions2[0].Key)

                $scope.lookups.celloOptions = stock.CelloOptions2
                $scope.applyCelloRules()

                if(stock.BackPrintOptions.length == 1)
                    job.BackPrinting = stock.BackPrintOptions[0]

                if(stock.FrontPrintOptions.length == 1)
                    job.FrontPrinting = stock.FrontPrintOptions[0]

                # if job doesnt have a qty
                #LORD-625: Remove default text from qty box
                #if !job.Quantity
                #   if $scope.qtylistValues[job.Template.Id]
                #       q = $scope.qtylistValues[job.Template.Id][0]
                #       #$log.debug("Qty list says min qty is #{q}")
                #       job.Quantity = q
                #   else if minValue
                #       q = minValue
                #       #$log.debug("QuantityOptions min qty is #{q}")
                #       job.Quantity = q

                # if foldOptions.length == 1
                # job.FoldedSize = { PaperSize : {Id: foldOptions[0].Id }}

                # magnet check
                #currentTemplateAllowsMagnet = [t.Brochure, t.DLSpecial, t.DL, t.Custom].indexOf(job.Template.Id) > -1
                $scope.vis.magnet = stock.Magnet
                if !$scope.vis.magnet then job.Magnet = false
                # magnet check end

                $scope.QuantityOptions = stock.QuantityOptions


        $scope.$watch 'job.Facility', (n, o) ->
            if bypassWatches then  return
            if n is null
                job.IsCustomFacility = false
            else if n != o
                job.IsCustomFacility = true


        #$scope.$watch 'job.Stock.Id', (n, o) ->
        #   if !n then return


        # when quantity changes get print types
        $scope.printTypesAvaiable = null

        $scope.$watch 'JobDetailsForm.quantity.$dirty', (n, o)->
            if bypassWatches then  return
            if !n then return
            #$log.debug('qty dirty')
            job.Price = ""
            job.QuoteNeedApprove = true


        $scope.$watch 'job.FoldedSize', (n, o)->
            if bypassWatches then  return
            if !n then return
            if n.PaperSize?.Name != o?.PaperSize?.Name && n.PaperSize?.Name is 'Custom'
                job.FoldedSize.Width = null
                job.FoldedSize.Height = null

            if n.PaperSize?.Name is 'Custom'
                #job.FoldedSize.Width  = 0
                #job.FoldedSize.Height =    0
                return
            job.FoldedSize.Width = n?.PaperSize?.Width || 0
            job.FoldedSize.Height = n?.PaperSize?.Height || 0
        , true

        $scope.$watchGroup ['job.Template.Id','job.Stock.Id',
                            'job.FinishedSize.PaperSize.Id','job.FoldedSize.PaperSize.Id','job.FinishedSize.Width','job.FinishedSize.Height',
                            'job.BrochureDistPackInfo.MailHouse',  'job.BrochureDistPackInfo.PackingInstruction'
                           ], (n,o) ->
            if bypassWatches then  return
            tId = job.Template?.Id
            psId = job.FinishedSize?.PaperSize?.Id
            sId = job.Stock?.Id
            qty = job.Quantity || 0
            if $scope.JobDetailsForm.$invalid
                return
            if !$scope.validateJob(job)
                return

            # if tId is 7 and (n[1] != o[1]   or  n[2] != o[2] or n[3] != o[3] )
            #     $timeout( ()->
            #         $rootScope.$broadcast('savejob-refresh')
            #     , 200)



        $scope.$watchGroup ['job.Template.Id','job.Stock.Id','job.FinishedSize.PaperSize.Id','job.PrintType','job.FinishedSize.Width','job.FinishedSize.Height', 'job.Pages'], (n,o) ->
            if bypassWatches then  return
            tId = job.Template?.Id
            psId = job.FinishedSize?.PaperSize?.Id
            sId = job.Stock?.Id
            qty = job.Quantity || 0

            $scope.getStock()
            if tId is t.WiroMagazines
                lookups.celloOptionsW = [
                    'Matt front only', 
                    'Matt front & back',
                    'Gloss front only', 
                    'Gloss front & back',
                ]
                lookups.wireColorOptions = ["Black", "White"];
                lookups.outerFrontOptions = ["None", "Clear PVC", "Matt PVC"]
                lookups.outerBackOptions = ["None", "Clear PVC", "Black Leather grain", "Blue Leather grain"]
                job.BindingOption = {Id: 76}
                JobService.getStocksForCoverOver250().then (list) ->
                    # remove stock 101 from list
                    list = _.reject(list, {Id: 101})
                    lookups.stocksForCover = list
            else if utils.isTemplateMagazine(tId)
                JobService.getStocksForCover(sId).then (list) ->
                    lookups.stocksForCover = list
            if utils.isTemplateNCR(tId)
                lookups.NCR = {}
                lookups.NCR.stocksForCover = [
                        'White System Board 300 GSM',
                        'Buff System Board 300 GSM',
                        'Blue Crocodile Board 590 GSM',
                        'Green Crocodile Board 590 GSM',
                        'Red Crocodile Board 590 GSM',
                        'Grey Crocodile Board 590 GSM'
                        #'Blue Crocodile Board 300 GSM',
                        #'Green Crocodile Board 300 GSM',
                        #'Red Crocodile Board 300 GSM',
                        #'Grey Crocodile Board 300 GSM',
                        ]
                lookups.NCR.bindingTapes = ['Black', 'Blue', 'Red', 'Green']
                lookups.NCR.paperColors =  [
                        ['White']
                        ['Yellow', 'Pink', 'Blue', 'Green']
                        ['Yellow', 'Pink', 'Blue', 'Green']
                        ['Yellow', 'Pink', 'Blue', 'Green']
                ]
                numberOfSheets = tId - 49
                if !job.NCRInfo
                    job.NCRInfo = { "Sheets": { "1": { "PaperColor": "White" } } }


            if n[0] is 41 and n[2] is 12 and n[2] != o[2]
                window.toastr.info "Please attach a Cut Contour file. LEP may reject or suggest changes to any custom design sticker that includes cut contours lines that are less than 5mm."

            #if  !tId or  !psId or !sId then return
            JobService.getPrintTypes(tId, psId, sId, qty, job).then (d) ->
                setPrintType(d)

            $scope.sizeMsg = JobService.getSizeMessageFromTemplateAndSize(tId, psId)
            $scope.numberOfPages = JobService.getNumberOfPagesByTemplate(job)
            $scope.vis       = JobService.getVisibilityByTemplate(tId, job)
            $scope.cutOptions    = JobService.getCutOptionsByTemplate(tId, job)
            JobService.applyRules(job, $scope)

        #Binding options 'job.PrintType',
        # $scope.$watchGroup ['job.Template.Id', 'job.FinishedSize.PaperSize.Id', 'job.Stock.Id', 'job.PrintType', 'job.Pages'], (n, o) ->
        #     if bypassWatches then  return
        #     JobService.getBindingOptions(n[0], n[1], n[2], n[3], n[4]).then (list) ->
        #         lookups.bindingOptions = list
        #         if list.length == 1 then job.BindingOption = list[0]
        #     return






        # the LEP data structure or design didnt have  binding as an influencer for cello before
        # so doing it like the following in a $watch
        # for magazines if burst binding selected remove some cello options
        $scope.$watchGroup ['job.Template.Id', 'job.BindingOption.Id'], (n, o) ->
            if bypassWatches then  return
            $scope.applyCelloRules()

        #qtyChange
        $scope.qtyChange = (n) ->
            #$log.debug('$scope.qtyChange() == ' , n)
            #if !n then return
            $scope.setjobQty(n)
            #$scope.jobQuantity =  parseInt(n,10)

            #$scope.$root.$broadcast('rzSliderForceRender');


            tId = job.Template.Id
            psId = job.FinishedSize?.PaperSize?.Id
            sId = job.Stock?.Id
            #if  !tId or  !psId or !sId then return
            JobService.getPrintTypes(tId, psId, sId, n, job).then (d) ->
                setPrintType(d)
                $scope.priceResult = null
                return

            #JobService.getMyob(job).then (myob) ->
            #   $log.log("myob :" , myob)
            #   job.MYOB = myob

            $scope.getPrice2()
            return


        #
        $scope.$watch 'job.Quantity', (n, o) ->
            if bypassWatches then  return
            $scope.qtyChange(n)


        #
        propertiesToUpdatePriceOn = ['job.Template.Id', 'job.FinishedSize.PaperSize.Id','job.PrintType',
            'job.Rotation',
            'job.FoldedSize.PaperSize.Id', 'job.Stock.Id', 'job.BindingOption.Id', 'job.FrontPrinting',
            'job.BackPrinting',
            'job.FrontCelloglaze', 'job.BackCelloglaze', 'selectedCello', 'job.HoleDrilling', 'job.RoundOption', 'job.RoundDetailOption',
            'job.StockForCover.Id', 'job.Magnet', 'job.NumberOfMagnets', 'job.Perforating', 'job.Scoring', 'job.Pages', 'job.SendSamples',
            'job.DeliveryAddress.Postcode',
            'job.DeliveryAddress.City', 'job.DeliveryAddress.Country', 'job.SpecialInstructions', 'job.DieCutType',
            'job.FinishedSize.Width', 'job.FinishedSize.Height', 'job.Quantity', 'job.DateModified2', 'job.BindingOption.Id',
            'job.BrochureDistPackInfo.MailHouse.Id','job.BrochureDistPackInfo.MailHouse.Name','job.BrochureDistPackInfo.PackingInstruction', 'job.NumberOfHoles',
            'job.NCRInfo.Sheets["1"].PrintedOnBack',
            'job.NCRInfo.Sheets["2"].PrintedOnBack',
            'job.NCRInfo.Sheets["3"].PrintedOnBack',
            'job.NCRInfo.Sheets["4"].PrintedOnBack',
            'job.WiroInfo.OuterFront',
            'job.WiroInfo.OuterBack',
            'job.WiroInfo.InnerFrontCello',
            'job.WiroInfo.InnerBackCello',
            'job.WiroInfo.InnerFrontStockForCover.Id',
            'job.WiroInfo.InnerBackStockForCover.Id',
        ]

        $scope.$watchGroup propertiesToUpdatePriceOn, (n, o) ->
                if bypassWatches then  return
                JobService.applyRules(job, $scope)
                $scope.getPrice2()
                job.crpDate = new Date()
                # emit event some price related fields have changed

        , true

        specialChars =  /[^\w\s\(\)]/gi
        $scope.$watchGroup [
                'job.Category', 'job.Template.Id',
                'job.FinishedSize.PaperSize.Name',
                'job.FoldedSize.PaperSize.Name',
                'job.RoundOption',
                'job.DieCutType',
                'job.Rotation',
                'job.FrontCelloglaze'
            ], ( [category,templateId, trim, fold,roundOption, dieCutType, rotation, frontCello]) ->
            if !category then return job.artspec = ''

            if fold
                fold = fold.replace(specialChars, " ")
            #cxd category, templateId, trim, fold ,roundOption, frontCello

            artspec = ''

            if category.includes('Brochure')
                artspec = "./Brochures/"
                if trim then artspec += "#{trim}"
                if fold then artspec += " - #{fold}"
                artspec  += ".pdf"

            else if category.includes('Business card')
                artspec = "./Business Cards/"
                ro2 = null
                if roundOption > 0
                    ro2 = enums.ValueDesc.RoundOption[roundOption]
                #if roundOption is 2 then roundOption =
                if trim then artspec += "#{trim}"
                if fold then artspec += " - #{fold}"
                if ro2 then artspec += " - #{ro2}"
                artspec  += ".pdf"

            else if category.includes('Presentation Folders')
                artspec = "./Presentation Folders/"
                ro2 = null
                if dieCutType > 0
                    ro2 = enums.ValueDesc.CutOptions[dieCutType]
                #if roundOption is 2 then roundOption =
                if trim then artspec += "#{trim}"
                if ro2 then artspec += " - #{ro2}"
                artspec  += ".pdf"

            else if category.includes('Calendar')
                artspec = "./Calendar/"
                ro2 = null
                if templateId > 0
                    ro2 = enums.ValueDesc.JobTypeOptions[templateId]
                if ro2 then artspec += "#{ro2}"
                if ro2 is 'DL Calendars'
                    ro3 = enums.ValueDesc.RotationOption[rotation]
                    if ro3 then  artspec += " - #{ro3}"
                artspec  += ".pdf"

            else if category.includes('Envelopes')
                artspec = "./Envelopes/"
                artspec  += "Envelopes.pdf"

            else if category.includes('Banners/Pull Ups')
                artspec = "./Wide Format/"
                artspec  += "Banners Pull Ups.pdf"

            else if category.includes('Adhesive Signs, Rigid Signs & Stickers')
                artspec = "./Wide Format/"
                artspec  += "Adhesive Signs, Rigid Signs & Stickers.pdf"

            else if category.includes('Stationery')
                # files moved in Brochure folder
                artspec = './Stationery/'
                if trim then artspec += "#{trim}"
                artspec  += ".pdf"

            else if category.includes('Notepads/Deskpads')
                # files moved in Brochure folder
                artspec = './Notepads Deskpads/'
                if trim then artspec += "#{trim}"
                artspec  += ".pdf"

            else if category.includes('Posters')
                # files moved in Brochure folder
                artspec = './Posters/'
                if category then artspec += category
                if trim then artspec += " - #{trim}"
                artspec  += ".pdf"

            else if category.includes('NCR Books')
                artspec = './NCR Books/'
                if category then artspec += 'NCR'
                if trim then artspec += " - #{trim}"
                artspec  += ".pdf"

            i = window.artspecs.indexOf(artspec)
            result = []
            if i > -1 then result.push(window.artspecs[i])

            if category.includes('Business card') and frontCello is 5
                result.push("./Foil & Spot UV/Business Cards - 420 GSM Deluxe Artboard - Foil front, Matt Cello both sides.pdf")

            if category.includes('Business card') and frontCello is 6
                result.push("./Foil & Spot UV/Business Cards - 420 GSM Deluxe Artboard - Spot UV front, Matt Cello both sides.pdf")

            if category.includes('Presentation Folders') and frontCello is 6
                result.push('./Foil & Spot UV/Presentation Folders - 420 GSM Deluxe Artboard - Spot UV front, Matt Cello both sides.pdf')

            job.artspecs = result
        , true


        $scope.$watch 'job.PrintType', (n, o) ->
            if bypassWatches then  return
            JobService.applyRules(job, $scope)

        $scope.$watch 'job.SendSamples', (n, o) ->
            if bypassWatches then  return
            if n is o then return

            if job.IsQuotePrice == true
                if n
                    job.Price = parseFloat(job.Price) + 5.0
                else
                    job.Price = parseFloat(job.Price) - 5.0


        # when PrintType changes get stock and set fold options
        #$scope.$watch 'job.PrintType', (n, o) ->
        #    if !n then return
        #    $scope.getStock()


        # when PrintType changes get stock and set fold options
        $scope.$watch 'job.HoleDrilling', (n, o) ->
            if bypassWatches then  return
            if n == 0 then job.NumberOfHoles = null


        $scope.$watch 'job.FrontCelloglaze + job.BackCelloglaze', (n, o) ->
            if bypassWatches then  return
            if !n then return
            #$log.debug(n,o)
            $scope.selectedCello = job.FrontCelloglaze.toString() + job.BackCelloglaze.toString()

        $scope.selectedCello = (if job.FrontCelloglaze is null then '' else job.FrontCelloglaze).toString() +
            (if job.BackCelloglaze is null then '' else job.BackCelloglaze).toString()

        #$scope.$watch 'job.Template + job.Stock + job.FinishedSize + job.Quantity + job.FoldedSize + job.Pages', () ->
        #   #JobService.applyRules(job, $scope.vis)
        #, true


        $scope.$watchGroup ['job.Template.Id', 'job.Stock.Id', 'job.BindingOption.Id', 'job.StockForCover.Id', 'job.Pages', 'job.PrintType', 'job.StockOverride.Id', ], (n, o) ->
            if bypassWatches then  return
            job.spineWidth = 0
            templateId = job.Template.Id

            if templateId in [t.A4CalendarSelfCover, t.A4CalendarSeparateCover] 
                list = [{Id:1,Name:"Saddle Stitch"}]
                lookups.bindingOptions = list
                return

            if templateId is t.WiroMagazines
                list = [{Id:76,Name:"Wiro Binding"}]
                lookups.bindingOptions = list
                return

            return unless templateId in [t.Magazine, t.MagazineNDD, t.MagazineSeparate]
            #if [3, 4, 5].indexOf(job.BindingOption?.Id || 0) == -1 then return # if not pur or burst binding then return

            stockType = 0
            pages = 0

            jStock = if job.StockOverride?.Id  then  job.StockOverride else job.Stock

            # get stock type
            name = jStock?.Name || ''
            stockType = 0
            if name.search('Bond') > -1 or name.search('Uncoated') > -1
                stockType = 1.1
            else if name.search('Gloss') > -1
                stockType = 0.8
            else if name.search('Matt') > -1
                stockType = 0.9

            if stockType is 0 then return

            gsm = jStock?.GSM || 0
            if gsm is 0 then return


            pages = job.Pages || 0
            if pages is 0 then return

            result = ((gsm * stockType / 1000) * (pages / 2) * 1.1) + 0.5
            job.spineWidth = (Math.round(result * 10) / 10).toFixed(1)

            list = []

            # LORD-1201,  LORD-1094
            if templateId is t.MagazineSeparate
                if job.spineWidth < 3
                    list = [{Id:1,Name:"Saddle Stitch"}]

                if job.spineWidth >= 3 and job.spineWidth <= 6
                    if job.Pages <= 96
                        list = [{Id:1,Name:"Saddle Stitch"}, {Id:3,Name:"Burst Binding"} , {Id:4,Name:"Perfect Binding"} ]
                    if job.Pages > 96
                        list = [{Id:3,Name:"Burst Binding"} , {Id:4,Name:"Perfect Binding"} ]

                if job.spineWidth > 6 or job.Pages > 96
                    list =  [{Id:3,Name:"Burst Binding"} , {Id:4,Name:"Perfect Binding"} ]

                # restrict Perfect Binding and Burst Binding Cover stocks to a minimum of 170gsm?
                finalCoverStock = job.StockForCoverOverride ||  job.StockForCover
                if finalCoverStock?.GSM < 170
                    list = _.reject(list, {Id:4,Name:"Perfect Binding"})
                    list = _.reject(list, {Id:3,Name:"Burst Binding"})

            if templateId is t.Magazine or templateId is t.MagazineNDD
                list = [{Id:1,Name:"Saddle Stitch"}]

            # if Digital <- remove Burst
            if job.PrintType is 'D' then _.remove(list, {Id: 3})
            # if Offset <- remove Perfect
            if job.PrintType is 'O' then _.remove(list, {Id: 4})

            #if job.StockOverride?.Id
            #    list = [{Id:1,Name:"Saddle Stitch"}, {Id:3,Name:"Burst Binding"} , {Id:4,Name:"Perfect Binding"} ]
           
            #  if  burst  found then   add Perfect in there make sure no dupes
            if _(list).find({Id:3})
                if !_(list).find({Id:4})
                    list.push({Id:4,Name:"Perfect Binding"})
                    
            lookups.bindingOptions = list


            #if list.length == 1 then job.BindingOption = list[0]
            return


        $scope.brochurePackingInstructionIdToBundlesOf =
            0: null
            1: [100, 200, 500]
            2: [200, 500]
            3: null
            4: null
        $scope.currentBundlesOf = null

        # when BrochureDistPackInfo changes update price, if there is a mail house then null our packing instructions
        $scope.$watch 'job.BrochureDistPackInfo', (n, o) ->
            if bypassWatches then  return
            if !n  then return

            if n.MailHouse
                n.PackingInstruction = 0
                n.BundlesOf = null
            else
                $scope.currentBundlesOf = $scope.brochurePackingInstructionIdToBundlesOf[n.PackingInstruction]
            $scope.getPrice2()
        , true

        # # crp means customer requested price
        # # crp is visible when the job is a quote
        # # crp is required only when the job price is 0
        # crp = {visible: false, required : false, valid: false  , onlyCrpReqd: false }
        # crp.label = if $scope.$root.globals.IsStaff then "Customer named price" else "Price I want to pay"
        # if !job.Id
        #     crp.btnIcon = 'glyphicon-plus'
        #     crp.btnText = 'Continue'
        # else
        #     crp.btnIcon = 'glyphicon-floppy-save'
        #     crp.btnText = 'Continue'

        # $scope.crp = crp
        # $scope.$watchGroup ['job.Price','job.CustomerRequestedPrice'].concat( propertiesToUpdatePriceOn), (n,o) ->
        #     console.clear()
        #     cd +new Date
        #     f = $scope.JobDetailsForm
        #     e = f.$error
        #     $scope.e = e
        #     cd (f.crp)
        #     if bypassWatches then  return

        #     if !($scope.$root.globals.IsCustomer)
        #         return

        #     # # if job is while label crp not visible
        #     if job.IsWhiteLabel
        #         crp.visible = false
        #         return
        #     if job.Price > 0
        #         crp.visible = false
        #         return
        #     # crp is visible when the job is a quote

        #     crp.visible = (!f.crp && f.$valid && !job.Price) || (f.crp && f.$invalid )
        #     crp.onlyCrpReqd = crp.visible
        # , true


        # validation functions
        $scope.gt30 = (v, wh) ->
            if !v then return false

            if job.FinishedSize?.PaperSize?.Name != 'Custom'
                return true

            # for custom
            if job.Template.Id == 12
                return true

            # for wide format ignore size validation for now
            if job.Template.Id > 31
                return true

            v = parseInt(v, 10)
            sizeValMsg = ""
            if utils.isTemplateMagazine(job.Template.Id)
                if ['Custom'].indexOf(job.FinishedSize?.PaperSize?.Name) > -1

                    w = job.FinishedSize?.Width
                    h = job.FinishedSize?.Height

                    if job.Rotation == 0 # Portrait
                        #if job.PrintType is 'O'  or !job.PrintType
                        if wh is 'height' and not (v >= 148 and v <= 420)             # if D 148 -> 130, 420 -> 297
                            sizeValMsg = ("valid range 148-420mm")
                            window.toastr.error('', sizeValMsg, {maxOpened: 1})
                            return false

                        if wh is 'width' and not (v >= 99 and v <= 300)    #99 ->99  300->210
                            sizeValMsg = ("valid range 99-300mm")
                            window.toastr.error('', sizeValMsg, {maxOpened: 1})
                            return false

                    if job.Rotation == 1 # Landscape
                        #if job.PrintType is 'O'  or !job.PrintType
                        if wh is 'width' and not (v >= 148 and v <= 420)             # if D 148 -> 130, 420 -> 297
                            sizeValMsg = ("valid range 148-420mm")
                            window.toastr.error('', sizeValMsg, {maxOpened: 1})
                            return false

                        if wh is 'height' and not (v >= 99 and v <= 300)    #99 ->99  300->210
                            sizeValMsg = ("valid range 99-300mm")
                            window.toastr.error('', sizeValMsg, {maxOpened: 1})
                            return false


                    #else if job.PrintType is 'D'
                    #   if wh is 'width' and not (v>= 130 and v <=297)           # if D 148 -> 130, 420 -> 297
                    #       sizeValMsg = ("valid range 148-420mm")
                    #       window.toastr.error('', sizeValMsg, {maxOpened: 1})
                    #       return false

                    #   if wh is 'height' and not (v>= 99 and v<=210)   #99 ->99  300->210
                    #       sizeValMsg = ("valid range 99-300mm")
                    #       window.toastr.error('', sizeValMsg, {maxOpened: 1})
                    #       return false


                    window.toastr.clear()
                    window.toastr.success("")
                    return true

            else if job.Template.Id is 10 # poster
                valid = (v >= 30)
                if not valid
                    sizeValMsg = 'range 30mm upwards'
                    #window.toastr.clear()
                    window.toastr.info( '',  sizeValMsg, {maxOpened: 1})
                    return false

            else # not magazine
                valid = (v >= 30 and v <= 860)
                if not valid
                    sizeValMsg = 'range 30mm to 860mm'
                    #window.toastr.clear()
                    window.toastr.info( '',  sizeValMsg, {maxOpened: 1})
                    return false

            #window.toastr.clear()
            #window.toastr.success("")
            return true


        $scope.gt30JobFolded = (v) ->
            if job.FoldedSize?.PaperSize?.Name == 'Custom'
                #if v=="" then return true
                #if !v then return true
                v = parseInt(v, 10)
                if v >= 30 and v <= 860 then return true
                return false
            return true

        $scope.validCelloFn = (selectedCello) ->
            result = true
            #$log.debug("validationFnCello", $scope.vis.cello, $scope.lookups?.stock?.CelloOptions2?.length, selectedCello)
            if $scope.vis.cello && $scope.lookups.stock?.CelloOptions2.length > 0 # if cello required
                #$log.debug('cello reqd',selectedCello )
                if !selectedCello
                    result = false
            #$log.debug('selected cello ', selectedCello, result)
            result

        $scope.validateJob = (job) ->
            if !job.Template?.Id
                return false
            if !job.FinishedSize?.PaperSize?.Id
                return false
            if job.FinishedSize?.PaperSize?.Id is 12
                if !job.FinishedSize.Width or !job.FinishedSize.Height
                    return false
            if job.Template.Id is t.MagazineSeparate
                if job.StockForCover is null
                    return false
            if !job.Stock?.Id
                return false
            if job.FrontPrinting == null
                return false
            if job.BackPrinting == null
                return false
            if job.PrintType == null
                return false
            if job.Quantity == 0 or job.Quantity == '' or job.Quantity == null
                return false
            if $scope.vis.cello && (!$scope.selectedCello)
                return false
            if $scope.vis.binding && !job.BindingOption?.Id
                return false
            return true


        $scope.getPrice2 = _.debounce((fromButton) ->
            if !$scope.priceCalc
                if $scope.$root.globals.IsStaff
                    if !fromButton
                        return

            if $scope.$root.globals.IsCustomer
                if !job.Visibility.priceButton then return
                if job.Status != 0 || job.OrderStatus != 0 then return
                if job.IsQuotePrice then return

            if !job.Quantity or job.Quantity == 0 or job.Quantity == '0'
                return

            if !$scope.validateJob(job)
                $scope.$apply  () ->
                    job.Price = 0

                return

            #job = angular.copy($scope.job)
            priceRequest =
                Id: job.Id
                Template: {Id: job.Template.Id}
                FinishedSize: {
                    PaperSize: {Id: job.FinishedSize.PaperSize.Id},
                    Width: job.FinishedSize?.Width || 0,
                    Height: job.FinishedSize?.Height || 0
                }
                FoldedSize: {
                    PaperSize: {Id: job.FoldedSize?.PaperSize?.Id || 0},
                    Width: job.FoldedSize?.Width || 0,
                    Height: job.FoldedSize?.Height || 0
                }
                Stock: {Id: job.Stock.Id}
                BindingOption: {Id: job.BindingOption?.Id || 0}
                Quantity: job.Quantity
                PrintType: job.PrintType
                FrontPrinting: job.FrontPrinting || 0
                BackPrinting: job.BackPrinting || 0
                FrontCelloglaze: job.FrontCelloglaze
                BackCelloglaze: job.BackCelloglaze
                HoleDrilling: job.HoleDrilling || 0
                NumberOfHoles: job.NumberOfHoles || null
                RoundOption: job.RoundOption || 0
                RoundDetailOption: job.RoundDetailOption || 0
                StockForCover: {Id: job.StockForCover?.Id || 0}
                Magnet: job.Magnet || false
                NumberOfMagnets: job.NumberOfMagnets || 0
                Perforating: job.Perforating || false
                Scoring: job.Scoring || false
                Pages: job.Pages || 0
                SendSamples: job.SendSamples && $scope.vis.sendSamples || false
                DeliveryAddress: job.DeliveryAddress
                SpecialInstructions: job.SpecialInstructions
                DieCutType: job.DieCutType || 0
                OrderId: job.OrderId
                IsWhiteLabel: job.IsWhiteLabel
                Category: job.Category
                OrderCustomerId: job.OrderCustomerId
                OrderWLCustomerId: job.OrderWLCustomerId
                BrochureDistPackInfo: job.BrochureDistPackInfo
                PackingInstruction: job.PackingInstruction
                NCRInfo: job.NCRInfo
                WiroInfo: job.WiroInfo

            if $scope.$root.globals.IsStaff && fromButton is true
                priceRequest.SpecialInstructions = ''

            $scope.$emit('courierRates', [])

            job.Visibility.saveButton = false
            JobService.getPrice2(priceRequest).then((r) ->
                job.Visibility.saveButton = true
                $scope.priceResult = r
                job.Price = r.Price
                job.ThicknessOfSingleJob = r.ThicknessOfSingleJob

                if job.Template.Id is t.WiroMagazines and job.ThicknessOfSingleJob > 33.0
                    wm = "Thickness of a magazine has become #{job.ThicknessOfSingleJob}. We dont have a suitable coil for it."
                    toastr.warning('', wm, {positionClass: 'toast-center-center', timeOut: 10000})

                if r.Price > 0
                    job.QuoteNeedApprove = false
                    job.CustomerRequestedPrice = 0
                    if job.Quantity != r.Quantity
                        a = $scope.slider.options.stepsArray?.slice() || []
                        if a.indexOf(r.Quantity) == -1 and r.Quantity >= $scope.minValue and r.Quantity <= $scope.maxQty
                            a.push(r.Quantity)
                            $scope.slider.options.stepsArray = a.sort((a, b)-> a - b)
                        #$scope.jobQuantity = r.Quantity
                        $scope.job.Quantity = r.Quantity
                        $scope.setjobQty(r.Quantity)
                    #$scope.$broadcast('rzSliderForceRender')

                    $scope.job.PriceWL = r.PriceWL
                    $scope.$emit('priceUpdate', r)

                    if job.Category is 'Envelopes'
                        wm = "Price quoted is for 25% ink coverage on front only. Larger print areas and double sided print will incur an extra charge please contact us for a quote."
                        toastr.warning('', wm, {positionClass: 'toast-center-center', timeOut: 10000})

                    if job.Template.Id is 41
                        [w,h]  = [210, 297]
                        [W, H] = [job.FinishedSize?.Width ||0, job.FinishedSize?.Height || 0 ]
                        isSmaller = W <= w && H <= h || H <= w && W <= h
                        if isSmaller
                            wm = "Any size equal to A4 or smaller is supplied on A4 sheet – stickers are not trimmed to size."
                            toastr.warning('', wm, {positionClass: 'toast-center-center', timeOut: 10000})
                else
                    #job.Price =  "Continue "
                    $scope.job.PriceWL = undefined
                    job.QuoteNeedApprove = true

                $scope.$emit('priceResult', r)
                mx = {command: 'priceupdate', data: $scope.job}
                window.parent?.postMessage(mx, '*')


                #job.MYOB = r.Myob
                $scope.$broadcast('rzSliderForceRender')
                job.Visibility.saveButton = true
                return
            , (r) ->
                    #$log.debug(r)
            )
            #
            if $scope.$root.globals.IsCustomer
                JobService.getPrice3(priceRequest).then((r) ->
                    $scope.priceResults = r
                    return
                , (r) ->
                        #$log.debug(r)
                )
            return
        , 100)


        $scope.init()

        $scope.$on  'category-changed', () ->
            #$log.debug('category-changed -> resetting values')
            lookups.celloOptions = []
            lookups.stocksForCover = []
            lookups.stockOptions = []
            lookups.stock = {}
            job.Price = ''
            job.Quantity = 0
            $scope.jobQuantity = 0
            $scope.sizeOptions = []
            job.FinishedSize = {PaperSize: {Id: 0}, Width: 0, Height: 0}
            job.FoldedSize = {PaperSize: {Id: 0}, Width: 0, Height: 0}
            job.Stock = {Id: 0}
            job.StockForCover = {Id: 0}
            job.BindingOption = {Id: 0}
            job.Quantity = 0
            job.PrintType = null
            job.FrontPrinting = null
            job.BackPrinting = null
            job.NumberOfMagnets = 0
            job.FrontCelloglaze = 0
            job.BackCelloglaze = 0
            $scope.selectedCello = null
            job.HoleDrilling = 0
            job.RoundOption = 0
            job.RoundDetailOption = 0
            job.Magnet = false
            job.Perforating = false
            job.Scoring = false
            job.Pages = 0
            job.Envelope = null
            job.EnvelopeType = null
            job.FoilColour = null
            job.CustomDieCut  = null
            job.HoleDrilling = 0
            job.NumberOfHoles = null
            job.PadDirection = 0
            job.BoundEdge = 0
            job.spineWidth = 0
            job.DieCutType  = 0
            job.NCRNumbered = false
            job.NCRStartingNumber = null
            job.SpecialInstructions = null
            $scope.vis = {}
            $scope.labeldata = JobService.getLabelsByJobTemplate()
            job.BrochureDistPackInfo = {PackingInstruction: 0}
            job.Price = ''
            job.PriceWL = ''

        $scope.$watch 'vis', (vis,o) ->
            #return
            #cd o.magnet , ' -> ', vis.magnet
            #if not vis.magnet       then job.Magnet          = false
            if not vis.fold         then job.FoldedSize      = {PaperSize: {Id: 0}, Width: 0, Height: 0}
            if not vis.score        then job.Scoring         = false
            if not vis.perforate    then job.Perforating     = false
            if not vis.coverstock   then job.StockForCover   = {PaperSize :{Id: 0}}
            if not vis.page         then job.Pages           = 0
            if not vis.diecut       then job.DieCutType      = 0 # None
            if not vis.binding      then job.BindingOption   = {Id: 0}
            if not vis.boundEdge    then job.BoundEdge       = 0 # None
            if not vis.NCRNumbered  then job.NCRNumbered     = false
            if not vis.hole         then job.HoleDrilling    = 0 # None
            if not vis.cello        then job.FrontCelloglaze = 0 # None
            if not vis.cello        then job.BackCelloglaze  = 0 # None
            if not vis.finishedBy   then job.PadDirection    = 0 # None
            if not vis.envelope     then job.Envelope        = null
            if not vis.envelopeType then job.EnvelopeType    = null
            if not vis.sendSamples  then job.SendSamples     = false
            #if not vis.diecutTentCalendars then job.DieCutType = 0
            if not vis.roundDetail
                job.RoundDetailOption = 0
                job.TRround = false
                job.TLround = false
                job.BRround = false
                job.BLround = false

        , true


        $scope.$on 'clear-price', () ->
            job.Price = ''
            job.PriceWL = ''
        #       $scope.addSpecialInstructions = () ->
        #           ngDialog.open(
        #               template: 'common/special-instructions-popup.html',
        #               scope: $scope
        #           )


        $scope.fillJob = () ->
            x0 = '{"DateModified":"0001-01-01T00:00:00+10:00","Enabled":false,"AttachmentsToProcess":[],"Comments":[],"CommentsToAdd":[],"UploadType":"later","SpecStockId":0,"ColorSides":0,"RequiredPositions":[],"Id":0,"JobNr":"0","Name":"TEST JOB ","Category":"Business card","Template":{"Id":1,"Name":"Business Card"},"FinishedSize":{"PaperSize":{"Id":1,"Name":"Business Card (90 x 54)","Width":90,"Height":54},"Width":54,"Height":90},"FoldedSize":{"PaperSize":{"Id":0},"Width":0,"Height":0},"Stock":{"Id":17,"Name":"310 GSM Deluxe Artboard","GSM":310},"Rotation":0,"Artworks":[],"ArtworkStatus":0,"BindingOption":{"Id":0,"Name":""},"CustomDieCut":null,"CustomSlot":0,"DieCutting":"","DieCutType":0,"DispatchDate":null,"IsCustomFacility":false,"Facility":null,"FinishedDate":"0001-01-01T00:00:00+10:00","FrontCelloglaze":1,"BackCelloglaze":0,"FoilColour":null,"Envelope":null,"EnvelopeType":null,"FrontPrinting":1,"BackPrinting":1,"NumberOfHoles":null,"HoleDrilling":0,"TLround":false,"TRround":false,"BLround":false,"BRround":false,"RoundOption":0,"RoundDetailOption":0,"TrackProgress":false,"Urgent":false,"Folding":"","PrintType":"O","Price":42,"PriceWL":46.2,"Scoring":false,"ScoringInstructions":"","SelfCovered":false,"SendSamples":false,"SpecialInstructions":null,"Status":0,"StatusC":"Not Submitted","StatusCss":"#d56600","OrderStatus":0,"StatusDate":"2019-08-22T12:12:54.4170382+10:00","StockForCover":{"Id":0},"SupplyArtworkApproval":0,"Magnet":false,"Pages":0,"Perforating":false,"PerforatingInstructions":"","Quantity":500,"IsAutoPriceExpired":false,"IsQuoteExpired":false,"IsQuotePrice":false,"NeedApproval":false,"PadDirection":0,"PriceBase":"","PriceDate":"0001-01-01T00:00:00+10:00","PriceMargin":0,"PriceMarginValue":"","Printed":false,"ProductionInstructions":"","QuoteNeedApprove":false,"ReadyArtworkApproval":0,"ReOrderSourceJobId":0,"ReprintFromPreviousJobNo":0,"RequiredByDate":null,"LepSpecialInstructions":"","MYOB":"LGSSCB0001","InvolvesOutwork":false,"PreviewChk":false,"ProofChk":false,"OnHoldChk":false,"RunIds":[],"SuggestedSlot":0,"StatusRenderedForStaff":null,"StatusRenderedForCustomer":null,"BrochureDistPackInfo":{"PackingInstruction":0},"Copies":1,"AACNotPerformed":null,"WLCustomerId":null,"newComment":{"LepOnly":true},"spineWidth":0}'
            #x0 = '{ "CreatedBy": { "Username": "lepdemo", "IsEnabled": true, "IsStaff": false, "Email": "<EMAIL>", "FirstName": "", "LastName": "", "AreaCode": "", "Phone": "0412646331", "Mobile": "0412646331" }, "DateCreated": "0001-01-01T00:00:00+10:00", "DateModified": "0001-01-01T00:00:00+10:00", "OrderId": 0, "OrderCustomerId": 15313, "OrderWLCustomerId": 0, "Enabled": false, "AttachmentsToProcess": [], "Comments": [], "CommentsToAdd": [], "UploadType": null, "DeliveryAddress": { "Address1": "", "Address2": "", "Address3": "", "City": "", "State": "", "Postcode": "", "Country": "AU" }, "SpecStockId": 0, "ColorSides": 0, "Visibility": { "saveButton": true, "clearButton": true, "deleteButton": false, "approvalButton": false, "acceptButton": false, "hasReject": false, "acceptButtonWithSubmit": false, "optionPnlEnabled": true, "priceButton": true, "withdraw": false, "restart": false, "copy": true, "reorder": false, "previewButton": false, "awaitingArtwork": false, "artworkRequiredMsg": false, "addOrderHint": true, "inPrintInst": false, "quoteExpireInst": false, "hideFileUpload": false, "sendSamplesCheckVisible": true, "sendSamplesCheckEnabled": true, "Progress": { "routesAllList": [], "progress": 0 }, "removeArtButtonVisible": false, "IsArtworkEditable": true, "healthCss": "noAlert", "Thumbs": [], "splitDeliveryPossible": false, "splitCount": 0 }, "RequiredPositions": [], "Id": 0, "JobNr": "0", "Name": "Wide Format changes 1", "Category": "Wiro Magazines", "Template": { "Id": 76, "Name": null }, "FinishedSize": { "PaperSize": { "Id": 9, "Name": "A4", "Width": 210, "Height": 297 }, "Width": 210, "Height": 297 }, "FoldedSize": { "PaperSize": { "Id": 0 }, "Width": 0, "Height": 0 }, "Stock": { "Id": 1, "Name": "80 GSM Uncoated", "SType": "Uncoated", "IsCover": false, "GSM": 80, "Thickness": 0.103 }, "StockOverride": null, "Rotation": 0, "BoundEdge": 1, "Artworks": [], "ArtworkStatus": 0, "BindingOption": { "Id": 76, "Name": "Wiro Binding" }, "CustomDieCut": null, "CustomSlot": 0, "DieCutting": "", "DieCutType": 0, "DispatchDate": null, "IsCustomFacility": false, "Facility": null, "FinishedDate": "0001-01-01T00:00:00+10:00", "FrontCelloglaze": 0, "BackCelloglaze": 0, "FoilColour": null, "Envelope": null, "EnvelopeType": null, "FrontPrinting": 1, "BackPrinting": 1, "NumberOfHoles": null, "HoleDrilling": 0, "TLround": false, "TRround": false, "BLround": false, "BRround": false, "RoundOption": 0, "RoundDetailOption": 0, "TrackProgress": false, "Urgent": false, "Folding": "", "PrintType": "D", "ForcedPrintType": null, "Price": 850, "IsWhiteLabel": false, "PriceWL": 0, "CustomerRequestedPrice": 0, "Scoring": false, "ScoringInstructions": "", "SelfCovered": false, "SendSamples": false, "NCRNumbered": false, "NCRStartingNumber": null, "NCRInfo": null, "WiroInfo": { "InnerFrontStockForCover": { "Id": 17, "Name": "310 GSM Deluxe Artboard", "SType": "Artboard", "IsCover": true, "GSM": 310, "Thickness": 0.335 }, "InnerBackStockForCover": { "Id": 17, "Name": "310 GSM Deluxe Artboard", "SType": "Artboard", "IsCover": true, "GSM": 310, "Thickness": 0.335 }, "OuterFront": "Clear PVC", "OuterBack": "Clear PVC", "InnerFrontCello": null, "InnerBackCello": null, "WireColor": "black" }, "SpecialInstructions": null, "Status": 0, "StatusC": "Not Submitted", "StatusCss": "#d56600", "OrderStatus": 0, "StatusDate": "2024-05-18T11:41:26.5267468+10:00", "StockForCover": { "PaperSize": { "Id": 0 } }, "StockForCoverOverride": null, "SupplyArtworkApproval": 0, "Magnet": false, "Pages": 36, "Perforating": false, "PerforatingInstructions": "", "ThicknessOfSingleJob": 4.208, "Quantity": 100, "IsAutoPriceExpired": false, "IsQuoteExpired": false, "IsQuotePrice": false, "NeedApproval": false, "PadDirection": 0, "PriceBase": "", "PriceDate": "0001-01-01T00:00:00+10:00", "PriceMargin": 0, "PriceMarginValue": "", "Printed": false, "ProductionInstructions": "", "QuoteNeedApprove": false, "ReadyArtworkApproval": 0, "ReOrderSourceJobId": 0, "ReprintFromPreviousJobNo": 0, "RequiredByDate": null, "LepSpecialInstructions": "", "MYOB": null, "InvolvesOutwork": false, "PreviewChk": false, "ProofChk": false, "OnHoldChk": false, "RunIds": [], "SuggestedSlot": 0, "StatusRenderedForStaff": null, "StatusRenderedForCustomer": null, "BrochureDistPackInfo": { "PackingInstruction": 0 }, "Copies": 1, "AACNotPerformed": false, "WLCustomerId": null, "WLAnonymousUserId": null, "QuoteEstimator": null, "QuoteCOGS": null, "QuoteOutworkCost": null, "QuoteComments": null, "QuotePrimary": null, "QuoteOutcome": null, "QuoteFollowUpNotes": null, "QuoteFollowUpNotesAdd": null, "NumberOfMagnets": 0, "Splits": [], "HasSplitDelivery": false, "SpineWidth": 0, "newComment": { "LepOnly": true }, "crpDate": "2024-05-18T01:41:53.192Z", "artspec": "", "spineWidth": 0, "artspecs": [] }'
            x1 = JSON.parse(x0)
            angular.extend($scope.job, x1)
            angular.extend($scope.job, x1)
            

        return @
]



