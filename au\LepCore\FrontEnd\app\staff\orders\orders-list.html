﻿<form >
	<div leppane="Orders search">
		<div class="row form-horizontal">
			<div class="col-sm-3 col-md-3">

				<div class="form-group form-group-sm">
					<label class="col-xs-4 control-label" for="customer">Customer</label>
					<div class="col-xs-8">
						<input ng-model="filters.Customer"
							   id="customer" type="text" class="form-control input" ng-minlength="3" />
					</div>
				</div>

				<div class="form-group form-group-sm">
					<label class="col-xs-4 control-label" for="order-no">Order #</label>
					<div class="col-xs-8">
						<input ng-model="filters.OrderNr" id="order-no" type="text" class="form-control input" ng-minlength="4" />
					</div>
				</div>

				<div class="form-group form-group-sm">
					<label class="col-xs-4 control-label" for="Job-num">Job #</label>
					<div class="col-xs-8">
						<input ng-model="filters.JobNr" id="Job-num" type="text" class="form-control input" ng-minlength="4" />
					</div>
				</div>

				<div class="form-group form-group-sm">
					<label class="col-xs-4 control-label" for="status">Status</label>
					<div class="col-xs-8">
						<select ng-model="filters.OrderStatus" id="status" class="form-control input">

							<option selected="selected" value="">-- Any --</option>
							<option value="Submitted">New Order</option>
							<option value="Ready">Ready</option>
							<option value="Finished">Completed</option>
							<option value="Dispatched">Dispatched</option>
							<option value="Archived">Archived</option>


						</select>
					</div>
				</div>
				<div class="form-group form-group-sm">
					<label class="col-xs-4 control-label" for="customer">Facility</label>
					<div class="col-xs-8">
						<label class="control-label"><input type="radio" ng-model="filters.Facility" value="" /> Any </label>&nbsp;&nbsp;
						<label class="control-label"><input type="radio" ng-model="filters.Facility" value="FG" /> Forest Glen</label>&nbsp;&nbsp;
						<label class="control-label"><input type="radio" ng-model="filters.Facility" value="PM" /> Melbourne</label>
					</div>
				</div>

			</div>
			<div class="col-sm-3 col-md-3">


				<div class="form-group form-group-sm ">
					<label class="col-xs-4 control-label" for="job-type">Category</label>
					<div class="col-xs-8">
						<select ng-model="filters.JobTypes" id="job-types" class="form-control input ng-class:{ 'highlight-search': vm.JobTypes != null}"
								ng-options="templateTree.Ids(tv) as t for (t, tv)  in templateTree.topdown.Category">
							<option value="">Any</option>
						</select>
					</div>
				</div>

				<div class="form-group form-group-sm">
					<label class="col-xs-4 control-label" for="job-type">Template</label>
					<div class="col-xs-8">
						<select ng-model="filters.JobType" id="job-type" class="form-control input"
								ng-options="t.Id as t.Name for t in templates">
							<option selected="selected" value="">-- Any --</option>
						</select>
					</div>
				</div>

				<div class="form-group form-group-sm">
					<label class="col-xs-4 control-label" for="celloglaze">Celloglaze</label>
					<div class="col-xs-8">
						<select ng-model="filters.Celloglaze" id="celloglaze" class="form-control input"
								ng-options="k*1 as v for (k,v) in $root.enums.ValueDesc.RunCelloglazeOptions">

							<option selected="selected" value="">-- Any --</option>

						</select>
					</div>
				</div>

				<div class="form-group form-group-sm">
					<label class="col-xs-4 control-label" for="stock">Stock</label>
					<div class="col-xs-8">
						<select ng-model="filters.Stock" id="stock" class="form-control input"
								ng-options="s.Id as  s.Name for s in allStocks">
							<option selected="selected" value="">-- Any --</option>
						</select>
					</div>
				</div>

				<div class="form-group form-group-sm">
					<label class="col-xs-4 control-label nowrap" for="finish-size">Finish Size</label>
					<div class="col-xs-8">
						<select ng-model="filters.Size" id="finish-size" class="form-control input"
								ng-options="s.Id as s.Name for s in allSizes">
							<option selected="selected" value="">-- Any --</option>
						</select>
					</div>
				</div>



			</div>

			<div class="col-sm-3 col-md-2 col-xs-6">
				<div class="form-group form-group-sm">

					<div>
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsNewOrder" />
							New orders
						</label>
					</div>



					<div>
						<label class="control-label">
							<input type="checkbox" ng-model="filters.HideOnHoldOrders" ng-change="filters.ShowOnlyOnHoldOrders = filters.HideOnHoldOrders ? false : filters.ShowOnlyOnHoldOrders" />
							Hide On Hold orders
						</label>
					</div>

					<div>
						<label class="control-label">
							<input type="checkbox" ng-model="filters.ShowOnlyOnHoldOrders" ng-change="filters.HideOnHoldOrders = filters.ShowOnlyOnHoldOrders ? false : filters.HideOnHoldOrders" />
							Show only On Hold orders
						</label>
					</div>

					<!--<div>
						<label class="control-label" >
							<input type="checkbox" ng-model="filters.IsOnlyUrgentOrder" />
							Only Urgent orders
						</label>
					</div>-->

					<div ng-show="filters.full">
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsOnPrepay" />
							On Pre-pay
						</label>
					</div>
					<div ng-show="filters.full">
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsOnhold" />
							Payment On hold
						</label>
					</div>
					<div ng-show="filters.full">
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsAwaitingPayment" />
							Awaiting payment
						</label>
					</div>

					<div ng-show="filters.full">
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsPaidFor" />
							Is Paid for
						</label>
					</div>


					<div>
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsCorrectedOrder" />
							Corrected orders
						</label>
					</div>

					<div>
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsRejected" />
							Rejected orders
						</label>
					</div>

					<div>
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsUnableToMeetPrice" />
							Unable to meet Price
						</label>
					</div>

				</div>
			</div>


			<div class="col-sm-3 col-md-2 col-xs-6">
				<div class="form-group form-group-sm">
					<div>
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsOpenOrder" />
							Open orders
						</label>
					</div>

					<div ng-show="filters.full">
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsNonBusinessCard" />
							Non Business Card
						</label>
					</div>
					<div>
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsWaitingApproval" />
							Need Approval orders
						</label>
					</div>


					<div>
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsQuoteRequired" />
							Quote Required
						</label>
					</div>

					<!--<div>
		<label class="control-label">
			<input type="checkbox" ng-model="filters.IsWithdraw" />
			Cancelled orders
		</label>
	</div>-->

					<div ng-show="filters.full">
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsOrderWithDigitalJob" />
							Orders with Digital Jobs
						</label>
					</div>

					<div>
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsOrderWithOutworkJob" />
							Orders with Outwork Jobs
						</label>
					</div>

					<div>
						<label class="control-label">
							<input type="checkbox" ng-model="filters.IsWhiteLabel" />
							Web Orders
						</label>
					</div>

				</div>
			</div>

			<div class="col-md-2">
				<table class="table table-striped table-hover">
					<tr ng-repeat="fs in favSearches" class="fav-search">
						<td><a ng-click="setAsCurrentSearch(fs)" >{{fs.Name}}</a></td>
						<td><a class="r"  ng-click="removeFavSearch(fs)"  > <i class="glyphicon glyphicon-remove"></i>   </a> </td>
					</tr>
				</table>

			</div>

		</div>

		<div class="row">
			<div class="col-xs-8">


			</div>
			<div class="col-xs-4">
				<div class="pull-right">

					<label class="switch" title="Show less used search options">
						<input type="checkbox" ng-model="filters.full">
						<span class="slider round"></span>
					</label>&nbsp;&nbsp;&nbsp;&nbsp;

					<button type="button" class="btn" ng-click="clear()"><i class="glyphicon glyphicon-erase"></i> Clear</button>&nbsp;
					<button type="submit" class="btn" ng-click="search()"> <i class="glyphicon glyphicon-search"></i>  Search</button>
					<button type="button" class="btn" ng-click="saveAsFavSearch()">Save <i class="glyphicon glyphicon-star"></i></button>
				</div>
			</div>
		</div>



	</div>
</form>

<div class="col-md-2">

</div>

<style>
	[ng\:cloak], [ng-cloak], [data-ng-cloak], [x-ng-cloak], .ng-cloak, .x-ng-cloak {
		display: none !important;
	}
</style>
<!--
<pre>{{filters}}
	 {{favSearches | json}}</pre> -->

<div class="expander">
	<div class="top-border expander-title"><h4>Orders</h4></div>
	<div class="row">


		<div class="col-xs-12">
			<table class="table table-striped table-hover" style="font-size: smaller; background: white;">
				<thead>
					<tr>
						<th style="width:30px"></th>
						<th style="width:70px"><a ng-click="toggleSort('Id')">Order#</a></th>
						<th style="width:80px"><a ng-click="toggleSort('Status')">Status</a></th>
						<th style="width:50px"></th><th style="width:50px"></th>
						<th style="width:120px" class="nowrap"><a ng-click="toggleSort('SubmissionDate')">Submit Date</a></th>
						<th style="width:120px" class="nowrap">Required by</th>
						<th style="width:200px"><a ng-click="toggleSort('c.Name')">Customer</a></th>
						<th style="width:200px" class="nowrap">Job Type</th>
						<th style="width:100px" class="nowrap">Job Size</th>
						<th style="width:300px">Description</th>
						<th style="width:50px">#jobs</th>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat="o in r.List track by o.Id" class="{{::o.Css}}">
						<td class="nowrap">
							<i style="color:red;font-size:12pt" ng-show="::o.ArtRequired" class="glyphicon glyphicon-warning-sign" title="Artwork required!"></i>

							<i style="color: red; font-size: 12pt; text-decoration: line-through;" ng-show="::o.AACNotPerformed===true"  title="AAC not performed">AAC</i>

							<i style="color: red; font-size: 12pt;" ng-show="::o.IsDeleted===true"
							   class="glyphicon glyphicon-trash"
							   title="deleted"></i>
						</td>
						<td  class="nowrap">
						 <!--
							<a ui-sref="orderprint({orderId:o.Id})">
							    Print
							</a>
							-->
							<a ui-sref="staff.order.view({orderId:o.Id})"
							   title="{{::o.Summary}}" class="id-link">
								{{::o.Id}}
							</a>
							<a href="javascript:void(0)" class="tip" ng-if="::o.IsSpecialInstructions.length">
								<i class="glyphicon glyphicon-comment"></i>
								<span><strong>Special Instructions</strong><br/>{{::o.IsSpecialInstructions}}</span>
							</a>
						</td>
						<td class="nowrap">{{::o.StatusS}} </td>
						<td class="nowrap">

							<span ng-bind-html="::o.ExtraStatus"></span>


							<span ng-if="::o.IsWLOrder" class="help-block" style="font-weight: bold; display: inline-block; color: navy; padding-left: 30px;">
								<span ng-if="::o.IsWLOrderPaidFor"  style="color:green" title="{{::o.WLOrderPaymentDetails}}">Web</span>
								<span ng-if="::!o.IsWLOrderPaidFor" style="color:red">Web</span>
							</span>

						</td>
						<td class="nowrap">{{o.InUseBy}}</td>
						<td class="nowrap">{{o.SubmissionDate | date:'dd/MMM/yyyy HH:mm' }}</td>
						<td class="nowrap">{{::o.RevisedDispatchDate  | date:'dd/MMM/yyyy'}}</td>
						<td class="nowrap td180">{{::o.CustomerName}}</td>
						<td class="nowrap"> {{::o.JobType | eatlastdot}}</td>
						<td class="nowrap">{{::o.JobSize}}</td>
						<td class="td180" title="{{::o.Description}}">
							{{::o.Description}}
						</td>
						<td>{{::o.NumJobs}}</td>

<!--
						<td>
								<i class="glyphicon glyphicon-lock btn" ng-click="setO(o)"></i>

								<i class="glyphicon glyphicon-lock btn" ng-click="clearO(o)"></i>
								{{o.InUseBy}}
						</td> -->
					</tr>

				</tbody>
			</table>

		</div>


		<div  class="col-xs-12">

			<span paging
				page="r.Page"
				page-size="r.PageLength"
				total="r.Total"
				paging-action="goPage(page)"
				scroll-top="false"
				hide-if-empty="true"
				show-prev-next="true"
				show-first-last="true"
				text-next-class="glyphicon glyphicon-chevron-right"
				text-prev-class="glyphicon glyphicon-chevron-left"
				text-first-class="glyphicon glyphicon-backward"
				text-last-class="glyphicon glyphicon-forward">
			</span>
			<br/>
			<span class="control-label"> Orders {{r.Summary}} </span>
		</div>
	</div>

</div>
