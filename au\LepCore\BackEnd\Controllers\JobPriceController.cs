#region
using AutoMapper;
using lep;
using lep.address.impl;
using lep.configuration;
using lep.courier;
using lep.extensionmethods;
using lep.freight;
using lep.job;
using lep.job.impl;
using lep.order;
using lep.pricing;
using lep.printPortal;
using lep.user;
using LepCore.Dto;

using Serilog;
using lumen.csv;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Security.Claims;
using System.Text;
using static lep.job.JobTypeOptions;

#endregion

using Rate = lep.courier.Rate;
using lep.promotion;
using lep.pricing.impl;
using System.Drawing;
using System.Security.Policy;
using Microsoft.Extensions.Caching.Memory;
using System.Windows.Forms;

namespace LepCore.Controllers
{
	[Produces("application/json")]
	[Route("api/[controller]")]
	[Authorize]
	public class JobPriceController : Controller
	{
		private readonly IConfigurationApplication _configApp;

		private readonly ICourierApplication _courierApplication;
		private readonly IPackageApplication _packageApplication;
		private readonly IFreightApplication _freightApplication;
		private readonly IJobApplication _jobApp;
		private readonly IOrderApplication _orderApp;
		private readonly IPricePointApplication _pricePointApplication;
		private readonly IPricingEngine _pricingEngine;
		private readonly IPromotionApplication _promoApp;
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		private readonly IUserApplication _userApplication;
		private NHibernate.ISession _session;
		private IUser _currentUser;
		private bool _currentUserIsCust;
		private bool _currentUserIsAnonymousWLCustomer;
		private bool _currentUserIsLoggedInWLCustomer;
		private bool _currentUserIsStaff;
		private List<string> _roles;
		private IMemoryCache _cache;
		private IMapper _mapper { get; }
		public JobPriceController(
			IUserApplication userApplication,
			IOrderApplication orderApp,
			IJobApplication jobApp,
			IPricingEngine pricingEngine,
			IPricePointApplication pricePointApplication,
			IFreightApplication freightApplication,
			IPackageApplication packageApplication,
			ICourierApplication courierApplication,
			IConfigurationApplication configApp,
			IPromotionApplication promoApp,
			NHibernate.ISession session,
			IMemoryCache memoryCache,
			IMapper _mapper
		)
		{
			_orderApp = orderApp;
			_jobApp = jobApp;
			_packageApplication = packageApplication;
			_userApplication = userApplication;
			_pricePointApplication = pricePointApplication;
			_pricingEngine = pricingEngine;
			_freightApplication = freightApplication;
			_courierApplication = courierApplication;
			_configApp = configApp;
			_promoApp = promoApp;
			_session = session;
			_cache = memoryCache;
		}

		[ApiExplorerSettings(IgnoreApi = true)]
		public override void OnActionExecuting(ActionExecutingContext context)
		{
			var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
			_currentUser = _userApplication.GetUser(userId);
			_roles = User.Claims.Where(c => c.Type == ClaimTypes.Role).Select(c => c.Value).ToList();
			_currentUserIsStaff = _roles.Contains(LepRoles.SuperAdministrator) || _roles.Contains(LepRoles.Administrator) || _roles.Contains(LepRoles.Staff); // TODO
			_currentUserIsCust = _roles.Contains(LepRoles.Customer);
			_currentUserIsAnonymousWLCustomer = _roles.Contains(LepRoles.AnonymousWLCustomer);
			_currentUserIsLoggedInWLCustomer = _roles.Contains(LepRoles.LoggedInWLCustomer);
		}

		/// <summary>
		/// Price a job
		/// stateless: creates a temporary job/order for pricing
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>

		[HttpPost("GetPrice2")]
		[Produces(typeof(PriceRequestResult))]
		public IActionResult GetPrice2([FromBody][Required] JobViewCustDto request)
		{
			if (!ModelState.IsValid)
			{
				var messages = (from kvp in ModelState
								from e in kvp.Value.Errors
								select kvp.Key + ": " + e.ErrorMessage).ToArray();
				return new BadRequestObjectResult(ModelState);
			}

			try
			{
				var result = _GetPrice2(request);
				return new OkObjectResult(result);
			}
			catch (System.Exception ex)
			{
				return new BadRequestObjectResult(ex.Message);
			}
		}

		private PriceRequestResult _GetPrice2([FromBody][Required] JobViewCustDto request)
		{
			var custId = User.Claims.Where(c => c.Type == "UserId").Select(c => c.Value).FirstOrDefault();
			var user = _userApplication.GetUser(Convert.ToInt32(custId));

			IOrder order = null;
			IJob job = null;
			ICustomerUser customer = null;
			//ICustomerUser subCustomer = null;

			var productpriceCode = "";
			var freightPriceCode = "";
			decimal freightPriceMargin = 0;


			if (request.Template.Id != (int)JobTypeOptions.TentCalendars)
			{
				request.Template.Id = CustomJobType.Original(request.Template.Id);
			}


			var template = _jobApp.GetJobTemplate(request.Template.Id);





			if (request.OrderId == 0)
			{
				order = _orderApp.NewOrder();
			}
			else
			{
				order = _orderApp.GetOrder(request.OrderId);
			}

			var includeRates = false;
			if (!string.IsNullOrEmpty(request.DeliveryAddress.Country) &&
				!string.IsNullOrEmpty(request.DeliveryAddress.Postcode) &&
				!string.IsNullOrEmpty(request.DeliveryAddress.City)) includeRates = true;
			// todo replace by some thibng valid
			order.DeliveryAddress = includeRates ? request.DeliveryAddress : new PhysicalAddress { Postcode = "4000", City = "Brisbane", Country = "Australia" };



			job = ConstructJob(request, order, template);

			var jd = _packageApplication.GetSingleJobThicknessWeight(job);
			job.ThicknessOfSingleJob = jd.depthOf1Job;

			var qty = 0;
			var sb = new StringBuilder();

			var jobOrderCustomerId = job.Order?.Customer?.Id ?? 0;
			if (jobOrderCustomerId == 0 && request.OrderCustomerId != 0)
			{
				jobOrderCustomerId = request.OrderCustomerId;
			}

			if (jobOrderCustomerId != 0)
			{
				customer = _userApplication.GetCustomerUser(jobOrderCustomerId); ;
				productpriceCode = customer.ProductPriceCode;
				freightPriceCode = customer.FreightPriceCode;
				freightPriceMargin = _freightApplication.GetCustomerFreightMarginFromCode(freightPriceCode);
				if ((freightPriceCode != "") & (freightPriceMargin != 0))
				{
					sb.AppendLine($"Freight price code {freightPriceCode} with margin {freightPriceMargin}% applied to total cost");
				}
			}

			//if (request.OrderId != 0)
			//{
			//	var cco = _orderApp.GetOrder(request.OrderId);
			//	var cc = cco.Customer;
			//	productpriceCode = cc.ProductPriceCode;
			//	freightPriceCode = cc.FreightPriceCode;
			//	freightPriceMargin = _freightApplication.GetCustomerFreightMarginFromCode(freightPriceCode);
			//}

			var facility = _orderApp.GetProductionFacilityByPostCode(order.DeliveryAddress.Postcode);
			if (_jobApp.IsFacilityAvailable(facility.Value, job))
			{
				job.Facility = facility.Value;
			}
			else { job.Facility = Facility.FG; }


			var price = _pricingEngine.PriceJob(job, sb, out qty, productpriceCode);
			job.SetPrice(user, price, 0, false);
			//var myob = _pricingEngine.FindMYOB(job, qty);
			job.Quantity = qty;

			/*

			Pricing logic:
				SubCustomer Logged in ?
							If No  then
									Are there any PriceMarkup Rules for this job?
									If yes Apply that
									If not apply global markup
							If Yes
                            What Method is specified in Sub Customer settings Page?
                                            If General markup
                                            If General Price File
                                            If Customer specific Markup and Markups provided use that
                                            If Customer specific Price file and data provided use that.

			*/
			// calculate White label price if indicated in the request

			job.IsWhiteLabel = request.IsWhiteLabel;

			decimal whiteLabelPrice = 0;
			GetWhiteLabelPrice(job, customer, sb, price, ref whiteLabelPrice);

			var freightMsg = "";
			IList<Rate> courierRates = null;

			if (price > 0 && includeRates)
			{
				_freightApplication.SetFreight(job);
				_freightApplication.SetFreight(order);
				courierRates = _courierApplication.GetAvailableRates(order, facility, false, false);
				if (facility != null)
					freightMsg = "Prices from nearest facility : " + facility.ToDescription();
				else
					freightMsg = "Prices (facility could not be determined) ";

				sb.AppendLine(freightMsg);
			}

			if (_currentUser is IStaff)
			{
				var edd = _orderApp.GetDispatchDate2(order, DateTime.Now, out bool estimated);
				sb.AppendLine($"EDD {edd}");
			}



			var offerDiscounts = new Dictionary<string, decimal>();
			if (price > 0 && _currentUser is ICustomerUser)
			{
				var now = DateTime.Now.Date;
				order.Customer = (ICustomerUser)_currentUser;
				var offers = _session.Query<CustomerOffer>()
						.Where(x => x.Customer.Id == _currentUser.Id)
						.Where(x => x.Promotion.PromotionCode.Contains("My Reward"))
						.Where(x => (x.DateOffered.Date <= now && x.DateOfferEnds.Date >= now))
						.Where(x => x.DateTakenUp == null || (x.DateTakenUp < DateTime.Now && x.AllowReuse))
						.OrderByDescending(x => x.Id)
						.ToList();
				if (offers.Any())
				{
					//foreach(var offer in offers)
					var offer = offers.First();
					{
						order.Promotion = offer.Promotion;
						decimal benefitAmount = 0;
						var stepsFollowed = new StringBuilder();
						if (_promoApp.IsPromotionValid(order, stepsFollowed, out benefitAmount))
						{
							order.PromotionJobPriceBenefit = benefitAmount;
							offerDiscounts.Add(offer.Promotion.PromotionCode, benefitAmount);
						}
					}

				}
			}

			var logMsg = sb.ToString();


			// do  a mock packing
			foreach (var j in order.Jobs)
			{
				_packageApplication.SetPackage(j);
			}
			_packageApplication.SetPackage(order);

			var pFGPackageStr = order.PackDetail.FGPackageJson?.ToString() ?? "";
			var pPMPackageStr = order.PackDetail.PMPackageJson?.ToString() ?? "";



			var result = new PriceRequestResult
			{
				Price = Math.Round(price, 2, MidpointRounding.AwayFromZero),
				PriceWL = whiteLabelPrice,
				Quantity = qty,
				Log = logMsg,
				freightMsg = freightMsg,
				courierRates = courierRates,
				ThicknessOfSingleJob = jd.depthOf1Job,
				FGPackageStr = pFGPackageStr,
				PMPackageStr = pPMPackageStr,
				EDRs = offerDiscounts
			};
			return result;
		}

		private void GetWhiteLabelPrice(IJob job, ICustomerUser customer, StringBuilder sb, decimal price, ref decimal whiteLabelPrice)
		{
			if (job.IsWhiteLabel)
			{
				sb.AppendLine("This is a White label Job.");
				decimal markUp = 0;

				var subCustId = 0; ICustomerUser subCust = null;

				if (job.Order?.WLCustomerId != null)
				{
					subCustId = (int)job.Order.WLCustomerId;
					subCust = _userApplication.GetCustomerUser(subCustId);
				}
				else
				if (_currentUserIsLoggedInWLCustomer)
				{
					subCustId = User.Claims.Where(c => c.Type == "SubCustId").Select(c => Int32.Parse(c.Value)).FirstOrDefault();
					subCust = _userApplication.GetCustomerUser(subCustId);
				}

				// If sub customer has a login
				if (subCustId != 0)
				{
					sb.AppendLine($"SubCustomer #{subCust.Name} 's settings");
					var markUp0 = subCust.PrintPortalSettings.MarkupForJob(job, sb);
					if (markUp0 == null)
					{
						sb.AppendLine($"ParentCustomer #{subCust.ParentCustomer.Name} 's settings");
						markUp0 = subCust.ParentCustomer.PrintPortalSettings.MarkupForJob(job, sb) ?? 0;
					}

					markUp = markUp0 ?? 0;
					sb.AppendLine($"Mark up = {markUp}%");
					whiteLabelPrice = price + price * (markUp / 100);
					whiteLabelPrice = Math.Ceiling(whiteLabelPrice);
				}
				// If Anonymous Sub Customer
				else if (subCustId == 0)
				{
					sb.AppendLine($"Anonymous Sub Customer.");
					sb.AppendLine($"#{customer.Name} 's settings");
					var markUp0 = customer.PrintPortalSettings.MarkupForJob(job, sb);

					markUp = markUp0 ?? 0m;
					sb.AppendLine($"Mark up = {markUp}%");
					whiteLabelPrice = price + price * (markUp / 100);
					whiteLabelPrice = Math.Ceiling(whiteLabelPrice);
				}
				sb.AppendLine($"White Label price is {whiteLabelPrice}");
			}
		}

		private IJob ConstructJob(JobViewCustDto request, IOrder order, IJobTemplate template)
		{
			IJob job = order.NewJob(string.Empty, request.Quantity, false, string.Empty, template, _currentUser);
			job.Template = template;
			job.Quantity = request.Quantity;

			job.Stock = _jobApp.GetStock(request.Stock.Id);
			job.PrintType = request.PrintType;

			job.FrontPrinting = request.FrontPrinting;
			job.BackPrinting = request.BackPrinting;
			job.FrontCelloglaze = request.FrontCelloglaze;
			job.BackCelloglaze = request.BackCelloglaze;

			job.BackCelloglazeOverride = request.BackCelloglazeOverride;
			job.FrontCelloglazeOverride = request.FrontCelloglazeOverride;


			if (job.FinalFrontCelloglaze.Is(JobCelloglazeOptions.EmbossFoil, JobCelloglazeOptions.Foil))
			{
				job.FoilColour = request.FoilColour;
			}
			else
			{
				job.FoilColour = null;
			}

			job.Envelope = request.Envelope;
			job.EnvelopeType = request.EnvelopeType;

			job.NumberOfMagnets = request.NumberOfMagnets;
			job.Perforating = request.Perforating;
			job.Scoring = request.Scoring;

			job.SendSamples = request.SendSamples;

			job.Rotation = request.Rotation;
			var ps = _jobApp.GetPaperSize(request.FinishedSize.PaperSize.Id);
			job.FinishedSize.PaperSize = ps;

			if (job.FinishedSize.PaperSize.Name == "Custom")
			{
				job.FinishedSize.Height = request.FinishedSize.Height;
				job.FinishedSize.Width = request.FinishedSize.Width;
				job.FinishedSize.PaperSize.Size.Width = request.FinishedSize.Width;
				job.FinishedSize.PaperSize.Size.Height = request.FinishedSize.Height;
			}
			else
			{
				job.FinishedSize.Height = job.Rotation == RotationOption.Portrait
					? job.FinishedSize.PaperSize.Size.Height
					: job.FinishedSize.PaperSize.Size.Width;
				job.FinishedSize.Width = job.Rotation == RotationOption.Portrait
					? job.FinishedSize.PaperSize.Size.Width
					: job.FinishedSize.PaperSize.Size.Height;
			}

			if (request.FoldedSize != null && request.FoldedSize.PaperSize != null && request.FoldedSize.PaperSize.Id != 0)
			{
				job.FoldedSize = new lep.job.impl.Size();
				job.FoldedSize.PaperSize = _jobApp.GetPaperSize(request.FoldedSize.PaperSize.Id);

				if (job.FoldedSize.PaperSize.Name == "Custom")
				{
					job.FoldedSize.Height = request.FoldedSize.Height;
					job.FoldedSize.Width = request.FoldedSize.Width;

					job.FoldedSize.PaperSize.Size.Width = request.FoldedSize.Width;
					job.FoldedSize.PaperSize.Size.Height = request.FoldedSize.Height;
				}
				else
				{
					job.FoldedSize.Height = job.FoldedSize.PaperSize.Size.Height;
					job.FoldedSize.Width = job.FoldedSize.PaperSize.Size.Width;
				}
			}
			else
			{
				job.FoldedSize = null;
			}

			job.RoundOption = request.RoundOption;
			job.RoundDetailOption = request.RoundDetailOption;
			job.TLround = request.TLround;
			job.TRround = request.TRround;
			job.BLround = request.BLround;
			job.BRround = request.BRround;
			job.CustomDieCut = request.CustomDieCut;
			if (job.Template.Is(PresentationFolder, PresentationFolderNDD, TentCalendars))
			{
				job.DieCutType = request.DieCutType; // to do
			}
			else if (job.Template.Is(BusinessCard, BusinessCardNdd, BusinessCardNdd, Postcard))
			{
				job.RoundOption = request.RoundOption;
				job.HoleDrilling = request.HoleDrilling;
			}
			else if (job.IsMagazine())
			{
				job.Pages = request.Pages;
				if (request.BindingOption != null && request.BindingOption.Id != 0) job.BindingOption = _jobApp.GetBindingOption(request.BindingOption.Id);
				if (job.IsMagazineSeparate())
				{
					if (request.StockForCover.Id != 0) job.StockForCover = _jobApp.GetStock(request.StockForCover.Id);
					job.SelfCovered = false;
				}
				else
				{
					job.SelfCovered = true;
				}
			}
			if (job.Template.Is(WiroMagazines))
			{
				job.Pages = request.Pages;

				var wiroInfo = new WiroMagazineInfo();

				wiroInfo.OuterFront = request.WiroInfo.OuterFront;
				wiroInfo.OuterBack = request.WiroInfo.OuterBack;
				wiroInfo.InnerFrontCello = request.WiroInfo.InnerFrontCello;
				wiroInfo.InnerBackCello = request.WiroInfo.InnerBackCello;

				if (request.WiroInfo.InnerFrontStockForCover != null)
				{
					wiroInfo.InnerFrontStockForCover = (Stock)_jobApp.GetStock(request.WiroInfo.InnerFrontStockForCover.Id);
				}

				if (request.WiroInfo.InnerBackStockForCover != null)
				{
					wiroInfo.InnerBackStockForCover = (Stock)_jobApp.GetStock(request.WiroInfo.InnerBackStockForCover.Id);
				}

				wiroInfo.WireColor = request.WiroInfo.WireColor;

				job.WiroInfo = wiroInfo;


			}
			if (job.Template.Is(Notepads, DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks))
			{
				job.Pages = request.Pages;
			}

			if (job.IsMagazine())
			{
				job.HoleDrilling = request.HoleDrilling;

			}

			if (job.Template.Is(MagazineNDD))
			{
				job.HoleDrilling = HoleDrilling.None;
			}

			job.NumberOfHoles = request.NumberOfHoles;
			job.SpecialInstructions = request.SpecialInstructions;
			job.BrochureDistPackInfo = request.BrochureDistPackInfo;

			job.NumberOfMagnets = request.NumberOfMagnets;
			job.Perforating = request.Perforating;
			job.Scoring = request.Scoring;


			if (job.Template.Is(Notepads))
			{
				job.Pages = request.Pages;
			}

			if (job.Template.Is(DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks))
			{
				job.Pages = request.Pages;
				job.NCRNumbered = request.NCRNumbered;
				job.NCRStartingNumber = request.NCRStartingNumber;
				job.NCRInfo = request.NCRInfo;
			}

			return job;
		}



		[HttpPost("GetPrice3")]
		[Produces(typeof(List<PriceRequestResult>))]

		public IActionResult GetPrice3([FromBody][Required] JobViewCustDto request)
		{
			//var cacheKey = JsonConvert.SerializeObject(request); // Convert request to string to use as cache key
			var custId = User.Claims.Where(c => c.Type == "UserId").Select(c => c.Value).FirstOrDefault();
			var user = _userApplication.GetUser(Convert.ToInt32(custId));

			//var cacheKey = custId.ToString() +
			//				request.Template.Id.ToString()  +
			//				request.FinishedSize.PaperSize.Id +
			//				request.Stock.Id +
			//				request.StockForCover.Id +
			//				request.NCRInfo.ToString() +

			//if (_cache.TryGetValue(cacheKey, out List<PriceRequestResult> cachedResponse))
			//{
			//	return Ok(cachedResponse);
			//}


			if (!ModelState.IsValid)
			{
				var messages = (from kvp in ModelState
								from e in kvp.Value.Errors
								select kvp.Key + ": " + e.ErrorMessage).ToArray();
				return new BadRequestObjectResult(ModelState);
			}



			IOrder order = null;
			IJob job = null;
			ICustomerUser customer = null;
			//ICustomerUser subCustomer = null;

			var productpriceCode = "";
			var freightPriceCode = "";
			decimal freightPriceMargin = 0;


			if (request.Template.Id != (int)JobTypeOptions.TentCalendars)
			{
				request.Template.Id = CustomJobType.Original(request.Template.Id);
			}


			var template = _jobApp.GetJobTemplate(request.Template.Id);

			if (request.OrderId == 0)
			{
				order = _orderApp.NewOrder();
			}
			else
			{
				order = _orderApp.GetOrder(request.OrderId);
			}

			var includeRates = false;
			if (!string.IsNullOrEmpty(request.DeliveryAddress.Country) &&
				!string.IsNullOrEmpty(request.DeliveryAddress.Postcode) &&
				!string.IsNullOrEmpty(request.DeliveryAddress.City)) includeRates = true;
			// todo replace by some thibng valid
			order.DeliveryAddress = includeRates ? request.DeliveryAddress : new PhysicalAddress { Postcode = "4000", City = "Brisbane", Country = "Australia" };

			//var qty = 0;
			var sb = new StringBuilder();
			job = ConstructJob(request, order, template);

			var jd = _packageApplication.GetSingleJobThicknessWeight(job);
			job.ThicknessOfSingleJob = jd.depthOf1Job;



			var jobOrderCustomerId = job.Order?.Customer?.Id ?? 0;
			if (jobOrderCustomerId == 0 && request.OrderCustomerId != 0)
			{
				jobOrderCustomerId = request.OrderCustomerId;
			}

			if (jobOrderCustomerId != 0)
			{
				customer = _userApplication.GetCustomerUser(jobOrderCustomerId); ;
				productpriceCode = customer.ProductPriceCode;
				freightPriceCode = customer.FreightPriceCode;
				freightPriceMargin = _freightApplication.GetCustomerFreightMarginFromCode(freightPriceCode);
				if ((freightPriceCode != "") & (freightPriceMargin != 0))
				{
					sb.AppendLine($"Freight price code {freightPriceCode} with margin {freightPriceMargin}% applied to total cost");
				}
			}

			//if (request.OrderId != 0)
			//{
			//	var cco = _orderApp.GetOrder(request.OrderId);
			//	var cc = cco.Customer;
			//	productpriceCode = cc.ProductPriceCode;
			//	freightPriceCode = cc.FreightPriceCode;
			//	freightPriceMargin = _freightApplication.GetCustomerFreightMarginFromCode(freightPriceCode);
			//}

			var facility = _orderApp.GetProductionFacilityByPostCode(order.DeliveryAddress.Postcode);
			if (_jobApp.IsFacilityAvailable(facility.Value, job))
			{
				job.Facility = facility.Value;
			}
			else { job.Facility = Facility.FG; }

			var size = job.FinishedSize.PaperSize;
			if (size.Name == "Custom")
			{
				var smallestSizeThatllFitThisCustomSize = _pricePointApplication.FindSmallestFit(job);
				if (smallestSizeThatllFitThisCustomSize != null)
					size = smallestSizeThatllFitThisCustomSize;
			}

			//var specstock = _jobApp.GetSpecStock(request.SpecStockId);
			//var xx = specstock.QuantityOptions;

			var pricePoints = _pricePointApplication.FindPricePointsIgnoringPrintType(job);

			var initialQuantity = job.Quantity;



			// Only call PriceJob on the final quantities
			var results = pricePoints
				.Where(p => p.Quantity <= 1000 || p.Quantity % 1000 == 0)
				.Select(p =>
				{
					job.PrintType = p.PrintType;
					job.Quantity = p.Quantity;
					var price = _pricingEngine.PriceJob(job, sb, out var qty, productpriceCode);
					return new { Quantity = qty, Price = price };
				})
				.GroupBy(p => p.Quantity)
				.Select(group => new //PriceRequestResult
				{
					Quantity = group.Key,
					Price = group.Any(p => p.Price != 0) ? group.Where(p => p.Price != 0).Min(p => p.Price) : 0
				})
				.OrderBy(p => p.Quantity)
				.Where(p => p.Price != 0)
				.ToList();

			//decimal whiteLabelPrice = 0;
			//if (request.IsWhiteLabel)
			//{
			//	foreach (var p in prices)
			//	{
			//		GetWhiteLabelPrice(job, customer, sb, p.Price, ref whiteLabelPrice);
			//		p.PriceWL = whiteLabelPrice;
			//	}
			//}


			// Store the response in the cache for 10 minutes
			//_cache.Set(cacheKey, results, TimeSpan.FromMinutes(10));

			return new OkObjectResult(results);
		}



		/*
[HttpPost("GetMyob")]
[Produces(typeof(PriceRequestResult))]
public IActionResult GetMyob([FromBody] [Required] JobViewCustDto request)
{
	if (!ModelState.IsValid)
	{
		var messages = (from kvp in ModelState
						from e in kvp.Value.Errors
						select kvp.Key + ": " + e.ErrorMessage).ToArray();
		return new BadRequestObjectResult(ModelState);
	}
	var s = JsonConvert.SerializeObject(request);

	var custId = User.Claims.Where(c => c.Type == "UserId").Select(c => c.Value).FirstOrDefault();
	var user = _userApplication.GetUser(Convert.ToInt32(custId));

	IJob job = null;

	try
	{
		IOrder order = null;
		if (request.OrderId != 0)
		{
			order = _orderApp.GetOrder(request.OrderId);
			order.DeliveryAddress = job.Order.Customer.PostalAddress;
		}
		else
		{
			order = _orderApp.NewOrder();
		}

		var template = _jobApp.GetJobTemplate(request.Template.Id);
		job = order.NewJob(string.Empty, request.Quantity, false, string.Empty, template, _currentUser);
		job.Template = template;
		job.Quantity = request.Quantity;

		job.Stock = _jobApp.GetStock(request.Stock.Id);
		job.PrintType = request.PrintType;

		job.FrontPrinting = request.FrontPrinting;
		job.BackPrinting = request.BackPrinting;
		job.FrontCelloglaze = request.FrontCelloglaze;
		job.BackCelloglaze = request.BackCelloglaze;

		job.Magnet = request.Magnet;
		job.Perforating = request.Perforating;
		job.Scoring = request.Scoring;

		job.SendSamples = request.SendSamples;

		job.FinishedSize.PaperSize = _jobApp.GetPaperSize(request.FinishedSize.PaperSize.Id);

		if (job.FinishedSize.PaperSize.Name == "Custom")
		{
			job.FinishedSize.Height = request.FinishedSize.Height;
			job.FinishedSize.Width = request.FinishedSize.Width;
			job.FinishedSize.PaperSize.Size.Width = request.FinishedSize.Width;
			job.FinishedSize.PaperSize.Size.Height = request.FinishedSize.Height;
		}

		if (request.FoldedSize?.PaperSize != null && request.FoldedSize.PaperSize.Id != 0)
		{
			job.FoldedSize = new Size();
			job.FoldedSize.PaperSize = _jobApp.GetPaperSize(request.FoldedSize.PaperSize.Id);

			if (job.FoldedSize.PaperSize.Name == "Custom")
			{
				job.FoldedSize.Height = request.FoldedSize.Height;
				job.FoldedSize.Width = request.FoldedSize.Width;

				job.FoldedSize.PaperSize.Size.Width = request.FoldedSize.Width;
				job.FoldedSize.PaperSize.Size.Height = request.FoldedSize.Height;
			}
			else
			{
				job.FoldedSize.Height = job.FoldedSize.PaperSize.Size.Height;
				job.FoldedSize.Width = job.FoldedSize.PaperSize.Size.Width;
			}
		}
		else
		{
			job.FoldedSize = null;
		}

		job.RoundOption = request.RoundOption;
		job.RoundDetailOption = request.RoundDetailOption;
		job.TLround = request.TLround;
		job.TRround = request.TRround;
		job.BLround = request.BLround;
		job.BRround = request.BRround;

		var myob = _pricingEngine.FindMYOB(job, job.Quantity);

		return new OkObjectResult(myob);
	}
	catch (Exception ex)
	{
		return new BadRequestObjectResult(ex.StackTrace);
	}
}
*/

		[HttpPost("GetPricePointsForJobType")]
		public IActionResult GetPricePointsForJobType([FromBody] JobViewCustDto request)
		{
			if (!ModelState.IsValid)
			{
				var messages = (from kvp in ModelState
								from e in kvp.Value.Errors
								select kvp.Key + ": " + e.ErrorMessage).ToArray();
				return new BadRequestObjectResult(ModelState);
			}

			var specstock = _jobApp.GetSpecStock(request.SpecStockId);

			var template = _jobApp.GetJobTemplate(request.Template.Id);
			var stock = _jobApp.GetStock(request.Stock.Id);
			var printType = request.PrintType;
			var size = _jobApp.GetPaperSize(request.FinishedSize.PaperSize.Id);

			// show the price points required by the system for this range
			IDictionary<int, decimal> reqdQtysList = new Dictionary<int, decimal>();
			reqdQtysList = _pricePointApplication.CreateEnoughPoints((JobTypeOptions)request.Template.Id, specstock.QuantityOption, reqdQtysList);

			var ReqdQtys = reqdQtysList.Select(x => x.Key).ToList();

			var numcoloursides = request.ColorSides;

			var pages = 1;
			if (template.HasMultiplePages()) pages = request.Pages;

			var tmp = _pricePointApplication.FindPricePoints(SiteLocation.AU, printType, template, stock, size, numcoloursides,
				request.FrontCelloglaze, request.BackCelloglaze);

			IList<IPricePoint> prices = tmp.Where(p => p.NumPages == pages).ToList();

			//var productMYOB = _pricePointApplication.FindProductMYOB(SiteLocation.AU, printType, template, stock, size,
			//	numcoloursides,
			//	request.FrontCelloglaze, request.BackCelloglaze, pages);

			//var myob = "";
			//if (productMYOB != null && !string.IsNullOrEmpty(productMYOB.MYOB1)) myob = productMYOB.MYOB1;

			var pricePoints = prices.OrderBy(x => x.Quantity).Select(x => new { Qty = x.Quantity, x.Price }).ToList(); //, x.MYOB 

			var lastChanged = "";
			var lastChangedBy = "";

			if (prices.Any())
			{
				lastChanged = prices[0].DateModified.ToString("dd/MM/yyyy HH:mm");
				lastChangedBy = string.Format("{0} {1}", prices[0].ChangeBy.FirstName,
					prices[0].ChangeBy.LastName);
			}

			// var allowedQuantities = QuantitiesByTemplate.Get((JobTypeOptions)request.Template.Id, specstock.QuantityOption);

			return new OkObjectResult(new
			{
				pricePoints,
				//productMYOB = myob,
				lastChanged,
				lastChangedBy,
				ReqdQtys
				//   allowedQuantities = allowedQuantities
			});
		}

		[HttpPost("SavePricePointsForJobType")]
		[Authorize(Roles = LepRoles.Staff)]
		public IActionResult SavePricePointsForJobType([FromBody] UpdatePricesRequest request)
		{
			if (!ModelState.IsValid)
			{
				var messages = (from kvp in ModelState
								from e in kvp.Value.Errors
								select kvp.Key + ": " + e.ErrorMessage).ToArray();
				return new BadRequestObjectResult(ModelState);
			}

			var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => c.Value).FirstOrDefault();
			var user = _userApplication.GetUser(Convert.ToInt32(userId));

			var specstock = _jobApp.GetSpecStock(request.SpecStockId);
			var template = _jobApp.GetJobTemplate(request.Template.Id);
			var stock = _jobApp.GetStock(request.Stock.Id);
			var printType = request.PrintType;
			var size = _jobApp.GetPaperSize(request.FinishedSize.PaperSize.Id);
			var numcoloursides = request.ColorSides;
			var pages = 1;
			if (template.HasMultiplePages()) pages = request.Pages;

			if (_pricePointApplication.IsValidPoints(printType, template, stock, size, request.NewPrices))
				_pricePointApplication.SetPricePoints(printType, template, stock, size, numcoloursides, request.FrontCelloglaze,
					request.BackCelloglaze, pages, request.NewPrices, request.NewMYOBs, user,
					request.OtherMYOB1, request.OtherMYOB2);
			else
				return new ContentResult
				{
					StatusCode = (int)HttpStatusCode.BadRequest,
					Content = "Not enough price points to save"
				};

			return Ok();
		}

		[HttpPost("ImportJobOptionCSVs")]
		public IActionResult ImportJobOptionCSVs()
		{
			var result = _jobApp.ImportJobOption();
			return new ContentResult { StatusCode = (int)HttpStatusCode.OK, Content = result };
		}

		[HttpPost("JobPriceCsvExport")]///{desiredTemplate:int}
		[AllowAnonymous] // as we will authorize it by the token passed in Form
		public IActionResult JobPriceCsvExport([FromForm] string token, [FromForm] int desiredTemplate)
		{
			var x = Startup.ValidateJWT(token);
			if (!x.IsInRole(LepRoles.Staff))
				return Unauthorized();

			var desiredTemplateName = desiredTemplate == 0 ? "All" : _jobApp.GetJobTemplate(desiredTemplate).Name;

			var downloadCsvFileName = $"Price_{desiredTemplateName}_{DateTime.Now:yyyyMMdd_HHMMss}.csv";

			var serial = new CsvSerialiser();

			var stream = new MemoryStream();
			var csvWriter = new StreamWriter(stream, Encoding.UTF8);
			serial.Writer = csvWriter;
			_pricePointApplication.ExportPriceCSV(serial, desiredTemplate);
			serial.EndDocument();
			stream.Position = 0;
			return File(stream, "text/csv", downloadCsvFileName);
		}

		[HttpPost("JobPriceCsvImport")]
		[Authorize(Roles = LepRoles.Staff)]
		public IActionResult JobPriceCsvImport()
		{
			var files = Request.Form.Files;
			if (!files.Any()) return BadRequest();

			try
			{
				var filename = ContentDispositionHeaderValue.Parse(files[0].ContentDisposition).FileName.Trim().ToString();
				var extension = Path.GetExtension(filename);
				var f = files[0];

				TextReader reader = new StreamReader(f.OpenReadStream());
				var msg = _pricePointApplication.ImportPriceCSV(reader);

				return new ContentResult { StatusCode = (int)HttpStatusCode.OK, Content = msg };
			}
			catch (Exception ex)
			{
				return new ContentResult { StatusCode = (int)HttpStatusCode.BadRequest, Content = ex.Message };
			}
		}

		//[HttpPost("CartonCsvImport")]
		//public IActionResult CartonCsvImport()
		//{
		//var files = Request.Form.Files;
		//if (files.Count() == 0) return BadRequest();

		//try
		//{
		//	var filename = ContentDispositionHeaderValue.Parse(files[0].ContentDisposition).FileName.Trim().ToString();
		//	var extension = Path.GetExtension(filename);
		//	var f = files[0];
		//	int c = 0;\

		//	var msg = _freightApplication.ImportPaperPack(f.OpenReadStream(), out c);

		//	return new ContentResult { StatusCode = (int)HttpStatusCode.OK,
		//		Content = string.Join("\n", msg.ToArray()) };
		//}
		//catch (Exception ex)
		//{
		//	return new ContentResult { StatusCode = (int)HttpStatusCode.BadRequest, Content = ex.Message };
		//}
		//}
	}
}
