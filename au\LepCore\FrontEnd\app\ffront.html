﻿<!--LOGIN_PARTIAL-->

	<div id="login-row" class="row">
		<div class="row text-center">
			<div id="u4185-2">LEP online is a trade only print job ordering tool.</div>
			<p>Find out more about LEP <a href="#">here</a>, register <a href="#">here</a>, or login below.</p>
		</div>
		<hr />
		<div class="row text-center">
			<div ng-show="error" class="alert alert-danger">{{error}}</div>
			<form name="form" ng-submit="login()" role="form" class="form-horizontal col-sm-12">
				<div class="form-group row">
					<label for="username" class="col-sm-4 text-right">Username</label>
					<i class="fa fa-key"></i>
					<div class="col-sm-7">
						<input type="text" name="username" id="username" class="form-control" ng-model="username" ng-required="true" required maxlength="10" />
					</div>
					<div role="alert">
						<div ng-show="form.username.$error.required">Please enter Username</div>
						<div ng-show="form.username.$error.maxlength">This field can be at most 10 characters long.</div>
					</div>
				</div>
				<div class="form-group row">
					<label for="password" class="col-sm-4 text-right">Password</label>
					<i class="fa fa-lock"></i>
					<div class="col-sm-7">
						<input type="password" name="password" id="password" class="form-control" ng-model="password" ng-required="true" />
					</div>
					<span ng-show="form.password.$dirty && form.password.$error.required" class="help-block">Password is required</span>
				</div>
				<div class="row">
					<div class="form-actions col-sm-10 col-sm-offset-4">
						<button id="login-btn" class="btn pull-left" ng-enabled="form.isValid" ng-click="login()">Login</button>
					</div>
				</div>
				--{{form.isValid}}--
				<div class="row">
					<p><a href="#">Forgot your password?</a>, or <a href="#">want an account?</a></p>
				</div>

				<br />
				<smalll>
					<br />
					<a ng-click="loginCust()" href="#">login  as customer called Lepdemo </a>
					<br><br>
					<a ng-click="loginStaff()" href="#">login  as Login as staff mgreenan </a>
				</smalll>
			</form>

		</div>
	</div>

