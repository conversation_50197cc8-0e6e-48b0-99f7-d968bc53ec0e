/* General CSS Setup */
/* container */
:root {
    --normal-color: rgb(118, 193, 235);
    --error-color: #bddf9a ;
  }


  /* CSS talk bubble */

  .talk-bubble {
    margin: 10px;
    display: inline-block;
    position: relative;
    height: auto;
    color: #000;
    background-color: var(--normal-color);
  }

  .border {
    border: 8px solid #666;
  }

  .round {
    border-radius: 40px;
    -webkit-border-radius: 40px;
    -moz-border-radius: 40px;
  }

  /* Right triangle placed top left flush. */

  .tri-right {
    &.border.left-top:before {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: -40px;
      right: auto;
      top: -8px;
      bottom: auto;
      border: 32px solid;
      border-color: #666 transparent transparent transparent;
    }

    &.left-top:after {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: -20px;
      right: auto;
      top: 0px;
      bottom: auto;
      border: 22px solid;
      border-color: var(--normal-color) transparent transparent transparent;
    }

    &.border.left-in:before {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: -40px;
      right: auto;
      top: 30px;
      bottom: auto;
      border: 20px solid;
      border-color: #666 #666 transparent transparent;
    }

    &.left-in:after {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: -20px;
      right: auto;
      top: 38px;
      bottom: auto;
      border: 12px solid;
      border-color: var(--normal-color) var(--normal-color) transparent transparent;
    }

    &.border.btm-left:before {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: -8px;
      right: auto;
      top: auto;
      bottom: -40px;
      border: 32px solid;
      border-color: transparent transparent transparent #666;
    }

    &.btm-left:after {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: 0px;
      right: auto;
      top: auto;
      bottom: -20px;
      border: 22px solid;
      border-color: transparent transparent transparent var(--normal-color);
    }

    &.border.btm-left-in:before {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: 30px;
      right: auto;
      top: auto;
      bottom: -40px;
      border: 20px solid;
      border-color: #666 transparent transparent #666;
    }

    &.btm-left-in:after {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: 38px;
      right: auto;
      top: auto;
      bottom: -20px;
      border: 12px solid;
      border-color: var(--normal-color) transparent transparent var(--normal-color);
    }

    &.border.btm-right-in:before {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: auto;
      right: 30px;
      bottom: -40px;
      border: 20px solid;
      border-color: #666 #666 transparent transparent;
    }

    &.btm-right-in:after {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: auto;
      right: 38px;
      bottom: -20px;
      border: 12px solid;
      border-color: var(--normal-color) var(--normal-color) transparent transparent;
    }

    &.border.btm-right:before {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: auto;
      right: -8px;
      bottom: -40px;
      border: 20px solid;
      border-color: #666 #666 transparent transparent;
    }

    &.btm-right:after {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: auto;
      right: 0px;
      bottom: -20px;
      border: 12px solid;
      border-color: var(--normal-color) var(--normal-color) transparent transparent;
    }

    &.border.right-in:before {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: auto;
      right: -40px;
      top: 30px;
      bottom: auto;
      border: 20px solid;
      border-color: #666 transparent transparent #666;
    }

    &.right-in:after {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: auto;
      right: -20px;
      top: 38px;
      bottom: auto;
      border: 12px solid;
      border-color: var(--normal-color) transparent transparent var(--normal-color);
    }

    &.border.right-top:before {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: auto;
      right: -40px;
      top: -8px;
      bottom: auto;
      border: 32px solid;
      border-color: #666 transparent transparent transparent;
    }

    &.right-top:after {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: auto;
      right: -20px;
      top: 0px;
      bottom: auto;
      border: 20px solid;
      border-color: var(--normal-color) transparent transparent transparent;
    }
  }

  /* Right triangle, left side slightly down */

  /*Right triangle, placed bottom left side slightly in*/

  /*Right triangle, placed bottom left side slightly in*/

  /*Right triangle, placed bottom right side slightly in*/

  /*
        left: -8px;
      right: auto;
      top: auto;
        bottom: -40px;
        border: 32px solid;
        border-color: transparent transparent transparent #666;
        left: 0px;
      right: auto;
      top: auto;
        bottom: -20px;
        border: 22px solid;
        border-color: transparent transparent transparent  var(--normal-color);

    /*Right triangle, placed bottom right side slightly in*/

  /* Right triangle, right side slightly down*/

  /* Right triangle placed top right flush. */

  /* talk bubble contents */

  .talktext {
    padding: 1em;
    text-align: left;
    line-height: 1.5em;

    p {
      /* remove webkit p margins */
      -webkit-margin-before: 0em;
      -webkit-margin-after: 0em;
    }
  }



  .talk-bubble {
    display: inline-block;
    position: relative;
    height: auto;
    background-color: var(--normal-color);
    padding: 20px;
    // filter: drop-shadow(2px 2px 4px var(--normal-color));
    border-radius: 10px;
    color:#fff;
  }

  .border {
    border: 4px solid #666;
  }

  .round {
    border-radius: 30px;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
  }

  /* Right triangle placed top left flush. */

  .tri-right {
    &.border.left-top:before {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: -40px;
      right: auto;
      top: -8px;
      bottom: auto;
      border: 32px solid;
      border-color: var(--normal-color) transparent transparent transparent;
    }

    &.left-top:after {
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      left: -20px;
      right: auto;
      top: 0px;
      bottom: auto;
      border: 22px solid;
      border-color: var(--normal-color) transparent transparent transparent;
    }
  }

.red {
    .talk-bubble {
        color: #000;
        background-color: var(--normal-color);
        // filter: drop-shadow(2px 2px 4px var(--error-color));
    }

    .tri-right.border.left-top:before {
        border-color: var(--normal-color) transparent transparent transparent;
    }

    .tri-right.left-top:after {
        border-color: var(--normal-color) transparent transparent transparent;
    }

    .tri-right.btm-right-in:after {
        border-color: var(--normal-color) var(--normal-color) transparent transparent;
    }
}
