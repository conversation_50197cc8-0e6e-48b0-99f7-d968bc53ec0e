<div leppane="Search">
    <form >
      <div class="row form-horizontal ">
          <div class="col-sm-6">


            <div class="form-group">
              <label class="col-xs-3 control-label" for="FromDate">From </label>
              <div class="col-xs-7">
                  <div class="input-group">
                      <div class="col-sm-6 col-xs-6 lpad0 rpad0">
                          <input type="text" size="10" class="form-control" ng-model="vm.FromDate" data-date-format="dd-MMM-yy" data-autoclose="1" placeholder="Date" bs-datepicker>
                      </div>
                      <div class="col-sm-6 col-xs-6 lpad0 rpad0">
                          <input type="text" size="8" class="form-control" ng-model="vm.FromDate" data-time-format="HH:mm" data-autoclose="1" placeholder="Time" data-default-time="false" bs-timepicker>
                      </div>
                  </div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-xs-3 control-label" for="ToDate">To </label>
              <div class="col-xs-7">
                  <div class="input-group">
                      <div class="col-sm-6 col-xs-6 lpad0 rpad0">
                          <input type="text" size="10" class="form-control" ng-model="vm.ToDate" data-date-format="dd-MMM-yy" data-autoclose="1" placeholder="Date" bs-datepicker>
                      </div>
                      <div class="col-sm-6 col-xs-6 lpad0 rpad0">
                          <input type="text" size="8" class="form-control" ng-model="vm.ToDate" data-time-format="HH:mm" data-autoclose="1" placeholder="Time" data-default-time="false" bs-timepicker>
                      </div>
                  </div>
              </div>
            </div>
              <div class="form-group form-group-sm">
                  <label class="col-sm-3 control-label" for="Entity">Entity</label>
                  <div class="col-sm-6">
                      <input id="Entity" type="text" class="form-control input" ng-model="vm.Entity" ng-minlength="3"
                       placeholder="Entity name, order/job/customer" />
                  </div>
              </div>
  
              <div class="form-group form-group-sm">
                  <label class="col-sm-3 control-label" for="EntityId">Entity Id</label>
                  <div class="col-sm-6">
                      <input id="EntityId" type="text" class="form-control input" ng-model="vm.EntityId"  />
                  </div>
              </div>
  

 
  
          </div>
  
        <div class="col-sm-6">
  
            <div class="form-group form-group-sm">
                <label class="col-sm-4 control-label" for="payment-terms">By</label>
                <div class="col-sm-6">
                    <label class="control-label"><input type="radio" name="IsStaff" ng-model="vm.IsStaff" ng-value="true" /> Staff    </label>&nbsp;&nbsp;
                    <label class="control-label"><input type="radio" name="IsStaff" ng-model="vm.IsStaff" ng-value="false" /> Customer </label>&nbsp;&nbsp;
                    <label class="control-label"><input type="radio" name="IsStaff" ng-model="vm.IsStaff" ng-value="" />  Any </label>&nbsp;&nbsp;
                </div>
            </div>
  
            <div class="form-group form-group-sm">
              <label class="col-sm-3 control-label" for="UserName">UserName</label>
              <div class="col-sm-6">
                  <input id="UserName" type="text" class="form-control input" ng-model="vm.UserName" />
              </div>
          </div>

          <div class="form-group form-group-sm">
              <label class="col-sm-3 control-label" for="">Sales consultant</label>
              <div class="col-sm-6">
                  <select class="form-control input" ng-model="vm.SalesConsultant"
                          ng-options="k.Name  as k.Name for k in SalesConsultant"
                          ng-disabled="vm.Id != 0 && !$root.globals.IsA">
                      <option value="">-- Select --</option>
                  </select>
              </div>
          </div>

  
  
        </div>
  
        <div class="col-sm-12">
          <div class="pull-right">
            <button type="reset" class="btn " ng-click="clear()"><i class="glyphicon glyphicon-erase"></i> Clear</button>
            <button id="cust-btn"   class="btn btn-default"  ng-click="search()"><i class="glyphicon glyphicon-search"></i> Search</button>
          </div>
        </div>
      </div>
    </form>
</div>
  
  
  <div leppane="Audit">
    <div class="row">
      <div class="col-sm-12 white">
           <table class="table table-striped table-hover">
              <thead>
              <tr>
                  
                  <th><a ng-click="toggleSort('Id')">DateTime</a></th>
                  <th><a ng-click="toggleSort('Contact1.Name')">UserName</a></th>
                  <th><a ng-click="toggleSort('PaymentTerms')">Entity</a></th>
                  <th><a ng-click="toggleSort('Name')">Id</a></th>

                  <th><a ng-click="toggleSort('Contact1.Phone')">Changes (field: [from, to])</a></th>
                  <th>Action</th>
              </tr>
              </thead>
              <tbody>
  
              <tr ng-repeat="o in r.List" >
                  <td>{{::o.EventDate | date:'dd-MMM-yy HH:mm:ss:sss'}}
                    <br/>
                     <span class="text-muted" am-time-ago="::o.EventDate"></span>

                  </td>
                  <td>{{::o.UserName}}</td>
                  <td>{{::o.Entity}}</td>
                  <td>{{::o.EntityId}}</td>
                  <td class="pre">{{::o.Body}}</td>
              </tr>
              </tbody>
          </table>
  
          {{r.Summary}}
          <div paging
               page="r.Page"
               page-size="r.PageLength"
               total="r.Total"
               paging-action="goPage(page)"
               scroll-top="false"
               hide-if-empty="true"
               show-prev-next="true"
               show-first-last="true"
  
               text-next-class="glyphicon glyphicon-chevron-right"
               text-prev-class="glyphicon glyphicon-chevron-left"
               text-first-class="glyphicon glyphicon-backward"
               text-last-class="glyphicon glyphicon-forward">
        </div>
      </div>
    </div>
  </div>
  
  
  
  
  
  
  
  
  