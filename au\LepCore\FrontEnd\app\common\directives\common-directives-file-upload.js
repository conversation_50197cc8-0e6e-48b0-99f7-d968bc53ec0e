app = angular.module('app');


function hasItemsIn(result, prop) {
	return _.reduce(result.data, (accumulator, file) => {
		return (file.AAC && file.AAC[prop].length > 0) || accumulator;
	}, false);
}

const TEMPLATE_PROFILES = {
	spotcolor: [51, 52, 53, 61, 62, 63, 64],
	wideformat: [32, 33, 34, 35, 36, 37, 38, 41, 42, 43, 44]
};

function getAacProfileToRunFromJob(job) {
	const templateId = job.TemplateId || job.Template.Id;

	if (TEMPLATE_PROFILES.spotcolor.includes(templateId)) {
		return 'spotcolor';
	}

	if (TEMPLATE_PROFILES.wideformat.includes(templateId)) {
		return 'wideformat';
	}

	return 'default';
}

function extractAacMessages(aac) {
	var rtext = "";
	if (aac) {
		if (aac.errors.length) {
			rtext += "\nErrors:\n" + aac.errors.join('\n');
		}
		if (aac.warnings.length) {
			rtext += "\nWarnings:\n" + aac.warnings.join('\n');
		}
		if (aac.fixes.length) {
			rtext += "\nFixes:\n" + aac.fixes.join('\n');
		}
	} else {
		rtext += "\nAAC not performed";
	}
	return rtext;
}

const resetFileInput = (element, label) => {
	try {
		element.val('');
		element.prop('type', 'text');
		element.prop('type', 'file');
		label.removeClass('state-waiting2');
	} catch (e) {
		console.error('Failed to reset input:', e);
	}
};


function tryAutoAssignPosition(j, r) {
	// if job has an option to take multiart and 1 file was uploaded
	// set that to be the new mutliart
	var hasOptionForMultipageArt = j.RequiredPositions.indexOf('multiart') > -1;
	var hasOnlyOneOptionForArt = j.RequiredPositions.length == 1;
	var oneFileDroped = r.data.length == 1;


	if (oneFileDroped && hasOptionForMultipageArt) {
		// but remove the old multiart if there existed one
		j.Files = _.reject(j.Files, function (xx) { return xx.Position == "multiart"; });
		_.each(j.Files, function (x) {
			if (x.Id == r.data[0].Id) {
				x.Position = 'multiart';
			}
		});
	}

	if (oneFileDroped && hasOnlyOneOptionForArt) {
		// but remove the old if there existed one
		j.Files = _.reject(j.Files, function (xx) { return xx.Position == j.RequiredPositions[0]; });
		_.each(j.Files, function (x) {
			if (x.Id == r.data[0].Id) {
				x.Position = j.RequiredPositions[0];
			}
		});
	}
}


app.directive('attachArtwork', ['lepApi2', 'PdfChecker', 'ngDialog', function (lepApi2, pdfChecker, ngDialog) {
	var isAnyActive = 0;
	return {
		restrict: 'A',
		replace: false,
		scope: {
			order: '=order',
			job: '=attachArtwork',             // the job to which artwork would be attached
			enabled: '=attachArtworkEnabled'   // enable disable if job status does not permit attaching now
		},
		link: function (scope, el, attrs, ngModel) {
			var enterTarget = null;
			el.addClass('droptarget');

			function dragenter(ev) {
				enterTarget = ev.target;
				ev.preventDefault();
				ev.stopPropagation();
				el.addClass('over');
				return false;
			}

			function dragover(ev) {
				ev.preventDefault();
				ev.stopPropagation();
				return false;
			}

			function dragleave(ev) {
				if (enterTarget != ev.target)
					return;
				ev.preventDefault();
				ev.stopPropagation();
				el.removeClass('over');
			}

			function addComment(ctext, aacNotPerformed) {
				var m = { CommentText: ctext, Author: null, LepOnly: false, AACPerformed: true };
				if (!scope.job.CommentsToAdd)
					scope.job.CommentsToAdd = [];
				scope.job.CommentsToAdd.push(m);
			}

			function handleFileUpload(r) {
				var hasErrors = hasItemsIn(r, 'errors');
				scope.hasErrors = hasErrors;

				var hasWarnings = hasItemsIn(r, 'warnings');
				scope.hasWarnings = hasWarnings;

				var hasFixes = hasItemsIn(r, 'fixes');
				scope.hasFixes = hasFixes;

				scope.downloadReport = lepApi2.downloadReport;
				scope.downloadPostAac = lepApi2.downloadPostAac;

				if (!scope.job.CommentsToAdd)
					scope.job.CommentsToAdd = [];

				var aacNotPerformed = _.some(r.data, function (uf) {
					return (uf.AAC === null);
				});

				var ctext = "";
				for (var i = 0; i < r.data.length; i++) {
					ctext += "File: " + r.data[i].Id;
					ctext += extractAacMessages(r.data[i].AAC);
					ctext += '\n';
				}
				addComment(ctext, aacNotPerformed);

				if (!hasErrors && !hasWarnings && !hasFixes && !aacNotPerformed) {
					handleSuccessfulUpload(r);
				} else if (hasErrors || hasWarnings || hasFixes) {
					showAacDialog(r, aacNotPerformed, hasErrors);
				} else if ((!aacNotPerformed) || (!hasErrors)) {
					handleUnfixedFile(r, aacNotPerformed);
				}
			}

			function handleSuccessfulUpload(r) {
				_.each(r.data, function (uf) {
					uf.Id = uf.NameAac;
					uf.AACPerformed = true;
				});

				scope.job.Files = scope.job.Files.concat(r.data);
				addComment("Using AAC checked file. No issues found.");

				if (scope.job.Files.length > 1) {
					window.toastr.info("You have dropped multiple files on to this job! We need to know which the position of each file. Please denote the position of each file from the drop down beside the file name.");
				}
				tryAutoAssignPosition(scope.job, r);
			}

			function showAacDialog(r, aacNotPerformed, hasErrors) {
				el.removeClass('state-waiting2');
				scope.uploadResults = r.data;
				var dialog = ngDialog.open({
					template: 'common/aac-result.html',
					className: 'ngdialog-theme-default custom-width-900',
					scope: scope
				});
				dialog.closePromise.then(function (dv) {
					if (dv.value === false) {
						hasErrors = true;
					}

					if (dv.value === 2 && !aacNotPerformed && !hasErrors) {
						_.each(r.data, function (uf) {
							uf.Id = uf.Name;
							uf.AACPerformed = false;
						});

						scope.job.Files = scope.job.Files.concat(r.data);
						addComment("Customer has proceeded with unfixed file.", aacNotPerformed);
						tryAutoAssignPosition(scope.job, r);
					}

					if (dv.value === true && !hasErrors) {
						_.each(r.data, function (uf) {
							uf.Id = uf.NameAac;
							uf.AACPerformed = true;
						});

						scope.job.Files = scope.job.Files.concat(r.data);
						addComment("Using AAC checked file.", aacNotPerformed);

						if (scope.job.Files.length > 1) {
							window.toastr.info("You have dropped multiple files on to this job! We need to know which the position of each file. Please denote the position of each file from the drop down beside the file name.");
						}
						tryAutoAssignPosition(scope.job, r);
					}
					el.removeClass('state-waiting2');
				});
			}

			function handleUnfixedFile(r, aacNotPerformed) {
				_.each(r.data, function (uf) {
					if (aacNotPerformed)
						uf.AACPerformed = false;
					else if (!hasErrors)
						uf.AACPerformed = true;
				});
				scope.job.Files = scope.job.Files.concat(r.data);
				el.removeClass('state-waiting2');
				if (scope.job.Files.length > 1) {
					window.toastr.info("You have dropped multiple files on to this job! We need to know which the position of each file. Please denote the position of each file from the drop down beside the file name.");
				}
				tryAutoAssignPosition(scope.job, r);
			}

			function uploadFiles(ev) {
				ev.preventDefault();
				ev.stopPropagation();

				el.removeClass('over');
				el.addClass('state-waiting2');

				var files = ev.originalEvent.dataTransfer.files || ev.target.files;
				if (!scope.job.Files) scope.job.Files = [];

				var profile = getAacProfileToRunFromJob(scope.job);

				if (files.length > 0) {
					pdfChecker.check(files[0]).then(function () {
						lepApi2.documentsPost(files, profile).then(handleFileUpload, function () {
							window.toastr.error("Drag and drop a pdf file");
							scope.job.Files = null;
							el.removeClass('state-waiting2');
						});
					}, function () {
						window.toastr.error("Drag and drop a pdf file");
						scope.job.Files = null;
						el.removeClass('state-waiting2');
					});
				}
				return false;
			}

			scope.$watch('enabled', function (n, o) {
				if (n === true) {
					el.on('dragenter', dragenter);
					el.on('dragover', dragover);
					el.on('drop', uploadFiles);
					el.on('dragleave', dragleave);
				} else if (n === false) {
					el.off('dragenter', dragenter);
					el.off('dragover', dragover);
					el.off('drop', uploadFiles);
					el.off('dragleave', dragleave);
				}
			});
		}
	};
}]);



app.directive("ngFileModelPdfAac", [
	'$log', 'PdfChecker', 'lepApi2', 'ngDialog',
	function ($log, pdfChecker, lepApi2, ngDialog) {
		return {
			scope: { attachFilesTo: "=", aac: "=", job: "=" },

			link: function (scope, element, attributes) {
				var $input = element,
					$label = $input.next('label'),
					labelVal = $label.html();
				var position = attributes.name;

				if (window.isWL) {
					scope.isWL = true;
				}


				function addComment(ctext, aacNotPerformed) {
					var m = { Id: 0, CommentText: ctext };
					if (scope.job.Id === 0) {
						scope.job.CommentsToAdd.push(m);
						scope.job.Comments.push(m);
					} else {
						lepApi2.addCommentAac(scope.job.Id, ctext, aacNotPerformed);
						scope.job.Comments.push(m);
					}
				}

				function uploadFile(changeEvent) {

					$label.addClass('state-waiting2');

					// figure out which AAC profile the server needs to check against
					var profile = getAacProfileToRunFromJob(scope.job);

					var hasItemsIn = function (r, prop) {
						return _.reduce(r.data, function (result, uf) {
							return (uf.AAC && (uf.AAC[prop].length) > 0) || result;
						}, false);
					};

					var f = changeEvent.target.files[0];
					// pass through the PDF checker and then Upload to document/post
					pdfChecker.check(f).then(function () {
						lepApi2.documentsPost([f], profile).then(handleFileUpload, function () {
							resetFileInput($input, $label);
						});

						function handleFileUpload(r) {
							var hasErrors = hasItemsIn(r, 'errors');
							scope.hasErrors = hasErrors;

							var hasWarnings = hasItemsIn(r, 'warnings');
							scope.hasWarnings = hasWarnings;

							var hasFixes = hasItemsIn(r, 'fixes');
							scope.hasFixes = hasFixes;

							scope.uploadResults = r.data;

							var aacNotPerformed = _.some(r.data, function (uf) {
								return (uf.AAC === null);
							});

							if (window.ignoreErrorAndAttachAnyways) {
								hasErrors = hasWarnings = hasFixes = false;
								aacNotPerformed = true;
							}

							var ctext = generateCommentText(r.data, aacNotPerformed);
							addComment(ctext, aacNotPerformed);

							if (!hasErrors && !hasWarnings && !hasFixes && !aacNotPerformed) {
								handleSuccessfulUpload(r.data[0], position, f, $label, labelVal);
							} else if (hasErrors || hasWarnings || hasFixes) {
								showAacDialog(r, position, f, $label, labelVal, aacNotPerformed, hasErrors);
							} else if (aacNotPerformed || (!hasErrors && r.data[0])) {
								handleUnfixedFile(r.data[0], position, f, $label, labelVal, aacNotPerformed);
							}
						}

						function generateCommentText(data, aacNotPerformed) {
							var ctext = "";
							for (var i = 0; i < data.length; i++) {
								ctext += "File: " + data[i].Id;
								ctext += extractAacMessages(data[i].AAC);
								ctext += '\n';
								if (window.ignoreErrorAndAttachAnyways) {
									ctext += "\nAAC disabled in portal settings, using original uploaded file.";
								}
							}
							return ctext;
						}

						function handleSuccessfulUpload(uf, position, f, $label, labelVal) {
							uf.Id = uf.NameAac; //aac fixed
							uf.Position = position;
							uf.AACPerformed = true;
							_.remove(scope.attachFilesTo, { Position: position });
							scope.attachFilesTo.push(uf);
							updateModelAndLabel(f, $label, labelVal);
							addComment("File attached successfully without any issues.", false);
							toastr.info("File attached successfully without any issues.");
						}

						function showAacDialog(r, position, f, $label, labelVal, aacNotPerformed, hasErrors) {
							var dialog = ngDialog.open({
								template: 'common/aac-result.html',
								className: 'ngdialog-theme-default custom-width-900',
								scope: scope,
								closeByDocument: false,
								closeByEscape: false,
								overlay: false,
								backdrop: 'static',
							});
							dialog.closePromise.then(function (dv) {
								if (dv.value === false) {
									hasErrors = true;
								}
								if (dv.value === 2 && !aacNotPerformed && !hasErrors) {
									handleUnfixedFile(r.data[0], position, f, $label, labelVal, true);
								}
								if (dv.value === true && !hasErrors && r.data[0]) {
									handleSuccessfulUpload(r.data[0], position, f, $label, labelVal);
								}
							});
							$label.removeClass('state-waiting2');
						}

						function handleUnfixedFile(uf, position, f, $label, labelVal, aacNotPerformed) {
							uf.Id = uf.Name; //original supplied
							uf.Position = position;
							uf.AACPerformed = !aacNotPerformed;
							_.remove(scope.attachFilesTo, { Position: position });
							scope.attachFilesTo.push(uf);
							updateModelAndLabel(f, $label, labelVal);
						}

						function updateModelAndLabel(f, $label, labelVal) {
							scope.ngFileModelPdf = {
								lastModified: f.lastModified,
								lastModifiedDate: f.lastModifiedDate,
								name: f.name,
								size: f.size,
								type: f.type,
								file: f
							};
							var fileName = f.name.split('\\').pop();
							if (fileName)
								$label.find('span').html(fileName);
							else
								$label.html(labelVal);
							$label.removeClass('state-waiting2');
						}
					},
						function () {
							resetFileInput($input, $label);
						}
					);
				}

				// on pdf check fail
				function cleanInputOnError() {
					window.toastr.error('Only PDF files are allowed. The selected file does not appear to be a correct PDF file.');
					try {
						$input.value = '';
						$input.type = "text";
						$input.type = "file";
						$label.removeClass('state-waiting2');
					} catch (e) {
					}
				}


				scope.downloadReport = lepApi2.downloadReport;
				scope.downloadPostAac = lepApi2.downloadPostAac;

				element.bind("change", uploadFile);

				element
					.on('focus', function () { $input.addClass('has-focus'); })
					.on('blur', function () { $input.removeClass('has-focus'); });
			}
		};
	}]);




app.directive("ngFileModelPdf", ['$log', 'PdfChecker', function ($log, pdfChecker) {
	return {
		scope: { ngFileModelPdf: "=" },

		link: function (scope, element, attributes) {
			var $input = element,
				$label = $input.next('label'),
				labelVal = $label.html();

			//when the file input has a file
			element.bind("change", function (changeEvent) {
				var f = changeEvent.target.files[0];

				// pass through the PDF checker and
				pdfChecker.check(f).then(function () {
						// update the model and text lavel on the Label of the input
						scope.ngFileModelPdf = {
							lastModified: f.lastModified,
							lastModifiedDate: f.lastModifiedDate,
							name: f.name,
							size: f.size,
							type: f.type,
							file: f
						};

						var fileName = f.name.split('\\').pop();

						if (fileName)
							$label.find('span').html(fileName);
						else
							$label.html(labelVal);
					},
					function () {
						resetFileInput($input, $label);
					}
					);
			});

			element
				.on('focus', function () { $input.addClass('has-focus'); })
				.on('blur', function () { $input.removeClass('has-focus'); });
		}
	};
}]);

app.directive("filesModel",
	[function filesModelDirective() {
		return {
			controller: ['$parse', '$element', '$attrs', '$scope',
				function ($parse, $element, $attrs, $scope) {
					var exp = $parse($attrs.filesModel);

					$element.on('change',
						function () {
							exp.assign($scope, this.files);
							$scope.$apply();
						});
				}]
		};
	}]);

app.directive("ngFileModel", [function () {
	return {
		scope: {
			ngFileModel: "="
		},
		link: function (scope, element, attributes) {
			element.bind("change", function (changeEvent) {
				scope.$apply(function () {
					const f = changeEvent.target.files[0];
					scope.ngFileModel = {
						lastModified: f.lastModified,
						lastModifiedDate: f.lastModifiedDate,
						name: f.name,
						size: f.size,
						type: f.type,
						file: f
					};
				});
			});
		}
	};
}]);





