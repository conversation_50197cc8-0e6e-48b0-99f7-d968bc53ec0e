﻿

<div ng-form="orderForm" ng-if="order.Id && !jobForm.$dirty">

    <h4>Order # {{order.Id}}</h4>

    <div class="form-horizontal">
        <h4>Ordered by</h4>
        <div disabled="!order.Visibility.contactPnl">
            
            <div class="form-horizontal" all-input-disabled="{{!order.Visibility.contactPnl}}">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group form-group-sm">
                            <label class="control-label  col-xs-3" for="person">Person</label>
                            <div class="col-xs-7">
                                <div class="input-group">
                                    <div class="input-group-addon">
                                        <i class="glyphicon glyphicon-user"></i>
                                    </div>
                                    <input class="form-control" type="text" ng-model="order.WLContact.Name" ng-maxlength="50" size="50" />


                                    <div class="input-group-btn" ng-click="getSubCustomersRecipients()" 
                                         ng-if="order.Visibility.contactPnl">
                                        <a class="btn btn-sm btn-default" data-toggle="dropdown" href="#">
                                        <b class="glyphicon glyphicon-menu-down"></b></a>
                                        <ul class="dropdown-menu" role="menu">
                                            <li ng-repeat="fa in ContactsList | orderBy:'Name'">
                                                <a href="" ng-click="setOrderSubCustReipientDetails(fa)">{{fa.Name}}</a>
                                            </li>
                                        </ul>
                                    </div>



                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-6">
                        <div class="form-group form-group-sm">
                            <label class="control-label  col-xs-3" for="email">Email</label>
                            <div class="col-xs-7">
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <i class="glyphicon   glyphicon-envelope"></i>
                                    </span>
                                    <input class="form-control" type="email" ng-model=" order.WLContact.Email" name="email" maxlength="200" />
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="col-sm-6">
                        <div class="form-group form-group-sm">
                            <label class="control-label col-xs-3" for="phone">Phone</label>
                            <div class="col-xs-7">
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <i class="glyphicon glyphicon-phone-alt"></i>
                                    </span>

                                    <div class="col-sm-3 col-xs-2 lpad0 rpad0">
                                        <input class="form-control" type="text" ng-model=" order.WLContact.AreaCode" maxlength="6" />
                                    </div>
                                    <div class="col-sm-9 col-xs-10 lpad0 rpad0">
                                        <input class="form-control" type="text" ng-model=" order.WLContact.Phone" maxlength="12" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-6">
                        <div class="form-group form-group-sm">
                            <label class="control-label  col-xs-3" for="tel">Mobile</label>
                            <div class="col-xs-7">
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <i class="glyphicon   glyphicon-phone"></i>
                                    </span>
                                    <input class="form-control" type="text" ng-model=" order.WLContact.Mobile" maxlength="12" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>




    </div>

    <div class="form-horizontal">
        <h4>Delivery details</h4>

        <div class="row form-horizontal" ng-f0orm="deliveryDetailsForm">

            <div class="col-md-6 col-xs-12">
                <fieldset ng-disabled="!order.Visibility.saveButton">
                    <div class="form-group">
                        <label class="control-label  col-xs-3" for="person">Recipient / Business Name</label>
                        <div class="col-xs-7">
                            <input class="form-control" type="text" ng-model="order.RecipientName"
                                   name="RecipientName" ng-required="!addressDefault"
                                   maxlength="200" size="30" mvs />
                        </div>
                    </div>

                    <div class="form-group ">
                        <label class="control-label  col-xs-3" for="person">Recipient Phone</label>
                        <div class="col-xs-7">
                            <input class="form-control" type="text" ng-model="order.RecipientPhone" name="RecipientPhone"
                                   maxlength="12" size="12" mvs />
                        </div>
                    </div>

                    <div class="form-group form-group-sm" ng-show="order.Visibility.PackWithoutPallets">
                        <label class="col-xs-4 control-label" for="customer">Pack</label>
                        <div class="col-xs-8">
                            <label class="control-label"><input type="radio" ng-model="order.PackWithoutPallets" data-ng-value="false" /> Palletized </label>&nbsp;&nbsp;
                            <label class="control-label"><input type="radio" ng-model="order.PackWithoutPallets" data-ng-value="true" /> Loose Cartons</label>&nbsp;&nbsp;
                        </div>
                    </div>

                </fieldset>
            </div>

            <div class="col-md-6 col-xs-12">
                <div address-details address="order.DeliveryAddress" noteditable="!order.Visibility.saveButton"></div>
            </div>
        </div>

        <div class="row form-horizontal">
            <div class="col-xs-10  form-actions">
                <a class="btn btn-default bold" 
                   style="border:4px solid red" 
                   ng-show="deliveryDetailsChanged && order.Visibility.resetAddress"
                   ng-click="saveOrder()">
                    Save address and update freight options
                </a>
            </div>
        </div>
    </div>


    <!--FREIGHT SELECTION-->
    <!--<pre>

    {{order.Visibility.addressPnl  | json}}
    {{order.Courier | json}}
    {{(order.Visibility.addressPnl   && (order.Courier == 'None ~ None' || !order.Courier )) |json}}
    </pre>-->
    <div class="row">
        <div class="col-xs-11">

            <h4>Freight</h4>
            <div class="form-group  ng-class:{'has-error': order.Visibility.addressPnl   && (order.Courier == 'None ~ None' || !order.Courier ) }">
                
                    <!--&& !order.PackDetail.IsCustom-->
                    <div ng-if="order.Visibility.addressPnl" ng-hide="updatingCourier">
                        <select id="freights" class="form-control input monospace" ng-disabled="!order.Visibility.addressPnl"
                                ng-model="order.Courier"
                                ng-options="f.Value as f.Name for f in freights" ng-required="true">
                            <option value="">Select a courier and service</option>
                        </select>
                    </div>
                    <span ng-show="updatingCourier" style="font-weight: bold; color: orangered">
                        Finding couriers and freight charges, please wait...
                    </span> <!--|| order.PackDetail.IsCustom-->
                    <span ng-if="!order.Visibility.addressPnl">{{::order.Courier}}</span>
    
            </div>

        </div>
    </div>
    <!--ORDER SUMMARY FOOTER-->
    <div class="row">
        <div class="col-md-12 col-sm-12">


            <h4>Summary</h4>
            <table class="table table-striped col-xs-7">
                <thead>
                    <tr class="jobrow">
                        <th></th>
                        <th>Job#</th>
                        <th>Print</th>
                        <th>Job Name</th>
                        <th class="rlabel">Quantity</th>
                        <th>Type</th>
                        <th class="rlabel">Price</th>
                        <th class="actions">Action</th>
                    </tr>
                </thead>

                <tbody ng-repeat="j in order.Jobs">
                    <tr class="jobrow"
                        style="margin: 2px;">

                        <td class="" style="padding: 2px; vertical-align: middle; text-align: center; width: 120px;">
                            <span ng-show="::(j.Visibility.Thumbs.length>0)">
                                <img ng-repeat="t in j.Visibility.Thumbs track by $index"
                                     ng-src="/api/orders/Job/{{::j.Id}}/thumb/{{t}}"
                                     class="template-img1 grow axxx" />

                            </span>

                            <i ng-show="j.Visibility.artworkRequiredMsg && ( j.Files == undefined || (j.Files && j.Files.length==0))"
                               style="font-size: 30px; margin:5px;"
                               bs-tooltip placement="right" trigger="hover" toggle="tooltip" html="true"
                               data-title="This job has no artwork! <br/>Open/Edit the job and attach artwork."
                                class="glyphicon glyphicon-alert error-redish" >

                            </i>


                        </td>

                        <td class="actions">
                            <a class="bold" style="font-size: larger" ui-sref="^.job({jobId: j.Id, orderId: j.OrderId, customerId: customerId,t:t })">
                            {{::j.Id}}
                            </a>
                            <div style="white-space: nowrap">


                            </div>
                        </td>

                        <td class="nowrap">{{::j.PrintType | printtype}}</td>
                        <td>{{::j.Name}}</td>
                        <td class="rlabel">{{::j.Quantity | number : 0}}</td>
                        <td class="">{{::j.TemplateName}}</td>
                        <td class="rlabel">

                            {{j.PriceWL | currency}}
                        </td>
                        <td class="actions">
                            <!-- these need to be checked for permissions -->
                            <a class="" ui-sref="^.job({jobId: j.Id, orderId: j.OrderId, customerId: customerId,t:t })">
                                Edit
                            </a>
                            <a class="" ng-show="::j.Visibility.deleteButton" ng-click="jobCommand('Delete',j.Id)">Delete</a>
                        </td>
                    </tr>
                </tbody>


                <tbody>
                    <tr ng-show="j.Files.length>0">
                        <td colspan="8">
                            <b style="color: red;">
                                {{j.Files.length}} file will be attached to this job. <span ng-if="j.Files.length > 1">Please select files position from the dropdown above.</span> Press 'Save Order' button to save changes. To change these files drag and drop new file(s).
                            </b>
                        </td>

                    </tr>
                    <tr ng-if="(order.PriceOfJobsWL === '' || order.PriceOfJobsWL === null)  ">
                        <td colspan="6" class="rlabel">
                            Subject to Quote as system is unable to price.
                        </td>
                        <td class="total rlabel">TBD</td><td></td>
                    </tr>

                    <!--<tr ng-if="order.Visibility.promoBenifit">
                        <td colspan="6" class="rlabel">
                            - {{order.Visibility.promoBenifit}}
                        </td>
                        <td class="total rlabel">
                            - {{order.Visibility.promoBenifitAmount | currency}}
                        </td>

                    </tr>-->

                <tr id="freightTr">


                    <td colspan="6" class="rlabel">


                        Freight (Inc. handling)
                    </td>
                    <td class="rlabel">
                        {{order.PackDetail.Price | currency}}
                    </td>
                    <td></td>
                </tr>

                    <tr ng-if="!(order.PriceOfJobsWL === '' || order.PriceOfJobsWL === null)">
                        <td colspan="6" class="rlabel"></td>
                        <td class="total rlabel">
                            {{order.PriceOfJobsWL | currency}}
                        </td>
                        <td></td>
                    </tr>



                    <tr id="">
                        <td colspan="6" class="rlabel">
                            GST( {{::order.GST}} %)
                        </td>
                        <td class="total rlabel">
                            {{::order.PriceOfGSTWL | currency}}
                        </td>
                        <td></td>
                    </tr>

                    <tr>
                        <td colspan="6" class="price rlabel">Total (Inc. GST)</td>
                        <td colspan="1" class="total  rlabel price">{{::order.PriceWL | currency}}</td>
                        <td></td>
                    </tr>
                </tbody>
            </table>

            <!--<a ng-show="order.Visibility.addButton" class="btn btn-sm pull-right"
                   ui-sref="cust.order.addnewjob({orderId:order.Id})">
                Add  new job
            </a>-->
        </div>
    </div>

    <div class="row" ng-hide="pending > 0">
        <div class="col-md-12 col-sm-12">

            <div class="form-actions ">
                <a class="btn btn-default" ng-show="order.Visibility.addButton"
                   ui-sref="whitelabel.job({orderId:order.Id,jobId:0})">
                    <i class="glyphicon glyphicon-plus"></i>Add new job
                </a>
                <button class="btn btn-default" ng-show="order.Visibility.deleteButton" ng-click="deleteOrder()"><i class="glyphicon glyphicon-trash"></i> Delete</button>

                <a class="btn btn-default " ng-show="order.Visibility.saveButton"
                   click-and-wait="saveOrder()">
                    <i class="glyphicon glyphicon-floppy-save"></i>&nbsp;&nbsp;{{saveButtonText}}
                </a>


            </div>
        </div>
        <div class="col-md-12 col-sm-12">

            <div class="form-actions ">


                <div ng-show="orderCanBePaidForNow && showPaypal">
                    <paypal-fast-checkout config="paypal" txn="paypalTxns" callbacksuccess="orderPaidForInPaypal"></paypal-fast-checkout>
                </div>

                <a href="" ng-click="onStripe()" class="btn " 
                   style="background-color:#2eb1ff; color:white; border-radius:4px; width: 250px; border-color: none!important; padding:4px; "
                   ng-show="orderCanBePaidForNow && showStripe">
                    <img src="../images/stripe.png" style="margin:0; height:26px;"> Pay by Card
                </a>

                <br />     <br />
                <a href="" ng-click="chargeToMyAccount()" class="btn "
                   style="background-color:#2eb1ff; color:white; border-radius:4px; width: 250px; border-color: none!important; padding:4px;"
                   ng-show="orderCanBePaidForNow && ppc.PaymentTerms === 0">
                    Charge to my account
                </a>
            </div>
        </div>


    </div>


    <!--        <pre>orderForm.$valid = {{orderForm.$valid}}
    order.Visibility.submitButton   = {{ order.Visibility.submitButton  | json}}
    !deliveryDetailsChanged			= {{ !deliveryDetailsChanged  | json }}
    !updatingCourier				= {{ !updatingCourier   | json}}
    !fileAttachmentPending			= {{ !fileAttachmentPending   | json}}
    </pre>       -->



<div ng-if="order.IsWLOrderPaidFor">
    <h4>Order has been submitted. Thank you.</h4>
</div>

</div>

