{
    "IMP": {
        "HotfolderPath": "\\\\ppp01\\HotFolderRoot\\MyLEP"
    },
    "Serilog": {
        "AllowedHosts": "*",
        "Using": [
            "Serilog.Sinks.Console",
            //"Serilog.Sinks.File",
            "Serilog.Sinks.Seq"
        ],
        "MinimumLevel": {
            "Default": "Debug",
            "Override": {
                "Microsoft": "Warning",
                "System": "Warning"
            }
        },
        "WriteTo": [
            { "Name": "Console" },
            //{
            //    "Name": "File",
            //    "Args": { "path": "Serilog\\logs.txt" }
            //},
            {
                "Name": "Seq",
                "Args": { "serverUrl": "http://localhost:5341" }
            }
        ],
        "Enrich": [ "FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId" ]
        //"Destructure": [
        //    {
        //        "Name": "With",
        //        "Args": { "policy": "Sample.CustomPolicy, Sample" }
        //    },
        //    {
        //        "Name": "ToMaximumDepth",
        //        "Args": { "maximumDestructuringDepth": 4 }
        //    },
        //    {
        //        "Name": "ToMaximumStringLength",
        //        "Args": { "maximumStringLength": 100 }
        //    },
        //    {
        //        "Name": "ToMaximumCollectionCount",
        //        "Args": { "maximumCollectionCount": 10 }
        //    }
        //],
        //"Properties": {
        //    "Application": "Sample"
        //}
    }
}