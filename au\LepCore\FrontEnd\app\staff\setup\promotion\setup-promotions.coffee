appStaff = angular.module('app.staff')

appStaff.factory 'Promotion', ['$resource', ($resource) ->
	$resource 'api/Staff/Promotions/:Id', { Id: '@Id' },
		'update':
			method: 'PUT'
		'query':
			method: 'POST'
			isArray: false
		'patch':
			method: 'PATCH'
]

appStaff.controller 'StaffSetupPromotionsController', [
	'$scope', 'lepApi2', 'enums', 'Promotion'
	($scope, lepApi2,  enums, Promotion) ->
		$scope.enums = enums;
		$scope.vm = {Page:1}

		$scope.search = () ->
			Promotion.query($scope.vm, (r) -> $scope.r = r)

		$scope.toggleSort = (sort) ->

			$scope.vm.SortDir =  sort == $scope.vm.SortField and !$scope.vm.SortDir
			$scope.vm.SortField = sort

			$scope.search();

		$scope.goPage= (p) ->
			$scope.vm.Page = p
			$scope.search()

		$scope.search()

		return @
	]

appStaff.controller 'StaffSetupPromotionsEditController', [
	'$scope','lepApi2','enums', '$stateParams', 'Promotion', '$state',
	($scope, lepApi2,  enums, $stateParams, Promotion, $state) ->
		$scope.vis = {}
		$scope.enums = enums;

		vmr = null
		vmo = null

		$scope.load = (id) ->
			lepApi2.get('staff/promotions/matrix3').then (d) ->
				$scope.d = d
				$scope.d.matrix2 = {}
				Promotion.get({Id:id},  (r) ->
					vmr = r

					vmo = angular.copy(vmr)
					delete vmo.$resource
					delete vmo.$promise

					$scope.vm = angular.copy(vmo)
					pps = $scope.vm.PromotedProducts

					r = {}
					for t in d.templatesL
						if !(r[t.Id]) then  r[t.Id] = { Sizes: [], Stocks: [] }

					_.each pps, (x) ->
						if r[x.T].Sizes.indexOf(x.P) == -1 then r[x.T].Sizes.push(x.P)
						if r[x.T].Stocks.indexOf(x.S) == -1 then r[x.T].Stocks.push(x.S)

					$scope.d.matrix2 = r
				)

		$scope.load($stateParams.id)


		$scope.save = () ->
			a = []
			for t in  $scope.d.templatesL
				x = $scope.d.matrix2[t.Id]
				if !x then continue
				for stk in x.Stocks
					for ps in x.Sizes
						a.push({T:t.Id, S:stk, P: ps})
			$scope.vm.PromotedProducts = a
			Promotion.update({Id: $stateParams.id}, $scope.vm, (value, responseHeadersFn, status, statusText) ->
				id = parseInt(responseHeadersFn('Id'), 10)
				toastr.info( "Success")
				$state.go(  'staff.setup.setup-promotions-edit', {id: id}, {reload: true})
				)



		$scope.patch = () ->
			Promotion.patch({id: $stateParams.id},   $scope.diff)



		$scope.toggleSizesUnderTemplate = (e,templateId) ->
			checked = e.target.checked
			if !$scope.d.matrix2[templateId] then  $scope.d.matrix2[templateId] = { Sizes: [], Stocks: [] }
			if checked
				$scope.d.matrix2[templateId].Sizes = angular.copy($scope.d.matrix[templateId].Sizes)
			else
				$scope.d.matrix2[templateId].Sizes = []

		$scope.toggleStocksUnderTemplate =  (e,templateId) ->
			checked = e.target.checked
			if !$scope.d.matrix2[templateId] then  $scope.d.matrix2[templateId] = { Sizes: [], Stocks: [] }
			if checked
				$scope.d.matrix2[templateId].Stocks = angular.copy($scope.d.matrix[templateId].Stocks)
			else
				$scope.d.matrix2[templateId].Stocks = []

		$scope.toggleAllUnderTemplate = (e,templateId) ->
			$scope.toggleSizesUnderTemplate(e,templateId)
			$scope.toggleStocksUnderTemplate(e,templateId)

		$scope.toggleAllTemplates = (e) ->
			checked = e.target.checked
			if checked
				$scope.d.matrix2 = angular.copy($scope.d.matrix)
			else
				$scope.d.matrix2 = {}

#		$scope.toggleSizesUnderTemplate = (e,templateId) ->
#			_.each($scope.d.matrix[templateId].Sizes, (x)->
#				x.c = e.target.checked
#				x
#			)
#
#		$scope.toggleStocksUnderTemplate =  (e,templateId) ->
#			_.each($scope.d.matrix[templateId].Stocks, (x)->
#				x.c = e.target.checked
#				x
#			)
#
		return
	]
