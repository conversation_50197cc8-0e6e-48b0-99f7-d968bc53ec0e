<div class="form-horizontal">
    <div class="row">

        <div class="form-group">
            <label class="col-xs-2 control-label" for="order-no">AAC enabled? </label>
            <div class="col-xs-10">
                <span>
                    <span class="radio-inline">
                        <label>
                            <input name="IsAacEnabled" type="radio" ng-model="vm.PrintPortalSettings.IsAacEnabled" ng-value="false" />No
                        </label>
                    </span>
                    <span class="radio-inline">
                        <label>
                            <input name="IsAacEnabled" type="radio" ng-model="vm.PrintPortalSettings.IsAacEnabled" ng-value="true" />Yes
                        </label>
                    </span>
                </span>
                <span class="help-block">
                    Automated Artwork Check (AAC) can automatically detect a number of common problems with artwork.
                </span>
            </div>
        </div>


        <div class="form-group">
            <label class="control-label col-xs-2">Iframe HTML</label>
            <div class="col-xs-10" style="color:darkblue">
                <pre>{{wlInclude}}</pre>
                <span class="help-block">
                    Copy and paste this code in your webpage where you want LEP's order page to show up.
                    You can modify the width and height as per your needs.
                </span>
            </div>
        </div>


        <div class="form-group">
            <label class="control-label col-xs-2">Test</label>
            <div class="col-xs-10 form-control-static" style="color:darkblue">
                <a href="{{baseSite2}}" target="_test">{{baseSite2}}</a>
                
            </div>
        </div>

        <div class="form-group">
            <label class="control-label col-xs-2" for="CustomCssURL">Style URL</label>
            <div class="col-xs-10">
                <input class="form-control" type="URL" ng-model="vm.PrintPortalSettings.CustomCssURL" id="CustomCssURL" />
                <span class="help-block">
                    Specify the URL of the css style file so the iframe blends in nicely with the look and feel of your website.
                </span>
            </div>
        </div>


        <div style="margin-bottom:30px">
            <div class="form-group">
                <label class="col-xs-2 control-label">PayPal </label>
                <div class="col-xs-10"></div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-2 smaller" for="PayPalClientId">PayPal Id</label>
                <div class="col-xs-10">
                    <input class="form-control" type="text" ng-model="vm.PrintPortalSettings.PayPalClientId" id="PayPalClientId" />
                    <div class="help-block">
                        To accept payment through PayPal, enter your PayPal client ID here. (otherwise leave blank)
                        <a href="/images/pdfs/paypal.pdf" target="_blank">more help here.</a>
                    </div>
                </div>
            </div>
        </div>


        <div style="margin-bottom:30px">
            <div class="form-group">
                <label class="col-xs-2 control-label" for="order-no">Stripe </label>
                <div class="col-xs-10"></div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-2 smaller" for="stripePublishableKey">Publishable Key</label>
                <div class="col-xs-10">
                    <input class="form-control" type="text" ng-model="vm.PrintPortalSettings.StripePublishableKey" id="stripePublishableKey" />
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-2  smaller" for="stripeRestrictedChargeKey">Charge key</label>
                <div class="col-xs-10">
                    <input class="form-control" type="text" ng-model="vm.PrintPortalSettings.StripeRestrictedChargeKey" id="stripeRestrictedChargeKey" />
                    <div class="help-block">
                        To accept payment through Stripe, enter your Stripe API Keys    <a href="https://dashboard.stripe.com/test/apikeys" target="_blank">from here </a>  (otherwise leave blank).


                        <a href="/images/pdfs/stripe.pdf" target="_blank">more help here.</a>
                    </div>
                    <div class="help-block">
                        
                        
                    </div>
                </div>
            </div>
        </div>



        <div class="form-group">
            <label class="control-label col-xs-2">Denied Templates</label>
            <div class="col-xs-10">
                <ui-select multiple ng-model="vm.PrintPortalSettings.DeniedTemplates" theme="bootstrap" close-on-select="false" style="width: 100%; "
                           title="Choose job templates not available on your print portal">
                    <ui-select-match placeholder="Select templates...">
                        {{$item.Name}}
                    </ui-select-match>

                    <ui-select-choices repeat="c.Id as c in allTemplatesName | filter:$select.search">
                        {{c.Name}}
                    </ui-select-choices>
                </ui-select>
                <div class="help-block">
                    If you want any job categories not orderable by your web users, please select them here.
                </div>
            </div>
        </div>


        <div class="form-group" ng-click="updateVm()" sddtyle="max-height: 400px; overflow-y:scroll">
            <label class="control-label col-xs-2">Allowed Products</label>
            <div class="col-xs-10" style="font-size: smaller;">
                <div class="help-block">
                    Please select product comibations that you want offer in your site.
                    If nothing is selected eveything will be allowed.
                </div>



                <label class="checkbox"><input type="checkbox" ng-click="toggleAllTemplates($event)" /> All templates</label>
                
                <div class="row template-matrix-row" ng-repeat="t in d.templatesL  | filter:filterOutDeniedTemplates ">
                    <div class="col-sm-12">
                        <label>
                            <input type="checkbox" ng-click="toggleAllUnderTemplate($event,t.Id)" />&nbsp;&nbsp;&nbsp;<h5 style="display: inline">{{::t.Name}}</h5>
                        </label>
                    </div>
                    <div class="col-md-1 ">
        
        
        
                    </div>
                    <div class="col-sm-3">
                        <label class="checkbox alltoggle"><input type="checkbox" ng-click="toggleSizesUnderTemplate($event,t.Id)" /> All sizes</label>
                        <div ng-repeat="k in d.matrix[t.Id].Sizes">
                            <label class="checkbox"><input type="checkbox" checkbox-model="d.matrix2[t.Id].Sizes" checkbox-value="k*1" multiple>{{:: d.sizesD[k]}}</label>
                        </div>
                    </div>
        
                    <div class="col-sm-8">
                        <div class="row ">
                            <div class="col-sm-12">
                                <label class="checkbox alltoggle"><input type="checkbox" ng-click="toggleStocksUnderTemplate($event,t.Id)" /> All stocks</label>
                            </div>
                        </div>
        
                        <div class="row ">
                            <div ng-repeat="k in d.matrix[t.Id].Stocks" class="col-md-5">
                                <label class="checkbox"><input type="checkbox" checkbox-model="d.matrix2[t.Id].Stocks" checkbox-value="k*1" multiple>{{:: d.stocksD[k]}}</label>
                            </div>
                        </div>
                    </div>
        
                </div>
            </div>
        </div>







        <div class="form-group ng-class:{'has-error' : (!vm.PrintPortalSettings.WhiteLabelGlobalMarkup)}">
            <label class="control-label col-xs-2"> General (%)</label>
            <div class="col-xs-2">
                <input class="form-control" type="number" ng-model="vm.PrintPortalSettings.WhiteLabelGlobalMarkup"
                       ng-required="true" ng-min="0"  />
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-2"></label>

            <div class="help-block col-xs-10">
                    A catch all markup % that gets applied to all jobs, if a Category or Price range specific markup is not specified below.
                </div>
        </div>



    <div class="form-group">
        <label class="col-xs-2 control-label" for="order-no">Pricing method</label>
        <div class="col-xs-10">
            <div>
                <div class="radio">
                    <label class="ng-class:{'bold': vm.PrintPortalSettings.PricingModel == 1}">
                        <input name="PricingModel" type="radio" ng-model="vm.PrintPortalSettings.PricingModel" ng-value="1*1" />Category Markup %

                    </label>
                    <span class="help-block">Specify product type / catergory specific markups based on quantity</span>
                </div>
                <div class="radio" >
                    <label class="ng-class:{'bold': vm.PrintPortalSettings.PricingModel == 2}">
                        <input name="PricingModel" type="radio" ng-model="vm.PrintPortalSettings.PricingModel" ng-value="1*2" />Price Range Markup %

                    </label>
                    <span class="help-block">specify price range/band specific markup</span>
                </div>
            </div>
            <div class="help-block" >
                This pricing model selection allows choice between "Category based Markup Scheme" or a "Price Range based Markup Scheme".
                Pricing model for registered Sub Customers can be over-ridden in the  Sub Customer's setting page.
            </div>
        </div>
    </div>


    <div class="" ng-if="vm.PrintPortalSettings.PricingModel == 1">
        <div lep-white-label-setup-pricing-1 vm="vm" ct="allTemplatesName"></div>
    </div>


    <div class="" ng-if="vm.PrintPortalSettings.PricingModel == 2">
        <div lep-white-label-setup-pricing-2 vm="vm"></div>
    </div>
</div>
</div>



